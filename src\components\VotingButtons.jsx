import React, { useState, useEffect } from 'react';
import { voteOnContent, getUserVote } from '../services/firebase';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

const VotingButtons = ({ contentId, initialUpvotes = 0, initialDownvotes = 0, initialNetVotes = 0, onVoteUpdate }) => {
  const [upvotes, setUpvotes] = useState(initialUpvotes);
  const [downvotes, setDownvotes] = useState(initialDownvotes);
  const [netVotes, setNetVotes] = useState(initialNetVotes);
  const [userVote, setUserVote] = useState(null); // 'upvote', 'downvote', or null
  const [isVoting, setIsVoting] = useState(false);

  // Generate a simple user ID based on browser fingerprint
  // In a real app, you'd use proper authentication
  const getUserId = () => {
    let userId = localStorage.getItem('grinbin_user_id');
    if (!userId) {
      userId = 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
      localStorage.setItem('grinbin_user_id', userId);
    }
    return userId;
  };

  const userId = getUserId();

  // Load user's existing vote when component mounts
  useEffect(() => {
    const loadUserVote = async () => {
      const vote = await getUserVote(contentId, userId);
      setUserVote(vote);
    };
    loadUserVote();
  }, [contentId, userId]);

  const handleVote = async (voteType) => {
    if (isVoting) return;

    setIsVoting(true);

    try {
      const result = await voteOnContent(contentId, voteType, userId);

      if (result.success) {
        // Update local state based on the vote action
        if (userVote === voteType) {
          // Removing existing vote
          if (voteType === 'upvote') {
            setUpvotes(prev => prev - 1);
            setNetVotes(prev => prev - 1);
          } else {
            setDownvotes(prev => prev - 1);
            setNetVotes(prev => prev + 1);
          }
          setUserVote(null);
        } else if (userVote && userVote !== voteType) {
          // Changing vote
          if (voteType === 'upvote') {
            setUpvotes(prev => prev + 1);
            setDownvotes(prev => prev - 1);
            setNetVotes(prev => prev + 2);
          } else {
            setUpvotes(prev => prev - 1);
            setDownvotes(prev => prev + 1);
            setNetVotes(prev => prev - 2);
          }
          setUserVote(voteType);
        } else {
          // New vote
          if (voteType === 'upvote') {
            setUpvotes(prev => prev + 1);
            setNetVotes(prev => prev + 1);
          } else {
            setDownvotes(prev => prev + 1);
            setNetVotes(prev => prev - 1);
          }
          setUserVote(voteType);
        }

        // Notify parent component of vote update for potential re-sorting
        if (onVoteUpdate) {
          onVoteUpdate(contentId, netVotes);
        }
      } else {
        console.error('Voting failed:', result.error);
      }
    } catch (error) {
      console.error('Error voting:', error);
    } finally {
      setIsVoting(false);
    }
  };

  return (
    <div className="voting-buttons">
      <button
        className={`vote-button upvote ${userVote === 'upvote' ? 'active' : ''}`}
        onClick={() => handleVote('upvote')}
        disabled={isVoting}
        aria-label="Upvote"
      >
        <KeyboardArrowUpIcon fontSize="small" />
        <span className="vote-count">{upvotes}</span>
      </button>

      <button
        className={`vote-button downvote ${userVote === 'downvote' ? 'active' : ''}`}
        onClick={() => handleVote('downvote')}
        disabled={isVoting}
        aria-label="Downvote"
      >
        <KeyboardArrowDownIcon fontSize="small" />
        <span className="vote-count">{downvotes}</span>
      </button>

      <div className="net-votes">
        <span className={`net-score ${netVotes > 0 ? 'positive' : netVotes < 0 ? 'negative' : 'neutral'}`}>
          {netVotes > 0 ? '+' : ''}{netVotes}
        </span>
      </div>
    </div>
  );
};

export default VotingButtons;
