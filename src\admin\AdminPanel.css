.admin-panel {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.admin-panel h1 {
  text-align: center;
  margin-bottom: 2rem;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.filter-controls select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ccc;
}

.loading, .no-submissions {
  text-align: center;
  padding: 2rem;
  background: #f5f5f5;
  border-radius: 8px;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.submissions-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.submission-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.submission-header {
  display: flex;
  justify-content: space-between;
  padding: 1rem;
  background: #f0f0f0;
  border-bottom: 1px solid #ddd;
}

.submission-type {
  text-transform: uppercase;
  font-weight: bold;
}

.submission-date {
  font-size: 0.9rem;
  color: #666;
}

.submission-content {
  padding: 1.5rem;
}

.submission-author {
  padding: 0 1.5rem 1rem;
  font-style: italic;
  color: #666;
  font-size: 0.9rem;
}

.submission-actions {
  display: flex;
  padding: 1rem;
  background: #f0f0f0;
  border-top: 1px solid #ddd;
  gap: 1rem;
}

.approve-button, .reject-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
}

.approve-button {
  background: #4caf50;
  color: white;
}

.approve-button:hover {
  background: #43a047;
}

.reject-button {
  background: #f44336;
  color: white;
}

.reject-button:hover {
  background: #e53935;
}