<svg width="320" height="50" viewBox="0 0 320 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<defs>
  <linearGradient id="textGrad" x1="0%" y1="0%" x2="100%" y2="0%">
    <stop offset="0%" style="stop-color:#1976D2"/>
    <stop offset="100%" style="stop-color:#2196F3"/>
  </linearGradient>
</defs>

<!-- Pure typography design -->
<g transform="translate(160, 25)">
  <text x="-140" y="-5" font-family="<PERSON><PERSON>, <PERSON><PERSON>, sans-serif" font-size="20" font-weight="300" fill="#666">The</text>
  <text x="-90" y="15" font-family="<PERSON><PERSON>, <PERSON>l, sans-serif" font-size="36" font-weight="700" fill="url(#textGrad)" letter-spacing="2px">GRIN</text>
  <text x="40" y="15" font-family="<PERSON><PERSON>, <PERSON><PERSON>, sans-serif" font-size="36" font-weight="300" fill="#0d47a1" letter-spacing="1px">BIN</text>
</g>

<!-- Subtle underline -->
<line x1="70" y1="42" x2="180" y2="42" stroke="#2196F3" stroke-width="1" opacity="0.3"/>
</svg>
