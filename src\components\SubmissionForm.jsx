import React, { useState, useEffect } from 'react';
import { submitUserContent } from '../services/firebase';
import CloseIcon from '@mui/icons-material/Close';

const SubmissionForm = ({ onClose, category = 'joke' }) => {
  const [type, setType] = useState(category);
  const [content, setContent] = useState('');
  const [authorName, setAuthorName] = useState('');
  const [status, setStatus] = useState('idle'); // idle, submitting, success, error
  const [networkStatus, setNetworkStatus] = useState(navigator.onLine);
  const [errorMessage, setErrorMessage] = useState('');

  // For riddles and trivia
  const [question, setQuestion] = useState('');
  const [answer, setAnswer] = useState('');
  const [incorrectAnswers, setIncorrectAnswers] = useState(['', '', '']);

  // Monitor network status
  useEffect(() => {
    const handleOnline = () => setNetworkStatus(true);
    const handleOffline = () => setNetworkStatus(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!networkStatus) {
      setStatus('error');
      setErrorMessage('No internet connection. Please check your network and try again.');
      return;
    }

    setStatus('submitting');
    setErrorMessage('');
    let submissionContent;

    try {
      if (type === 'joke' || type === 'fact') {
        submissionContent = content;
      } else if (type === 'riddle') {
        submissionContent = { question, answer };
      } else if (type === 'trivia') {
        submissionContent = {
          question,
          correctAnswer: answer,
          incorrectAnswers: incorrectAnswers.filter(a => a.trim() !== '')
        };
      }

      const result = await submitUserContent(type, submissionContent, authorName || 'Anonymous');

      if (result.success) {
        setStatus('success');
        setContent('');
        setQuestion('');
        setAnswer('');
        setIncorrectAnswers(['', '', '']);

        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setStatus('error');
        if (result.error === 'Request timed out') {
          setErrorMessage('Connection to our servers timed out. Please try again later.');
        } else {
          setErrorMessage(result.error || 'Something went wrong. Please try again.');
        }
        console.error('Submission failed:', result.error);
      }
    } catch (error) {
      console.error('Error in form submission:', error);
      setStatus('error');
      setErrorMessage('An unexpected error occurred. Please try again.');
    }
  };

  // Test functions have been removed

  const renderFormFields = () => {
    if (type === 'joke' || type === 'fact') {
      return (
        <div className="form-group">
          <label>{type === 'joke' ? 'Your Joke' : 'Your Fact'}</label>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={type === 'joke' ? "Enter your joke..." : "Enter your fact..."}
            rows={5}
            required
          />
        </div>
      );
    } else if (type === 'riddle') {
      return (
        <>
          <div className="form-group">
            <label>Riddle Question</label>
            <textarea
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="Enter your riddle question..."
              rows={3}
              required
            />
          </div>
          <div className="form-group">
            <label>Riddle Answer</label>
            <textarea
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              placeholder="Enter the answer to your riddle..."
              rows={2}
              required
            />
          </div>
        </>
      );
    } else if (type === 'trivia') {
      return (
        <>
          <div className="form-group">
            <label>Trivia Question</label>
            <textarea
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              placeholder="Enter your trivia question..."
              rows={3}
              required
            />
          </div>
          <div className="form-group">
            <label>Correct Answer</label>
            <input
              type="text"
              value={answer}
              onChange={(e) => setAnswer(e.target.value)}
              placeholder="Enter the correct answer..."
              required
            />
          </div>
          <div className="form-group">
            <label>Incorrect Answers (at least one required)</label>
            {incorrectAnswers.map((ans, index) => (
              <input
                key={index}
                type="text"
                value={ans}
                onChange={(e) => {
                  const newAnswers = [...incorrectAnswers];
                  newAnswers[index] = e.target.value;
                  setIncorrectAnswers(newAnswers);
                }}
                placeholder={`Incorrect answer ${index + 1}...`}
                className="incorrect-answer-input"
                required={index === 0}
              />
            ))}
          </div>
        </>
      );
    }
  };

  return (
    <div className="submission-form">
      <button className="close-button" onClick={onClose} aria-label="Close">
        <CloseIcon />
      </button>
      <h2>Submit Your Own {type.charAt(0).toUpperCase() + type.slice(1)}</h2>
      <p className="submission-note">
        Your submission will be reviewed before being added to the community content.
      </p>
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>Content Type</label>
          <select value={type} onChange={(e) => setType(e.target.value)}>
            <option value="joke">Joke</option>
            <option value="fact">Fact</option>
            <option value="riddle">Riddle</option>
            <option value="trivia">Trivia</option>
          </select>
        </div>

        <div className="form-group">
          <label>Your Name (optional)</label>
          <input
            type="text"
            value={authorName}
            onChange={(e) => setAuthorName(e.target.value)}
            placeholder="Anonymous"
          />
        </div>

        {renderFormFields()}

        <button
          type="submit"
          disabled={status === 'submitting' ||
            (type === 'joke' || type === 'fact' ? !content.trim() :
              type === 'riddle' ? !question.trim() || !answer.trim() :
                !question.trim() || !answer.trim() || !incorrectAnswers[0].trim())}
          className="submit-button"
        >
          {status === 'submitting' ? 'Submitting...' : 'Submit'}
        </button>

        {/* Remove the test buttons */}

        {status === 'success' && <p className="success-message">Thank you for your submission!</p>}
        {status === 'error' && (
          <p className="error-message">
            {errorMessage || 'Something went wrong. Please try again.'}
          </p>
        )}
      </form>
    </div>
  );
};

export default SubmissionForm;








