function bg(u,c){for(var f=0;f<c.length;f++){const r=c[f];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in u)){const h=Object.getOwnPropertyDescriptor(r,o);h&&Object.defineProperty(u,o,h.get?h:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const h of o)if(h.type==="childList")for(const g of h.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&r(g)}).observe(document,{childList:!0,subtree:!0});function f(o){const h={};return o.integrity&&(h.integrity=o.integrity),o.referrerPolicy&&(h.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?h.credentials="include":o.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function r(o){if(o.ep)return;o.ep=!0;const h=f(o);fetch(o.href,h)}})();function Th(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var yf={exports:{}},nu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X0;function Sg(){if(X0)return nu;X0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function f(r,o,h){var g=null;if(h!==void 0&&(g=""+h),o.key!==void 0&&(g=""+o.key),"key"in o){h={};for(var S in o)S!=="key"&&(h[S]=o[S])}else h=o;return o=h.ref,{$$typeof:u,type:r,key:g,ref:o!==void 0?o:null,props:h}}return nu.Fragment=c,nu.jsx=f,nu.jsxs=f,nu}var L0;function Tg(){return L0||(L0=1,yf.exports=Sg()),yf.exports}var L=Tg(),gf={exports:{}},ft={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Q0;function Ag(){if(Q0)return ft;Q0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),g=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),R=Symbol.iterator;function M(p){return p===null||typeof p!="object"?null:(p=R&&p[R]||p["@@iterator"],typeof p=="function"?p:null)}var X={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},q=Object.assign,x={};function V(p,j,w){this.props=p,this.context=j,this.refs=x,this.updater=w||X}V.prototype.isReactComponent={},V.prototype.setState=function(p,j){if(typeof p!="object"&&typeof p!="function"&&p!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,p,j,"setState")},V.prototype.forceUpdate=function(p){this.updater.enqueueForceUpdate(this,p,"forceUpdate")};function Z(){}Z.prototype=V.prototype;function lt(p,j,w){this.props=p,this.context=j,this.refs=x,this.updater=w||X}var F=lt.prototype=new Z;F.constructor=lt,q(F,V.prototype),F.isPureReactComponent=!0;var W=Array.isArray,G={H:null,A:null,T:null,S:null,V:null},tt=Object.prototype.hasOwnProperty;function $(p,j,w,Y,K,rt){return w=rt.ref,{$$typeof:u,type:p,key:j,ref:w!==void 0?w:null,props:rt}}function yt(p,j){return $(p.type,j,void 0,void 0,void 0,p.props)}function gt(p){return typeof p=="object"&&p!==null&&p.$$typeof===u}function m(p){var j={"=":"=0",":":"=2"};return"$"+p.replace(/[=:]/g,function(w){return j[w]})}var k=/\/+/g;function P(p,j){return typeof p=="object"&&p!==null&&p.key!=null?m(""+p.key):j.toString(36)}function st(){}function Ht(p){switch(p.status){case"fulfilled":return p.value;case"rejected":throw p.reason;default:switch(typeof p.status=="string"?p.then(st,st):(p.status="pending",p.then(function(j){p.status==="pending"&&(p.status="fulfilled",p.value=j)},function(j){p.status==="pending"&&(p.status="rejected",p.reason=j)})),p.status){case"fulfilled":return p.value;case"rejected":throw p.reason}}throw p}function bt(p,j,w,Y,K){var rt=typeof p;(rt==="undefined"||rt==="boolean")&&(p=null);var ct=!1;if(p===null)ct=!0;else switch(rt){case"bigint":case"string":case"number":ct=!0;break;case"object":switch(p.$$typeof){case u:case c:ct=!0;break;case z:return ct=p._init,bt(ct(p._payload),j,w,Y,K)}}if(ct)return K=K(p),ct=Y===""?"."+P(p,0):Y,W(K)?(w="",ct!=null&&(w=ct.replace(k,"$&/")+"/"),bt(K,j,w,"",function(Qt){return Qt})):K!=null&&(gt(K)&&(K=yt(K,w+(K.key==null||p&&p.key===K.key?"":(""+K.key).replace(k,"$&/")+"/")+ct)),j.push(K)),1;ct=0;var se=Y===""?".":Y+":";if(W(p))for(var Ct=0;Ct<p.length;Ct++)Y=p[Ct],rt=se+P(Y,Ct),ct+=bt(Y,j,w,rt,K);else if(Ct=M(p),typeof Ct=="function")for(p=Ct.call(p),Ct=0;!(Y=p.next()).done;)Y=Y.value,rt=se+P(Y,Ct++),ct+=bt(Y,j,w,rt,K);else if(rt==="object"){if(typeof p.then=="function")return bt(Ht(p),j,w,Y,K);throw j=String(p),Error("Objects are not valid as a React child (found: "+(j==="[object Object]"?"object with keys {"+Object.keys(p).join(", ")+"}":j)+"). If you meant to render a collection of children, use an array instead.")}return ct}function D(p,j,w){if(p==null)return p;var Y=[],K=0;return bt(p,Y,"","",function(rt){return j.call(w,rt,K++)}),Y}function Q(p){if(p._status===-1){var j=p._result;j=j(),j.then(function(w){(p._status===0||p._status===-1)&&(p._status=1,p._result=w)},function(w){(p._status===0||p._status===-1)&&(p._status=2,p._result=w)}),p._status===-1&&(p._status=0,p._result=j)}if(p._status===1)return p._result.default;throw p._result}var et=typeof reportError=="function"?reportError:function(p){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var j=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof p=="object"&&p!==null&&typeof p.message=="string"?String(p.message):String(p),error:p});if(!window.dispatchEvent(j))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",p);return}console.error(p)};function Et(){}return ft.Children={map:D,forEach:function(p,j,w){D(p,function(){j.apply(this,arguments)},w)},count:function(p){var j=0;return D(p,function(){j++}),j},toArray:function(p){return D(p,function(j){return j})||[]},only:function(p){if(!gt(p))throw Error("React.Children.only expected to receive a single React element child.");return p}},ft.Component=V,ft.Fragment=f,ft.Profiler=o,ft.PureComponent=lt,ft.StrictMode=r,ft.Suspense=A,ft.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,ft.__COMPILER_RUNTIME={__proto__:null,c:function(p){return G.H.useMemoCache(p)}},ft.cache=function(p){return function(){return p.apply(null,arguments)}},ft.cloneElement=function(p,j,w){if(p==null)throw Error("The argument must be a React element, but you passed "+p+".");var Y=q({},p.props),K=p.key,rt=void 0;if(j!=null)for(ct in j.ref!==void 0&&(rt=void 0),j.key!==void 0&&(K=""+j.key),j)!tt.call(j,ct)||ct==="key"||ct==="__self"||ct==="__source"||ct==="ref"&&j.ref===void 0||(Y[ct]=j[ct]);var ct=arguments.length-2;if(ct===1)Y.children=w;else if(1<ct){for(var se=Array(ct),Ct=0;Ct<ct;Ct++)se[Ct]=arguments[Ct+2];Y.children=se}return $(p.type,K,void 0,void 0,rt,Y)},ft.createContext=function(p){return p={$$typeof:g,_currentValue:p,_currentValue2:p,_threadCount:0,Provider:null,Consumer:null},p.Provider=p,p.Consumer={$$typeof:h,_context:p},p},ft.createElement=function(p,j,w){var Y,K={},rt=null;if(j!=null)for(Y in j.key!==void 0&&(rt=""+j.key),j)tt.call(j,Y)&&Y!=="key"&&Y!=="__self"&&Y!=="__source"&&(K[Y]=j[Y]);var ct=arguments.length-2;if(ct===1)K.children=w;else if(1<ct){for(var se=Array(ct),Ct=0;Ct<ct;Ct++)se[Ct]=arguments[Ct+2];K.children=se}if(p&&p.defaultProps)for(Y in ct=p.defaultProps,ct)K[Y]===void 0&&(K[Y]=ct[Y]);return $(p,rt,void 0,void 0,null,K)},ft.createRef=function(){return{current:null}},ft.forwardRef=function(p){return{$$typeof:S,render:p}},ft.isValidElement=gt,ft.lazy=function(p){return{$$typeof:z,_payload:{_status:-1,_result:p},_init:Q}},ft.memo=function(p,j){return{$$typeof:v,type:p,compare:j===void 0?null:j}},ft.startTransition=function(p){var j=G.T,w={};G.T=w;try{var Y=p(),K=G.S;K!==null&&K(w,Y),typeof Y=="object"&&Y!==null&&typeof Y.then=="function"&&Y.then(Et,et)}catch(rt){et(rt)}finally{G.T=j}},ft.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},ft.use=function(p){return G.H.use(p)},ft.useActionState=function(p,j,w){return G.H.useActionState(p,j,w)},ft.useCallback=function(p,j){return G.H.useCallback(p,j)},ft.useContext=function(p){return G.H.useContext(p)},ft.useDebugValue=function(){},ft.useDeferredValue=function(p,j){return G.H.useDeferredValue(p,j)},ft.useEffect=function(p,j,w){var Y=G.H;if(typeof w=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Y.useEffect(p,j)},ft.useId=function(){return G.H.useId()},ft.useImperativeHandle=function(p,j,w){return G.H.useImperativeHandle(p,j,w)},ft.useInsertionEffect=function(p,j){return G.H.useInsertionEffect(p,j)},ft.useLayoutEffect=function(p,j){return G.H.useLayoutEffect(p,j)},ft.useMemo=function(p,j){return G.H.useMemo(p,j)},ft.useOptimistic=function(p,j){return G.H.useOptimistic(p,j)},ft.useReducer=function(p,j,w){return G.H.useReducer(p,j,w)},ft.useRef=function(p){return G.H.useRef(p)},ft.useState=function(p){return G.H.useState(p)},ft.useSyncExternalStore=function(p,j,w){return G.H.useSyncExternalStore(p,j,w)},ft.useTransition=function(){return G.H.useTransition()},ft.version="19.1.0",ft}var V0;function Hf(){return V0||(V0=1,gf.exports=Ag()),gf.exports}var it=Hf();const Ah=Th(it),Z0=bg({__proto__:null,default:Ah},[it]);var vf={exports:{}},uu={},pf={exports:{}},bf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k0;function Eg(){return k0||(k0=1,function(u){function c(D,Q){var et=D.length;D.push(Q);t:for(;0<et;){var Et=et-1>>>1,p=D[Et];if(0<o(p,Q))D[Et]=Q,D[et]=p,et=Et;else break t}}function f(D){return D.length===0?null:D[0]}function r(D){if(D.length===0)return null;var Q=D[0],et=D.pop();if(et!==Q){D[0]=et;t:for(var Et=0,p=D.length,j=p>>>1;Et<j;){var w=2*(Et+1)-1,Y=D[w],K=w+1,rt=D[K];if(0>o(Y,et))K<p&&0>o(rt,Y)?(D[Et]=rt,D[K]=et,Et=K):(D[Et]=Y,D[w]=et,Et=w);else if(K<p&&0>o(rt,et))D[Et]=rt,D[K]=et,Et=K;else break t}}return Q}function o(D,Q){var et=D.sortIndex-Q.sortIndex;return et!==0?et:D.id-Q.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;u.unstable_now=function(){return h.now()}}else{var g=Date,S=g.now();u.unstable_now=function(){return g.now()-S}}var A=[],v=[],z=1,R=null,M=3,X=!1,q=!1,x=!1,V=!1,Z=typeof setTimeout=="function"?setTimeout:null,lt=typeof clearTimeout=="function"?clearTimeout:null,F=typeof setImmediate<"u"?setImmediate:null;function W(D){for(var Q=f(v);Q!==null;){if(Q.callback===null)r(v);else if(Q.startTime<=D)r(v),Q.sortIndex=Q.expirationTime,c(A,Q);else break;Q=f(v)}}function G(D){if(x=!1,W(D),!q)if(f(A)!==null)q=!0,tt||(tt=!0,P());else{var Q=f(v);Q!==null&&bt(G,Q.startTime-D)}}var tt=!1,$=-1,yt=5,gt=-1;function m(){return V?!0:!(u.unstable_now()-gt<yt)}function k(){if(V=!1,tt){var D=u.unstable_now();gt=D;var Q=!0;try{t:{q=!1,x&&(x=!1,lt($),$=-1),X=!0;var et=M;try{e:{for(W(D),R=f(A);R!==null&&!(R.expirationTime>D&&m());){var Et=R.callback;if(typeof Et=="function"){R.callback=null,M=R.priorityLevel;var p=Et(R.expirationTime<=D);if(D=u.unstable_now(),typeof p=="function"){R.callback=p,W(D),Q=!0;break e}R===f(A)&&r(A),W(D)}else r(A);R=f(A)}if(R!==null)Q=!0;else{var j=f(v);j!==null&&bt(G,j.startTime-D),Q=!1}}break t}finally{R=null,M=et,X=!1}Q=void 0}}finally{Q?P():tt=!1}}}var P;if(typeof F=="function")P=function(){F(k)};else if(typeof MessageChannel<"u"){var st=new MessageChannel,Ht=st.port2;st.port1.onmessage=k,P=function(){Ht.postMessage(null)}}else P=function(){Z(k,0)};function bt(D,Q){$=Z(function(){D(u.unstable_now())},Q)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(D){D.callback=null},u.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):yt=0<D?Math.floor(1e3/D):5},u.unstable_getCurrentPriorityLevel=function(){return M},u.unstable_next=function(D){switch(M){case 1:case 2:case 3:var Q=3;break;default:Q=M}var et=M;M=Q;try{return D()}finally{M=et}},u.unstable_requestPaint=function(){V=!0},u.unstable_runWithPriority=function(D,Q){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var et=M;M=D;try{return Q()}finally{M=et}},u.unstable_scheduleCallback=function(D,Q,et){var Et=u.unstable_now();switch(typeof et=="object"&&et!==null?(et=et.delay,et=typeof et=="number"&&0<et?Et+et:Et):et=Et,D){case 1:var p=-1;break;case 2:p=250;break;case 5:p=1073741823;break;case 4:p=1e4;break;default:p=5e3}return p=et+p,D={id:z++,callback:Q,priorityLevel:D,startTime:et,expirationTime:p,sortIndex:-1},et>Et?(D.sortIndex=et,c(v,D),f(A)===null&&D===f(v)&&(x?(lt($),$=-1):x=!0,bt(G,et-Et))):(D.sortIndex=p,c(A,D),q||X||(q=!0,tt||(tt=!0,P()))),D},u.unstable_shouldYield=m,u.unstable_wrapCallback=function(D){var Q=M;return function(){var et=M;M=Q;try{return D.apply(this,arguments)}finally{M=et}}}}(bf)),bf}var K0;function Og(){return K0||(K0=1,pf.exports=Eg()),pf.exports}var Sf={exports:{}},re={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $0;function xg(){if($0)return re;$0=1;var u=Hf();function c(A){var v="https://react.dev/errors/"+A;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)v+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+A+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var r={d:{f,r:function(){throw Error(c(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},o=Symbol.for("react.portal");function h(A,v,z){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:R==null?null:""+R,children:A,containerInfo:v,implementation:z}}var g=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(A,v){if(A==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return re.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,re.createPortal=function(A,v){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(c(299));return h(A,v,null,z)},re.flushSync=function(A){var v=g.T,z=r.p;try{if(g.T=null,r.p=2,A)return A()}finally{g.T=v,r.p=z,r.d.f()}},re.preconnect=function(A,v){typeof A=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,r.d.C(A,v))},re.prefetchDNS=function(A){typeof A=="string"&&r.d.D(A)},re.preinit=function(A,v){if(typeof A=="string"&&v&&typeof v.as=="string"){var z=v.as,R=S(z,v.crossOrigin),M=typeof v.integrity=="string"?v.integrity:void 0,X=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;z==="style"?r.d.S(A,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:R,integrity:M,fetchPriority:X}):z==="script"&&r.d.X(A,{crossOrigin:R,integrity:M,fetchPriority:X,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},re.preinitModule=function(A,v){if(typeof A=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var z=S(v.as,v.crossOrigin);r.d.M(A,{crossOrigin:z,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&r.d.M(A)},re.preload=function(A,v){if(typeof A=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var z=v.as,R=S(z,v.crossOrigin);r.d.L(A,z,{crossOrigin:R,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},re.preloadModule=function(A,v){if(typeof A=="string")if(v){var z=S(v.as,v.crossOrigin);r.d.m(A,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:z,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else r.d.m(A)},re.requestFormReset=function(A){r.d.r(A)},re.unstable_batchedUpdates=function(A,v){return A(v)},re.useFormState=function(A,v,z){return g.H.useFormState(A,v,z)},re.useFormStatus=function(){return g.H.useHostTransitionStatus()},re.version="19.1.0",re}var J0;function Cg(){if(J0)return Sf.exports;J0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),Sf.exports=xg(),Sf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W0;function _g(){if(W0)return uu;W0=1;var u=Og(),c=Hf(),f=Cg();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function g(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(h(t)!==t)throw Error(r(188))}function A(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(r(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return S(n),t;if(i===a)return S(n),e;i=i.sibling}throw Error(r(188))}if(l.return!==a.return)l=n,a=i;else{for(var s=!1,d=n.child;d;){if(d===l){s=!0,l=n,a=i;break}if(d===a){s=!0,a=n,l=i;break}d=d.sibling}if(!s){for(d=i.child;d;){if(d===l){s=!0,l=i,a=n;break}if(d===a){s=!0,a=i,l=n;break}d=d.sibling}if(!s)throw Error(r(189))}}if(l.alternate!==a)throw Error(r(190))}if(l.tag!==3)throw Error(r(188));return l.stateNode.current===l?t:e}function v(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=v(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,R=Symbol.for("react.element"),M=Symbol.for("react.transitional.element"),X=Symbol.for("react.portal"),q=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),V=Symbol.for("react.profiler"),Z=Symbol.for("react.provider"),lt=Symbol.for("react.consumer"),F=Symbol.for("react.context"),W=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),tt=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),yt=Symbol.for("react.lazy"),gt=Symbol.for("react.activity"),m=Symbol.for("react.memo_cache_sentinel"),k=Symbol.iterator;function P(t){return t===null||typeof t!="object"?null:(t=k&&t[k]||t["@@iterator"],typeof t=="function"?t:null)}var st=Symbol.for("react.client.reference");function Ht(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===st?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case q:return"Fragment";case V:return"Profiler";case x:return"StrictMode";case G:return"Suspense";case tt:return"SuspenseList";case gt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case X:return"Portal";case F:return(t.displayName||"Context")+".Provider";case lt:return(t._context.displayName||"Context")+".Consumer";case W:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case $:return e=t.displayName||null,e!==null?e:Ht(t.type)||"Memo";case yt:e=t._payload,t=t._init;try{return Ht(t(e))}catch{}}return null}var bt=Array.isArray,D=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,et={pending:!1,data:null,method:null,action:null},Et=[],p=-1;function j(t){return{current:t}}function w(t){0>p||(t.current=Et[p],Et[p]=null,p--)}function Y(t,e){p++,Et[p]=t.current,t.current=e}var K=j(null),rt=j(null),ct=j(null),se=j(null);function Ct(t,e){switch(Y(ct,e),Y(rt,t),Y(K,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?y0(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=y0(e),t=g0(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}w(K),Y(K,t)}function Qt(){w(K),w(rt),w(ct)}function tl(t){t.memoizedState!==null&&Y(se,t);var e=K.current,l=g0(e,t.type);e!==l&&(Y(rt,t),Y(K,l))}function pu(t){rt.current===t&&(w(K),w(rt)),se.current===t&&(w(se),In._currentValue=et)}var tc=Object.prototype.hasOwnProperty,ec=u.unstable_scheduleCallback,lc=u.unstable_cancelCallback,Fh=u.unstable_shouldYield,Ph=u.unstable_requestPaint,Ve=u.unstable_now,Ih=u.unstable_getCurrentPriorityLevel,$f=u.unstable_ImmediatePriority,Jf=u.unstable_UserBlockingPriority,bu=u.unstable_NormalPriority,tm=u.unstable_LowPriority,Wf=u.unstable_IdlePriority,em=u.log,lm=u.unstable_setDisableYieldValue,cn=null,pe=null;function bl(t){if(typeof em=="function"&&lm(t),pe&&typeof pe.setStrictMode=="function")try{pe.setStrictMode(cn,t)}catch{}}var be=Math.clz32?Math.clz32:um,am=Math.log,nm=Math.LN2;function um(t){return t>>>=0,t===0?32:31-(am(t)/nm|0)|0}var Su=256,Tu=4194304;function Zl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Au(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,i=t.suspendedLanes,s=t.pingedLanes;t=t.warmLanes;var d=a&134217727;return d!==0?(a=d&~i,a!==0?n=Zl(a):(s&=d,s!==0?n=Zl(s):l||(l=d&~t,l!==0&&(n=Zl(l))))):(d=a&~i,d!==0?n=Zl(d):s!==0?n=Zl(s):l||(l=a&~t,l!==0&&(n=Zl(l)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:n}function rn(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function im(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ff(){var t=Su;return Su<<=1,(Su&4194048)===0&&(Su=256),t}function Pf(){var t=Tu;return Tu<<=1,(Tu&62914560)===0&&(Tu=4194304),t}function ac(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function fn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function cm(t,e,l,a,n,i){var s=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var d=t.entanglements,y=t.expirationTimes,O=t.hiddenUpdates;for(l=s&~l;0<l;){var U=31-be(l),H=1<<U;d[U]=0,y[U]=-1;var C=O[U];if(C!==null)for(O[U]=null,U=0;U<C.length;U++){var _=C[U];_!==null&&(_.lane&=-536870913)}l&=~H}a!==0&&If(t,a,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(s&~e))}function If(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-be(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function ts(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-be(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function nc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function uc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function es(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:H0(t.type))}function rm(t,e){var l=Q.p;try{return Q.p=t,e()}finally{Q.p=l}}var Sl=Math.random().toString(36).slice(2),ie="__reactFiber$"+Sl,de="__reactProps$"+Sl,sa="__reactContainer$"+Sl,ic="__reactEvents$"+Sl,fm="__reactListeners$"+Sl,sm="__reactHandles$"+Sl,ls="__reactResources$"+Sl,sn="__reactMarker$"+Sl;function cc(t){delete t[ie],delete t[de],delete t[ic],delete t[fm],delete t[sm]}function oa(t){var e=t[ie];if(e)return e;for(var l=t.parentNode;l;){if(e=l[sa]||l[ie]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=S0(t);t!==null;){if(l=t[ie])return l;t=S0(t)}return e}t=l,l=t.parentNode}return null}function da(t){if(t=t[ie]||t[sa]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function on(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function ha(t){var e=t[ls];return e||(e=t[ls]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Pt(t){t[sn]=!0}var as=new Set,ns={};function kl(t,e){ma(t,e),ma(t+"Capture",e)}function ma(t,e){for(ns[t]=e,t=0;t<e.length;t++)as.add(e[t])}var om=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),us={},is={};function dm(t){return tc.call(is,t)?!0:tc.call(us,t)?!1:om.test(t)?is[t]=!0:(us[t]=!0,!1)}function Eu(t,e,l){if(dm(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Ou(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function el(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var rc,cs;function ya(t){if(rc===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);rc=e&&e[1]||"",cs=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+rc+t+cs}var fc=!1;function sc(t,e){if(!t||fc)return"";fc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(_){var C=_}Reflect.construct(t,[],H)}else{try{H.call()}catch(_){C=_}t.call(H.prototype)}}else{try{throw Error()}catch(_){C=_}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(_){if(_&&C&&typeof _.stack=="string")return[_.stack,C.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),s=i[0],d=i[1];if(s&&d){var y=s.split(`
`),O=d.split(`
`);for(n=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;n<O.length&&!O[n].includes("DetermineComponentFrameRoot");)n++;if(a===y.length||n===O.length)for(a=y.length-1,n=O.length-1;1<=a&&0<=n&&y[a]!==O[n];)n--;for(;1<=a&&0<=n;a--,n--)if(y[a]!==O[n]){if(a!==1||n!==1)do if(a--,n--,0>n||y[a]!==O[n]){var U=`
`+y[a].replace(" at new "," at ");return t.displayName&&U.includes("<anonymous>")&&(U=U.replace("<anonymous>",t.displayName)),U}while(1<=a&&0<=n);break}}}finally{fc=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ya(l):""}function hm(t){switch(t.tag){case 26:case 27:case 5:return ya(t.type);case 16:return ya("Lazy");case 13:return ya("Suspense");case 19:return ya("SuspenseList");case 0:case 15:return sc(t.type,!1);case 11:return sc(t.type.render,!1);case 1:return sc(t.type,!0);case 31:return ya("Activity");default:return""}}function rs(t){try{var e="";do e+=hm(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function fs(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function mm(t){var e=fs(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(s){a=""+s,i.call(this,s)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(s){a=""+s},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function xu(t){t._valueTracker||(t._valueTracker=mm(t))}function ss(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=fs(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Cu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var ym=/[\n"\\]/g;function De(t){return t.replace(ym,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function oc(t,e,l,a,n,i,s,d){t.name="",s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"?t.type=s:t.removeAttribute("type"),e!=null?s==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Me(e)):t.value!==""+Me(e)&&(t.value=""+Me(e)):s!=="submit"&&s!=="reset"||t.removeAttribute("value"),e!=null?dc(t,s,Me(e)):l!=null?dc(t,s,Me(l)):a!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+Me(d):t.removeAttribute("name")}function os(t,e,l,a,n,i,s,d){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+Me(l):"",e=e!=null?""+Me(e):l,d||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=d?t.checked:!!a,t.defaultChecked=!!a,s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(t.name=s)}function dc(t,e,l){e==="number"&&Cu(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function ga(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+Me(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function ds(t,e,l){if(e!=null&&(e=""+Me(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+Me(l):""}function hs(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(r(92));if(bt(a)){if(1<a.length)throw Error(r(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=Me(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function va(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var gm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ms(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||gm.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function ys(t,e,l){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&ms(t,n,a)}else for(var i in e)e.hasOwnProperty(i)&&ms(t,i,e[i])}function hc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var vm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),pm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _u(t){return pm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var mc=null;function yc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var pa=null,ba=null;function gs(t){var e=da(t);if(e&&(t=e.stateNode)){var l=t[de]||null;t:switch(t=e.stateNode,e.type){case"input":if(oc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+De(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[de]||null;if(!n)throw Error(r(90));oc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&ss(a)}break t;case"textarea":ds(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&ga(t,!!l.multiple,e,!1)}}}var gc=!1;function vs(t,e,l){if(gc)return t(e,l);gc=!0;try{var a=t(e);return a}finally{if(gc=!1,(pa!==null||ba!==null)&&(di(),pa&&(e=pa,t=ba,ba=pa=null,gs(e),t)))for(e=0;e<t.length;e++)gs(t[e])}}function dn(t,e){var l=t.stateNode;if(l===null)return null;var a=l[de]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(r(231,e,typeof l));return l}var ll=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vc=!1;if(ll)try{var hn={};Object.defineProperty(hn,"passive",{get:function(){vc=!0}}),window.addEventListener("test",hn,hn),window.removeEventListener("test",hn,hn)}catch{vc=!1}var Tl=null,pc=null,zu=null;function ps(){if(zu)return zu;var t,e=pc,l=e.length,a,n="value"in Tl?Tl.value:Tl.textContent,i=n.length;for(t=0;t<l&&e[t]===n[t];t++);var s=l-t;for(a=1;a<=s&&e[l-a]===n[i-a];a++);return zu=n.slice(t,1<a?1-a:void 0)}function Ru(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Mu(){return!0}function bs(){return!1}function he(t){function e(l,a,n,i,s){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(l=t[d],this[d]=l?l(i):i[d]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Mu:bs,this.isPropagationStopped=bs,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Mu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Mu)},persist:function(){},isPersistent:Mu}),e}var Kl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Du=he(Kl),mn=z({},Kl,{view:0,detail:0}),bm=he(mn),bc,Sc,yn,Nu=z({},mn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ac,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==yn&&(yn&&t.type==="mousemove"?(bc=t.screenX-yn.screenX,Sc=t.screenY-yn.screenY):Sc=bc=0,yn=t),bc)},movementY:function(t){return"movementY"in t?t.movementY:Sc}}),Ss=he(Nu),Sm=z({},Nu,{dataTransfer:0}),Tm=he(Sm),Am=z({},mn,{relatedTarget:0}),Tc=he(Am),Em=z({},Kl,{animationName:0,elapsedTime:0,pseudoElement:0}),Om=he(Em),xm=z({},Kl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Cm=he(xm),_m=z({},Kl,{data:0}),Ts=he(_m),zm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Rm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Mm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Dm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Mm[t])?!!e[t]:!1}function Ac(){return Dm}var Nm=z({},mn,{key:function(t){if(t.key){var e=zm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ru(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Rm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ac,charCode:function(t){return t.type==="keypress"?Ru(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ru(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Um=he(Nm),Bm=z({},Nu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),As=he(Bm),Hm=z({},mn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ac}),jm=he(Hm),wm=z({},Kl,{propertyName:0,elapsedTime:0,pseudoElement:0}),qm=he(wm),Ym=z({},Nu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Gm=he(Ym),Xm=z({},Kl,{newState:0,oldState:0}),Lm=he(Xm),Qm=[9,13,27,32],Ec=ll&&"CompositionEvent"in window,gn=null;ll&&"documentMode"in document&&(gn=document.documentMode);var Vm=ll&&"TextEvent"in window&&!gn,Es=ll&&(!Ec||gn&&8<gn&&11>=gn),Os=" ",xs=!1;function Cs(t,e){switch(t){case"keyup":return Qm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _s(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Sa=!1;function Zm(t,e){switch(t){case"compositionend":return _s(e);case"keypress":return e.which!==32?null:(xs=!0,Os);case"textInput":return t=e.data,t===Os&&xs?null:t;default:return null}}function km(t,e){if(Sa)return t==="compositionend"||!Ec&&Cs(t,e)?(t=ps(),zu=pc=Tl=null,Sa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Es&&e.locale!=="ko"?null:e.data;default:return null}}var Km={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zs(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Km[t.type]:e==="textarea"}function Rs(t,e,l,a){pa?ba?ba.push(a):ba=[a]:pa=a,e=pi(e,"onChange"),0<e.length&&(l=new Du("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var vn=null,pn=null;function $m(t){s0(t,0)}function Uu(t){var e=on(t);if(ss(e))return t}function Ms(t,e){if(t==="change")return e}var Ds=!1;if(ll){var Oc;if(ll){var xc="oninput"in document;if(!xc){var Ns=document.createElement("div");Ns.setAttribute("oninput","return;"),xc=typeof Ns.oninput=="function"}Oc=xc}else Oc=!1;Ds=Oc&&(!document.documentMode||9<document.documentMode)}function Us(){vn&&(vn.detachEvent("onpropertychange",Bs),pn=vn=null)}function Bs(t){if(t.propertyName==="value"&&Uu(pn)){var e=[];Rs(e,pn,t,yc(t)),vs($m,e)}}function Jm(t,e,l){t==="focusin"?(Us(),vn=e,pn=l,vn.attachEvent("onpropertychange",Bs)):t==="focusout"&&Us()}function Wm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Uu(pn)}function Fm(t,e){if(t==="click")return Uu(e)}function Pm(t,e){if(t==="input"||t==="change")return Uu(e)}function Im(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Se=typeof Object.is=="function"?Object.is:Im;function bn(t,e){if(Se(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!tc.call(e,n)||!Se(t[n],e[n]))return!1}return!0}function Hs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function js(t,e){var l=Hs(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Hs(l)}}function ws(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?ws(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function qs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Cu(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Cu(t.document)}return e}function Cc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var ty=ll&&"documentMode"in document&&11>=document.documentMode,Ta=null,_c=null,Sn=null,zc=!1;function Ys(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;zc||Ta==null||Ta!==Cu(a)||(a=Ta,"selectionStart"in a&&Cc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Sn&&bn(Sn,a)||(Sn=a,a=pi(_c,"onSelect"),0<a.length&&(e=new Du("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=Ta)))}function $l(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Aa={animationend:$l("Animation","AnimationEnd"),animationiteration:$l("Animation","AnimationIteration"),animationstart:$l("Animation","AnimationStart"),transitionrun:$l("Transition","TransitionRun"),transitionstart:$l("Transition","TransitionStart"),transitioncancel:$l("Transition","TransitionCancel"),transitionend:$l("Transition","TransitionEnd")},Rc={},Gs={};ll&&(Gs=document.createElement("div").style,"AnimationEvent"in window||(delete Aa.animationend.animation,delete Aa.animationiteration.animation,delete Aa.animationstart.animation),"TransitionEvent"in window||delete Aa.transitionend.transition);function Jl(t){if(Rc[t])return Rc[t];if(!Aa[t])return t;var e=Aa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Gs)return Rc[t]=e[l];return t}var Xs=Jl("animationend"),Ls=Jl("animationiteration"),Qs=Jl("animationstart"),ey=Jl("transitionrun"),ly=Jl("transitionstart"),ay=Jl("transitioncancel"),Vs=Jl("transitionend"),Zs=new Map,Mc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Mc.push("scrollEnd");function Xe(t,e){Zs.set(t,e),kl(e,[t])}var ks=new WeakMap;function Ne(t,e){if(typeof t=="object"&&t!==null){var l=ks.get(t);return l!==void 0?l:(e={value:t,source:e,stack:rs(e)},ks.set(t,e),e)}return{value:t,source:e,stack:rs(e)}}var Ue=[],Ea=0,Dc=0;function Bu(){for(var t=Ea,e=Dc=Ea=0;e<t;){var l=Ue[e];Ue[e++]=null;var a=Ue[e];Ue[e++]=null;var n=Ue[e];Ue[e++]=null;var i=Ue[e];if(Ue[e++]=null,a!==null&&n!==null){var s=a.pending;s===null?n.next=n:(n.next=s.next,s.next=n),a.pending=n}i!==0&&Ks(l,n,i)}}function Hu(t,e,l,a){Ue[Ea++]=t,Ue[Ea++]=e,Ue[Ea++]=l,Ue[Ea++]=a,Dc|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Nc(t,e,l,a){return Hu(t,e,l,a),ju(t)}function Oa(t,e){return Hu(t,null,null,e),ju(t)}function Ks(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=t.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-be(l),t=i.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),i):null}function ju(t){if(50<Zn)throw Zn=0,qr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var xa={};function ny(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Te(t,e,l,a){return new ny(t,e,l,a)}function Uc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function al(t,e){var l=t.alternate;return l===null?(l=Te(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function $s(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function wu(t,e,l,a,n,i){var s=0;if(a=t,typeof t=="function")Uc(t)&&(s=1);else if(typeof t=="string")s=ig(t,l,K.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case gt:return t=Te(31,l,e,n),t.elementType=gt,t.lanes=i,t;case q:return Wl(l.children,n,i,e);case x:s=8,n|=24;break;case V:return t=Te(12,l,e,n|2),t.elementType=V,t.lanes=i,t;case G:return t=Te(13,l,e,n),t.elementType=G,t.lanes=i,t;case tt:return t=Te(19,l,e,n),t.elementType=tt,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Z:case F:s=10;break t;case lt:s=9;break t;case W:s=11;break t;case $:s=14;break t;case yt:s=16,a=null;break t}s=29,l=Error(r(130,t===null?"null":typeof t,"")),a=null}return e=Te(s,l,e,n),e.elementType=t,e.type=a,e.lanes=i,e}function Wl(t,e,l,a){return t=Te(7,t,a,e),t.lanes=l,t}function Bc(t,e,l){return t=Te(6,t,null,e),t.lanes=l,t}function Hc(t,e,l){return e=Te(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ca=[],_a=0,qu=null,Yu=0,Be=[],He=0,Fl=null,nl=1,ul="";function Pl(t,e){Ca[_a++]=Yu,Ca[_a++]=qu,qu=t,Yu=e}function Js(t,e,l){Be[He++]=nl,Be[He++]=ul,Be[He++]=Fl,Fl=t;var a=nl;t=ul;var n=32-be(a)-1;a&=~(1<<n),l+=1;var i=32-be(e)+n;if(30<i){var s=n-n%5;i=(a&(1<<s)-1).toString(32),a>>=s,n-=s,nl=1<<32-be(e)+n|l<<n|a,ul=i+t}else nl=1<<i|l<<n|a,ul=t}function jc(t){t.return!==null&&(Pl(t,1),Js(t,1,0))}function wc(t){for(;t===qu;)qu=Ca[--_a],Ca[_a]=null,Yu=Ca[--_a],Ca[_a]=null;for(;t===Fl;)Fl=Be[--He],Be[He]=null,ul=Be[--He],Be[He]=null,nl=Be[--He],Be[He]=null}var oe=null,Yt=null,St=!1,Il=null,Ze=!1,qc=Error(r(519));function ta(t){var e=Error(r(418,""));throw En(Ne(e,t)),qc}function Ws(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[ie]=t,e[de]=a,l){case"dialog":mt("cancel",e),mt("close",e);break;case"iframe":case"object":case"embed":mt("load",e);break;case"video":case"audio":for(l=0;l<Kn.length;l++)mt(Kn[l],e);break;case"source":mt("error",e);break;case"img":case"image":case"link":mt("error",e),mt("load",e);break;case"details":mt("toggle",e);break;case"input":mt("invalid",e),os(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),xu(e);break;case"select":mt("invalid",e);break;case"textarea":mt("invalid",e),hs(e,a.value,a.defaultValue,a.children),xu(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||m0(e.textContent,l)?(a.popover!=null&&(mt("beforetoggle",e),mt("toggle",e)),a.onScroll!=null&&mt("scroll",e),a.onScrollEnd!=null&&mt("scrollend",e),a.onClick!=null&&(e.onclick=bi),e=!0):e=!1,e||ta(t)}function Fs(t){for(oe=t.return;oe;)switch(oe.tag){case 5:case 13:Ze=!1;return;case 27:case 3:Ze=!0;return;default:oe=oe.return}}function Tn(t){if(t!==oe)return!1;if(!St)return Fs(t),St=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||tf(t.type,t.memoizedProps)),l=!l),l&&Yt&&ta(t),Fs(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Yt=Qe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Yt=null}}else e===27?(e=Yt,wl(t.type)?(t=nf,nf=null,Yt=t):Yt=e):Yt=oe?Qe(t.stateNode.nextSibling):null;return!0}function An(){Yt=oe=null,St=!1}function Ps(){var t=Il;return t!==null&&(ge===null?ge=t:ge.push.apply(ge,t),Il=null),t}function En(t){Il===null?Il=[t]:Il.push(t)}var Yc=j(null),ea=null,il=null;function Al(t,e,l){Y(Yc,e._currentValue),e._currentValue=l}function cl(t){t._currentValue=Yc.current,w(Yc)}function Gc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Xc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var s=n.child;i=i.firstContext;t:for(;i!==null;){var d=i;i=n;for(var y=0;y<e.length;y++)if(d.context===e[y]){i.lanes|=l,d=i.alternate,d!==null&&(d.lanes|=l),Gc(i.return,l,t),a||(s=null);break t}i=d.next}}else if(n.tag===18){if(s=n.return,s===null)throw Error(r(341));s.lanes|=l,i=s.alternate,i!==null&&(i.lanes|=l),Gc(s,l,t),s=null}else s=n.child;if(s!==null)s.return=n;else for(s=n;s!==null;){if(s===t){s=null;break}if(n=s.sibling,n!==null){n.return=s.return,s=n;break}s=s.return}n=s}}function On(t,e,l,a){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var s=n.alternate;if(s===null)throw Error(r(387));if(s=s.memoizedProps,s!==null){var d=n.type;Se(n.pendingProps.value,s.value)||(t!==null?t.push(d):t=[d])}}else if(n===se.current){if(s=n.alternate,s===null)throw Error(r(387));s.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(In):t=[In])}n=n.return}t!==null&&Xc(e,t,l,a),e.flags|=262144}function Gu(t){for(t=t.firstContext;t!==null;){if(!Se(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function la(t){ea=t,il=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ce(t){return Is(ea,t)}function Xu(t,e){return ea===null&&la(t),Is(t,e)}function Is(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},il===null){if(t===null)throw Error(r(308));il=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else il=il.next=e;return l}var uy=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},iy=u.unstable_scheduleCallback,cy=u.unstable_NormalPriority,Wt={$$typeof:F,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Lc(){return{controller:new uy,data:new Map,refCount:0}}function xn(t){t.refCount--,t.refCount===0&&iy(cy,function(){t.controller.abort()})}var Cn=null,Qc=0,za=0,Ra=null;function ry(t,e){if(Cn===null){var l=Cn=[];Qc=0,za=Zr(),Ra={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Qc++,e.then(to,to),e}function to(){if(--Qc===0&&Cn!==null){Ra!==null&&(Ra.status="fulfilled");var t=Cn;Cn=null,za=0,Ra=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function fy(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var eo=D.S;D.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&ry(t,e),eo!==null&&eo(t,e)};var aa=j(null);function Vc(){var t=aa.current;return t!==null?t:Bt.pooledCache}function Lu(t,e){e===null?Y(aa,aa.current):Y(aa,e.pool)}function lo(){var t=Vc();return t===null?null:{parent:Wt._currentValue,pool:t}}var _n=Error(r(460)),ao=Error(r(474)),Qu=Error(r(542)),Zc={then:function(){}};function no(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Vu(){}function uo(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(Vu,Vu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,co(t),t;default:if(typeof e.status=="string")e.then(Vu,Vu);else{if(t=Bt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,co(t),t}throw zn=e,_n}}var zn=null;function io(){if(zn===null)throw Error(r(459));var t=zn;return zn=null,t}function co(t){if(t===_n||t===Qu)throw Error(r(483))}var El=!1;function kc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Kc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ol(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function xl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(Ot&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=ju(t),Ks(t,null,l),e}return Hu(t,a,e,l),ju(t)}function Rn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,ts(t,l)}}function $c(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var s={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=s:i=i.next=s,l=l.next}while(l!==null);i===null?n=i=e:i=i.next=e}else n=i=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var Jc=!1;function Mn(){if(Jc){var t=Ra;if(t!==null)throw t}}function Dn(t,e,l,a){Jc=!1;var n=t.updateQueue;El=!1;var i=n.firstBaseUpdate,s=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var y=d,O=y.next;y.next=null,s===null?i=O:s.next=O,s=y;var U=t.alternate;U!==null&&(U=U.updateQueue,d=U.lastBaseUpdate,d!==s&&(d===null?U.firstBaseUpdate=O:d.next=O,U.lastBaseUpdate=y))}if(i!==null){var H=n.baseState;s=0,U=O=y=null,d=i;do{var C=d.lane&-536870913,_=C!==d.lane;if(_?(vt&C)===C:(a&C)===C){C!==0&&C===za&&(Jc=!0),U!==null&&(U=U.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var ut=t,at=d;C=e;var Rt=l;switch(at.tag){case 1:if(ut=at.payload,typeof ut=="function"){H=ut.call(Rt,H,C);break t}H=ut;break t;case 3:ut.flags=ut.flags&-65537|128;case 0:if(ut=at.payload,C=typeof ut=="function"?ut.call(Rt,H,C):ut,C==null)break t;H=z({},H,C);break t;case 2:El=!0}}C=d.callback,C!==null&&(t.flags|=64,_&&(t.flags|=8192),_=n.callbacks,_===null?n.callbacks=[C]:_.push(C))}else _={lane:C,tag:d.tag,payload:d.payload,callback:d.callback,next:null},U===null?(O=U=_,y=H):U=U.next=_,s|=C;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;_=d,d=_.next,_.next=null,n.lastBaseUpdate=_,n.shared.pending=null}}while(!0);U===null&&(y=H),n.baseState=y,n.firstBaseUpdate=O,n.lastBaseUpdate=U,i===null&&(n.shared.lanes=0),Ul|=s,t.lanes=s,t.memoizedState=H}}function ro(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function fo(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)ro(l[t],e)}var Ma=j(null),Zu=j(0);function so(t,e){t=ml,Y(Zu,t),Y(Ma,e),ml=t|e.baseLanes}function Wc(){Y(Zu,ml),Y(Ma,Ma.current)}function Fc(){ml=Zu.current,w(Ma),w(Zu)}var Cl=0,ot=null,_t=null,Kt=null,ku=!1,Da=!1,na=!1,Ku=0,Nn=0,Na=null,sy=0;function Vt(){throw Error(r(321))}function Pc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!Se(t[l],e[l]))return!1;return!0}function Ic(t,e,l,a,n,i){return Cl=i,ot=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,D.H=t===null||t.memoizedState===null?$o:Jo,na=!1,i=l(a,n),na=!1,Da&&(i=ho(e,l,a,n)),oo(t),i}function oo(t){D.H=Iu;var e=_t!==null&&_t.next!==null;if(Cl=0,Kt=_t=ot=null,ku=!1,Nn=0,Na=null,e)throw Error(r(300));t===null||It||(t=t.dependencies,t!==null&&Gu(t)&&(It=!0))}function ho(t,e,l,a){ot=t;var n=0;do{if(Da&&(Na=null),Nn=0,Da=!1,25<=n)throw Error(r(301));if(n+=1,Kt=_t=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}D.H=vy,i=e(l,a)}while(Da);return i}function oy(){var t=D.H,e=t.useState()[0];return e=typeof e.then=="function"?Un(e):e,t=t.useState()[0],(_t!==null?_t.memoizedState:null)!==t&&(ot.flags|=1024),e}function tr(){var t=Ku!==0;return Ku=0,t}function er(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function lr(t){if(ku){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ku=!1}Cl=0,Kt=_t=ot=null,Da=!1,Nn=Ku=0,Na=null}function me(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Kt===null?ot.memoizedState=Kt=t:Kt=Kt.next=t,Kt}function $t(){if(_t===null){var t=ot.alternate;t=t!==null?t.memoizedState:null}else t=_t.next;var e=Kt===null?ot.memoizedState:Kt.next;if(e!==null)Kt=e,_t=t;else{if(t===null)throw ot.alternate===null?Error(r(467)):Error(r(310));_t=t,t={memoizedState:_t.memoizedState,baseState:_t.baseState,baseQueue:_t.baseQueue,queue:_t.queue,next:null},Kt===null?ot.memoizedState=Kt=t:Kt=Kt.next=t}return Kt}function ar(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Un(t){var e=Nn;return Nn+=1,Na===null&&(Na=[]),t=uo(Na,t,e),e=ot,(Kt===null?e.memoizedState:Kt.next)===null&&(e=e.alternate,D.H=e===null||e.memoizedState===null?$o:Jo),t}function $u(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Un(t);if(t.$$typeof===F)return ce(t)}throw Error(r(438,String(t)))}function nr(t){var e=null,l=ot.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=ot.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=ar(),ot.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=m;return e.index++,l}function rl(t,e){return typeof e=="function"?e(t):e}function Ju(t){var e=$t();return ur(e,_t,t)}function ur(t,e,l){var a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=l;var n=t.baseQueue,i=a.pending;if(i!==null){if(n!==null){var s=n.next;n.next=i.next,i.next=s}e.baseQueue=n=i,a.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var d=s=null,y=null,O=e,U=!1;do{var H=O.lane&-536870913;if(H!==O.lane?(vt&H)===H:(Cl&H)===H){var C=O.revertLane;if(C===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),H===za&&(U=!0);else if((Cl&C)===C){O=O.next,C===za&&(U=!0);continue}else H={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=H,s=i):y=y.next=H,ot.lanes|=C,Ul|=C;H=O.action,na&&l(i,H),i=O.hasEagerState?O.eagerState:l(i,H)}else C={lane:H,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=C,s=i):y=y.next=C,ot.lanes|=H,Ul|=H;O=O.next}while(O!==null&&O!==e);if(y===null?s=i:y.next=d,!Se(i,t.memoizedState)&&(It=!0,U&&(l=Ra,l!==null)))throw l;t.memoizedState=i,t.baseState=s,t.baseQueue=y,a.lastRenderedState=i}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function ir(t){var e=$t(),l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,i=e.memoizedState;if(n!==null){l.pending=null;var s=n=n.next;do i=t(i,s.action),s=s.next;while(s!==n);Se(i,e.memoizedState)||(It=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,a]}function mo(t,e,l){var a=ot,n=$t(),i=St;if(i){if(l===void 0)throw Error(r(407));l=l()}else l=e();var s=!Se((_t||n).memoizedState,l);s&&(n.memoizedState=l,It=!0),n=n.queue;var d=vo.bind(null,a,n,t);if(Bn(2048,8,d,[t]),n.getSnapshot!==e||s||Kt!==null&&Kt.memoizedState.tag&1){if(a.flags|=2048,Ua(9,Wu(),go.bind(null,a,n,l,e),null),Bt===null)throw Error(r(349));i||(Cl&124)!==0||yo(a,e,l)}return l}function yo(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=ot.updateQueue,e===null?(e=ar(),ot.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function go(t,e,l,a){e.value=l,e.getSnapshot=a,po(e)&&bo(t)}function vo(t,e,l){return l(function(){po(e)&&bo(t)})}function po(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!Se(t,l)}catch{return!0}}function bo(t){var e=Oa(t,2);e!==null&&Ce(e,t,2)}function cr(t){var e=me();if(typeof t=="function"){var l=t;if(t=l(),na){bl(!0);try{l()}finally{bl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:rl,lastRenderedState:t},e}function So(t,e,l,a){return t.baseState=l,ur(t,_t,typeof a=="function"?a:rl)}function dy(t,e,l,a,n){if(Pu(t))throw Error(r(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(s){i.listeners.push(s)}};D.T!==null?l(!0):i.isTransition=!1,a(i),l=e.pending,l===null?(i.next=e.pending=i,To(e,i)):(i.next=l.next,e.pending=l.next=i)}}function To(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var i=D.T,s={};D.T=s;try{var d=l(n,a),y=D.S;y!==null&&y(s,d),Ao(t,e,d)}catch(O){rr(t,e,O)}finally{D.T=i}}else try{i=l(n,a),Ao(t,e,i)}catch(O){rr(t,e,O)}}function Ao(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Eo(t,e,a)},function(a){return rr(t,e,a)}):Eo(t,e,l)}function Eo(t,e,l){e.status="fulfilled",e.value=l,Oo(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,To(t,l)))}function rr(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Oo(e),e=e.next;while(e!==a)}t.action=null}function Oo(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function xo(t,e){return e}function Co(t,e){if(St){var l=Bt.formState;if(l!==null){t:{var a=ot;if(St){if(Yt){e:{for(var n=Yt,i=Ze;n.nodeType!==8;){if(!i){n=null;break e}if(n=Qe(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Yt=Qe(n.nextSibling),a=n.data==="F!";break t}}ta(a)}a=!1}a&&(e=l[0])}}return l=me(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},l.queue=a,l=Zo.bind(null,ot,a),a.dispatch=l,a=cr(!1),i=hr.bind(null,ot,!1,a.queue),a=me(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=dy.bind(null,ot,n,i,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function _o(t){var e=$t();return zo(e,_t,t)}function zo(t,e,l){if(e=ur(t,e,xo)[0],t=Ju(rl)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Un(e)}catch(s){throw s===_n?Qu:s}else a=e;e=$t();var n=e.queue,i=n.dispatch;return l!==e.memoizedState&&(ot.flags|=2048,Ua(9,Wu(),hy.bind(null,n,l),null)),[a,i,t]}function hy(t,e){t.action=e}function Ro(t){var e=$t(),l=_t;if(l!==null)return zo(e,l,t);$t(),e=e.memoizedState,l=$t();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function Ua(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=ot.updateQueue,e===null&&(e=ar(),ot.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Wu(){return{destroy:void 0,resource:void 0}}function Mo(){return $t().memoizedState}function Fu(t,e,l,a){var n=me();a=a===void 0?null:a,ot.flags|=t,n.memoizedState=Ua(1|e,Wu(),l,a)}function Bn(t,e,l,a){var n=$t();a=a===void 0?null:a;var i=n.memoizedState.inst;_t!==null&&a!==null&&Pc(a,_t.memoizedState.deps)?n.memoizedState=Ua(e,i,l,a):(ot.flags|=t,n.memoizedState=Ua(1|e,i,l,a))}function Do(t,e){Fu(8390656,8,t,e)}function No(t,e){Bn(2048,8,t,e)}function Uo(t,e){return Bn(4,2,t,e)}function Bo(t,e){return Bn(4,4,t,e)}function Ho(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function jo(t,e,l){l=l!=null?l.concat([t]):null,Bn(4,4,Ho.bind(null,e,t),l)}function fr(){}function wo(t,e){var l=$t();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&Pc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function qo(t,e){var l=$t();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&Pc(e,a[1]))return a[0];if(a=t(),na){bl(!0);try{t()}finally{bl(!1)}}return l.memoizedState=[a,e],a}function sr(t,e,l){return l===void 0||(Cl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Xd(),ot.lanes|=t,Ul|=t,l)}function Yo(t,e,l,a){return Se(l,e)?l:Ma.current!==null?(t=sr(t,l,a),Se(t,e)||(It=!0),t):(Cl&42)===0?(It=!0,t.memoizedState=l):(t=Xd(),ot.lanes|=t,Ul|=t,e)}function Go(t,e,l,a,n){var i=Q.p;Q.p=i!==0&&8>i?i:8;var s=D.T,d={};D.T=d,hr(t,!1,e,l);try{var y=n(),O=D.S;if(O!==null&&O(d,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var U=fy(y,a);Hn(t,e,U,xe(t))}else Hn(t,e,a,xe(t))}catch(H){Hn(t,e,{then:function(){},status:"rejected",reason:H},xe())}finally{Q.p=i,D.T=s}}function my(){}function or(t,e,l,a){if(t.tag!==5)throw Error(r(476));var n=Xo(t).queue;Go(t,n,e,et,l===null?my:function(){return Lo(t),l(a)})}function Xo(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:et,baseState:et,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:rl,lastRenderedState:et},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:rl,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Lo(t){var e=Xo(t).next.queue;Hn(t,e,{},xe())}function dr(){return ce(In)}function Qo(){return $t().memoizedState}function Vo(){return $t().memoizedState}function yy(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=xe();t=Ol(l);var a=xl(e,t,l);a!==null&&(Ce(a,e,l),Rn(a,e,l)),e={cache:Lc()},t.payload=e;return}e=e.return}}function gy(t,e,l){var a=xe();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Pu(t)?ko(e,l):(l=Nc(t,e,l,a),l!==null&&(Ce(l,t,a),Ko(l,e,a)))}function Zo(t,e,l){var a=xe();Hn(t,e,l,a)}function Hn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Pu(t))ko(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var s=e.lastRenderedState,d=i(s,l);if(n.hasEagerState=!0,n.eagerState=d,Se(d,s))return Hu(t,e,n,0),Bt===null&&Bu(),!1}catch{}finally{}if(l=Nc(t,e,n,a),l!==null)return Ce(l,t,a),Ko(l,e,a),!0}return!1}function hr(t,e,l,a){if(a={lane:2,revertLane:Zr(),action:a,hasEagerState:!1,eagerState:null,next:null},Pu(t)){if(e)throw Error(r(479))}else e=Nc(t,l,a,2),e!==null&&Ce(e,t,2)}function Pu(t){var e=t.alternate;return t===ot||e!==null&&e===ot}function ko(t,e){Da=ku=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Ko(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,ts(t,l)}}var Iu={readContext:ce,use:$u,useCallback:Vt,useContext:Vt,useEffect:Vt,useImperativeHandle:Vt,useLayoutEffect:Vt,useInsertionEffect:Vt,useMemo:Vt,useReducer:Vt,useRef:Vt,useState:Vt,useDebugValue:Vt,useDeferredValue:Vt,useTransition:Vt,useSyncExternalStore:Vt,useId:Vt,useHostTransitionStatus:Vt,useFormState:Vt,useActionState:Vt,useOptimistic:Vt,useMemoCache:Vt,useCacheRefresh:Vt},$o={readContext:ce,use:$u,useCallback:function(t,e){return me().memoizedState=[t,e===void 0?null:e],t},useContext:ce,useEffect:Do,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Fu(4194308,4,Ho.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Fu(4194308,4,t,e)},useInsertionEffect:function(t,e){Fu(4,2,t,e)},useMemo:function(t,e){var l=me();e=e===void 0?null:e;var a=t();if(na){bl(!0);try{t()}finally{bl(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=me();if(l!==void 0){var n=l(e);if(na){bl(!0);try{l(e)}finally{bl(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=gy.bind(null,ot,t),[a.memoizedState,t]},useRef:function(t){var e=me();return t={current:t},e.memoizedState=t},useState:function(t){t=cr(t);var e=t.queue,l=Zo.bind(null,ot,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:fr,useDeferredValue:function(t,e){var l=me();return sr(l,t,e)},useTransition:function(){var t=cr(!1);return t=Go.bind(null,ot,t.queue,!0,!1),me().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=ot,n=me();if(St){if(l===void 0)throw Error(r(407));l=l()}else{if(l=e(),Bt===null)throw Error(r(349));(vt&124)!==0||yo(a,e,l)}n.memoizedState=l;var i={value:l,getSnapshot:e};return n.queue=i,Do(vo.bind(null,a,i,t),[t]),a.flags|=2048,Ua(9,Wu(),go.bind(null,a,i,l,e),null),l},useId:function(){var t=me(),e=Bt.identifierPrefix;if(St){var l=ul,a=nl;l=(a&~(1<<32-be(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=Ku++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=sy++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:dr,useFormState:Co,useActionState:Co,useOptimistic:function(t){var e=me();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=hr.bind(null,ot,!0,l),l.dispatch=e,[t,e]},useMemoCache:nr,useCacheRefresh:function(){return me().memoizedState=yy.bind(null,ot)}},Jo={readContext:ce,use:$u,useCallback:wo,useContext:ce,useEffect:No,useImperativeHandle:jo,useInsertionEffect:Uo,useLayoutEffect:Bo,useMemo:qo,useReducer:Ju,useRef:Mo,useState:function(){return Ju(rl)},useDebugValue:fr,useDeferredValue:function(t,e){var l=$t();return Yo(l,_t.memoizedState,t,e)},useTransition:function(){var t=Ju(rl)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:Un(t),e]},useSyncExternalStore:mo,useId:Qo,useHostTransitionStatus:dr,useFormState:_o,useActionState:_o,useOptimistic:function(t,e){var l=$t();return So(l,_t,t,e)},useMemoCache:nr,useCacheRefresh:Vo},vy={readContext:ce,use:$u,useCallback:wo,useContext:ce,useEffect:No,useImperativeHandle:jo,useInsertionEffect:Uo,useLayoutEffect:Bo,useMemo:qo,useReducer:ir,useRef:Mo,useState:function(){return ir(rl)},useDebugValue:fr,useDeferredValue:function(t,e){var l=$t();return _t===null?sr(l,t,e):Yo(l,_t.memoizedState,t,e)},useTransition:function(){var t=ir(rl)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:Un(t),e]},useSyncExternalStore:mo,useId:Qo,useHostTransitionStatus:dr,useFormState:Ro,useActionState:Ro,useOptimistic:function(t,e){var l=$t();return _t!==null?So(l,_t,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:nr,useCacheRefresh:Vo},Ba=null,jn=0;function ti(t){var e=jn;return jn+=1,Ba===null&&(Ba=[]),uo(Ba,t,e)}function wn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ei(t,e){throw e.$$typeof===R?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Wo(t){var e=t._init;return e(t._payload)}function Fo(t){function e(T,b){if(t){var E=T.deletions;E===null?(T.deletions=[b],T.flags|=16):E.push(b)}}function l(T,b){if(!t)return null;for(;b!==null;)e(T,b),b=b.sibling;return null}function a(T){for(var b=new Map;T!==null;)T.key!==null?b.set(T.key,T):b.set(T.index,T),T=T.sibling;return b}function n(T,b){return T=al(T,b),T.index=0,T.sibling=null,T}function i(T,b,E){return T.index=E,t?(E=T.alternate,E!==null?(E=E.index,E<b?(T.flags|=67108866,b):E):(T.flags|=67108866,b)):(T.flags|=1048576,b)}function s(T){return t&&T.alternate===null&&(T.flags|=67108866),T}function d(T,b,E,B){return b===null||b.tag!==6?(b=Bc(E,T.mode,B),b.return=T,b):(b=n(b,E),b.return=T,b)}function y(T,b,E,B){var J=E.type;return J===q?U(T,b,E.props.children,B,E.key):b!==null&&(b.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===yt&&Wo(J)===b.type)?(b=n(b,E.props),wn(b,E),b.return=T,b):(b=wu(E.type,E.key,E.props,null,T.mode,B),wn(b,E),b.return=T,b)}function O(T,b,E,B){return b===null||b.tag!==4||b.stateNode.containerInfo!==E.containerInfo||b.stateNode.implementation!==E.implementation?(b=Hc(E,T.mode,B),b.return=T,b):(b=n(b,E.children||[]),b.return=T,b)}function U(T,b,E,B,J){return b===null||b.tag!==7?(b=Wl(E,T.mode,B,J),b.return=T,b):(b=n(b,E),b.return=T,b)}function H(T,b,E){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Bc(""+b,T.mode,E),b.return=T,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case M:return E=wu(b.type,b.key,b.props,null,T.mode,E),wn(E,b),E.return=T,E;case X:return b=Hc(b,T.mode,E),b.return=T,b;case yt:var B=b._init;return b=B(b._payload),H(T,b,E)}if(bt(b)||P(b))return b=Wl(b,T.mode,E,null),b.return=T,b;if(typeof b.then=="function")return H(T,ti(b),E);if(b.$$typeof===F)return H(T,Xu(T,b),E);ei(T,b)}return null}function C(T,b,E,B){var J=b!==null?b.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return J!==null?null:d(T,b,""+E,B);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case M:return E.key===J?y(T,b,E,B):null;case X:return E.key===J?O(T,b,E,B):null;case yt:return J=E._init,E=J(E._payload),C(T,b,E,B)}if(bt(E)||P(E))return J!==null?null:U(T,b,E,B,null);if(typeof E.then=="function")return C(T,b,ti(E),B);if(E.$$typeof===F)return C(T,b,Xu(T,E),B);ei(T,E)}return null}function _(T,b,E,B,J){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return T=T.get(E)||null,d(b,T,""+B,J);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case M:return T=T.get(B.key===null?E:B.key)||null,y(b,T,B,J);case X:return T=T.get(B.key===null?E:B.key)||null,O(b,T,B,J);case yt:var dt=B._init;return B=dt(B._payload),_(T,b,E,B,J)}if(bt(B)||P(B))return T=T.get(E)||null,U(b,T,B,J,null);if(typeof B.then=="function")return _(T,b,E,ti(B),J);if(B.$$typeof===F)return _(T,b,E,Xu(b,B),J);ei(b,B)}return null}function ut(T,b,E,B){for(var J=null,dt=null,I=b,nt=b=0,ee=null;I!==null&&nt<E.length;nt++){I.index>nt?(ee=I,I=null):ee=I.sibling;var pt=C(T,I,E[nt],B);if(pt===null){I===null&&(I=ee);break}t&&I&&pt.alternate===null&&e(T,I),b=i(pt,b,nt),dt===null?J=pt:dt.sibling=pt,dt=pt,I=ee}if(nt===E.length)return l(T,I),St&&Pl(T,nt),J;if(I===null){for(;nt<E.length;nt++)I=H(T,E[nt],B),I!==null&&(b=i(I,b,nt),dt===null?J=I:dt.sibling=I,dt=I);return St&&Pl(T,nt),J}for(I=a(I);nt<E.length;nt++)ee=_(I,T,nt,E[nt],B),ee!==null&&(t&&ee.alternate!==null&&I.delete(ee.key===null?nt:ee.key),b=i(ee,b,nt),dt===null?J=ee:dt.sibling=ee,dt=ee);return t&&I.forEach(function(Ll){return e(T,Ll)}),St&&Pl(T,nt),J}function at(T,b,E,B){if(E==null)throw Error(r(151));for(var J=null,dt=null,I=b,nt=b=0,ee=null,pt=E.next();I!==null&&!pt.done;nt++,pt=E.next()){I.index>nt?(ee=I,I=null):ee=I.sibling;var Ll=C(T,I,pt.value,B);if(Ll===null){I===null&&(I=ee);break}t&&I&&Ll.alternate===null&&e(T,I),b=i(Ll,b,nt),dt===null?J=Ll:dt.sibling=Ll,dt=Ll,I=ee}if(pt.done)return l(T,I),St&&Pl(T,nt),J;if(I===null){for(;!pt.done;nt++,pt=E.next())pt=H(T,pt.value,B),pt!==null&&(b=i(pt,b,nt),dt===null?J=pt:dt.sibling=pt,dt=pt);return St&&Pl(T,nt),J}for(I=a(I);!pt.done;nt++,pt=E.next())pt=_(I,T,nt,pt.value,B),pt!==null&&(t&&pt.alternate!==null&&I.delete(pt.key===null?nt:pt.key),b=i(pt,b,nt),dt===null?J=pt:dt.sibling=pt,dt=pt);return t&&I.forEach(function(pg){return e(T,pg)}),St&&Pl(T,nt),J}function Rt(T,b,E,B){if(typeof E=="object"&&E!==null&&E.type===q&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case M:t:{for(var J=E.key;b!==null;){if(b.key===J){if(J=E.type,J===q){if(b.tag===7){l(T,b.sibling),B=n(b,E.props.children),B.return=T,T=B;break t}}else if(b.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===yt&&Wo(J)===b.type){l(T,b.sibling),B=n(b,E.props),wn(B,E),B.return=T,T=B;break t}l(T,b);break}else e(T,b);b=b.sibling}E.type===q?(B=Wl(E.props.children,T.mode,B,E.key),B.return=T,T=B):(B=wu(E.type,E.key,E.props,null,T.mode,B),wn(B,E),B.return=T,T=B)}return s(T);case X:t:{for(J=E.key;b!==null;){if(b.key===J)if(b.tag===4&&b.stateNode.containerInfo===E.containerInfo&&b.stateNode.implementation===E.implementation){l(T,b.sibling),B=n(b,E.children||[]),B.return=T,T=B;break t}else{l(T,b);break}else e(T,b);b=b.sibling}B=Hc(E,T.mode,B),B.return=T,T=B}return s(T);case yt:return J=E._init,E=J(E._payload),Rt(T,b,E,B)}if(bt(E))return ut(T,b,E,B);if(P(E)){if(J=P(E),typeof J!="function")throw Error(r(150));return E=J.call(E),at(T,b,E,B)}if(typeof E.then=="function")return Rt(T,b,ti(E),B);if(E.$$typeof===F)return Rt(T,b,Xu(T,E),B);ei(T,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,b!==null&&b.tag===6?(l(T,b.sibling),B=n(b,E),B.return=T,T=B):(l(T,b),B=Bc(E,T.mode,B),B.return=T,T=B),s(T)):l(T,b)}return function(T,b,E,B){try{jn=0;var J=Rt(T,b,E,B);return Ba=null,J}catch(I){if(I===_n||I===Qu)throw I;var dt=Te(29,I,null,T.mode);return dt.lanes=B,dt.return=T,dt}finally{}}}var Ha=Fo(!0),Po=Fo(!1),je=j(null),ke=null;function _l(t){var e=t.alternate;Y(Ft,Ft.current&1),Y(je,t),ke===null&&(e===null||Ma.current!==null||e.memoizedState!==null)&&(ke=t)}function Io(t){if(t.tag===22){if(Y(Ft,Ft.current),Y(je,t),ke===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(ke=t)}}else zl()}function zl(){Y(Ft,Ft.current),Y(je,je.current)}function fl(t){w(je),ke===t&&(ke=null),w(Ft)}var Ft=j(0);function li(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||af(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function mr(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var yr={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=xe(),n=Ol(a);n.payload=e,l!=null&&(n.callback=l),e=xl(t,n,a),e!==null&&(Ce(e,t,a),Rn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=xe(),n=Ol(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=xl(t,n,a),e!==null&&(Ce(e,t,a),Rn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=xe(),a=Ol(l);a.tag=2,e!=null&&(a.callback=e),e=xl(t,a,l),e!==null&&(Ce(e,t,l),Rn(e,t,l))}};function td(t,e,l,a,n,i,s){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,i,s):e.prototype&&e.prototype.isPureReactComponent?!bn(l,a)||!bn(n,i):!0}function ed(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&yr.enqueueReplaceState(e,e.state,null)}function ua(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var ai=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function ld(t){ai(t)}function ad(t){console.error(t)}function nd(t){ai(t)}function ni(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ud(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function gr(t,e,l){return l=Ol(l),l.tag=3,l.payload={element:null},l.callback=function(){ni(t,e)},l}function id(t){return t=Ol(t),t.tag=3,t}function cd(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;t.payload=function(){return n(i)},t.callback=function(){ud(e,l,a)}}var s=l.stateNode;s!==null&&typeof s.componentDidCatch=="function"&&(t.callback=function(){ud(e,l,a),typeof n!="function"&&(Bl===null?Bl=new Set([this]):Bl.add(this));var d=a.stack;this.componentDidCatch(a.value,{componentStack:d!==null?d:""})})}function py(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&On(e,l,n,!0),l=je.current,l!==null){switch(l.tag){case 13:return ke===null?Gr():l.alternate===null&&Gt===0&&(Gt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Zc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Lr(t,a,n)),!1;case 22:return l.flags|=65536,a===Zc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Lr(t,a,n)),!1}throw Error(r(435,l.tag))}return Lr(t,a,n),Gr(),!1}if(St)return e=je.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==qc&&(t=Error(r(422),{cause:a}),En(Ne(t,l)))):(a!==qc&&(e=Error(r(423),{cause:a}),En(Ne(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=Ne(a,l),n=gr(t.stateNode,a,n),$c(t,n),Gt!==4&&(Gt=2)),!1;var i=Error(r(520),{cause:a});if(i=Ne(i,l),Vn===null?Vn=[i]:Vn.push(i),Gt!==4&&(Gt=2),e===null)return!0;a=Ne(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=gr(l.stateNode,a,t),$c(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Bl===null||!Bl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=id(n),cd(n,t,l,a),$c(l,n),!1}l=l.return}while(l!==null);return!1}var rd=Error(r(461)),It=!1;function le(t,e,l,a){e.child=t===null?Po(e,null,l,a):Ha(e,t.child,l,a)}function fd(t,e,l,a,n){l=l.render;var i=e.ref;if("ref"in a){var s={};for(var d in a)d!=="ref"&&(s[d]=a[d])}else s=a;return la(e),a=Ic(t,e,l,s,i,n),d=tr(),t!==null&&!It?(er(t,e,n),sl(t,e,n)):(St&&d&&jc(e),e.flags|=1,le(t,e,a,n),e.child)}function sd(t,e,l,a,n){if(t===null){var i=l.type;return typeof i=="function"&&!Uc(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,od(t,e,i,a,n)):(t=wu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Or(t,n)){var s=i.memoizedProps;if(l=l.compare,l=l!==null?l:bn,l(s,a)&&t.ref===e.ref)return sl(t,e,n)}return e.flags|=1,t=al(i,a),t.ref=e.ref,t.return=e,e.child=t}function od(t,e,l,a,n){if(t!==null){var i=t.memoizedProps;if(bn(i,a)&&t.ref===e.ref)if(It=!1,e.pendingProps=a=i,Or(t,n))(t.flags&131072)!==0&&(It=!0);else return e.lanes=t.lanes,sl(t,e,n)}return vr(t,e,l,a,n)}function dd(t,e,l){var a=e.pendingProps,n=a.children,i=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~a}else e.childLanes=0,e.child=null;return hd(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Lu(e,i!==null?i.cachePool:null),i!==null?so(e,i):Wc(),Io(e);else return e.lanes=e.childLanes=536870912,hd(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(Lu(e,i.cachePool),so(e,i),zl(),e.memoizedState=null):(t!==null&&Lu(e,null),Wc(),zl());return le(t,e,n,l),e.child}function hd(t,e,l,a){var n=Vc();return n=n===null?null:{parent:Wt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&Lu(e,null),Wc(),Io(e),t!==null&&On(t,e,a,!0),null}function ui(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(r(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function vr(t,e,l,a,n){return la(e),l=Ic(t,e,l,a,void 0,n),a=tr(),t!==null&&!It?(er(t,e,n),sl(t,e,n)):(St&&a&&jc(e),e.flags|=1,le(t,e,l,n),e.child)}function md(t,e,l,a,n,i){return la(e),e.updateQueue=null,l=ho(e,a,l,n),oo(t),a=tr(),t!==null&&!It?(er(t,e,i),sl(t,e,i)):(St&&a&&jc(e),e.flags|=1,le(t,e,l,i),e.child)}function yd(t,e,l,a,n){if(la(e),e.stateNode===null){var i=xa,s=l.contextType;typeof s=="object"&&s!==null&&(i=ce(s)),i=new l(a,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=yr,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=a,i.state=e.memoizedState,i.refs={},kc(e),s=l.contextType,i.context=typeof s=="object"&&s!==null?ce(s):xa,i.state=e.memoizedState,s=l.getDerivedStateFromProps,typeof s=="function"&&(mr(e,l,s,a),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(s=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),s!==i.state&&yr.enqueueReplaceState(i,i.state,null),Dn(e,a,i,n),Mn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){i=e.stateNode;var d=e.memoizedProps,y=ua(l,d);i.props=y;var O=i.context,U=l.contextType;s=xa,typeof U=="object"&&U!==null&&(s=ce(U));var H=l.getDerivedStateFromProps;U=typeof H=="function"||typeof i.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,U||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(d||O!==s)&&ed(e,i,a,s),El=!1;var C=e.memoizedState;i.state=C,Dn(e,a,i,n),Mn(),O=e.memoizedState,d||C!==O||El?(typeof H=="function"&&(mr(e,l,H,a),O=e.memoizedState),(y=El||td(e,l,y,a,C,O,s))?(U||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=O),i.props=a,i.state=O,i.context=s,a=y):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{i=e.stateNode,Kc(t,e),s=e.memoizedProps,U=ua(l,s),i.props=U,H=e.pendingProps,C=i.context,O=l.contextType,y=xa,typeof O=="object"&&O!==null&&(y=ce(O)),d=l.getDerivedStateFromProps,(O=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==H||C!==y)&&ed(e,i,a,y),El=!1,C=e.memoizedState,i.state=C,Dn(e,a,i,n),Mn();var _=e.memoizedState;s!==H||C!==_||El||t!==null&&t.dependencies!==null&&Gu(t.dependencies)?(typeof d=="function"&&(mr(e,l,d,a),_=e.memoizedState),(U=El||td(e,l,U,a,C,_,y)||t!==null&&t.dependencies!==null&&Gu(t.dependencies))?(O||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,_,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,_,y)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=_),i.props=a,i.state=_,i.context=y,a=U):(typeof i.componentDidUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),a=!1)}return i=a,ui(t,e),a=(e.flags&128)!==0,i||a?(i=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&a?(e.child=Ha(e,t.child,null,n),e.child=Ha(e,null,l,n)):le(t,e,l,n),e.memoizedState=i.state,t=e.child):t=sl(t,e,n),t}function gd(t,e,l,a){return An(),e.flags|=256,le(t,e,l,a),e.child}var pr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function br(t){return{baseLanes:t,cachePool:lo()}}function Sr(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=we),t}function vd(t,e,l){var a=e.pendingProps,n=!1,i=(e.flags&128)!==0,s;if((s=i)||(s=t!==null&&t.memoizedState===null?!1:(Ft.current&2)!==0),s&&(n=!0,e.flags&=-129),s=(e.flags&32)!==0,e.flags&=-33,t===null){if(St){if(n?_l(e):zl(),St){var d=Yt,y;if(y=d){t:{for(y=d,d=Ze;y.nodeType!==8;){if(!d){d=null;break t}if(y=Qe(y.nextSibling),y===null){d=null;break t}}d=y}d!==null?(e.memoizedState={dehydrated:d,treeContext:Fl!==null?{id:nl,overflow:ul}:null,retryLane:536870912,hydrationErrors:null},y=Te(18,null,null,0),y.stateNode=d,y.return=e,e.child=y,oe=e,Yt=null,y=!0):y=!1}y||ta(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return af(d)?e.lanes=32:e.lanes=536870912,null;fl(e)}return d=a.children,a=a.fallback,n?(zl(),n=e.mode,d=ii({mode:"hidden",children:d},n),a=Wl(a,n,l,null),d.return=e,a.return=e,d.sibling=a,e.child=d,n=e.child,n.memoizedState=br(l),n.childLanes=Sr(t,s,l),e.memoizedState=pr,a):(_l(e),Tr(e,d))}if(y=t.memoizedState,y!==null&&(d=y.dehydrated,d!==null)){if(i)e.flags&256?(_l(e),e.flags&=-257,e=Ar(t,e,l)):e.memoizedState!==null?(zl(),e.child=t.child,e.flags|=128,e=null):(zl(),n=a.fallback,d=e.mode,a=ii({mode:"visible",children:a.children},d),n=Wl(n,d,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ha(e,t.child,null,l),a=e.child,a.memoizedState=br(l),a.childLanes=Sr(t,s,l),e.memoizedState=pr,e=n);else if(_l(e),af(d)){if(s=d.nextSibling&&d.nextSibling.dataset,s)var O=s.dgst;s=O,a=Error(r(419)),a.stack="",a.digest=s,En({value:a,source:null,stack:null}),e=Ar(t,e,l)}else if(It||On(t,e,l,!1),s=(l&t.childLanes)!==0,It||s){if(s=Bt,s!==null&&(a=l&-l,a=(a&42)!==0?1:nc(a),a=(a&(s.suspendedLanes|l))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,Oa(t,a),Ce(s,t,a),rd;d.data==="$?"||Gr(),e=Ar(t,e,l)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,Yt=Qe(d.nextSibling),oe=e,St=!0,Il=null,Ze=!1,t!==null&&(Be[He++]=nl,Be[He++]=ul,Be[He++]=Fl,nl=t.id,ul=t.overflow,Fl=e),e=Tr(e,a.children),e.flags|=4096);return e}return n?(zl(),n=a.fallback,d=e.mode,y=t.child,O=y.sibling,a=al(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,O!==null?n=al(O,n):(n=Wl(n,d,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,d=t.child.memoizedState,d===null?d=br(l):(y=d.cachePool,y!==null?(O=Wt._currentValue,y=y.parent!==O?{parent:O,pool:O}:y):y=lo(),d={baseLanes:d.baseLanes|l,cachePool:y}),n.memoizedState=d,n.childLanes=Sr(t,s,l),e.memoizedState=pr,a):(_l(e),l=t.child,t=l.sibling,l=al(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(s=e.deletions,s===null?(e.deletions=[t],e.flags|=16):s.push(t)),e.child=l,e.memoizedState=null,l)}function Tr(t,e){return e=ii({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ii(t,e){return t=Te(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Ar(t,e,l){return Ha(e,t.child,null,l),t=Tr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function pd(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Gc(t.return,e,l)}function Er(t,e,l,a,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function bd(t,e,l){var a=e.pendingProps,n=a.revealOrder,i=a.tail;if(le(t,e,a.children,l),a=Ft.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&pd(t,l,e);else if(t.tag===19)pd(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(Y(Ft,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&li(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Er(e,!1,n,l,i);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&li(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Er(e,!0,l,null,i);break;case"together":Er(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function sl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),Ul|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(On(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,l=al(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=al(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Or(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Gu(t)))}function by(t,e,l){switch(e.tag){case 3:Ct(e,e.stateNode.containerInfo),Al(e,Wt,t.memoizedState.cache),An();break;case 27:case 5:tl(e);break;case 4:Ct(e,e.stateNode.containerInfo);break;case 10:Al(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(_l(e),e.flags|=128,null):(l&e.child.childLanes)!==0?vd(t,e,l):(_l(e),t=sl(t,e,l),t!==null?t.sibling:null);_l(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(On(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return bd(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),Y(Ft,Ft.current),a)break;return null;case 22:case 23:return e.lanes=0,dd(t,e,l);case 24:Al(e,Wt,t.memoizedState.cache)}return sl(t,e,l)}function Sd(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)It=!0;else{if(!Or(t,l)&&(e.flags&128)===0)return It=!1,by(t,e,l);It=(t.flags&131072)!==0}else It=!1,St&&(e.flags&1048576)!==0&&Js(e,Yu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Uc(a)?(t=ua(a,t),e.tag=1,e=yd(null,e,a,t,l)):(e.tag=0,e=vr(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===W){e.tag=11,e=fd(null,e,a,t,l);break t}else if(n===$){e.tag=14,e=sd(null,e,a,t,l);break t}}throw e=Ht(a)||a,Error(r(306,e,""))}}return e;case 0:return vr(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=ua(a,e.pendingProps),yd(t,e,a,n,l);case 3:t:{if(Ct(e,e.stateNode.containerInfo),t===null)throw Error(r(387));a=e.pendingProps;var i=e.memoizedState;n=i.element,Kc(t,e),Dn(e,a,null,l);var s=e.memoizedState;if(a=s.cache,Al(e,Wt,a),a!==i.cache&&Xc(e,[Wt],l,!0),Mn(),a=s.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:s.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=gd(t,e,a,l);break t}else if(a!==n){n=Ne(Error(r(424)),e),En(n),e=gd(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Yt=Qe(t.firstChild),oe=e,St=!0,Il=null,Ze=!0,l=Po(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(An(),a===n){e=sl(t,e,l);break t}le(t,e,a,l)}e=e.child}return e;case 26:return ui(t,e),t===null?(l=O0(e.type,null,e.pendingProps,null))?e.memoizedState=l:St||(l=e.type,t=e.pendingProps,a=Si(ct.current).createElement(l),a[ie]=e,a[de]=t,ne(a,l,t),Pt(a),e.stateNode=a):e.memoizedState=O0(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return tl(e),t===null&&St&&(a=e.stateNode=T0(e.type,e.pendingProps,ct.current),oe=e,Ze=!0,n=Yt,wl(e.type)?(nf=n,Yt=Qe(a.firstChild)):Yt=n),le(t,e,e.pendingProps.children,l),ui(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&St&&((n=a=Yt)&&(a=Ky(a,e.type,e.pendingProps,Ze),a!==null?(e.stateNode=a,oe=e,Yt=Qe(a.firstChild),Ze=!1,n=!0):n=!1),n||ta(e)),tl(e),n=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,a=i.children,tf(n,i)?a=null:s!==null&&tf(n,s)&&(e.flags|=32),e.memoizedState!==null&&(n=Ic(t,e,oy,null,null,l),In._currentValue=n),ui(t,e),le(t,e,a,l),e.child;case 6:return t===null&&St&&((t=l=Yt)&&(l=$y(l,e.pendingProps,Ze),l!==null?(e.stateNode=l,oe=e,Yt=null,t=!0):t=!1),t||ta(e)),null;case 13:return vd(t,e,l);case 4:return Ct(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ha(e,null,a,l):le(t,e,a,l),e.child;case 11:return fd(t,e,e.type,e.pendingProps,l);case 7:return le(t,e,e.pendingProps,l),e.child;case 8:return le(t,e,e.pendingProps.children,l),e.child;case 12:return le(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,Al(e,e.type,a.value),le(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,la(e),n=ce(n),a=a(n),e.flags|=1,le(t,e,a,l),e.child;case 14:return sd(t,e,e.type,e.pendingProps,l);case 15:return od(t,e,e.type,e.pendingProps,l);case 19:return bd(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=ii(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=al(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return dd(t,e,l);case 24:return la(e),a=ce(Wt),t===null?(n=Vc(),n===null&&(n=Bt,i=Lc(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),e.memoizedState={parent:a,cache:n},kc(e),Al(e,Wt,n)):((t.lanes&l)!==0&&(Kc(t,e),Dn(e,null,null,l),Mn()),n=t.memoizedState,i=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),Al(e,Wt,a)):(a=i.cache,Al(e,Wt,a),a!==n.cache&&Xc(e,[Wt],l,!0))),le(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function ol(t){t.flags|=4}function Td(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!R0(e)){if(e=je.current,e!==null&&((vt&4194048)===vt?ke!==null:(vt&62914560)!==vt&&(vt&536870912)===0||e!==ke))throw zn=Zc,ao;t.flags|=8192}}function ci(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Pf():536870912,t.lanes|=e,Ya|=e)}function qn(t,e){if(!St)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function qt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function Sy(t,e,l){var a=e.pendingProps;switch(wc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qt(e),null;case 1:return qt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),cl(Wt),Qt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Tn(e)?ol(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Ps())),qt(e),null;case 26:return l=e.memoizedState,t===null?(ol(e),l!==null?(qt(e),Td(e,l)):(qt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(ol(e),qt(e),Td(e,l)):(qt(e),e.flags&=-16777217):(t.memoizedProps!==a&&ol(e),qt(e),e.flags&=-16777217),null;case 27:pu(e),l=ct.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&ol(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return qt(e),null}t=K.current,Tn(e)?Ws(e):(t=T0(n,a,l),e.stateNode=t,ol(e))}return qt(e),null;case 5:if(pu(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&ol(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return qt(e),null}if(t=K.current,Tn(e))Ws(e);else{switch(n=Si(ct.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[ie]=e,t[de]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(ne(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&ol(e)}}return qt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&ol(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(r(166));if(t=ct.current,Tn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=oe,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[ie]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||m0(t.nodeValue,l)),t||ta(e)}else t=Si(t).createTextNode(a),t[ie]=e,e.stateNode=t}return qt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=Tn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(r(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[ie]=e}else An(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;qt(e),n=!1}else n=Ps(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(fl(e),e):(fl(e),null)}if(fl(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),ci(e,e.updateQueue),qt(e),null;case 4:return Qt(),t===null&&Jr(e.stateNode.containerInfo),qt(e),null;case 10:return cl(e.type),qt(e),null;case 19:if(w(Ft),n=e.memoizedState,n===null)return qt(e),null;if(a=(e.flags&128)!==0,i=n.rendering,i===null)if(a)qn(n,!1);else{if(Gt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=li(t),i!==null){for(e.flags|=128,qn(n,!1),t=i.updateQueue,e.updateQueue=t,ci(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)$s(l,t),l=l.sibling;return Y(Ft,Ft.current&1|2),e.child}t=t.sibling}n.tail!==null&&Ve()>si&&(e.flags|=128,a=!0,qn(n,!1),e.lanes=4194304)}else{if(!a)if(t=li(i),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ci(e,t),qn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!St)return qt(e),null}else 2*Ve()-n.renderingStartTime>si&&l!==536870912&&(e.flags|=128,a=!0,qn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Ve(),e.sibling=null,t=Ft.current,Y(Ft,a?t&1|2:t&1),e):(qt(e),null);case 22:case 23:return fl(e),Fc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(qt(e),e.subtreeFlags&6&&(e.flags|=8192)):qt(e),l=e.updateQueue,l!==null&&ci(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&w(aa),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),cl(Wt),qt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function Ty(t,e){switch(wc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return cl(Wt),Qt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return pu(e),null;case 13:if(fl(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));An()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return w(Ft),null;case 4:return Qt(),null;case 10:return cl(e.type),null;case 22:case 23:return fl(e),Fc(),t!==null&&w(aa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return cl(Wt),null;case 25:return null;default:return null}}function Ad(t,e){switch(wc(e),e.tag){case 3:cl(Wt),Qt();break;case 26:case 27:case 5:pu(e);break;case 4:Qt();break;case 13:fl(e);break;case 19:w(Ft);break;case 10:cl(e.type);break;case 22:case 23:fl(e),Fc(),t!==null&&w(aa);break;case 24:cl(Wt)}}function Yn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var i=l.create,s=l.inst;a=i(),s.destroy=a}l=l.next}while(l!==n)}}catch(d){Dt(e,e.return,d)}}function Rl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){var s=a.inst,d=s.destroy;if(d!==void 0){s.destroy=void 0,n=e;var y=l,O=d;try{O()}catch(U){Dt(n,y,U)}}}a=a.next}while(a!==i)}}catch(U){Dt(e,e.return,U)}}function Ed(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{fo(e,l)}catch(a){Dt(t,t.return,a)}}}function Od(t,e,l){l.props=ua(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Dt(t,e,a)}}function Gn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Dt(t,e,n)}}function Ke(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Dt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Dt(t,e,n)}else l.current=null}function xd(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Dt(t,t.return,n)}}function xr(t,e,l){try{var a=t.stateNode;Ly(a,t.type,l,e),a[de]=e}catch(n){Dt(t,t.return,n)}}function Cd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&wl(t.type)||t.tag===4}function Cr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Cd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&wl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function _r(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=bi));else if(a!==4&&(a===27&&wl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(_r(t,e,l),t=t.sibling;t!==null;)_r(t,e,l),t=t.sibling}function ri(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&wl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(ri(t,e,l),t=t.sibling;t!==null;)ri(t,e,l),t=t.sibling}function _d(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);ne(e,a,l),e[ie]=t,e[de]=l}catch(i){Dt(t,t.return,i)}}var dl=!1,Zt=!1,zr=!1,zd=typeof WeakSet=="function"?WeakSet:Set,te=null;function Ay(t,e){if(t=t.containerInfo,Pr=Ci,t=qs(t),Cc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var s=0,d=-1,y=-1,O=0,U=0,H=t,C=null;e:for(;;){for(var _;H!==l||n!==0&&H.nodeType!==3||(d=s+n),H!==i||a!==0&&H.nodeType!==3||(y=s+a),H.nodeType===3&&(s+=H.nodeValue.length),(_=H.firstChild)!==null;)C=H,H=_;for(;;){if(H===t)break e;if(C===l&&++O===n&&(d=s),C===i&&++U===a&&(y=s),(_=H.nextSibling)!==null)break;H=C,C=H.parentNode}H=_}l=d===-1||y===-1?null:{start:d,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(Ir={focusedElem:t,selectionRange:l},Ci=!1,te=e;te!==null;)if(e=te,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,te=t;else for(;te!==null;){switch(e=te,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var ut=ua(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(ut,i),a.__reactInternalSnapshotBeforeUpdate=t}catch(at){Dt(l,l.return,at)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)lf(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":lf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,te=t;break}te=e.return}}function Rd(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Ml(t,l),a&4&&Yn(5,l);break;case 1:if(Ml(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(s){Dt(l,l.return,s)}else{var n=ua(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(s){Dt(l,l.return,s)}}a&64&&Ed(l),a&512&&Gn(l,l.return);break;case 3:if(Ml(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{fo(t,e)}catch(s){Dt(l,l.return,s)}}break;case 27:e===null&&a&4&&_d(l);case 26:case 5:Ml(t,l),e===null&&a&4&&xd(l),a&512&&Gn(l,l.return);break;case 12:Ml(t,l);break;case 13:Ml(t,l),a&4&&Nd(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Dy.bind(null,l),Jy(t,l))));break;case 22:if(a=l.memoizedState!==null||dl,!a){e=e!==null&&e.memoizedState!==null||Zt,n=dl;var i=Zt;dl=a,(Zt=e)&&!i?Dl(t,l,(l.subtreeFlags&8772)!==0):Ml(t,l),dl=n,Zt=i}break;case 30:break;default:Ml(t,l)}}function Md(t){var e=t.alternate;e!==null&&(t.alternate=null,Md(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&cc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var jt=null,ye=!1;function hl(t,e,l){for(l=l.child;l!==null;)Dd(t,e,l),l=l.sibling}function Dd(t,e,l){if(pe&&typeof pe.onCommitFiberUnmount=="function")try{pe.onCommitFiberUnmount(cn,l)}catch{}switch(l.tag){case 26:Zt||Ke(l,e),hl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Zt||Ke(l,e);var a=jt,n=ye;wl(l.type)&&(jt=l.stateNode,ye=!1),hl(t,e,l),Jn(l.stateNode),jt=a,ye=n;break;case 5:Zt||Ke(l,e);case 6:if(a=jt,n=ye,jt=null,hl(t,e,l),jt=a,ye=n,jt!==null)if(ye)try{(jt.nodeType===9?jt.body:jt.nodeName==="HTML"?jt.ownerDocument.body:jt).removeChild(l.stateNode)}catch(i){Dt(l,e,i)}else try{jt.removeChild(l.stateNode)}catch(i){Dt(l,e,i)}break;case 18:jt!==null&&(ye?(t=jt,b0(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),au(t)):b0(jt,l.stateNode));break;case 4:a=jt,n=ye,jt=l.stateNode.containerInfo,ye=!0,hl(t,e,l),jt=a,ye=n;break;case 0:case 11:case 14:case 15:Zt||Rl(2,l,e),Zt||Rl(4,l,e),hl(t,e,l);break;case 1:Zt||(Ke(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Od(l,e,a)),hl(t,e,l);break;case 21:hl(t,e,l);break;case 22:Zt=(a=Zt)||l.memoizedState!==null,hl(t,e,l),Zt=a;break;default:hl(t,e,l)}}function Nd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{au(t)}catch(l){Dt(e,e.return,l)}}function Ey(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new zd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new zd),e;default:throw Error(r(435,t.tag))}}function Rr(t,e){var l=Ey(t);e.forEach(function(a){var n=Ny.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function Ae(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=t,s=e,d=s;t:for(;d!==null;){switch(d.tag){case 27:if(wl(d.type)){jt=d.stateNode,ye=!1;break t}break;case 5:jt=d.stateNode,ye=!1;break t;case 3:case 4:jt=d.stateNode.containerInfo,ye=!0;break t}d=d.return}if(jt===null)throw Error(r(160));Dd(i,s,n),jt=null,ye=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Ud(e,t),e=e.sibling}var Le=null;function Ud(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ae(e,t),Ee(t),a&4&&(Rl(3,t,t.return),Yn(3,t),Rl(5,t,t.return));break;case 1:Ae(e,t),Ee(t),a&512&&(Zt||l===null||Ke(l,l.return)),a&64&&dl&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Le;if(Ae(e,t),Ee(t),a&512&&(Zt||l===null||Ke(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[sn]||i[ie]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),ne(i,a,l),i[ie]=t,Pt(i),a=i;break t;case"link":var s=_0("link","href",n).get(a+(l.href||""));if(s){for(var d=0;d<s.length;d++)if(i=s[d],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){s.splice(d,1);break e}}i=n.createElement(a),ne(i,a,l),n.head.appendChild(i);break;case"meta":if(s=_0("meta","content",n).get(a+(l.content||""))){for(d=0;d<s.length;d++)if(i=s[d],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){s.splice(d,1);break e}}i=n.createElement(a),ne(i,a,l),n.head.appendChild(i);break;default:throw Error(r(468,a))}i[ie]=t,Pt(i),a=i}t.stateNode=a}else z0(n,t.type,t.stateNode);else t.stateNode=C0(n,a,t.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?z0(n,t.type,t.stateNode):C0(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&xr(t,t.memoizedProps,l.memoizedProps)}break;case 27:Ae(e,t),Ee(t),a&512&&(Zt||l===null||Ke(l,l.return)),l!==null&&a&4&&xr(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Ae(e,t),Ee(t),a&512&&(Zt||l===null||Ke(l,l.return)),t.flags&32){n=t.stateNode;try{va(n,"")}catch(_){Dt(t,t.return,_)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,xr(t,n,l!==null?l.memoizedProps:n)),a&1024&&(zr=!0);break;case 6:if(Ae(e,t),Ee(t),a&4){if(t.stateNode===null)throw Error(r(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(_){Dt(t,t.return,_)}}break;case 3:if(Ei=null,n=Le,Le=Ti(e.containerInfo),Ae(e,t),Le=n,Ee(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{au(e.containerInfo)}catch(_){Dt(t,t.return,_)}zr&&(zr=!1,Bd(t));break;case 4:a=Le,Le=Ti(t.stateNode.containerInfo),Ae(e,t),Ee(t),Le=a;break;case 12:Ae(e,t),Ee(t);break;case 13:Ae(e,t),Ee(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Hr=Ve()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Rr(t,a)));break;case 22:n=t.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,O=dl,U=Zt;if(dl=O||n,Zt=U||y,Ae(e,t),Zt=U,dl=O,Ee(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||y||dl||Zt||ia(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){y=l=e;try{if(i=y.stateNode,n)s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none";else{d=y.stateNode;var H=y.memoizedProps.style,C=H!=null&&H.hasOwnProperty("display")?H.display:null;d.style.display=C==null||typeof C=="boolean"?"":(""+C).trim()}}catch(_){Dt(y,y.return,_)}}}else if(e.tag===6){if(l===null){y=e;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(_){Dt(y,y.return,_)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Rr(t,l))));break;case 19:Ae(e,t),Ee(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Rr(t,a)));break;case 30:break;case 21:break;default:Ae(e,t),Ee(t)}}function Ee(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Cd(a)){l=a;break}a=a.return}if(l==null)throw Error(r(160));switch(l.tag){case 27:var n=l.stateNode,i=Cr(t);ri(t,i,n);break;case 5:var s=l.stateNode;l.flags&32&&(va(s,""),l.flags&=-33);var d=Cr(t);ri(t,d,s);break;case 3:case 4:var y=l.stateNode.containerInfo,O=Cr(t);_r(t,O,y);break;default:throw Error(r(161))}}catch(U){Dt(t,t.return,U)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Bd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Bd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ml(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Rd(t,e.alternate,e),e=e.sibling}function ia(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Rl(4,e,e.return),ia(e);break;case 1:Ke(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Od(e,e.return,l),ia(e);break;case 27:Jn(e.stateNode);case 26:case 5:Ke(e,e.return),ia(e);break;case 22:e.memoizedState===null&&ia(e);break;case 30:ia(e);break;default:ia(e)}t=t.sibling}}function Dl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,i=e,s=i.flags;switch(i.tag){case 0:case 11:case 15:Dl(n,i,l),Yn(4,i);break;case 1:if(Dl(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(O){Dt(a,a.return,O)}if(a=i,n=a.updateQueue,n!==null){var d=a.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)ro(y[n],d)}catch(O){Dt(a,a.return,O)}}l&&s&64&&Ed(i),Gn(i,i.return);break;case 27:_d(i);case 26:case 5:Dl(n,i,l),l&&a===null&&s&4&&xd(i),Gn(i,i.return);break;case 12:Dl(n,i,l);break;case 13:Dl(n,i,l),l&&s&4&&Nd(n,i);break;case 22:i.memoizedState===null&&Dl(n,i,l),Gn(i,i.return);break;case 30:break;default:Dl(n,i,l)}e=e.sibling}}function Mr(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&xn(l))}function Dr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&xn(t))}function $e(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Hd(t,e,l,a),e=e.sibling}function Hd(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:$e(t,e,l,a),n&2048&&Yn(9,e);break;case 1:$e(t,e,l,a);break;case 3:$e(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&xn(t)));break;case 12:if(n&2048){$e(t,e,l,a),t=e.stateNode;try{var i=e.memoizedProps,s=i.id,d=i.onPostCommit;typeof d=="function"&&d(s,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){Dt(e,e.return,y)}}else $e(t,e,l,a);break;case 13:$e(t,e,l,a);break;case 23:break;case 22:i=e.stateNode,s=e.alternate,e.memoizedState!==null?i._visibility&2?$e(t,e,l,a):Xn(t,e):i._visibility&2?$e(t,e,l,a):(i._visibility|=2,ja(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Mr(s,e);break;case 24:$e(t,e,l,a),n&2048&&Dr(e.alternate,e);break;default:$e(t,e,l,a)}}function ja(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,s=e,d=l,y=a,O=s.flags;switch(s.tag){case 0:case 11:case 15:ja(i,s,d,y,n),Yn(8,s);break;case 23:break;case 22:var U=s.stateNode;s.memoizedState!==null?U._visibility&2?ja(i,s,d,y,n):Xn(i,s):(U._visibility|=2,ja(i,s,d,y,n)),n&&O&2048&&Mr(s.alternate,s);break;case 24:ja(i,s,d,y,n),n&&O&2048&&Dr(s.alternate,s);break;default:ja(i,s,d,y,n)}e=e.sibling}}function Xn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Xn(l,a),n&2048&&Mr(a.alternate,a);break;case 24:Xn(l,a),n&2048&&Dr(a.alternate,a);break;default:Xn(l,a)}e=e.sibling}}var Ln=8192;function wa(t){if(t.subtreeFlags&Ln)for(t=t.child;t!==null;)jd(t),t=t.sibling}function jd(t){switch(t.tag){case 26:wa(t),t.flags&Ln&&t.memoizedState!==null&&rg(Le,t.memoizedState,t.memoizedProps);break;case 5:wa(t);break;case 3:case 4:var e=Le;Le=Ti(t.stateNode.containerInfo),wa(t),Le=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ln,Ln=16777216,wa(t),Ln=e):wa(t));break;default:wa(t)}}function wd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Qn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];te=a,Yd(a,t)}wd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)qd(t),t=t.sibling}function qd(t){switch(t.tag){case 0:case 11:case 15:Qn(t),t.flags&2048&&Rl(9,t,t.return);break;case 3:Qn(t);break;case 12:Qn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,fi(t)):Qn(t);break;default:Qn(t)}}function fi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];te=a,Yd(a,t)}wd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Rl(8,e,e.return),fi(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,fi(e));break;default:fi(e)}t=t.sibling}}function Yd(t,e){for(;te!==null;){var l=te;switch(l.tag){case 0:case 11:case 15:Rl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:xn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,te=a;else t:for(l=t;te!==null;){a=te;var n=a.sibling,i=a.return;if(Md(a),a===l){te=null;break t}if(n!==null){n.return=i,te=n;break t}te=i}}}var Oy={getCacheForType:function(t){var e=ce(Wt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},xy=typeof WeakMap=="function"?WeakMap:Map,Ot=0,Bt=null,ht=null,vt=0,xt=0,Oe=null,Nl=!1,qa=!1,Nr=!1,ml=0,Gt=0,Ul=0,ca=0,Ur=0,we=0,Ya=0,Vn=null,ge=null,Br=!1,Hr=0,si=1/0,oi=null,Bl=null,ae=0,Hl=null,Ga=null,Xa=0,jr=0,wr=null,Gd=null,Zn=0,qr=null;function xe(){if((Ot&2)!==0&&vt!==0)return vt&-vt;if(D.T!==null){var t=za;return t!==0?t:Zr()}return es()}function Xd(){we===0&&(we=(vt&536870912)===0||St?Ff():536870912);var t=je.current;return t!==null&&(t.flags|=32),we}function Ce(t,e,l){(t===Bt&&(xt===2||xt===9)||t.cancelPendingCommit!==null)&&(La(t,0),jl(t,vt,we,!1)),fn(t,l),((Ot&2)===0||t!==Bt)&&(t===Bt&&((Ot&2)===0&&(ca|=l),Gt===4&&jl(t,vt,we,!1)),Je(t))}function Ld(t,e,l){if((Ot&6)!==0)throw Error(r(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||rn(t,e),n=a?zy(t,e):Xr(t,e,!0),i=a;do{if(n===0){qa&&!a&&jl(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!Cy(l)){n=Xr(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var s=0;else s=t.pendingLanes&-536870913,s=s!==0?s:s&536870912?536870912:0;if(s!==0){e=s;t:{var d=t;n=Vn;var y=d.current.memoizedState.isDehydrated;if(y&&(La(d,s).flags|=256),s=Xr(d,s,!1),s!==2){if(Nr&&!y){d.errorRecoveryDisabledLanes|=i,ca|=i,n=4;break t}i=ge,ge=n,i!==null&&(ge===null?ge=i:ge.push.apply(ge,i))}n=s}if(i=!1,n!==2)continue}}if(n===1){La(t,0),jl(t,e,0,!0);break}t:{switch(a=t,i=n,i){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:jl(a,e,we,!Nl);break t;case 2:ge=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(n=Hr+300-Ve(),10<n)){if(jl(a,e,we,!Nl),Au(a,0,!0)!==0)break t;a.timeoutHandle=v0(Qd.bind(null,a,l,ge,oi,Br,e,we,ca,Ya,Nl,i,2,-0,0),n);break t}Qd(a,l,ge,oi,Br,e,we,ca,Ya,Nl,i,0,-0,0)}}break}while(!0);Je(t)}function Qd(t,e,l,a,n,i,s,d,y,O,U,H,C,_){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Pn={stylesheets:null,count:0,unsuspend:cg},jd(e),H=fg(),H!==null)){t.cancelPendingCommit=H(Wd.bind(null,t,e,i,l,a,n,s,d,y,U,1,C,_)),jl(t,i,s,!O);return}Wd(t,e,i,l,a,n,s,d,y)}function Cy(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!Se(i(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function jl(t,e,l,a){e&=~Ur,e&=~ca,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var i=31-be(n),s=1<<i;a[i]=-1,n&=~s}l!==0&&If(t,l,e)}function di(){return(Ot&6)===0?(kn(0),!1):!0}function Yr(){if(ht!==null){if(xt===0)var t=ht.return;else t=ht,il=ea=null,lr(t),Ba=null,jn=0,t=ht;for(;t!==null;)Ad(t.alternate,t),t=t.return;ht=null}}function La(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Vy(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Yr(),Bt=t,ht=l=al(t.current,null),vt=e,xt=0,Oe=null,Nl=!1,qa=rn(t,e),Nr=!1,Ya=we=Ur=ca=Ul=Gt=0,ge=Vn=null,Br=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-be(a),i=1<<n;e|=t[n],a&=~i}return ml=e,Bu(),l}function Vd(t,e){ot=null,D.H=Iu,e===_n||e===Qu?(e=io(),xt=3):e===ao?(e=io(),xt=4):xt=e===rd?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Oe=e,ht===null&&(Gt=1,ni(t,Ne(e,t.current)))}function Zd(){var t=D.H;return D.H=Iu,t===null?Iu:t}function kd(){var t=D.A;return D.A=Oy,t}function Gr(){Gt=4,Nl||(vt&4194048)!==vt&&je.current!==null||(qa=!0),(Ul&134217727)===0&&(ca&134217727)===0||Bt===null||jl(Bt,vt,we,!1)}function Xr(t,e,l){var a=Ot;Ot|=2;var n=Zd(),i=kd();(Bt!==t||vt!==e)&&(oi=null,La(t,e)),e=!1;var s=Gt;t:do try{if(xt!==0&&ht!==null){var d=ht,y=Oe;switch(xt){case 8:Yr(),s=6;break t;case 3:case 2:case 9:case 6:je.current===null&&(e=!0);var O=xt;if(xt=0,Oe=null,Qa(t,d,y,O),l&&qa){s=0;break t}break;default:O=xt,xt=0,Oe=null,Qa(t,d,y,O)}}_y(),s=Gt;break}catch(U){Vd(t,U)}while(!0);return e&&t.shellSuspendCounter++,il=ea=null,Ot=a,D.H=n,D.A=i,ht===null&&(Bt=null,vt=0,Bu()),s}function _y(){for(;ht!==null;)Kd(ht)}function zy(t,e){var l=Ot;Ot|=2;var a=Zd(),n=kd();Bt!==t||vt!==e?(oi=null,si=Ve()+500,La(t,e)):qa=rn(t,e);t:do try{if(xt!==0&&ht!==null){e=ht;var i=Oe;e:switch(xt){case 1:xt=0,Oe=null,Qa(t,e,i,1);break;case 2:case 9:if(no(i)){xt=0,Oe=null,$d(e);break}e=function(){xt!==2&&xt!==9||Bt!==t||(xt=7),Je(t)},i.then(e,e);break t;case 3:xt=7;break t;case 4:xt=5;break t;case 7:no(i)?(xt=0,Oe=null,$d(e)):(xt=0,Oe=null,Qa(t,e,i,7));break;case 5:var s=null;switch(ht.tag){case 26:s=ht.memoizedState;case 5:case 27:var d=ht;if(!s||R0(s)){xt=0,Oe=null;var y=d.sibling;if(y!==null)ht=y;else{var O=d.return;O!==null?(ht=O,hi(O)):ht=null}break e}}xt=0,Oe=null,Qa(t,e,i,5);break;case 6:xt=0,Oe=null,Qa(t,e,i,6);break;case 8:Yr(),Gt=6;break t;default:throw Error(r(462))}}Ry();break}catch(U){Vd(t,U)}while(!0);return il=ea=null,D.H=a,D.A=n,Ot=l,ht!==null?0:(Bt=null,vt=0,Bu(),Gt)}function Ry(){for(;ht!==null&&!Fh();)Kd(ht)}function Kd(t){var e=Sd(t.alternate,t,ml);t.memoizedProps=t.pendingProps,e===null?hi(t):ht=e}function $d(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=md(l,e,e.pendingProps,e.type,void 0,vt);break;case 11:e=md(l,e,e.pendingProps,e.type.render,e.ref,vt);break;case 5:lr(e);default:Ad(l,e),e=ht=$s(e,ml),e=Sd(l,e,ml)}t.memoizedProps=t.pendingProps,e===null?hi(t):ht=e}function Qa(t,e,l,a){il=ea=null,lr(e),Ba=null,jn=0;var n=e.return;try{if(py(t,n,e,l,vt)){Gt=1,ni(t,Ne(l,t.current)),ht=null;return}}catch(i){if(n!==null)throw ht=n,i;Gt=1,ni(t,Ne(l,t.current)),ht=null;return}e.flags&32768?(St||a===1?t=!0:qa||(vt&536870912)!==0?t=!1:(Nl=t=!0,(a===2||a===9||a===3||a===6)&&(a=je.current,a!==null&&a.tag===13&&(a.flags|=16384))),Jd(e,t)):hi(e)}function hi(t){var e=t;do{if((e.flags&32768)!==0){Jd(e,Nl);return}t=e.return;var l=Sy(e.alternate,e,ml);if(l!==null){ht=l;return}if(e=e.sibling,e!==null){ht=e;return}ht=e=t}while(e!==null);Gt===0&&(Gt=5)}function Jd(t,e){do{var l=Ty(t.alternate,t);if(l!==null){l.flags&=32767,ht=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ht=t;return}ht=t=l}while(t!==null);Gt=6,ht=null}function Wd(t,e,l,a,n,i,s,d,y){t.cancelPendingCommit=null;do mi();while(ae!==0);if((Ot&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(i=e.lanes|e.childLanes,i|=Dc,cm(t,l,i,s,d,y),t===Bt&&(ht=Bt=null,vt=0),Ga=e,Hl=t,Xa=l,jr=i,wr=n,Gd=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Uy(bu,function(){return e0(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=D.T,D.T=null,n=Q.p,Q.p=2,s=Ot,Ot|=4;try{Ay(t,e,l)}finally{Ot=s,Q.p=n,D.T=a}}ae=1,Fd(),Pd(),Id()}}function Fd(){if(ae===1){ae=0;var t=Hl,e=Ga,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=D.T,D.T=null;var a=Q.p;Q.p=2;var n=Ot;Ot|=4;try{Ud(e,t);var i=Ir,s=qs(t.containerInfo),d=i.focusedElem,y=i.selectionRange;if(s!==d&&d&&d.ownerDocument&&ws(d.ownerDocument.documentElement,d)){if(y!==null&&Cc(d)){var O=y.start,U=y.end;if(U===void 0&&(U=O),"selectionStart"in d)d.selectionStart=O,d.selectionEnd=Math.min(U,d.value.length);else{var H=d.ownerDocument||document,C=H&&H.defaultView||window;if(C.getSelection){var _=C.getSelection(),ut=d.textContent.length,at=Math.min(y.start,ut),Rt=y.end===void 0?at:Math.min(y.end,ut);!_.extend&&at>Rt&&(s=Rt,Rt=at,at=s);var T=js(d,at),b=js(d,Rt);if(T&&b&&(_.rangeCount!==1||_.anchorNode!==T.node||_.anchorOffset!==T.offset||_.focusNode!==b.node||_.focusOffset!==b.offset)){var E=H.createRange();E.setStart(T.node,T.offset),_.removeAllRanges(),at>Rt?(_.addRange(E),_.extend(b.node,b.offset)):(E.setEnd(b.node,b.offset),_.addRange(E))}}}}for(H=[],_=d;_=_.parentNode;)_.nodeType===1&&H.push({element:_,left:_.scrollLeft,top:_.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<H.length;d++){var B=H[d];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}Ci=!!Pr,Ir=Pr=null}finally{Ot=n,Q.p=a,D.T=l}}t.current=e,ae=2}}function Pd(){if(ae===2){ae=0;var t=Hl,e=Ga,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=D.T,D.T=null;var a=Q.p;Q.p=2;var n=Ot;Ot|=4;try{Rd(t,e.alternate,e)}finally{Ot=n,Q.p=a,D.T=l}}ae=3}}function Id(){if(ae===4||ae===3){ae=0,Ph();var t=Hl,e=Ga,l=Xa,a=Gd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ae=5:(ae=0,Ga=Hl=null,t0(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Bl=null),uc(l),e=e.stateNode,pe&&typeof pe.onCommitFiberRoot=="function")try{pe.onCommitFiberRoot(cn,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=D.T,n=Q.p,Q.p=2,D.T=null;try{for(var i=t.onRecoverableError,s=0;s<a.length;s++){var d=a[s];i(d.value,{componentStack:d.stack})}}finally{D.T=e,Q.p=n}}(Xa&3)!==0&&mi(),Je(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===qr?Zn++:(Zn=0,qr=t):Zn=0,kn(0)}}function t0(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,xn(e)))}function mi(t){return Fd(),Pd(),Id(),e0()}function e0(){if(ae!==5)return!1;var t=Hl,e=jr;jr=0;var l=uc(Xa),a=D.T,n=Q.p;try{Q.p=32>l?32:l,D.T=null,l=wr,wr=null;var i=Hl,s=Xa;if(ae=0,Ga=Hl=null,Xa=0,(Ot&6)!==0)throw Error(r(331));var d=Ot;if(Ot|=4,qd(i.current),Hd(i,i.current,s,l),Ot=d,kn(0,!1),pe&&typeof pe.onPostCommitFiberRoot=="function")try{pe.onPostCommitFiberRoot(cn,i)}catch{}return!0}finally{Q.p=n,D.T=a,t0(t,e)}}function l0(t,e,l){e=Ne(l,e),e=gr(t.stateNode,e,2),t=xl(t,e,2),t!==null&&(fn(t,2),Je(t))}function Dt(t,e,l){if(t.tag===3)l0(t,t,l);else for(;e!==null;){if(e.tag===3){l0(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Bl===null||!Bl.has(a))){t=Ne(l,t),l=id(2),a=xl(e,l,2),a!==null&&(cd(l,a,e,t),fn(a,2),Je(a));break}}e=e.return}}function Lr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new xy;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Nr=!0,n.add(l),t=My.bind(null,t,e,l),e.then(t,t))}function My(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Bt===t&&(vt&l)===l&&(Gt===4||Gt===3&&(vt&62914560)===vt&&300>Ve()-Hr?(Ot&2)===0&&La(t,0):Ur|=l,Ya===vt&&(Ya=0)),Je(t)}function a0(t,e){e===0&&(e=Pf()),t=Oa(t,e),t!==null&&(fn(t,e),Je(t))}function Dy(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),a0(t,l)}function Ny(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(r(314))}a!==null&&a.delete(e),a0(t,l)}function Uy(t,e){return ec(t,e)}var yi=null,Va=null,Qr=!1,gi=!1,Vr=!1,ra=0;function Je(t){t!==Va&&t.next===null&&(Va===null?yi=Va=t:Va=Va.next=t),gi=!0,Qr||(Qr=!0,Hy())}function kn(t,e){if(!Vr&&gi){Vr=!0;do for(var l=!1,a=yi;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var s=a.suspendedLanes,d=a.pingedLanes;i=(1<<31-be(42|t)+1)-1,i&=n&~(s&~d),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,c0(a,i))}else i=vt,i=Au(a,a===Bt?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||rn(a,i)||(l=!0,c0(a,i));a=a.next}while(l);Vr=!1}}function By(){n0()}function n0(){gi=Qr=!1;var t=0;ra!==0&&(Qy()&&(t=ra),ra=0);for(var e=Ve(),l=null,a=yi;a!==null;){var n=a.next,i=u0(a,e);i===0?(a.next=null,l===null?yi=n:l.next=n,n===null&&(Va=l)):(l=a,(t!==0||(i&3)!==0)&&(gi=!0)),a=n}kn(t)}function u0(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var s=31-be(i),d=1<<s,y=n[s];y===-1?((d&l)===0||(d&a)!==0)&&(n[s]=im(d,e)):y<=e&&(t.expiredLanes|=d),i&=~d}if(e=Bt,l=vt,l=Au(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(xt===2||xt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&lc(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||rn(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&lc(a),uc(l)){case 2:case 8:l=Jf;break;case 32:l=bu;break;case 268435456:l=Wf;break;default:l=bu}return a=i0.bind(null,t),l=ec(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&lc(a),t.callbackPriority=2,t.callbackNode=null,2}function i0(t,e){if(ae!==0&&ae!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(mi()&&t.callbackNode!==l)return null;var a=vt;return a=Au(t,t===Bt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Ld(t,a,e),u0(t,Ve()),t.callbackNode!=null&&t.callbackNode===l?i0.bind(null,t):null)}function c0(t,e){if(mi())return null;Ld(t,e,!0)}function Hy(){Zy(function(){(Ot&6)!==0?ec($f,By):n0()})}function Zr(){return ra===0&&(ra=Ff()),ra}function r0(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_u(""+t)}function f0(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function jy(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var i=r0((n[de]||null).action),s=a.submitter;s&&(e=(e=s[de]||null)?r0(e.formAction):s.getAttribute("formAction"),e!==null&&(i=e,s=null));var d=new Du("action","action",null,a,n);t.push({event:d,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ra!==0){var y=s?f0(n,s):new FormData(n);or(l,{pending:!0,data:y,method:n.method,action:i},null,y)}}else typeof i=="function"&&(d.preventDefault(),y=s?f0(n,s):new FormData(n),or(l,{pending:!0,data:y,method:n.method,action:i},i,y))},currentTarget:n}]})}}for(var kr=0;kr<Mc.length;kr++){var Kr=Mc[kr],wy=Kr.toLowerCase(),qy=Kr[0].toUpperCase()+Kr.slice(1);Xe(wy,"on"+qy)}Xe(Xs,"onAnimationEnd"),Xe(Ls,"onAnimationIteration"),Xe(Qs,"onAnimationStart"),Xe("dblclick","onDoubleClick"),Xe("focusin","onFocus"),Xe("focusout","onBlur"),Xe(ey,"onTransitionRun"),Xe(ly,"onTransitionStart"),Xe(ay,"onTransitionCancel"),Xe(Vs,"onTransitionEnd"),ma("onMouseEnter",["mouseout","mouseover"]),ma("onMouseLeave",["mouseout","mouseover"]),ma("onPointerEnter",["pointerout","pointerover"]),ma("onPointerLeave",["pointerout","pointerover"]),kl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),kl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),kl("onBeforeInput",["compositionend","keypress","textInput","paste"]),kl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),kl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),kl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Kn));function s0(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var i=void 0;if(e)for(var s=a.length-1;0<=s;s--){var d=a[s],y=d.instance,O=d.currentTarget;if(d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(U){ai(U)}n.currentTarget=null,i=y}else for(s=0;s<a.length;s++){if(d=a[s],y=d.instance,O=d.currentTarget,d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(U){ai(U)}n.currentTarget=null,i=y}}}}function mt(t,e){var l=e[ic];l===void 0&&(l=e[ic]=new Set);var a=t+"__bubble";l.has(a)||(o0(e,t,2,!1),l.add(a))}function $r(t,e,l){var a=0;e&&(a|=4),o0(l,t,a,e)}var vi="_reactListening"+Math.random().toString(36).slice(2);function Jr(t){if(!t[vi]){t[vi]=!0,as.forEach(function(l){l!=="selectionchange"&&(Yy.has(l)||$r(l,!1,t),$r(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[vi]||(e[vi]=!0,$r("selectionchange",!1,e))}}function o0(t,e,l,a){switch(H0(e)){case 2:var n=dg;break;case 8:n=hg;break;default:n=sf}l=n.bind(null,e,l,t),n=void 0,!vc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function Wr(t,e,l,a,n){var i=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var s=a.tag;if(s===3||s===4){var d=a.stateNode.containerInfo;if(d===n)break;if(s===4)for(s=a.return;s!==null;){var y=s.tag;if((y===3||y===4)&&s.stateNode.containerInfo===n)return;s=s.return}for(;d!==null;){if(s=oa(d),s===null)return;if(y=s.tag,y===5||y===6||y===26||y===27){a=i=s;continue t}d=d.parentNode}}a=a.return}vs(function(){var O=i,U=yc(l),H=[];t:{var C=Zs.get(t);if(C!==void 0){var _=Du,ut=t;switch(t){case"keypress":if(Ru(l)===0)break t;case"keydown":case"keyup":_=Um;break;case"focusin":ut="focus",_=Tc;break;case"focusout":ut="blur",_=Tc;break;case"beforeblur":case"afterblur":_=Tc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":_=Ss;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":_=Tm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":_=jm;break;case Xs:case Ls:case Qs:_=Om;break;case Vs:_=qm;break;case"scroll":case"scrollend":_=bm;break;case"wheel":_=Gm;break;case"copy":case"cut":case"paste":_=Cm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":_=As;break;case"toggle":case"beforetoggle":_=Lm}var at=(e&4)!==0,Rt=!at&&(t==="scroll"||t==="scrollend"),T=at?C!==null?C+"Capture":null:C;at=[];for(var b=O,E;b!==null;){var B=b;if(E=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||E===null||T===null||(B=dn(b,T),B!=null&&at.push($n(b,B,E))),Rt)break;b=b.return}0<at.length&&(C=new _(C,ut,null,l,U),H.push({event:C,listeners:at}))}}if((e&7)===0){t:{if(C=t==="mouseover"||t==="pointerover",_=t==="mouseout"||t==="pointerout",C&&l!==mc&&(ut=l.relatedTarget||l.fromElement)&&(oa(ut)||ut[sa]))break t;if((_||C)&&(C=U.window===U?U:(C=U.ownerDocument)?C.defaultView||C.parentWindow:window,_?(ut=l.relatedTarget||l.toElement,_=O,ut=ut?oa(ut):null,ut!==null&&(Rt=h(ut),at=ut.tag,ut!==Rt||at!==5&&at!==27&&at!==6)&&(ut=null)):(_=null,ut=O),_!==ut)){if(at=Ss,B="onMouseLeave",T="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(at=As,B="onPointerLeave",T="onPointerEnter",b="pointer"),Rt=_==null?C:on(_),E=ut==null?C:on(ut),C=new at(B,b+"leave",_,l,U),C.target=Rt,C.relatedTarget=E,B=null,oa(U)===O&&(at=new at(T,b+"enter",ut,l,U),at.target=E,at.relatedTarget=Rt,B=at),Rt=B,_&&ut)e:{for(at=_,T=ut,b=0,E=at;E;E=Za(E))b++;for(E=0,B=T;B;B=Za(B))E++;for(;0<b-E;)at=Za(at),b--;for(;0<E-b;)T=Za(T),E--;for(;b--;){if(at===T||T!==null&&at===T.alternate)break e;at=Za(at),T=Za(T)}at=null}else at=null;_!==null&&d0(H,C,_,at,!1),ut!==null&&Rt!==null&&d0(H,Rt,ut,at,!0)}}t:{if(C=O?on(O):window,_=C.nodeName&&C.nodeName.toLowerCase(),_==="select"||_==="input"&&C.type==="file")var J=Ms;else if(zs(C))if(Ds)J=Pm;else{J=Wm;var dt=Jm}else _=C.nodeName,!_||_.toLowerCase()!=="input"||C.type!=="checkbox"&&C.type!=="radio"?O&&hc(O.elementType)&&(J=Ms):J=Fm;if(J&&(J=J(t,O))){Rs(H,J,l,U);break t}dt&&dt(t,C,O),t==="focusout"&&O&&C.type==="number"&&O.memoizedProps.value!=null&&dc(C,"number",C.value)}switch(dt=O?on(O):window,t){case"focusin":(zs(dt)||dt.contentEditable==="true")&&(Ta=dt,_c=O,Sn=null);break;case"focusout":Sn=_c=Ta=null;break;case"mousedown":zc=!0;break;case"contextmenu":case"mouseup":case"dragend":zc=!1,Ys(H,l,U);break;case"selectionchange":if(ty)break;case"keydown":case"keyup":Ys(H,l,U)}var I;if(Ec)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else Sa?Cs(t,l)&&(nt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(nt="onCompositionStart");nt&&(Es&&l.locale!=="ko"&&(Sa||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&Sa&&(I=ps()):(Tl=U,pc="value"in Tl?Tl.value:Tl.textContent,Sa=!0)),dt=pi(O,nt),0<dt.length&&(nt=new Ts(nt,t,null,l,U),H.push({event:nt,listeners:dt}),I?nt.data=I:(I=_s(l),I!==null&&(nt.data=I)))),(I=Vm?Zm(t,l):km(t,l))&&(nt=pi(O,"onBeforeInput"),0<nt.length&&(dt=new Ts("onBeforeInput","beforeinput",null,l,U),H.push({event:dt,listeners:nt}),dt.data=I)),jy(H,t,O,l,U)}s0(H,e)})}function $n(t,e,l){return{instance:t,listener:e,currentTarget:l}}function pi(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=dn(t,l),n!=null&&a.unshift($n(t,n,i)),n=dn(t,e),n!=null&&a.push($n(t,n,i))),t.tag===3)return a;t=t.return}return[]}function Za(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function d0(t,e,l,a,n){for(var i=e._reactName,s=[];l!==null&&l!==a;){var d=l,y=d.alternate,O=d.stateNode;if(d=d.tag,y!==null&&y===a)break;d!==5&&d!==26&&d!==27||O===null||(y=O,n?(O=dn(l,i),O!=null&&s.unshift($n(l,O,y))):n||(O=dn(l,i),O!=null&&s.push($n(l,O,y)))),l=l.return}s.length!==0&&t.push({event:e,listeners:s})}var Gy=/\r\n?/g,Xy=/\u0000|\uFFFD/g;function h0(t){return(typeof t=="string"?t:""+t).replace(Gy,`
`).replace(Xy,"")}function m0(t,e){return e=h0(e),h0(t)===e}function bi(){}function zt(t,e,l,a,n,i){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||va(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&va(t,""+a);break;case"className":Ou(t,"class",a);break;case"tabIndex":Ou(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ou(t,l,a);break;case"style":ys(t,a,i);break;case"data":if(e!=="object"){Ou(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&zt(t,e,"name",n.name,n,null),zt(t,e,"formEncType",n.formEncType,n,null),zt(t,e,"formMethod",n.formMethod,n,null),zt(t,e,"formTarget",n.formTarget,n,null)):(zt(t,e,"encType",n.encType,n,null),zt(t,e,"method",n.method,n,null),zt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=bi);break;case"onScroll":a!=null&&mt("scroll",t);break;case"onScrollEnd":a!=null&&mt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=_u(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":mt("beforetoggle",t),mt("toggle",t),Eu(t,"popover",a);break;case"xlinkActuate":el(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":el(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":el(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":el(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":el(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":el(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":el(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":el(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":el(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Eu(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=vm.get(l)||l,Eu(t,l,a))}}function Fr(t,e,l,a,n,i){switch(l){case"style":ys(t,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"children":typeof a=="string"?va(t,a):(typeof a=="number"||typeof a=="bigint")&&va(t,""+a);break;case"onScroll":a!=null&&mt("scroll",t);break;case"onScrollEnd":a!=null&&mt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=bi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ns.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),i=t[de]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Eu(t,l,a)}}}function ne(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":mt("error",t),mt("load",t);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var s=l[i];if(s!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:zt(t,e,i,s,l,null)}}n&&zt(t,e,"srcSet",l.srcSet,l,null),a&&zt(t,e,"src",l.src,l,null);return;case"input":mt("invalid",t);var d=i=s=n=null,y=null,O=null;for(a in l)if(l.hasOwnProperty(a)){var U=l[a];if(U!=null)switch(a){case"name":n=U;break;case"type":s=U;break;case"checked":y=U;break;case"defaultChecked":O=U;break;case"value":i=U;break;case"defaultValue":d=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(r(137,e));break;default:zt(t,e,a,U,l,null)}}os(t,i,d,y,O,s,n,!1),xu(t);return;case"select":mt("invalid",t),a=s=i=null;for(n in l)if(l.hasOwnProperty(n)&&(d=l[n],d!=null))switch(n){case"value":i=d;break;case"defaultValue":s=d;break;case"multiple":a=d;default:zt(t,e,n,d,l,null)}e=i,l=s,t.multiple=!!a,e!=null?ga(t,!!a,e,!1):l!=null&&ga(t,!!a,l,!0);return;case"textarea":mt("invalid",t),i=n=a=null;for(s in l)if(l.hasOwnProperty(s)&&(d=l[s],d!=null))switch(s){case"value":a=d;break;case"defaultValue":n=d;break;case"children":i=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(r(91));break;default:zt(t,e,s,d,l,null)}hs(t,a,n,i),xu(t);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:zt(t,e,y,a,l,null)}return;case"dialog":mt("beforetoggle",t),mt("toggle",t),mt("cancel",t),mt("close",t);break;case"iframe":case"object":mt("load",t);break;case"video":case"audio":for(a=0;a<Kn.length;a++)mt(Kn[a],t);break;case"image":mt("error",t),mt("load",t);break;case"details":mt("toggle",t);break;case"embed":case"source":case"link":mt("error",t),mt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in l)if(l.hasOwnProperty(O)&&(a=l[O],a!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:zt(t,e,O,a,l,null)}return;default:if(hc(e)){for(U in l)l.hasOwnProperty(U)&&(a=l[U],a!==void 0&&Fr(t,e,U,a,l,void 0));return}}for(d in l)l.hasOwnProperty(d)&&(a=l[d],a!=null&&zt(t,e,d,a,l,null))}function Ly(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,s=null,d=null,y=null,O=null,U=null;for(_ in l){var H=l[_];if(l.hasOwnProperty(_)&&H!=null)switch(_){case"checked":break;case"value":break;case"defaultValue":y=H;default:a.hasOwnProperty(_)||zt(t,e,_,null,a,H)}}for(var C in a){var _=a[C];if(H=l[C],a.hasOwnProperty(C)&&(_!=null||H!=null))switch(C){case"type":i=_;break;case"name":n=_;break;case"checked":O=_;break;case"defaultChecked":U=_;break;case"value":s=_;break;case"defaultValue":d=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,e));break;default:_!==H&&zt(t,e,C,_,a,H)}}oc(t,s,d,y,O,U,i,n);return;case"select":_=s=d=C=null;for(i in l)if(y=l[i],l.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":_=y;default:a.hasOwnProperty(i)||zt(t,e,i,null,a,y)}for(n in a)if(i=a[n],y=l[n],a.hasOwnProperty(n)&&(i!=null||y!=null))switch(n){case"value":C=i;break;case"defaultValue":d=i;break;case"multiple":s=i;default:i!==y&&zt(t,e,n,i,a,y)}e=d,l=s,a=_,C!=null?ga(t,!!l,C,!1):!!a!=!!l&&(e!=null?ga(t,!!l,e,!0):ga(t,!!l,l?[]:"",!1));return;case"textarea":_=C=null;for(d in l)if(n=l[d],l.hasOwnProperty(d)&&n!=null&&!a.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:zt(t,e,d,null,a,n)}for(s in a)if(n=a[s],i=l[s],a.hasOwnProperty(s)&&(n!=null||i!=null))switch(s){case"value":C=n;break;case"defaultValue":_=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==i&&zt(t,e,s,n,a,i)}ds(t,C,_);return;case"option":for(var ut in l)if(C=l[ut],l.hasOwnProperty(ut)&&C!=null&&!a.hasOwnProperty(ut))switch(ut){case"selected":t.selected=!1;break;default:zt(t,e,ut,null,a,C)}for(y in a)if(C=a[y],_=l[y],a.hasOwnProperty(y)&&C!==_&&(C!=null||_!=null))switch(y){case"selected":t.selected=C&&typeof C!="function"&&typeof C!="symbol";break;default:zt(t,e,y,C,a,_)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var at in l)C=l[at],l.hasOwnProperty(at)&&C!=null&&!a.hasOwnProperty(at)&&zt(t,e,at,null,a,C);for(O in a)if(C=a[O],_=l[O],a.hasOwnProperty(O)&&C!==_&&(C!=null||_!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(r(137,e));break;default:zt(t,e,O,C,a,_)}return;default:if(hc(e)){for(var Rt in l)C=l[Rt],l.hasOwnProperty(Rt)&&C!==void 0&&!a.hasOwnProperty(Rt)&&Fr(t,e,Rt,void 0,a,C);for(U in a)C=a[U],_=l[U],!a.hasOwnProperty(U)||C===_||C===void 0&&_===void 0||Fr(t,e,U,C,a,_);return}}for(var T in l)C=l[T],l.hasOwnProperty(T)&&C!=null&&!a.hasOwnProperty(T)&&zt(t,e,T,null,a,C);for(H in a)C=a[H],_=l[H],!a.hasOwnProperty(H)||C===_||C==null&&_==null||zt(t,e,H,C,a,_)}var Pr=null,Ir=null;function Si(t){return t.nodeType===9?t:t.ownerDocument}function y0(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function g0(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function tf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ef=null;function Qy(){var t=window.event;return t&&t.type==="popstate"?t===ef?!1:(ef=t,!0):(ef=null,!1)}var v0=typeof setTimeout=="function"?setTimeout:void 0,Vy=typeof clearTimeout=="function"?clearTimeout:void 0,p0=typeof Promise=="function"?Promise:void 0,Zy=typeof queueMicrotask=="function"?queueMicrotask:typeof p0<"u"?function(t){return p0.resolve(null).then(t).catch(ky)}:v0;function ky(t){setTimeout(function(){throw t})}function wl(t){return t==="head"}function b0(t,e){var l=e,a=0,n=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var s=t.ownerDocument;if(l&1&&Jn(s.documentElement),l&2&&Jn(s.body),l&4)for(l=s.head,Jn(l),s=l.firstChild;s;){var d=s.nextSibling,y=s.nodeName;s[sn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&s.rel.toLowerCase()==="stylesheet"||l.removeChild(s),s=d}}if(n===0){t.removeChild(i),au(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);au(e)}function lf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":lf(l),cc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Ky(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[sn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Qe(t.nextSibling),t===null)break}return null}function $y(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Qe(t.nextSibling),t===null))return null;return t}function af(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Jy(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Qe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var nf=null;function S0(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function T0(t,e,l){switch(e=Si(l),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function Jn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);cc(t)}var qe=new Map,A0=new Set;function Ti(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var yl=Q.d;Q.d={f:Wy,r:Fy,D:Py,C:Iy,L:tg,m:eg,X:ag,S:lg,M:ng};function Wy(){var t=yl.f(),e=di();return t||e}function Fy(t){var e=da(t);e!==null&&e.tag===5&&e.type==="form"?Lo(e):yl.r(t)}var ka=typeof document>"u"?null:document;function E0(t,e,l){var a=ka;if(a&&typeof e=="string"&&e){var n=De(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),A0.has(n)||(A0.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),ne(e,"link",t),Pt(e),a.head.appendChild(e)))}}function Py(t){yl.D(t),E0("dns-prefetch",t,null)}function Iy(t,e){yl.C(t,e),E0("preconnect",t,e)}function tg(t,e,l){yl.L(t,e,l);var a=ka;if(a&&t&&e){var n='link[rel="preload"][as="'+De(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+De(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+De(l.imageSizes)+'"]')):n+='[href="'+De(t)+'"]';var i=n;switch(e){case"style":i=Ka(t);break;case"script":i=$a(t)}qe.has(i)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),qe.set(i,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Wn(i))||e==="script"&&a.querySelector(Fn(i))||(e=a.createElement("link"),ne(e,"link",t),Pt(e),a.head.appendChild(e)))}}function eg(t,e){yl.m(t,e);var l=ka;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+De(a)+'"][href="'+De(t)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=$a(t)}if(!qe.has(i)&&(t=z({rel:"modulepreload",href:t},e),qe.set(i,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Fn(i)))return}a=l.createElement("link"),ne(a,"link",t),Pt(a),l.head.appendChild(a)}}}function lg(t,e,l){yl.S(t,e,l);var a=ka;if(a&&t){var n=ha(a).hoistableStyles,i=Ka(t);e=e||"default";var s=n.get(i);if(!s){var d={loading:0,preload:null};if(s=a.querySelector(Wn(i)))d.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=qe.get(i))&&uf(t,l);var y=s=a.createElement("link");Pt(y),ne(y,"link",t),y._p=new Promise(function(O,U){y.onload=O,y.onerror=U}),y.addEventListener("load",function(){d.loading|=1}),y.addEventListener("error",function(){d.loading|=2}),d.loading|=4,Ai(s,e,a)}s={type:"stylesheet",instance:s,count:1,state:d},n.set(i,s)}}}function ag(t,e){yl.X(t,e);var l=ka;if(l&&t){var a=ha(l).hoistableScripts,n=$a(t),i=a.get(n);i||(i=l.querySelector(Fn(n)),i||(t=z({src:t,async:!0},e),(e=qe.get(n))&&cf(t,e),i=l.createElement("script"),Pt(i),ne(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function ng(t,e){yl.M(t,e);var l=ka;if(l&&t){var a=ha(l).hoistableScripts,n=$a(t),i=a.get(n);i||(i=l.querySelector(Fn(n)),i||(t=z({src:t,async:!0,type:"module"},e),(e=qe.get(n))&&cf(t,e),i=l.createElement("script"),Pt(i),ne(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function O0(t,e,l,a){var n=(n=ct.current)?Ti(n):null;if(!n)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Ka(l.href),l=ha(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Ka(l.href);var i=ha(n).hoistableStyles,s=i.get(t);if(s||(n=n.ownerDocument||n,s={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,s),(i=n.querySelector(Wn(t)))&&!i._p&&(s.instance=i,s.state.loading=5),qe.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},qe.set(t,l),i||ug(n,t,l,s.state))),e&&a===null)throw Error(r(528,""));return s}if(e&&a!==null)throw Error(r(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$a(l),l=ha(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Ka(t){return'href="'+De(t)+'"'}function Wn(t){return'link[rel="stylesheet"]['+t+"]"}function x0(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function ug(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),ne(e,"link",l),Pt(e),t.head.appendChild(e))}function $a(t){return'[src="'+De(t)+'"]'}function Fn(t){return"script[async]"+t}function C0(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+De(l.href)+'"]');if(a)return e.instance=a,Pt(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Pt(a),ne(a,"style",n),Ai(a,l.precedence,t),e.instance=a;case"stylesheet":n=Ka(l.href);var i=t.querySelector(Wn(n));if(i)return e.state.loading|=4,e.instance=i,Pt(i),i;a=x0(l),(n=qe.get(n))&&uf(a,n),i=(t.ownerDocument||t).createElement("link"),Pt(i);var s=i;return s._p=new Promise(function(d,y){s.onload=d,s.onerror=y}),ne(i,"link",a),e.state.loading|=4,Ai(i,l.precedence,t),e.instance=i;case"script":return i=$a(l.src),(n=t.querySelector(Fn(i)))?(e.instance=n,Pt(n),n):(a=l,(n=qe.get(i))&&(a=z({},l),cf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Pt(n),ne(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ai(a,l.precedence,t));return e.instance}function Ai(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,s=0;s<a.length;s++){var d=a[s];if(d.dataset.precedence===e)i=d;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function uf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function cf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ei=null;function _0(t,e,l){if(Ei===null){var a=new Map,n=Ei=new Map;n.set(l,a)}else n=Ei,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var i=l[n];if(!(i[sn]||i[ie]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var s=i.getAttribute(e)||"";s=t+s;var d=a.get(s);d?d.push(i):a.set(s,[i])}}return a}function z0(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function ig(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function R0(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Pn=null;function cg(){}function rg(t,e,l){if(Pn===null)throw Error(r(475));var a=Pn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Ka(l.href),i=t.querySelector(Wn(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Oi.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=i,Pt(i);return}i=t.ownerDocument||t,l=x0(l),(n=qe.get(n))&&uf(l,n),i=i.createElement("link"),Pt(i);var s=i;s._p=new Promise(function(d,y){s.onload=d,s.onerror=y}),ne(i,"link",l),e.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Oi.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function fg(){if(Pn===null)throw Error(r(475));var t=Pn;return t.stylesheets&&t.count===0&&rf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&rf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Oi(){if(this.count--,this.count===0){if(this.stylesheets)rf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var xi=null;function rf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,xi=new Map,e.forEach(sg,t),xi=null,Oi.call(t))}function sg(t,e){if(!(e.state.loading&4)){var l=xi.get(t);if(l)var a=l.get(null);else{l=new Map,xi.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var s=n[i];(s.nodeName==="LINK"||s.getAttribute("media")!=="not all")&&(l.set(s.dataset.precedence,s),a=s)}a&&l.set(null,a)}n=e.instance,s=n.getAttribute("data-precedence"),i=l.get(s)||a,i===a&&l.set(null,n),l.set(s,n),this.count++,a=Oi.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var In={$$typeof:F,Provider:null,Consumer:null,_currentValue:et,_currentValue2:et,_threadCount:0};function og(t,e,l,a,n,i,s,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ac(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ac(0),this.hiddenUpdates=ac(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function M0(t,e,l,a,n,i,s,d,y,O,U,H){return t=new og(t,e,l,s,d,y,O,H),e=1,i===!0&&(e|=24),i=Te(3,null,null,e),t.current=i,i.stateNode=t,e=Lc(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:e},kc(i),t}function D0(t){return t?(t=xa,t):xa}function N0(t,e,l,a,n,i){n=D0(n),a.context===null?a.context=n:a.pendingContext=n,a=Ol(e),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=xl(t,a,e),l!==null&&(Ce(l,t,e),Rn(l,t,e))}function U0(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function ff(t,e){U0(t,e),(t=t.alternate)&&U0(t,e)}function B0(t){if(t.tag===13){var e=Oa(t,67108864);e!==null&&Ce(e,t,67108864),ff(t,67108864)}}var Ci=!0;function dg(t,e,l,a){var n=D.T;D.T=null;var i=Q.p;try{Q.p=2,sf(t,e,l,a)}finally{Q.p=i,D.T=n}}function hg(t,e,l,a){var n=D.T;D.T=null;var i=Q.p;try{Q.p=8,sf(t,e,l,a)}finally{Q.p=i,D.T=n}}function sf(t,e,l,a){if(Ci){var n=of(a);if(n===null)Wr(t,e,a,_i,l),j0(t,a);else if(yg(n,t,e,l,a))a.stopPropagation();else if(j0(t,a),e&4&&-1<mg.indexOf(t)){for(;n!==null;){var i=da(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var s=Zl(i.pendingLanes);if(s!==0){var d=i;for(d.pendingLanes|=2,d.entangledLanes|=2;s;){var y=1<<31-be(s);d.entanglements[1]|=y,s&=~y}Je(i),(Ot&6)===0&&(si=Ve()+500,kn(0))}}break;case 13:d=Oa(i,2),d!==null&&Ce(d,i,2),di(),ff(i,2)}if(i=of(a),i===null&&Wr(t,e,a,_i,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else Wr(t,e,a,null,l)}}function of(t){return t=yc(t),df(t)}var _i=null;function df(t){if(_i=null,t=oa(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=g(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _i=t,null}function H0(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ih()){case $f:return 2;case Jf:return 8;case bu:case tm:return 32;case Wf:return 268435456;default:return 32}default:return 32}}var hf=!1,ql=null,Yl=null,Gl=null,tu=new Map,eu=new Map,Xl=[],mg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function j0(t,e){switch(t){case"focusin":case"focusout":ql=null;break;case"dragenter":case"dragleave":Yl=null;break;case"mouseover":case"mouseout":Gl=null;break;case"pointerover":case"pointerout":tu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":eu.delete(e.pointerId)}}function lu(t,e,l,a,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},e!==null&&(e=da(e),e!==null&&B0(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function yg(t,e,l,a,n){switch(e){case"focusin":return ql=lu(ql,t,e,l,a,n),!0;case"dragenter":return Yl=lu(Yl,t,e,l,a,n),!0;case"mouseover":return Gl=lu(Gl,t,e,l,a,n),!0;case"pointerover":var i=n.pointerId;return tu.set(i,lu(tu.get(i)||null,t,e,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,eu.set(i,lu(eu.get(i)||null,t,e,l,a,n)),!0}return!1}function w0(t){var e=oa(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=g(l),e!==null){t.blockedOn=e,rm(t.priority,function(){if(l.tag===13){var a=xe();a=nc(a);var n=Oa(l,a);n!==null&&Ce(n,l,a),ff(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zi(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=of(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);mc=a,l.target.dispatchEvent(a),mc=null}else return e=da(l),e!==null&&B0(e),t.blockedOn=l,!1;e.shift()}return!0}function q0(t,e,l){zi(t)&&l.delete(e)}function gg(){hf=!1,ql!==null&&zi(ql)&&(ql=null),Yl!==null&&zi(Yl)&&(Yl=null),Gl!==null&&zi(Gl)&&(Gl=null),tu.forEach(q0),eu.forEach(q0)}function Ri(t,e){t.blockedOn===e&&(t.blockedOn=null,hf||(hf=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,gg)))}var Mi=null;function Y0(t){Mi!==t&&(Mi=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){Mi===t&&(Mi=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(df(a||l)===null)continue;break}var i=da(l);i!==null&&(t.splice(e,3),e-=3,or(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function au(t){function e(y){return Ri(y,t)}ql!==null&&Ri(ql,t),Yl!==null&&Ri(Yl,t),Gl!==null&&Ri(Gl,t),tu.forEach(e),eu.forEach(e);for(var l=0;l<Xl.length;l++){var a=Xl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Xl.length&&(l=Xl[0],l.blockedOn===null);)w0(l),l.blockedOn===null&&Xl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],s=n[de]||null;if(typeof i=="function")s||Y0(l);else if(s){var d=null;if(i&&i.hasAttribute("formAction")){if(n=i,s=i[de]||null)d=s.formAction;else if(df(n)!==null)continue}else d=s.action;typeof d=="function"?l[a+1]=d:(l.splice(a,3),a-=3),Y0(l)}}}function mf(t){this._internalRoot=t}Di.prototype.render=mf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var l=e.current,a=xe();N0(l,a,t,e,null,null)},Di.prototype.unmount=mf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;N0(t.current,2,null,t,null,null),di(),e[sa]=null}};function Di(t){this._internalRoot=t}Di.prototype.unstable_scheduleHydration=function(t){if(t){var e=es();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Xl.length&&e!==0&&e<Xl[l].priority;l++);Xl.splice(l,0,t),l===0&&w0(t)}};var G0=c.version;if(G0!=="19.1.0")throw Error(r(527,G0,"19.1.0"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=A(e),t=t!==null?v(t):null,t=t===null?null:t.stateNode,t};var vg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ni.isDisabled&&Ni.supportsFiber)try{cn=Ni.inject(vg),pe=Ni}catch{}}return uu.createRoot=function(t,e){if(!o(t))throw Error(r(299));var l=!1,a="",n=ld,i=ad,s=nd,d=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(s=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=M0(t,1,!1,null,null,l,a,n,i,s,d,null),t[sa]=e.current,Jr(t),new mf(e)},uu.hydrateRoot=function(t,e,l){if(!o(t))throw Error(r(299));var a=!1,n="",i=ld,s=ad,d=nd,y=null,O=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(s=l.onCaughtError),l.onRecoverableError!==void 0&&(d=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(O=l.formState)),e=M0(t,1,!0,e,l??null,a,n,i,s,d,y,O),e.context=D0(null),l=e.current,a=xe(),a=nc(a),n=Ol(a),n.callback=null,xl(l,n,a),l=a,e.current.lanes=l,fn(e,l),Je(e),t[sa]=e.current,Jr(t),new Di(e)},uu.version="19.1.0",uu}var F0;function zg(){if(F0)return vf.exports;F0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),vf.exports=_g(),vf.exports}var Rg=zg();const Mg=Th(Rg),Eh=it.createContext(),Dg=({children:u})=>{const[c,f]=it.useState(()=>{const o=localStorage.getItem("darkMode");return o?JSON.parse(o):!1});it.useEffect(()=>{localStorage.setItem("darkMode",JSON.stringify(c)),document.body.className=c?"dark-mode":"light-mode"},[c]);const r=()=>{f(!c)};return L.jsx(Eh.Provider,{value:{darkMode:c,toggleTheme:r},children:u})},P0=u=>u,Ng=()=>{let u=P0;return{configure(c){u=c},generate(c){return u(c)},reset(){u=P0}}},Ug=Ng();function fa(u,...c){const f=new URL(`https://mui.com/production-error/?code=${u}`);return c.forEach(r=>f.searchParams.append("args[]",r)),`Minified MUI error #${u}; visit ${f} for the full message.`}function ln(u){if(typeof u!="string")throw new Error(fa(7));return u.charAt(0).toUpperCase()+u.slice(1)}function Oh(u){var c,f,r="";if(typeof u=="string"||typeof u=="number")r+=u;else if(typeof u=="object")if(Array.isArray(u)){var o=u.length;for(c=0;c<o;c++)u[c]&&(f=Oh(u[c]))&&(r&&(r+=" "),r+=f)}else for(f in u)u[f]&&(r&&(r+=" "),r+=f);return r}function Bg(){for(var u,c,f=0,r="",o=arguments.length;f<o;f++)(u=arguments[f])&&(c=Oh(u))&&(r&&(r+=" "),r+=c);return r}function Hg(u,c,f=void 0){const r={};for(const o in u){const h=u[o];let g="",S=!0;for(let A=0;A<h.length;A+=1){const v=h[A];v&&(g+=(S===!0?"":" ")+c(v),S=!1,f&&f[v]&&(g+=" "+f[v]))}r[o]=g}return r}var Tf={exports:{}},Mt={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var I0;function jg(){if(I0)return Mt;I0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),g=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),v=Symbol.for("react.suspense_list"),z=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),M=Symbol.for("react.view_transition"),X=Symbol.for("react.client.reference");function q(x){if(typeof x=="object"&&x!==null){var V=x.$$typeof;switch(V){case u:switch(x=x.type,x){case f:case o:case r:case A:case v:case M:return x;default:switch(x=x&&x.$$typeof,x){case g:case S:case R:case z:return x;case h:return x;default:return V}}case c:return V}}}return Mt.ContextConsumer=h,Mt.ContextProvider=g,Mt.Element=u,Mt.ForwardRef=S,Mt.Fragment=f,Mt.Lazy=R,Mt.Memo=z,Mt.Portal=c,Mt.Profiler=o,Mt.StrictMode=r,Mt.Suspense=A,Mt.SuspenseList=v,Mt.isContextConsumer=function(x){return q(x)===h},Mt.isContextProvider=function(x){return q(x)===g},Mt.isElement=function(x){return typeof x=="object"&&x!==null&&x.$$typeof===u},Mt.isForwardRef=function(x){return q(x)===S},Mt.isFragment=function(x){return q(x)===f},Mt.isLazy=function(x){return q(x)===R},Mt.isMemo=function(x){return q(x)===z},Mt.isPortal=function(x){return q(x)===c},Mt.isProfiler=function(x){return q(x)===o},Mt.isStrictMode=function(x){return q(x)===r},Mt.isSuspense=function(x){return q(x)===A},Mt.isSuspenseList=function(x){return q(x)===v},Mt.isValidElementType=function(x){return typeof x=="string"||typeof x=="function"||x===f||x===o||x===r||x===A||x===v||typeof x=="object"&&x!==null&&(x.$$typeof===R||x.$$typeof===z||x.$$typeof===g||x.$$typeof===h||x.$$typeof===S||x.$$typeof===X||x.getModuleId!==void 0)},Mt.typeOf=q,Mt}var th;function wg(){return th||(th=1,Tf.exports=jg()),Tf.exports}var xh=wg();function vl(u){if(typeof u!="object"||u===null)return!1;const c=Object.getPrototypeOf(u);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(Symbol.toStringTag in u)&&!(Symbol.iterator in u)}function Ch(u){if(it.isValidElement(u)||xh.isValidElementType(u)||!vl(u))return u;const c={};return Object.keys(u).forEach(f=>{c[f]=Ch(u[f])}),c}function ze(u,c,f={clone:!0}){const r=f.clone?{...u}:u;return vl(u)&&vl(c)&&Object.keys(c).forEach(o=>{it.isValidElement(c[o])||xh.isValidElementType(c[o])?r[o]=c[o]:vl(c[o])&&Object.prototype.hasOwnProperty.call(u,o)&&vl(u[o])?r[o]=ze(u[o],c[o],f):f.clone?r[o]=vl(c[o])?Ch(c[o]):c[o]:r[o]=c[o]}),r}function su(u,c){return c?ze(u,c,{clone:!1}):u}function qg(u,c){if(!u.containerQueries)return c;const f=Object.keys(c).filter(r=>r.startsWith("@container")).sort((r,o)=>{var g,S;const h=/min-width:\s*([0-9.]+)/;return+(((g=r.match(h))==null?void 0:g[1])||0)-+(((S=o.match(h))==null?void 0:S[1])||0)});return f.length?f.reduce((r,o)=>{const h=c[o];return delete r[o],r[o]=h,r},{...c}):c}function Yg(u,c){return c==="@"||c.startsWith("@")&&(u.some(f=>c.startsWith(`@${f}`))||!!c.match(/^@\d/))}function Gg(u,c){const f=c.match(/^@([^/]+)?\/?(.+)?$/);if(!f)return null;const[,r,o]=f,h=Number.isNaN(+r)?r||0:+r;return u.containerQueries(o).up(h)}function Xg(u){const c=(h,g)=>h.replace("@media",g?`@container ${g}`:"@container");function f(h,g){h.up=(...S)=>c(u.breakpoints.up(...S),g),h.down=(...S)=>c(u.breakpoints.down(...S),g),h.between=(...S)=>c(u.breakpoints.between(...S),g),h.only=(...S)=>c(u.breakpoints.only(...S),g),h.not=(...S)=>{const A=c(u.breakpoints.not(...S),g);return A.includes("not all and")?A.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):A}}const r={},o=h=>(f(r,h),r);return f(o),{...u,containerQueries:o}}const Li={xs:0,sm:600,md:900,lg:1200,xl:1536},eh={keys:["xs","sm","md","lg","xl"],up:u=>`@media (min-width:${Li[u]}px)`},Lg={containerQueries:u=>({up:c=>{let f=typeof c=="number"?c:Li[c]||c;return typeof f=="number"&&(f=`${f}px`),u?`@container ${u} (min-width:${f})`:`@container (min-width:${f})`}})};function pl(u,c,f){const r=u.theme||{};if(Array.isArray(c)){const h=r.breakpoints||eh;return c.reduce((g,S,A)=>(g[h.up(h.keys[A])]=f(c[A]),g),{})}if(typeof c=="object"){const h=r.breakpoints||eh;return Object.keys(c).reduce((g,S)=>{if(Yg(h.keys,S)){const A=Gg(r.containerQueries?r:Lg,S);A&&(g[A]=f(c[S],S))}else if(Object.keys(h.values||Li).includes(S)){const A=h.up(S);g[A]=f(c[S],S)}else{const A=S;g[A]=c[A]}return g},{})}return f(c)}function Qg(u={}){var f;return((f=u.keys)==null?void 0:f.reduce((r,o)=>{const h=u.up(o);return r[h]={},r},{}))||{}}function Vg(u,c){return u.reduce((f,r)=>{const o=f[r];return(!o||Object.keys(o).length===0)&&delete f[r],f},c)}function Qi(u,c,f=!0){if(!c||typeof c!="string")return null;if(u&&u.vars&&f){const r=`vars.${c}`.split(".").reduce((o,h)=>o&&o[h]?o[h]:null,u);if(r!=null)return r}return c.split(".").reduce((r,o)=>r&&r[o]!=null?r[o]:null,u)}function Gi(u,c,f,r=f){let o;return typeof u=="function"?o=u(f):Array.isArray(u)?o=u[f]||r:o=Qi(u,f)||r,c&&(o=c(o,r,u)),o}function kt(u){const{prop:c,cssProperty:f=u.prop,themeKey:r,transform:o}=u,h=g=>{if(g[c]==null)return null;const S=g[c],A=g.theme,v=Qi(A,r)||{};return pl(g,S,R=>{let M=Gi(v,o,R);return R===M&&typeof R=="string"&&(M=Gi(v,o,`${c}${R==="default"?"":ln(R)}`,R)),f===!1?M:{[f]:M}})};return h.propTypes={},h.filterProps=[c],h}function Zg(u){const c={};return f=>(c[f]===void 0&&(c[f]=u(f)),c[f])}const kg={m:"margin",p:"padding"},Kg={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},lh={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},$g=Zg(u=>{if(u.length>2)if(lh[u])u=lh[u];else return[u];const[c,f]=u.split(""),r=kg[c],o=Kg[f]||"";return Array.isArray(o)?o.map(h=>r+h):[r+o]}),jf=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],wf=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...jf,...wf];function yu(u,c,f,r){const o=Qi(u,c,!0)??f;return typeof o=="number"||typeof o=="string"?h=>typeof h=="string"?h:typeof o=="string"?o.startsWith("var(")&&h===0?0:o.startsWith("var(")&&h===1?o:`calc(${h} * ${o})`:o*h:Array.isArray(o)?h=>{if(typeof h=="string")return h;const g=Math.abs(h),S=o[g];return h>=0?S:typeof S=="number"?-S:typeof S=="string"&&S.startsWith("var(")?`calc(-1 * ${S})`:`-${S}`}:typeof o=="function"?o:()=>{}}function qf(u){return yu(u,"spacing",8)}function gu(u,c){return typeof c=="string"||c==null?c:u(c)}function Jg(u,c){return f=>u.reduce((r,o)=>(r[o]=gu(c,f),r),{})}function Wg(u,c,f,r){if(!c.includes(f))return null;const o=$g(f),h=Jg(o,r),g=u[f];return pl(u,g,h)}function _h(u,c){const f=qf(u.theme);return Object.keys(u).map(r=>Wg(u,c,r,f)).reduce(su,{})}function Xt(u){return _h(u,jf)}Xt.propTypes={};Xt.filterProps=jf;function Lt(u){return _h(u,wf)}Lt.propTypes={};Lt.filterProps=wf;function Vi(...u){const c=u.reduce((r,o)=>(o.filterProps.forEach(h=>{r[h]=o}),r),{}),f=r=>Object.keys(r).reduce((o,h)=>c[h]?su(o,c[h](r)):o,{});return f.propTypes={},f.filterProps=u.reduce((r,o)=>r.concat(o.filterProps),[]),f}function Ye(u){return typeof u!="number"?u:`${u}px solid`}function Ge(u,c){return kt({prop:u,themeKey:"borders",transform:c})}const Fg=Ge("border",Ye),Pg=Ge("borderTop",Ye),Ig=Ge("borderRight",Ye),t1=Ge("borderBottom",Ye),e1=Ge("borderLeft",Ye),l1=Ge("borderColor"),a1=Ge("borderTopColor"),n1=Ge("borderRightColor"),u1=Ge("borderBottomColor"),i1=Ge("borderLeftColor"),c1=Ge("outline",Ye),r1=Ge("outlineColor"),Zi=u=>{if(u.borderRadius!==void 0&&u.borderRadius!==null){const c=yu(u.theme,"shape.borderRadius",4),f=r=>({borderRadius:gu(c,r)});return pl(u,u.borderRadius,f)}return null};Zi.propTypes={};Zi.filterProps=["borderRadius"];Vi(Fg,Pg,Ig,t1,e1,l1,a1,n1,u1,i1,Zi,c1,r1);const ki=u=>{if(u.gap!==void 0&&u.gap!==null){const c=yu(u.theme,"spacing",8),f=r=>({gap:gu(c,r)});return pl(u,u.gap,f)}return null};ki.propTypes={};ki.filterProps=["gap"];const Ki=u=>{if(u.columnGap!==void 0&&u.columnGap!==null){const c=yu(u.theme,"spacing",8),f=r=>({columnGap:gu(c,r)});return pl(u,u.columnGap,f)}return null};Ki.propTypes={};Ki.filterProps=["columnGap"];const $i=u=>{if(u.rowGap!==void 0&&u.rowGap!==null){const c=yu(u.theme,"spacing",8),f=r=>({rowGap:gu(c,r)});return pl(u,u.rowGap,f)}return null};$i.propTypes={};$i.filterProps=["rowGap"];const f1=kt({prop:"gridColumn"}),s1=kt({prop:"gridRow"}),o1=kt({prop:"gridAutoFlow"}),d1=kt({prop:"gridAutoColumns"}),h1=kt({prop:"gridAutoRows"}),m1=kt({prop:"gridTemplateColumns"}),y1=kt({prop:"gridTemplateRows"}),g1=kt({prop:"gridTemplateAreas"}),v1=kt({prop:"gridArea"});Vi(ki,Ki,$i,f1,s1,o1,d1,h1,m1,y1,g1,v1);function tn(u,c){return c==="grey"?c:u}const p1=kt({prop:"color",themeKey:"palette",transform:tn}),b1=kt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:tn}),S1=kt({prop:"backgroundColor",themeKey:"palette",transform:tn});Vi(p1,b1,S1);function _e(u){return u<=1&&u!==0?`${u*100}%`:u}const T1=kt({prop:"width",transform:_e}),Yf=u=>{if(u.maxWidth!==void 0&&u.maxWidth!==null){const c=f=>{var o,h,g,S,A;const r=((g=(h=(o=u.theme)==null?void 0:o.breakpoints)==null?void 0:h.values)==null?void 0:g[f])||Li[f];return r?((A=(S=u.theme)==null?void 0:S.breakpoints)==null?void 0:A.unit)!=="px"?{maxWidth:`${r}${u.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:_e(f)}};return pl(u,u.maxWidth,c)}return null};Yf.filterProps=["maxWidth"];const A1=kt({prop:"minWidth",transform:_e}),E1=kt({prop:"height",transform:_e}),O1=kt({prop:"maxHeight",transform:_e}),x1=kt({prop:"minHeight",transform:_e});kt({prop:"size",cssProperty:"width",transform:_e});kt({prop:"size",cssProperty:"height",transform:_e});const C1=kt({prop:"boxSizing"});Vi(T1,Yf,A1,E1,O1,x1,C1);const Ji={border:{themeKey:"borders",transform:Ye},borderTop:{themeKey:"borders",transform:Ye},borderRight:{themeKey:"borders",transform:Ye},borderBottom:{themeKey:"borders",transform:Ye},borderLeft:{themeKey:"borders",transform:Ye},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Ye},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Zi},color:{themeKey:"palette",transform:tn},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:tn},backgroundColor:{themeKey:"palette",transform:tn},p:{style:Lt},pt:{style:Lt},pr:{style:Lt},pb:{style:Lt},pl:{style:Lt},px:{style:Lt},py:{style:Lt},padding:{style:Lt},paddingTop:{style:Lt},paddingRight:{style:Lt},paddingBottom:{style:Lt},paddingLeft:{style:Lt},paddingX:{style:Lt},paddingY:{style:Lt},paddingInline:{style:Lt},paddingInlineStart:{style:Lt},paddingInlineEnd:{style:Lt},paddingBlock:{style:Lt},paddingBlockStart:{style:Lt},paddingBlockEnd:{style:Lt},m:{style:Xt},mt:{style:Xt},mr:{style:Xt},mb:{style:Xt},ml:{style:Xt},mx:{style:Xt},my:{style:Xt},margin:{style:Xt},marginTop:{style:Xt},marginRight:{style:Xt},marginBottom:{style:Xt},marginLeft:{style:Xt},marginX:{style:Xt},marginY:{style:Xt},marginInline:{style:Xt},marginInlineStart:{style:Xt},marginInlineEnd:{style:Xt},marginBlock:{style:Xt},marginBlockStart:{style:Xt},marginBlockEnd:{style:Xt},displayPrint:{cssProperty:!1,transform:u=>({"@media print":{display:u}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:ki},rowGap:{style:$i},columnGap:{style:Ki},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:_e},maxWidth:{style:Yf},minWidth:{transform:_e},height:{transform:_e},maxHeight:{transform:_e},minHeight:{transform:_e},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function _1(...u){const c=u.reduce((r,o)=>r.concat(Object.keys(o)),[]),f=new Set(c);return u.every(r=>f.size===Object.keys(r).length)}function z1(u,c){return typeof u=="function"?u(c):u}function R1(){function u(f,r,o,h){const g={[f]:r,theme:o},S=h[f];if(!S)return{[f]:r};const{cssProperty:A=f,themeKey:v,transform:z,style:R}=S;if(r==null)return null;if(v==="typography"&&r==="inherit")return{[f]:r};const M=Qi(o,v)||{};return R?R(g):pl(g,r,q=>{let x=Gi(M,z,q);return q===x&&typeof q=="string"&&(x=Gi(M,z,`${f}${q==="default"?"":ln(q)}`,q)),A===!1?x:{[A]:x}})}function c(f){const{sx:r,theme:o={}}=f||{};if(!r)return null;const h=o.unstable_sxConfig??Ji;function g(S){let A=S;if(typeof S=="function")A=S(o);else if(typeof S!="object")return S;if(!A)return null;const v=Qg(o.breakpoints),z=Object.keys(v);let R=v;return Object.keys(A).forEach(M=>{const X=z1(A[M],o);if(X!=null)if(typeof X=="object")if(h[M])R=su(R,u(M,X,o,h));else{const q=pl({theme:o},X,x=>({[M]:x}));_1(q,X)?R[M]=c({sx:X,theme:o}):R=su(R,q)}else R=su(R,u(M,X,o,h))}),qg(o,Vg(z,R))}return Array.isArray(r)?r.map(g):g(r)}return c}const an=R1();an.filterProps=["sx"];function _f(){return _f=Object.assign?Object.assign.bind():function(u){for(var c=1;c<arguments.length;c++){var f=arguments[c];for(var r in f)({}).hasOwnProperty.call(f,r)&&(u[r]=f[r])}return u},_f.apply(null,arguments)}function M1(u){if(u.sheet)return u.sheet;for(var c=0;c<document.styleSheets.length;c++)if(document.styleSheets[c].ownerNode===u)return document.styleSheets[c]}function D1(u){var c=document.createElement("style");return c.setAttribute("data-emotion",u.key),u.nonce!==void 0&&c.setAttribute("nonce",u.nonce),c.appendChild(document.createTextNode("")),c.setAttribute("data-s",""),c}var N1=function(){function u(f){var r=this;this._insertTag=function(o){var h;r.tags.length===0?r.insertionPoint?h=r.insertionPoint.nextSibling:r.prepend?h=r.container.firstChild:h=r.before:h=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(o,h),r.tags.push(o)},this.isSpeedy=f.speedy===void 0?!0:f.speedy,this.tags=[],this.ctr=0,this.nonce=f.nonce,this.key=f.key,this.container=f.container,this.prepend=f.prepend,this.insertionPoint=f.insertionPoint,this.before=null}var c=u.prototype;return c.hydrate=function(r){r.forEach(this._insertTag)},c.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(D1(this));var o=this.tags[this.tags.length-1];if(this.isSpeedy){var h=M1(o);try{h.insertRule(r,h.cssRules.length)}catch{}}else o.appendChild(document.createTextNode(r));this.ctr++},c.flush=function(){this.tags.forEach(function(r){var o;return(o=r.parentNode)==null?void 0:o.removeChild(r)}),this.tags=[],this.ctr=0},u}(),fe="-ms-",Xi="-moz-",Tt="-webkit-",zh="comm",Gf="rule",Xf="decl",U1="@import",Rh="@keyframes",B1="@layer",H1=Math.abs,Wi=String.fromCharCode,j1=Object.assign;function w1(u,c){return ue(u,0)^45?(((c<<2^ue(u,0))<<2^ue(u,1))<<2^ue(u,2))<<2^ue(u,3):0}function Mh(u){return u.trim()}function q1(u,c){return(u=c.exec(u))?u[0]:u}function At(u,c,f){return u.replace(c,f)}function zf(u,c){return u.indexOf(c)}function ue(u,c){return u.charCodeAt(c)|0}function ou(u,c,f){return u.slice(c,f)}function Fe(u){return u.length}function Lf(u){return u.length}function Ui(u,c){return c.push(u),u}function Y1(u,c){return u.map(c).join("")}var Fi=1,nn=1,Dh=0,ve=0,Jt=0,un="";function Pi(u,c,f,r,o,h,g){return{value:u,root:c,parent:f,type:r,props:o,children:h,line:Fi,column:nn,length:g,return:""}}function iu(u,c){return j1(Pi("",null,null,"",null,null,0),u,{length:-u.length},c)}function G1(){return Jt}function X1(){return Jt=ve>0?ue(un,--ve):0,nn--,Jt===10&&(nn=1,Fi--),Jt}function Re(){return Jt=ve<Dh?ue(un,ve++):0,nn++,Jt===10&&(nn=1,Fi++),Jt}function Ie(){return ue(un,ve)}function ji(){return ve}function vu(u,c){return ou(un,u,c)}function du(u){switch(u){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Nh(u){return Fi=nn=1,Dh=Fe(un=u),ve=0,[]}function Uh(u){return un="",u}function wi(u){return Mh(vu(ve-1,Rf(u===91?u+2:u===40?u+1:u)))}function L1(u){for(;(Jt=Ie())&&Jt<33;)Re();return du(u)>2||du(Jt)>3?"":" "}function Q1(u,c){for(;--c&&Re()&&!(Jt<48||Jt>102||Jt>57&&Jt<65||Jt>70&&Jt<97););return vu(u,ji()+(c<6&&Ie()==32&&Re()==32))}function Rf(u){for(;Re();)switch(Jt){case u:return ve;case 34:case 39:u!==34&&u!==39&&Rf(Jt);break;case 40:u===41&&Rf(u);break;case 92:Re();break}return ve}function V1(u,c){for(;Re()&&u+Jt!==57;)if(u+Jt===84&&Ie()===47)break;return"/*"+vu(c,ve-1)+"*"+Wi(u===47?u:Re())}function Z1(u){for(;!du(Ie());)Re();return vu(u,ve)}function k1(u){return Uh(qi("",null,null,null,[""],u=Nh(u),0,[0],u))}function qi(u,c,f,r,o,h,g,S,A){for(var v=0,z=0,R=g,M=0,X=0,q=0,x=1,V=1,Z=1,lt=0,F="",W=o,G=h,tt=r,$=F;V;)switch(q=lt,lt=Re()){case 40:if(q!=108&&ue($,R-1)==58){zf($+=At(wi(lt),"&","&\f"),"&\f")!=-1&&(Z=-1);break}case 34:case 39:case 91:$+=wi(lt);break;case 9:case 10:case 13:case 32:$+=L1(q);break;case 92:$+=Q1(ji()-1,7);continue;case 47:switch(Ie()){case 42:case 47:Ui(K1(V1(Re(),ji()),c,f),A);break;default:$+="/"}break;case 123*x:S[v++]=Fe($)*Z;case 125*x:case 59:case 0:switch(lt){case 0:case 125:V=0;case 59+z:Z==-1&&($=At($,/\f/g,"")),X>0&&Fe($)-R&&Ui(X>32?nh($+";",r,f,R-1):nh(At($," ","")+";",r,f,R-2),A);break;case 59:$+=";";default:if(Ui(tt=ah($,c,f,v,z,o,S,F,W=[],G=[],R),h),lt===123)if(z===0)qi($,c,tt,tt,W,h,R,S,G);else switch(M===99&&ue($,3)===110?100:M){case 100:case 108:case 109:case 115:qi(u,tt,tt,r&&Ui(ah(u,tt,tt,0,0,o,S,F,o,W=[],R),G),o,G,R,S,r?W:G);break;default:qi($,tt,tt,tt,[""],G,0,S,G)}}v=z=X=0,x=Z=1,F=$="",R=g;break;case 58:R=1+Fe($),X=q;default:if(x<1){if(lt==123)--x;else if(lt==125&&x++==0&&X1()==125)continue}switch($+=Wi(lt),lt*x){case 38:Z=z>0?1:($+="\f",-1);break;case 44:S[v++]=(Fe($)-1)*Z,Z=1;break;case 64:Ie()===45&&($+=wi(Re())),M=Ie(),z=R=Fe(F=$+=Z1(ji())),lt++;break;case 45:q===45&&Fe($)==2&&(x=0)}}return h}function ah(u,c,f,r,o,h,g,S,A,v,z){for(var R=o-1,M=o===0?h:[""],X=Lf(M),q=0,x=0,V=0;q<r;++q)for(var Z=0,lt=ou(u,R+1,R=H1(x=g[q])),F=u;Z<X;++Z)(F=Mh(x>0?M[Z]+" "+lt:At(lt,/&\f/g,M[Z])))&&(A[V++]=F);return Pi(u,c,f,o===0?Gf:S,A,v,z)}function K1(u,c,f){return Pi(u,c,f,zh,Wi(G1()),ou(u,2,-2),0)}function nh(u,c,f,r){return Pi(u,c,f,Xf,ou(u,0,r),ou(u,r+1,-1),r)}function en(u,c){for(var f="",r=Lf(u),o=0;o<r;o++)f+=c(u[o],o,u,c)||"";return f}function $1(u,c,f,r){switch(u.type){case B1:if(u.children.length)break;case U1:case Xf:return u.return=u.return||u.value;case zh:return"";case Rh:return u.return=u.value+"{"+en(u.children,r)+"}";case Gf:u.value=u.props.join(",")}return Fe(f=en(u.children,r))?u.return=u.value+"{"+f+"}":""}function J1(u){var c=Lf(u);return function(f,r,o,h){for(var g="",S=0;S<c;S++)g+=u[S](f,r,o,h)||"";return g}}function W1(u){return function(c){c.root||(c=c.return)&&u(c)}}function Bh(u){var c=Object.create(null);return function(f){return c[f]===void 0&&(c[f]=u(f)),c[f]}}var F1=function(c,f,r){for(var o=0,h=0;o=h,h=Ie(),o===38&&h===12&&(f[r]=1),!du(h);)Re();return vu(c,ve)},P1=function(c,f){var r=-1,o=44;do switch(du(o)){case 0:o===38&&Ie()===12&&(f[r]=1),c[r]+=F1(ve-1,f,r);break;case 2:c[r]+=wi(o);break;case 4:if(o===44){c[++r]=Ie()===58?"&\f":"",f[r]=c[r].length;break}default:c[r]+=Wi(o)}while(o=Re());return c},I1=function(c,f){return Uh(P1(Nh(c),f))},uh=new WeakMap,tv=function(c){if(!(c.type!=="rule"||!c.parent||c.length<1)){for(var f=c.value,r=c.parent,o=c.column===r.column&&c.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(c.props.length===1&&f.charCodeAt(0)!==58&&!uh.get(r))&&!o){uh.set(c,!0);for(var h=[],g=I1(f,h),S=r.props,A=0,v=0;A<g.length;A++)for(var z=0;z<S.length;z++,v++)c.props[v]=h[A]?g[A].replace(/&\f/g,S[z]):S[z]+" "+g[A]}}},ev=function(c){if(c.type==="decl"){var f=c.value;f.charCodeAt(0)===108&&f.charCodeAt(2)===98&&(c.return="",c.value="")}};function Hh(u,c){switch(w1(u,c)){case 5103:return Tt+"print-"+u+u;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Tt+u+u;case 5349:case 4246:case 4810:case 6968:case 2756:return Tt+u+Xi+u+fe+u+u;case 6828:case 4268:return Tt+u+fe+u+u;case 6165:return Tt+u+fe+"flex-"+u+u;case 5187:return Tt+u+At(u,/(\w+).+(:[^]+)/,Tt+"box-$1$2"+fe+"flex-$1$2")+u;case 5443:return Tt+u+fe+"flex-item-"+At(u,/flex-|-self/,"")+u;case 4675:return Tt+u+fe+"flex-line-pack"+At(u,/align-content|flex-|-self/,"")+u;case 5548:return Tt+u+fe+At(u,"shrink","negative")+u;case 5292:return Tt+u+fe+At(u,"basis","preferred-size")+u;case 6060:return Tt+"box-"+At(u,"-grow","")+Tt+u+fe+At(u,"grow","positive")+u;case 4554:return Tt+At(u,/([^-])(transform)/g,"$1"+Tt+"$2")+u;case 6187:return At(At(At(u,/(zoom-|grab)/,Tt+"$1"),/(image-set)/,Tt+"$1"),u,"")+u;case 5495:case 3959:return At(u,/(image-set\([^]*)/,Tt+"$1$`$1");case 4968:return At(At(u,/(.+:)(flex-)?(.*)/,Tt+"box-pack:$3"+fe+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Tt+u+u;case 4095:case 3583:case 4068:case 2532:return At(u,/(.+)-inline(.+)/,Tt+"$1$2")+u;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Fe(u)-1-c>6)switch(ue(u,c+1)){case 109:if(ue(u,c+4)!==45)break;case 102:return At(u,/(.+:)(.+)-([^]+)/,"$1"+Tt+"$2-$3$1"+Xi+(ue(u,c+3)==108?"$3":"$2-$3"))+u;case 115:return~zf(u,"stretch")?Hh(At(u,"stretch","fill-available"),c)+u:u}break;case 4949:if(ue(u,c+1)!==115)break;case 6444:switch(ue(u,Fe(u)-3-(~zf(u,"!important")&&10))){case 107:return At(u,":",":"+Tt)+u;case 101:return At(u,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Tt+(ue(u,14)===45?"inline-":"")+"box$3$1"+Tt+"$2$3$1"+fe+"$2box$3")+u}break;case 5936:switch(ue(u,c+11)){case 114:return Tt+u+fe+At(u,/[svh]\w+-[tblr]{2}/,"tb")+u;case 108:return Tt+u+fe+At(u,/[svh]\w+-[tblr]{2}/,"tb-rl")+u;case 45:return Tt+u+fe+At(u,/[svh]\w+-[tblr]{2}/,"lr")+u}return Tt+u+fe+u+u}return u}var lv=function(c,f,r,o){if(c.length>-1&&!c.return)switch(c.type){case Xf:c.return=Hh(c.value,c.length);break;case Rh:return en([iu(c,{value:At(c.value,"@","@"+Tt)})],o);case Gf:if(c.length)return Y1(c.props,function(h){switch(q1(h,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return en([iu(c,{props:[At(h,/:(read-\w+)/,":"+Xi+"$1")]})],o);case"::placeholder":return en([iu(c,{props:[At(h,/:(plac\w+)/,":"+Tt+"input-$1")]}),iu(c,{props:[At(h,/:(plac\w+)/,":"+Xi+"$1")]}),iu(c,{props:[At(h,/:(plac\w+)/,fe+"input-$1")]})],o)}return""})}},av=[lv],nv=function(c){var f=c.key;if(f==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(x){var V=x.getAttribute("data-emotion");V.indexOf(" ")!==-1&&(document.head.appendChild(x),x.setAttribute("data-s",""))})}var o=c.stylisPlugins||av,h={},g,S=[];g=c.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+f+' "]'),function(x){for(var V=x.getAttribute("data-emotion").split(" "),Z=1;Z<V.length;Z++)h[V[Z]]=!0;S.push(x)});var A,v=[tv,ev];{var z,R=[$1,W1(function(x){z.insert(x)})],M=J1(v.concat(o,R)),X=function(V){return en(k1(V),M)};A=function(V,Z,lt,F){z=lt,X(V?V+"{"+Z.styles+"}":Z.styles),F&&(q.inserted[Z.name]=!0)}}var q={key:f,sheet:new N1({key:f,container:g,nonce:c.nonce,speedy:c.speedy,prepend:c.prepend,insertionPoint:c.insertionPoint}),nonce:c.nonce,inserted:h,registered:{},insert:A};return q.sheet.hydrate(S),q},uv=!0;function iv(u,c,f){var r="";return f.split(" ").forEach(function(o){u[o]!==void 0?c.push(u[o]+";"):o&&(r+=o+" ")}),r}var jh=function(c,f,r){var o=c.key+"-"+f.name;(r===!1||uv===!1)&&c.registered[o]===void 0&&(c.registered[o]=f.styles)},cv=function(c,f,r){jh(c,f,r);var o=c.key+"-"+f.name;if(c.inserted[f.name]===void 0){var h=f;do c.insert(f===h?"."+o:"",h,c.sheet,!0),h=h.next;while(h!==void 0)}};function rv(u){for(var c=0,f,r=0,o=u.length;o>=4;++r,o-=4)f=u.charCodeAt(r)&255|(u.charCodeAt(++r)&255)<<8|(u.charCodeAt(++r)&255)<<16|(u.charCodeAt(++r)&255)<<24,f=(f&65535)*1540483477+((f>>>16)*59797<<16),f^=f>>>24,c=(f&65535)*1540483477+((f>>>16)*59797<<16)^(c&65535)*1540483477+((c>>>16)*59797<<16);switch(o){case 3:c^=(u.charCodeAt(r+2)&255)<<16;case 2:c^=(u.charCodeAt(r+1)&255)<<8;case 1:c^=u.charCodeAt(r)&255,c=(c&65535)*1540483477+((c>>>16)*59797<<16)}return c^=c>>>13,c=(c&65535)*1540483477+((c>>>16)*59797<<16),((c^c>>>15)>>>0).toString(36)}var fv={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},sv=/[A-Z]|^ms/g,ov=/_EMO_([^_]+?)_([^]*?)_EMO_/g,wh=function(c){return c.charCodeAt(1)===45},ih=function(c){return c!=null&&typeof c!="boolean"},Af=Bh(function(u){return wh(u)?u:u.replace(sv,"-$&").toLowerCase()}),ch=function(c,f){switch(c){case"animation":case"animationName":if(typeof f=="string")return f.replace(ov,function(r,o,h){return Pe={name:o,styles:h,next:Pe},o})}return fv[c]!==1&&!wh(c)&&typeof f=="number"&&f!==0?f+"px":f};function hu(u,c,f){if(f==null)return"";var r=f;if(r.__emotion_styles!==void 0)return r;switch(typeof f){case"boolean":return"";case"object":{var o=f;if(o.anim===1)return Pe={name:o.name,styles:o.styles,next:Pe},o.name;var h=f;if(h.styles!==void 0){var g=h.next;if(g!==void 0)for(;g!==void 0;)Pe={name:g.name,styles:g.styles,next:Pe},g=g.next;var S=h.styles+";";return S}return dv(u,c,f)}case"function":{if(u!==void 0){var A=Pe,v=f(u);return Pe=A,hu(u,c,v)}break}}var z=f;if(c==null)return z;var R=c[z];return R!==void 0?R:z}function dv(u,c,f){var r="";if(Array.isArray(f))for(var o=0;o<f.length;o++)r+=hu(u,c,f[o])+";";else for(var h in f){var g=f[h];if(typeof g!="object"){var S=g;c!=null&&c[S]!==void 0?r+=h+"{"+c[S]+"}":ih(S)&&(r+=Af(h)+":"+ch(h,S)+";")}else if(Array.isArray(g)&&typeof g[0]=="string"&&(c==null||c[g[0]]===void 0))for(var A=0;A<g.length;A++)ih(g[A])&&(r+=Af(h)+":"+ch(h,g[A])+";");else{var v=hu(u,c,g);switch(h){case"animation":case"animationName":{r+=Af(h)+":"+v+";";break}default:r+=h+"{"+v+"}"}}}return r}var rh=/label:\s*([^\s;{]+)\s*(;|$)/g,Pe;function qh(u,c,f){if(u.length===1&&typeof u[0]=="object"&&u[0]!==null&&u[0].styles!==void 0)return u[0];var r=!0,o="";Pe=void 0;var h=u[0];if(h==null||h.raw===void 0)r=!1,o+=hu(f,c,h);else{var g=h;o+=g[0]}for(var S=1;S<u.length;S++)if(o+=hu(f,c,u[S]),r){var A=h;o+=A[S]}rh.lastIndex=0;for(var v="",z;(z=rh.exec(o))!==null;)v+="-"+z[1];var R=rv(o)+v;return{name:R,styles:o,next:Pe}}var hv=function(c){return c()},mv=Z0.useInsertionEffect?Z0.useInsertionEffect:!1,yv=mv||hv,Yh=it.createContext(typeof HTMLElement<"u"?nv({key:"css"}):null);Yh.Provider;var gv=function(c){return it.forwardRef(function(f,r){var o=it.useContext(Yh);return c(f,o,r)})},vv=it.createContext({}),pv=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,bv=Bh(function(u){return pv.test(u)||u.charCodeAt(0)===111&&u.charCodeAt(1)===110&&u.charCodeAt(2)<91}),Sv=bv,Tv=function(c){return c!=="theme"},fh=function(c){return typeof c=="string"&&c.charCodeAt(0)>96?Sv:Tv},sh=function(c,f,r){var o;if(f){var h=f.shouldForwardProp;o=c.__emotion_forwardProp&&h?function(g){return c.__emotion_forwardProp(g)&&h(g)}:h}return typeof o!="function"&&r&&(o=c.__emotion_forwardProp),o},Av=function(c){var f=c.cache,r=c.serialized,o=c.isStringTag;return jh(f,r,o),yv(function(){return cv(f,r,o)}),null},Ev=function u(c,f){var r=c.__emotion_real===c,o=r&&c.__emotion_base||c,h,g;f!==void 0&&(h=f.label,g=f.target);var S=sh(c,f,r),A=S||fh(o),v=!A("as");return function(){var z=arguments,R=r&&c.__emotion_styles!==void 0?c.__emotion_styles.slice(0):[];if(h!==void 0&&R.push("label:"+h+";"),z[0]==null||z[0].raw===void 0)R.push.apply(R,z);else{var M=z[0];R.push(M[0]);for(var X=z.length,q=1;q<X;q++)R.push(z[q],M[q])}var x=gv(function(V,Z,lt){var F=v&&V.as||o,W="",G=[],tt=V;if(V.theme==null){tt={};for(var $ in V)tt[$]=V[$];tt.theme=it.useContext(vv)}typeof V.className=="string"?W=iv(Z.registered,G,V.className):V.className!=null&&(W=V.className+" ");var yt=qh(R.concat(G),Z.registered,tt);W+=Z.key+"-"+yt.name,g!==void 0&&(W+=" "+g);var gt=v&&S===void 0?fh(F):A,m={};for(var k in V)v&&k==="as"||gt(k)&&(m[k]=V[k]);return m.className=W,lt&&(m.ref=lt),it.createElement(it.Fragment,null,it.createElement(Av,{cache:Z,serialized:yt,isStringTag:typeof F=="string"}),it.createElement(F,m))});return x.displayName=h!==void 0?h:"Styled("+(typeof o=="string"?o:o.displayName||o.name||"Component")+")",x.defaultProps=c.defaultProps,x.__emotion_real=x,x.__emotion_base=o,x.__emotion_styles=R,x.__emotion_forwardProp=S,Object.defineProperty(x,"toString",{value:function(){return"."+g}}),x.withComponent=function(V,Z){var lt=u(V,_f({},f,Z,{shouldForwardProp:sh(x,Z,!0)}));return lt.apply(void 0,R)},x}},Ov=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Mf=Ev.bind(null);Ov.forEach(function(u){Mf[u]=Mf(u)});function xv(u,c){return Mf(u,c)}function Cv(u,c){Array.isArray(u.__emotion_styles)&&(u.__emotion_styles=c(u.__emotion_styles))}const oh=[];function dh(u){return oh[0]=u,qh(oh)}const _v=u=>{const c=Object.keys(u).map(f=>({key:f,val:u[f]}))||[];return c.sort((f,r)=>f.val-r.val),c.reduce((f,r)=>({...f,[r.key]:r.val}),{})};function zv(u){const{values:c={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:f="px",step:r=5,...o}=u,h=_v(c),g=Object.keys(h);function S(M){return`@media (min-width:${typeof c[M]=="number"?c[M]:M}${f})`}function A(M){return`@media (max-width:${(typeof c[M]=="number"?c[M]:M)-r/100}${f})`}function v(M,X){const q=g.indexOf(X);return`@media (min-width:${typeof c[M]=="number"?c[M]:M}${f}) and (max-width:${(q!==-1&&typeof c[g[q]]=="number"?c[g[q]]:X)-r/100}${f})`}function z(M){return g.indexOf(M)+1<g.length?v(M,g[g.indexOf(M)+1]):S(M)}function R(M){const X=g.indexOf(M);return X===0?S(g[1]):X===g.length-1?A(g[X]):v(M,g[g.indexOf(M)+1]).replace("@media","@media not all and")}return{keys:g,values:h,up:S,down:A,between:v,only:z,not:R,unit:f,...o}}const Rv={borderRadius:4};function Gh(u=8,c=qf({spacing:u})){if(u.mui)return u;const f=(...r)=>(r.length===0?[1]:r).map(h=>{const g=c(h);return typeof g=="number"?`${g}px`:g}).join(" ");return f.mui=!0,f}function Mv(u,c){var r;const f=this;if(f.vars){if(!((r=f.colorSchemes)!=null&&r[u])||typeof f.getColorSchemeSelector!="function")return{};let o=f.getColorSchemeSelector(u);return o==="&"?c:((o.includes("data-")||o.includes("."))&&(o=`*:where(${o.replace(/\s*&$/,"")}) &`),{[o]:c})}return f.palette.mode===u?c:{}}function Xh(u={},...c){const{breakpoints:f={},palette:r={},spacing:o,shape:h={},...g}=u,S=zv(f),A=Gh(o);let v=ze({breakpoints:S,direction:"ltr",components:{},palette:{mode:"light",...r},spacing:A,shape:{...Rv,...h}},g);return v=Xg(v),v.applyStyles=Mv,v=c.reduce((z,R)=>ze(z,R),v),v.unstable_sxConfig={...Ji,...g==null?void 0:g.unstable_sxConfig},v.unstable_sx=function(R){return an({sx:R,theme:this})},v}const Dv={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Lh(u,c,f="Mui"){const r=Dv[c];return r?`${f}-${r}`:`${Ug.generate(u)}-${c}`}function Nv(u,c,f="Mui"){const r={};return c.forEach(o=>{r[o]=Lh(u,o,f)}),r}function Qh(u){const{variants:c,...f}=u,r={variants:c,style:dh(f),isProcessed:!0};return r.style===f||c&&c.forEach(o=>{typeof o.style!="function"&&(o.style=dh(o.style))}),r}const Uv=Xh();function Ef(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}function Bv(u){return u?(c,f)=>f[u]:null}function Hv(u,c,f){u.theme=qv(u.theme)?f:u.theme[c]||u.theme}function Yi(u,c){const f=typeof c=="function"?c(u):c;if(Array.isArray(f))return f.flatMap(r=>Yi(u,r));if(Array.isArray(f==null?void 0:f.variants)){let r;if(f.isProcessed)r=f.style;else{const{variants:o,...h}=f;r=h}return Vh(u,f.variants,[r])}return f!=null&&f.isProcessed?f.style:f}function Vh(u,c,f=[]){var o;let r;t:for(let h=0;h<c.length;h+=1){const g=c[h];if(typeof g.props=="function"){if(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),!g.props(r))continue}else for(const S in g.props)if(u[S]!==g.props[S]&&((o=u.ownerState)==null?void 0:o[S])!==g.props[S])continue t;typeof g.style=="function"?(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),f.push(g.style(r))):f.push(g.style)}return f}function jv(u={}){const{themeId:c,defaultTheme:f=Uv,rootShouldForwardProp:r=Ef,slotShouldForwardProp:o=Ef}=u;function h(S){Hv(S,c,f)}return(S,A={})=>{Cv(S,G=>G.filter(tt=>tt!==an));const{name:v,slot:z,skipVariantsResolver:R,skipSx:M,overridesResolver:X=Bv(Gv(z)),...q}=A,x=R!==void 0?R:z&&z!=="Root"&&z!=="root"||!1,V=M||!1;let Z=Ef;z==="Root"||z==="root"?Z=r:z?Z=o:Yv(S)&&(Z=void 0);const lt=xv(S,{shouldForwardProp:Z,label:wv(),...q}),F=G=>{if(G.__emotion_real===G)return G;if(typeof G=="function")return function($){return Yi($,G)};if(vl(G)){const tt=Qh(G);return tt.variants?function(yt){return Yi(yt,tt)}:tt.style}return G},W=(...G)=>{const tt=[],$=G.map(F),yt=[];if(tt.push(h),v&&X&&yt.push(function(P){var D,Q;const Ht=(Q=(D=P.theme.components)==null?void 0:D[v])==null?void 0:Q.styleOverrides;if(!Ht)return null;const bt={};for(const et in Ht)bt[et]=Yi(P,Ht[et]);return X(P,bt)}),v&&!x&&yt.push(function(P){var bt,D;const st=P.theme,Ht=(D=(bt=st==null?void 0:st.components)==null?void 0:bt[v])==null?void 0:D.variants;return Ht?Vh(P,Ht):null}),V||yt.push(an),Array.isArray($[0])){const k=$.shift(),P=new Array(tt.length).fill(""),st=new Array(yt.length).fill("");let Ht;Ht=[...P,...k,...st],Ht.raw=[...P,...k.raw,...st],tt.unshift(Ht)}const gt=[...tt,...$,...yt],m=lt(...gt);return S.muiName&&(m.muiName=S.muiName),m};return lt.withConfig&&(W.withConfig=lt.withConfig),W}}function wv(u,c){return void 0}function qv(u){for(const c in u)return!1;return!0}function Yv(u){return typeof u=="string"&&u.charCodeAt(0)>96}function Gv(u){return u&&u.charAt(0).toLowerCase()+u.slice(1)}function Df(u,c){const f={...c};for(const r in u)if(Object.prototype.hasOwnProperty.call(u,r)){const o=r;if(o==="components"||o==="slots")f[o]={...u[o],...f[o]};else if(o==="componentsProps"||o==="slotProps"){const h=u[o],g=c[o];if(!g)f[o]=h||{};else if(!h)f[o]=g;else{f[o]={...g};for(const S in h)if(Object.prototype.hasOwnProperty.call(h,S)){const A=S;f[o][A]=Df(h[A],g[A])}}}else f[o]===void 0&&(f[o]=u[o])}return f}function Xv(u,c=Number.MIN_SAFE_INTEGER,f=Number.MAX_SAFE_INTEGER){return Math.max(c,Math.min(u,f))}function Qf(u,c=0,f=1){return Xv(u,c,f)}function Lv(u){u=u.slice(1);const c=new RegExp(`.{1,${u.length>=6?2:1}}`,"g");let f=u.match(c);return f&&f[0].length===1&&(f=f.map(r=>r+r)),f?`rgb${f.length===4?"a":""}(${f.map((r,o)=>o<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function Ql(u){if(u.type)return u;if(u.charAt(0)==="#")return Ql(Lv(u));const c=u.indexOf("("),f=u.substring(0,c);if(!["rgb","rgba","hsl","hsla","color"].includes(f))throw new Error(fa(9,u));let r=u.substring(c+1,u.length-1),o;if(f==="color"){if(r=r.split(" "),o=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(o))throw new Error(fa(10,o))}else r=r.split(",");return r=r.map(h=>parseFloat(h)),{type:f,values:r,colorSpace:o}}const Qv=u=>{const c=Ql(u);return c.values.slice(0,3).map((f,r)=>c.type.includes("hsl")&&r!==0?`${f}%`:f).join(" ")},ru=(u,c)=>{try{return Qv(u)}catch{return u}};function Ii(u){const{type:c,colorSpace:f}=u;let{values:r}=u;return c.includes("rgb")?r=r.map((o,h)=>h<3?parseInt(o,10):o):c.includes("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),c.includes("color")?r=`${f} ${r.join(" ")}`:r=`${r.join(", ")}`,`${c}(${r})`}function Zh(u){u=Ql(u);const{values:c}=u,f=c[0],r=c[1]/100,o=c[2]/100,h=r*Math.min(o,1-o),g=(v,z=(v+f/30)%12)=>o-h*Math.max(Math.min(z-3,9-z,1),-1);let S="rgb";const A=[Math.round(g(0)*255),Math.round(g(8)*255),Math.round(g(4)*255)];return u.type==="hsla"&&(S+="a",A.push(c[3])),Ii({type:S,values:A})}function Nf(u){u=Ql(u);let c=u.type==="hsl"||u.type==="hsla"?Ql(Zh(u)).values:u.values;return c=c.map(f=>(u.type!=="color"&&(f/=255),f<=.03928?f/12.92:((f+.055)/1.055)**2.4)),Number((.2126*c[0]+.7152*c[1]+.0722*c[2]).toFixed(3))}function Vv(u,c){const f=Nf(u),r=Nf(c);return(Math.max(f,r)+.05)/(Math.min(f,r)+.05)}function Zv(u,c){return u=Ql(u),c=Qf(c),(u.type==="rgb"||u.type==="hsl")&&(u.type+="a"),u.type==="color"?u.values[3]=`/${c}`:u.values[3]=c,Ii(u)}function Bi(u,c,f){try{return Zv(u,c)}catch{return u}}function Vf(u,c){if(u=Ql(u),c=Qf(c),u.type.includes("hsl"))u.values[2]*=1-c;else if(u.type.includes("rgb")||u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]*=1-c;return Ii(u)}function Nt(u,c,f){try{return Vf(u,c)}catch{return u}}function Zf(u,c){if(u=Ql(u),c=Qf(c),u.type.includes("hsl"))u.values[2]+=(100-u.values[2])*c;else if(u.type.includes("rgb"))for(let f=0;f<3;f+=1)u.values[f]+=(255-u.values[f])*c;else if(u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]+=(1-u.values[f])*c;return Ii(u)}function Ut(u,c,f){try{return Zf(u,c)}catch{return u}}function kv(u,c=.15){return Nf(u)>.5?Vf(u,c):Zf(u,c)}function Hi(u,c,f){try{return kv(u,c)}catch{return u}}const Kv=it.createContext(void 0);function $v(u){const{theme:c,name:f,props:r}=u;if(!c||!c.components||!c.components[f])return r;const o=c.components[f];return o.defaultProps?Df(o.defaultProps,r):!o.styleOverrides&&!o.variants?Df(o,r):r}function Jv({props:u,name:c}){const f=it.useContext(Kv);return $v({props:u,name:c,theme:{components:f}})}const hh={theme:void 0};function Wv(u){let c,f;return function(o){let h=c;return(h===void 0||o.theme!==f)&&(hh.theme=o.theme,h=Qh(u(hh)),c=h,f=o.theme),h}}function Fv(u=""){function c(...r){if(!r.length)return"";const o=r[0];return typeof o=="string"&&!o.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${u?`${u}-`:""}${o}${c(...r.slice(1))})`:`, ${o}`}return(r,...o)=>`var(--${u?`${u}-`:""}${r}${c(...o)})`}const mh=(u,c,f,r=[])=>{let o=u;c.forEach((h,g)=>{g===c.length-1?Array.isArray(o)?o[Number(h)]=f:o&&typeof o=="object"&&(o[h]=f):o&&typeof o=="object"&&(o[h]||(o[h]=r.includes(h)?[]:{}),o=o[h])})},Pv=(u,c,f)=>{function r(o,h=[],g=[]){Object.entries(o).forEach(([S,A])=>{(!f||f&&!f([...h,S]))&&A!=null&&(typeof A=="object"&&Object.keys(A).length>0?r(A,[...h,S],Array.isArray(A)?[...g,S]:g):c([...h,S],A,g))})}r(u)},Iv=(u,c)=>typeof c=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(r=>u.includes(r))||u[u.length-1].toLowerCase().includes("opacity")?c:`${c}px`:c;function Of(u,c){const{prefix:f,shouldSkipGeneratingVar:r}=c||{},o={},h={},g={};return Pv(u,(S,A,v)=>{if((typeof A=="string"||typeof A=="number")&&(!r||!r(S,A))){const z=`--${f?`${f}-`:""}${S.join("-")}`,R=Iv(S,A);Object.assign(o,{[z]:R}),mh(h,S,`var(${z})`,v),mh(g,S,`var(${z}, ${R})`,v)}},S=>S[0]==="vars"),{css:o,vars:h,varsWithDefaults:g}}function tp(u,c={}){const{getSelector:f=V,disableCssColorScheme:r,colorSchemeSelector:o}=c,{colorSchemes:h={},components:g,defaultColorScheme:S="light",...A}=u,{vars:v,css:z,varsWithDefaults:R}=Of(A,c);let M=R;const X={},{[S]:q,...x}=h;if(Object.entries(x||{}).forEach(([F,W])=>{const{vars:G,css:tt,varsWithDefaults:$}=Of(W,c);M=ze(M,$),X[F]={css:tt,vars:G}}),q){const{css:F,vars:W,varsWithDefaults:G}=Of(q,c);M=ze(M,G),X[S]={css:F,vars:W}}function V(F,W){var tt,$;let G=o;if(o==="class"&&(G=".%s"),o==="data"&&(G="[data-%s]"),o!=null&&o.startsWith("data-")&&!o.includes("%s")&&(G=`[${o}="%s"]`),F){if(G==="media")return u.defaultColorScheme===F?":root":{[`@media (prefers-color-scheme: ${(($=(tt=h[F])==null?void 0:tt.palette)==null?void 0:$.mode)||F})`]:{":root":W}};if(G)return u.defaultColorScheme===F?`:root, ${G.replace("%s",String(F))}`:G.replace("%s",String(F))}return":root"}return{vars:M,generateThemeVars:()=>{let F={...v};return Object.entries(X).forEach(([,{vars:W}])=>{F=ze(F,W)}),F},generateStyleSheets:()=>{var yt,gt;const F=[],W=u.defaultColorScheme||"light";function G(m,k){Object.keys(k).length&&F.push(typeof m=="string"?{[m]:{...k}}:m)}G(f(void 0,{...z}),z);const{[W]:tt,...$}=X;if(tt){const{css:m}=tt,k=(gt=(yt=h[W])==null?void 0:yt.palette)==null?void 0:gt.mode,P=!r&&k?{colorScheme:k,...m}:{...m};G(f(W,{...P}),P)}return Object.entries($).forEach(([m,{css:k}])=>{var Ht,bt;const P=(bt=(Ht=h[m])==null?void 0:Ht.palette)==null?void 0:bt.mode,st=!r&&P?{colorScheme:P,...k}:{...k};G(f(m,{...st}),st)}),F}}}function ep(u){return function(f){return u==="media"?`@media (prefers-color-scheme: ${f})`:u?u.startsWith("data-")&&!u.includes("%s")?`[${u}="${f}"] &`:u==="class"?`.${f} &`:u==="data"?`[data-${f}] &`:`${u.replace("%s",f)} &`:"&"}}const mu={black:"#000",white:"#fff"},lp={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},Ja={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Wa={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},cu={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Fa={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Pa={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Ia={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function kh(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:mu.white,default:mu.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const ap=kh();function Kh(){return{text:{primary:mu.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:mu.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const yh=Kh();function gh(u,c,f,r){const o=r.light||r,h=r.dark||r*1.5;u[c]||(u.hasOwnProperty(f)?u[c]=u[f]:c==="light"?u.light=Zf(u.main,o):c==="dark"&&(u.dark=Vf(u.main,h)))}function np(u="light"){return u==="dark"?{main:Fa[200],light:Fa[50],dark:Fa[400]}:{main:Fa[700],light:Fa[400],dark:Fa[800]}}function up(u="light"){return u==="dark"?{main:Ja[200],light:Ja[50],dark:Ja[400]}:{main:Ja[500],light:Ja[300],dark:Ja[700]}}function ip(u="light"){return u==="dark"?{main:Wa[500],light:Wa[300],dark:Wa[700]}:{main:Wa[700],light:Wa[400],dark:Wa[800]}}function cp(u="light"){return u==="dark"?{main:Pa[400],light:Pa[300],dark:Pa[700]}:{main:Pa[700],light:Pa[500],dark:Pa[900]}}function rp(u="light"){return u==="dark"?{main:Ia[400],light:Ia[300],dark:Ia[700]}:{main:Ia[800],light:Ia[500],dark:Ia[900]}}function fp(u="light"){return u==="dark"?{main:cu[400],light:cu[300],dark:cu[700]}:{main:"#ed6c02",light:cu[500],dark:cu[900]}}function kf(u){const{mode:c="light",contrastThreshold:f=3,tonalOffset:r=.2,...o}=u,h=u.primary||np(c),g=u.secondary||up(c),S=u.error||ip(c),A=u.info||cp(c),v=u.success||rp(c),z=u.warning||fp(c);function R(x){return Vv(x,yh.text.primary)>=f?yh.text.primary:ap.text.primary}const M=({color:x,name:V,mainShade:Z=500,lightShade:lt=300,darkShade:F=700})=>{if(x={...x},!x.main&&x[Z]&&(x.main=x[Z]),!x.hasOwnProperty("main"))throw new Error(fa(11,V?` (${V})`:"",Z));if(typeof x.main!="string")throw new Error(fa(12,V?` (${V})`:"",JSON.stringify(x.main)));return gh(x,"light",lt,r),gh(x,"dark",F,r),x.contrastText||(x.contrastText=R(x.main)),x};let X;return c==="light"?X=kh():c==="dark"&&(X=Kh()),ze({common:{...mu},mode:c,primary:M({color:h,name:"primary"}),secondary:M({color:g,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:M({color:S,name:"error"}),warning:M({color:z,name:"warning"}),info:M({color:A,name:"info"}),success:M({color:v,name:"success"}),grey:lp,contrastThreshold:f,getContrastText:R,augmentColor:M,tonalOffset:r,...X},o)}function sp(u){const c={};return Object.entries(u).forEach(r=>{const[o,h]=r;typeof h=="object"&&(c[o]=`${h.fontStyle?`${h.fontStyle} `:""}${h.fontVariant?`${h.fontVariant} `:""}${h.fontWeight?`${h.fontWeight} `:""}${h.fontStretch?`${h.fontStretch} `:""}${h.fontSize||""}${h.lineHeight?`/${h.lineHeight} `:""}${h.fontFamily||""}`)}),c}function op(u,c){return{toolbar:{minHeight:56,[u.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[u.up("sm")]:{minHeight:64}},...c}}function dp(u){return Math.round(u*1e5)/1e5}const vh={textTransform:"uppercase"},ph='"Roboto", "Helvetica", "Arial", sans-serif';function hp(u,c){const{fontFamily:f=ph,fontSize:r=14,fontWeightLight:o=300,fontWeightRegular:h=400,fontWeightMedium:g=500,fontWeightBold:S=700,htmlFontSize:A=16,allVariants:v,pxToRem:z,...R}=typeof c=="function"?c(u):c,M=r/14,X=z||(V=>`${V/A*M}rem`),q=(V,Z,lt,F,W)=>({fontFamily:f,fontWeight:V,fontSize:X(Z),lineHeight:lt,...f===ph?{letterSpacing:`${dp(F/Z)}em`}:{},...W,...v}),x={h1:q(o,96,1.167,-1.5),h2:q(o,60,1.2,-.5),h3:q(h,48,1.167,0),h4:q(h,34,1.235,.25),h5:q(h,24,1.334,0),h6:q(g,20,1.6,.15),subtitle1:q(h,16,1.75,.15),subtitle2:q(g,14,1.57,.1),body1:q(h,16,1.5,.15),body2:q(h,14,1.43,.15),button:q(g,14,1.75,.4,vh),caption:q(h,12,1.66,.4),overline:q(h,12,2.66,1,vh),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return ze({htmlFontSize:A,pxToRem:X,fontFamily:f,fontSize:r,fontWeightLight:o,fontWeightRegular:h,fontWeightMedium:g,fontWeightBold:S,...x},R,{clone:!1})}const mp=.2,yp=.14,gp=.12;function wt(...u){return[`${u[0]}px ${u[1]}px ${u[2]}px ${u[3]}px rgba(0,0,0,${mp})`,`${u[4]}px ${u[5]}px ${u[6]}px ${u[7]}px rgba(0,0,0,${yp})`,`${u[8]}px ${u[9]}px ${u[10]}px ${u[11]}px rgba(0,0,0,${gp})`].join(",")}const vp=["none",wt(0,2,1,-1,0,1,1,0,0,1,3,0),wt(0,3,1,-2,0,2,2,0,0,1,5,0),wt(0,3,3,-2,0,3,4,0,0,1,8,0),wt(0,2,4,-1,0,4,5,0,0,1,10,0),wt(0,3,5,-1,0,5,8,0,0,1,14,0),wt(0,3,5,-1,0,6,10,0,0,1,18,0),wt(0,4,5,-2,0,7,10,1,0,2,16,1),wt(0,5,5,-3,0,8,10,1,0,3,14,2),wt(0,5,6,-3,0,9,12,1,0,3,16,2),wt(0,6,6,-3,0,10,14,1,0,4,18,3),wt(0,6,7,-4,0,11,15,1,0,4,20,3),wt(0,7,8,-4,0,12,17,2,0,5,22,4),wt(0,7,8,-4,0,13,19,2,0,5,24,4),wt(0,7,9,-4,0,14,21,2,0,5,26,4),wt(0,8,9,-5,0,15,22,2,0,6,28,5),wt(0,8,10,-5,0,16,24,2,0,6,30,5),wt(0,8,11,-5,0,17,26,2,0,6,32,5),wt(0,9,11,-5,0,18,28,2,0,7,34,6),wt(0,9,12,-6,0,19,29,2,0,7,36,6),wt(0,10,13,-6,0,20,31,3,0,8,38,7),wt(0,10,13,-6,0,21,33,3,0,8,40,7),wt(0,10,14,-6,0,22,35,3,0,8,42,7),wt(0,11,14,-7,0,23,36,3,0,9,44,8),wt(0,11,15,-7,0,24,38,3,0,9,46,8)],pp={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},bp={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function bh(u){return`${Math.round(u)}ms`}function Sp(u){if(!u)return 0;const c=u/36;return Math.min(Math.round((4+15*c**.25+c/5)*10),3e3)}function Tp(u){const c={...pp,...u.easing},f={...bp,...u.duration};return{getAutoHeightDuration:Sp,create:(o=["all"],h={})=>{const{duration:g=f.standard,easing:S=c.easeInOut,delay:A=0,...v}=h;return(Array.isArray(o)?o:[o]).map(z=>`${z} ${typeof g=="string"?g:bh(g)} ${S} ${typeof A=="string"?A:bh(A)}`).join(",")},...u,easing:c,duration:f}}const Ap={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Ep(u){return vl(u)||typeof u>"u"||typeof u=="string"||typeof u=="boolean"||typeof u=="number"||Array.isArray(u)}function $h(u={}){const c={...u};function f(r){const o=Object.entries(r);for(let h=0;h<o.length;h++){const[g,S]=o[h];!Ep(S)||g.startsWith("unstable_")?delete r[g]:vl(S)&&(r[g]={...S},f(r[g]))}}return f(c),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(c,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Uf(u={},...c){const{breakpoints:f,mixins:r={},spacing:o,palette:h={},transitions:g={},typography:S={},shape:A,...v}=u;if(u.vars&&u.generateThemeVars===void 0)throw new Error(fa(20));const z=kf(h),R=Xh(u);let M=ze(R,{mixins:op(R.breakpoints,r),palette:z,shadows:vp.slice(),typography:hp(z,S),transitions:Tp(g),zIndex:{...Ap}});return M=ze(M,v),M=c.reduce((X,q)=>ze(X,q),M),M.unstable_sxConfig={...Ji,...v==null?void 0:v.unstable_sxConfig},M.unstable_sx=function(q){return an({sx:q,theme:this})},M.toRuntimeSource=$h,M}function Op(u){let c;return u<1?c=5.11916*u**2:c=4.5*Math.log(u+1)+2,Math.round(c*10)/1e3}const xp=[...Array(25)].map((u,c)=>{if(c===0)return"none";const f=Op(c);return`linear-gradient(rgba(255 255 255 / ${f}), rgba(255 255 255 / ${f}))`});function Jh(u){return{inputPlaceholder:u==="dark"?.5:.42,inputUnderline:u==="dark"?.7:.42,switchTrackDisabled:u==="dark"?.2:.12,switchTrack:u==="dark"?.3:.38}}function Wh(u){return u==="dark"?xp:[]}function Cp(u){const{palette:c={mode:"light"},opacity:f,overlays:r,...o}=u,h=kf(c);return{palette:h,opacity:{...Jh(h.mode),...f},overlays:r||Wh(h.mode),...o}}function _p(u){var c;return!!u[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!u[0].match(/sxConfig$/)||u[0]==="palette"&&!!((c=u[1])!=null&&c.match(/(mode|contrastThreshold|tonalOffset)/))}const zp=u=>[...[...Array(25)].map((c,f)=>`--${u?`${u}-`:""}overlays-${f}`),`--${u?`${u}-`:""}palette-AppBar-darkBg`,`--${u?`${u}-`:""}palette-AppBar-darkColor`],Rp=u=>(c,f)=>{const r=u.rootSelector||":root",o=u.colorSchemeSelector;let h=o;if(o==="class"&&(h=".%s"),o==="data"&&(h="[data-%s]"),o!=null&&o.startsWith("data-")&&!o.includes("%s")&&(h=`[${o}="%s"]`),u.defaultColorScheme===c){if(c==="dark"){const g={};return zp(u.cssVarPrefix).forEach(S=>{g[S]=f[S],delete f[S]}),h==="media"?{[r]:f,"@media (prefers-color-scheme: dark)":{[r]:g}}:h?{[h.replace("%s",c)]:g,[`${r}, ${h.replace("%s",c)}`]:f}:{[r]:{...f,...g}}}if(h&&h!=="media")return`${r}, ${h.replace("%s",String(c))}`}else if(c){if(h==="media")return{[`@media (prefers-color-scheme: ${String(c)})`]:{[r]:f}};if(h)return h.replace("%s",String(c))}return r};function Mp(u,c){c.forEach(f=>{u[f]||(u[f]={})})}function N(u,c,f){!u[c]&&f&&(u[c]=f)}function fu(u){return typeof u!="string"||!u.startsWith("hsl")?u:Zh(u)}function gl(u,c){`${c}Channel`in u||(u[`${c}Channel`]=ru(fu(u[c])))}function Dp(u){return typeof u=="number"?`${u}px`:typeof u=="string"||typeof u=="function"||Array.isArray(u)?u:"8px"}const We=u=>{try{return u()}catch{}},Np=(u="mui")=>Fv(u);function xf(u,c,f,r){if(!c)return;c=c===!0?{}:c;const o=r==="dark"?"dark":"light";if(!f){u[r]=Cp({...c,palette:{mode:o,...c==null?void 0:c.palette}});return}const{palette:h,...g}=Uf({...f,palette:{mode:o,...c==null?void 0:c.palette}});return u[r]={...c,palette:h,opacity:{...Jh(o),...c==null?void 0:c.opacity},overlays:(c==null?void 0:c.overlays)||Wh(o)},g}function Up(u={},...c){const{colorSchemes:f={light:!0},defaultColorScheme:r,disableCssColorScheme:o=!1,cssVarPrefix:h="mui",shouldSkipGeneratingVar:g=_p,colorSchemeSelector:S=f.light&&f.dark?"media":void 0,rootSelector:A=":root",...v}=u,z=Object.keys(f)[0],R=r||(f.light&&z!=="light"?"light":z),M=Np(h),{[R]:X,light:q,dark:x,...V}=f,Z={...V};let lt=X;if((R==="dark"&&!("dark"in f)||R==="light"&&!("light"in f))&&(lt=!0),!lt)throw new Error(fa(21,R));const F=xf(Z,lt,v,R);q&&!Z.light&&xf(Z,q,void 0,"light"),x&&!Z.dark&&xf(Z,x,void 0,"dark");let W={defaultColorScheme:R,...F,cssVarPrefix:h,colorSchemeSelector:S,rootSelector:A,getCssVar:M,colorSchemes:Z,font:{...sp(F.typography),...F.font},spacing:Dp(v.spacing)};Object.keys(W.colorSchemes).forEach(gt=>{const m=W.colorSchemes[gt].palette,k=P=>{const st=P.split("-"),Ht=st[1],bt=st[2];return M(P,m[Ht][bt])};if(m.mode==="light"&&(N(m.common,"background","#fff"),N(m.common,"onBackground","#000")),m.mode==="dark"&&(N(m.common,"background","#000"),N(m.common,"onBackground","#fff")),Mp(m,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),m.mode==="light"){N(m.Alert,"errorColor",Nt(m.error.light,.6)),N(m.Alert,"infoColor",Nt(m.info.light,.6)),N(m.Alert,"successColor",Nt(m.success.light,.6)),N(m.Alert,"warningColor",Nt(m.warning.light,.6)),N(m.Alert,"errorFilledBg",k("palette-error-main")),N(m.Alert,"infoFilledBg",k("palette-info-main")),N(m.Alert,"successFilledBg",k("palette-success-main")),N(m.Alert,"warningFilledBg",k("palette-warning-main")),N(m.Alert,"errorFilledColor",We(()=>m.getContrastText(m.error.main))),N(m.Alert,"infoFilledColor",We(()=>m.getContrastText(m.info.main))),N(m.Alert,"successFilledColor",We(()=>m.getContrastText(m.success.main))),N(m.Alert,"warningFilledColor",We(()=>m.getContrastText(m.warning.main))),N(m.Alert,"errorStandardBg",Ut(m.error.light,.9)),N(m.Alert,"infoStandardBg",Ut(m.info.light,.9)),N(m.Alert,"successStandardBg",Ut(m.success.light,.9)),N(m.Alert,"warningStandardBg",Ut(m.warning.light,.9)),N(m.Alert,"errorIconColor",k("palette-error-main")),N(m.Alert,"infoIconColor",k("palette-info-main")),N(m.Alert,"successIconColor",k("palette-success-main")),N(m.Alert,"warningIconColor",k("palette-warning-main")),N(m.AppBar,"defaultBg",k("palette-grey-100")),N(m.Avatar,"defaultBg",k("palette-grey-400")),N(m.Button,"inheritContainedBg",k("palette-grey-300")),N(m.Button,"inheritContainedHoverBg",k("palette-grey-A100")),N(m.Chip,"defaultBorder",k("palette-grey-400")),N(m.Chip,"defaultAvatarColor",k("palette-grey-700")),N(m.Chip,"defaultIconColor",k("palette-grey-700")),N(m.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),N(m.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),N(m.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),N(m.LinearProgress,"primaryBg",Ut(m.primary.main,.62)),N(m.LinearProgress,"secondaryBg",Ut(m.secondary.main,.62)),N(m.LinearProgress,"errorBg",Ut(m.error.main,.62)),N(m.LinearProgress,"infoBg",Ut(m.info.main,.62)),N(m.LinearProgress,"successBg",Ut(m.success.main,.62)),N(m.LinearProgress,"warningBg",Ut(m.warning.main,.62)),N(m.Skeleton,"bg",`rgba(${k("palette-text-primaryChannel")} / 0.11)`),N(m.Slider,"primaryTrack",Ut(m.primary.main,.62)),N(m.Slider,"secondaryTrack",Ut(m.secondary.main,.62)),N(m.Slider,"errorTrack",Ut(m.error.main,.62)),N(m.Slider,"infoTrack",Ut(m.info.main,.62)),N(m.Slider,"successTrack",Ut(m.success.main,.62)),N(m.Slider,"warningTrack",Ut(m.warning.main,.62));const P=Hi(m.background.default,.8);N(m.SnackbarContent,"bg",P),N(m.SnackbarContent,"color",We(()=>m.getContrastText(P))),N(m.SpeedDialAction,"fabHoverBg",Hi(m.background.paper,.15)),N(m.StepConnector,"border",k("palette-grey-400")),N(m.StepContent,"border",k("palette-grey-400")),N(m.Switch,"defaultColor",k("palette-common-white")),N(m.Switch,"defaultDisabledColor",k("palette-grey-100")),N(m.Switch,"primaryDisabledColor",Ut(m.primary.main,.62)),N(m.Switch,"secondaryDisabledColor",Ut(m.secondary.main,.62)),N(m.Switch,"errorDisabledColor",Ut(m.error.main,.62)),N(m.Switch,"infoDisabledColor",Ut(m.info.main,.62)),N(m.Switch,"successDisabledColor",Ut(m.success.main,.62)),N(m.Switch,"warningDisabledColor",Ut(m.warning.main,.62)),N(m.TableCell,"border",Ut(Bi(m.divider,1),.88)),N(m.Tooltip,"bg",Bi(m.grey[700],.92))}if(m.mode==="dark"){N(m.Alert,"errorColor",Ut(m.error.light,.6)),N(m.Alert,"infoColor",Ut(m.info.light,.6)),N(m.Alert,"successColor",Ut(m.success.light,.6)),N(m.Alert,"warningColor",Ut(m.warning.light,.6)),N(m.Alert,"errorFilledBg",k("palette-error-dark")),N(m.Alert,"infoFilledBg",k("palette-info-dark")),N(m.Alert,"successFilledBg",k("palette-success-dark")),N(m.Alert,"warningFilledBg",k("palette-warning-dark")),N(m.Alert,"errorFilledColor",We(()=>m.getContrastText(m.error.dark))),N(m.Alert,"infoFilledColor",We(()=>m.getContrastText(m.info.dark))),N(m.Alert,"successFilledColor",We(()=>m.getContrastText(m.success.dark))),N(m.Alert,"warningFilledColor",We(()=>m.getContrastText(m.warning.dark))),N(m.Alert,"errorStandardBg",Nt(m.error.light,.9)),N(m.Alert,"infoStandardBg",Nt(m.info.light,.9)),N(m.Alert,"successStandardBg",Nt(m.success.light,.9)),N(m.Alert,"warningStandardBg",Nt(m.warning.light,.9)),N(m.Alert,"errorIconColor",k("palette-error-main")),N(m.Alert,"infoIconColor",k("palette-info-main")),N(m.Alert,"successIconColor",k("palette-success-main")),N(m.Alert,"warningIconColor",k("palette-warning-main")),N(m.AppBar,"defaultBg",k("palette-grey-900")),N(m.AppBar,"darkBg",k("palette-background-paper")),N(m.AppBar,"darkColor",k("palette-text-primary")),N(m.Avatar,"defaultBg",k("palette-grey-600")),N(m.Button,"inheritContainedBg",k("palette-grey-800")),N(m.Button,"inheritContainedHoverBg",k("palette-grey-700")),N(m.Chip,"defaultBorder",k("palette-grey-700")),N(m.Chip,"defaultAvatarColor",k("palette-grey-300")),N(m.Chip,"defaultIconColor",k("palette-grey-300")),N(m.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),N(m.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),N(m.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),N(m.LinearProgress,"primaryBg",Nt(m.primary.main,.5)),N(m.LinearProgress,"secondaryBg",Nt(m.secondary.main,.5)),N(m.LinearProgress,"errorBg",Nt(m.error.main,.5)),N(m.LinearProgress,"infoBg",Nt(m.info.main,.5)),N(m.LinearProgress,"successBg",Nt(m.success.main,.5)),N(m.LinearProgress,"warningBg",Nt(m.warning.main,.5)),N(m.Skeleton,"bg",`rgba(${k("palette-text-primaryChannel")} / 0.13)`),N(m.Slider,"primaryTrack",Nt(m.primary.main,.5)),N(m.Slider,"secondaryTrack",Nt(m.secondary.main,.5)),N(m.Slider,"errorTrack",Nt(m.error.main,.5)),N(m.Slider,"infoTrack",Nt(m.info.main,.5)),N(m.Slider,"successTrack",Nt(m.success.main,.5)),N(m.Slider,"warningTrack",Nt(m.warning.main,.5));const P=Hi(m.background.default,.98);N(m.SnackbarContent,"bg",P),N(m.SnackbarContent,"color",We(()=>m.getContrastText(P))),N(m.SpeedDialAction,"fabHoverBg",Hi(m.background.paper,.15)),N(m.StepConnector,"border",k("palette-grey-600")),N(m.StepContent,"border",k("palette-grey-600")),N(m.Switch,"defaultColor",k("palette-grey-300")),N(m.Switch,"defaultDisabledColor",k("palette-grey-600")),N(m.Switch,"primaryDisabledColor",Nt(m.primary.main,.55)),N(m.Switch,"secondaryDisabledColor",Nt(m.secondary.main,.55)),N(m.Switch,"errorDisabledColor",Nt(m.error.main,.55)),N(m.Switch,"infoDisabledColor",Nt(m.info.main,.55)),N(m.Switch,"successDisabledColor",Nt(m.success.main,.55)),N(m.Switch,"warningDisabledColor",Nt(m.warning.main,.55)),N(m.TableCell,"border",Nt(Bi(m.divider,1),.68)),N(m.Tooltip,"bg",Bi(m.grey[700],.92))}gl(m.background,"default"),gl(m.background,"paper"),gl(m.common,"background"),gl(m.common,"onBackground"),gl(m,"divider"),Object.keys(m).forEach(P=>{const st=m[P];P!=="tonalOffset"&&st&&typeof st=="object"&&(st.main&&N(m[P],"mainChannel",ru(fu(st.main))),st.light&&N(m[P],"lightChannel",ru(fu(st.light))),st.dark&&N(m[P],"darkChannel",ru(fu(st.dark))),st.contrastText&&N(m[P],"contrastTextChannel",ru(fu(st.contrastText))),P==="text"&&(gl(m[P],"primary"),gl(m[P],"secondary")),P==="action"&&(st.active&&gl(m[P],"active"),st.selected&&gl(m[P],"selected")))})}),W=c.reduce((gt,m)=>ze(gt,m),W);const G={prefix:h,disableCssColorScheme:o,shouldSkipGeneratingVar:g,getSelector:Rp(W)},{vars:tt,generateThemeVars:$,generateStyleSheets:yt}=tp(W,G);return W.vars=tt,Object.entries(W.colorSchemes[W.defaultColorScheme]).forEach(([gt,m])=>{W[gt]=m}),W.generateThemeVars=$,W.generateStyleSheets=yt,W.generateSpacing=function(){return Gh(v.spacing,qf(this))},W.getColorSchemeSelector=ep(S),W.spacing=W.generateSpacing(),W.shouldSkipGeneratingVar=g,W.unstable_sxConfig={...Ji,...v==null?void 0:v.unstable_sxConfig},W.unstable_sx=function(m){return an({sx:m,theme:this})},W.toRuntimeSource=$h,W}function Sh(u,c,f){u.colorSchemes&&f&&(u.colorSchemes[c]={...f!==!0&&f,palette:kf({...f===!0?{}:f.palette,mode:c})})}function Bp(u={},...c){const{palette:f,cssVariables:r=!1,colorSchemes:o=f?void 0:{light:!0},defaultColorScheme:h=f==null?void 0:f.mode,...g}=u,S=h||"light",A=o==null?void 0:o[S],v={...o,...f?{[S]:{...typeof A!="boolean"&&A,palette:f}}:void 0};if(r===!1){if(!("colorSchemes"in u))return Uf(u,...c);let z=f;"palette"in u||v[S]&&(v[S]!==!0?z=v[S].palette:S==="dark"&&(z={mode:"dark"}));const R=Uf({...u,palette:z},...c);return R.defaultColorScheme=S,R.colorSchemes=v,R.palette.mode==="light"&&(R.colorSchemes.light={...v.light!==!0&&v.light,palette:R.palette},Sh(R,"dark",v.dark)),R.palette.mode==="dark"&&(R.colorSchemes.dark={...v.dark!==!0&&v.dark,palette:R.palette},Sh(R,"light",v.light)),R}return!f&&!("light"in v)&&S==="light"&&(v.light=!0),Up({...g,colorSchemes:v,defaultColorScheme:S,...typeof r!="boolean"&&r},...c)}const Hp=Bp(),jp="$$material";function wp(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}const qp=u=>wp(u)&&u!=="classes",Yp=jv({themeId:jp,defaultTheme:Hp,rootShouldForwardProp:qp}),Gp=Wv;function Xp(u){return Jv(u)}function Lp(u){return Lh("MuiSvgIcon",u)}Nv("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Qp=u=>{const{color:c,fontSize:f,classes:r}=u,o={root:["root",c!=="inherit"&&`color${ln(c)}`,`fontSize${ln(f)}`]};return Hg(o,Lp,r)},Vp=Yp("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(u,c)=>{const{ownerState:f}=u;return[c.root,f.color!=="inherit"&&c[`color${ln(f.color)}`],c[`fontSize${ln(f.fontSize)}`]]}})(Gp(({theme:u})=>{var c,f,r,o,h,g,S,A,v,z,R,M,X,q;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(o=(c=u.transitions)==null?void 0:c.create)==null?void 0:o.call(c,"fill",{duration:(r=(f=(u.vars??u).transitions)==null?void 0:f.duration)==null?void 0:r.shorter}),variants:[{props:x=>!x.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((g=(h=u.typography)==null?void 0:h.pxToRem)==null?void 0:g.call(h,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((A=(S=u.typography)==null?void 0:S.pxToRem)==null?void 0:A.call(S,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((z=(v=u.typography)==null?void 0:v.pxToRem)==null?void 0:z.call(v,35))||"2.1875rem"}},...Object.entries((u.vars??u).palette).filter(([,x])=>x&&x.main).map(([x])=>{var V,Z;return{props:{color:x},style:{color:(Z=(V=(u.vars??u).palette)==null?void 0:V[x])==null?void 0:Z.main}}}),{props:{color:"action"},style:{color:(M=(R=(u.vars??u).palette)==null?void 0:R.action)==null?void 0:M.active}},{props:{color:"disabled"},style:{color:(q=(X=(u.vars??u).palette)==null?void 0:X.action)==null?void 0:q.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Bf=it.forwardRef(function(c,f){const r=Xp({props:c,name:"MuiSvgIcon"}),{children:o,className:h,color:g="inherit",component:S="svg",fontSize:A="medium",htmlColor:v,inheritViewBox:z=!1,titleAccess:R,viewBox:M="0 0 24 24",...X}=r,q=it.isValidElement(o)&&o.type==="svg",x={...r,color:g,component:S,fontSize:A,instanceFontSize:c.fontSize,inheritViewBox:z,viewBox:M,hasSvgAsChild:q},V={};z||(V.viewBox=M);const Z=Qp(x);return L.jsxs(Vp,{as:S,className:Bg(Z.root,h),focusable:"false",color:v,"aria-hidden":R?void 0:!0,role:R?"img":void 0,ref:f,...V,...X,...q&&o.props,ownerState:x,children:[q?o.props.children:o,R?L.jsx("title",{children:R}):null]})});Bf.muiName="SvgIcon";function Vl(u,c){function f(r,o){return L.jsx(Bf,{"data-testid":void 0,ref:o,...r,children:u})}return f.muiName=Bf.muiName,it.memo(it.forwardRef(f))}const Zp=Vl(L.jsx("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6"})),kp=Vl(L.jsx("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6m0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4"})),Kp=Vl(L.jsx("path",{d:"m7 10 5 5 5-5z"})),Kf=Vl(L.jsx("path",{d:"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7zm-6 .67 2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"})),$p=({text:u,onSave:c})=>L.jsxs("div",{className:"joke-card card",children:[L.jsx("p",{children:u}),L.jsx("button",{className:"save-button",onClick:c,children:L.jsx(Kf,{})})]}),Jp=({text:u,onSave:c})=>L.jsxs("div",{className:"fact-card card",children:[L.jsx("p",{children:u}),L.jsx("button",{className:"save-button",onClick:c,children:L.jsx(Kf,{})})]}),Wp=({question:u,correctAnswer:c,incorrectAnswers:f,onSave:r})=>{const[o,h]=it.useState(!1),[g,S]=it.useState(null),[A,v]=it.useState([]);it.useEffect(()=>{h(!1),S(null),v([...f,c].sort(()=>Math.random()-.5))},[u,c,f]);const z=M=>{S(M),h(!0)},R=M=>o?M===c?"correct":M===g&&M!==c?"incorrect":"faded":"";return L.jsxs("div",{className:"trivia-card card",children:[L.jsx("div",{className:"trivia-question",children:L.jsx("h3",{dangerouslySetInnerHTML:{__html:u}})}),L.jsx("div",{className:"trivia-answers",children:A.map((M,X)=>L.jsx("button",{onClick:()=>z(M),className:`answer-button ${R(M)}`,disabled:o,dangerouslySetInnerHTML:{__html:M}},X))}),o&&L.jsx("div",{className:"trivia-result",children:g===c?L.jsx("p",{className:"correct-message",children:"Correct! 🎉"}):L.jsxs("p",{className:"incorrect-message",children:["Sorry! The correct answer is: ",L.jsx("span",{dangerouslySetInnerHTML:{__html:c}})]})}),L.jsx("button",{className:"save-button",onClick:r,title:"Save to Notepad",children:L.jsx(Kf,{})})]})},Fp=Vl(L.jsx("path",{d:"M18 2H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h9c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 14H9V4h9zM3 15v-2h2v2zm0-5.5h2v2H3zM10 20h2v2h-2zm-7-1.5v-2h2v2zM5 22c-1.1 0-2-.9-2-2h2zm3.5 0h-2v-2h2zm5 0v-2h2c0 1.1-.9 2-2 2M5 6v2H3c0-1.1.9-2 2-2"})),Cf=Vl(L.jsx("path",{d:"M5 13h14v-2H5zm-2 4h14v-2H3zM7 7v2h14V7z"})),Pp=Vl(L.jsx("path",{d:"M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8"})),Ip=({savedJokes:u,savedFacts:c,savedTrivia:f,notepadTab:r,setNotepadTab:o,copyNotepad:h,clearJokes:g,clearFacts:S,clearTrivia:A,removeItem:v,undoRemove:z})=>{const[R,M]=it.useState([]),X=()=>{switch(r){case"joke":return u;case"fact":return c;case"trivia":return f;default:return[]}},q=Z=>{const lt=X()[Z];M([...R,{tab:r,item:lt,index:Z}]),v(r,Z)},x=()=>{if(R.length>0){const Z=R[R.length-1];z(Z.tab,Z.item,Z.index),M(R.slice(0,-1))}},V=X();return L.jsxs("div",{className:"notepad",children:[L.jsxs("div",{className:"notepad-tabs",children:[L.jsx("button",{className:r==="joke"?"active":"",onClick:()=>o("joke"),children:"Jokes"}),L.jsx("button",{className:r==="fact"?"active":"",onClick:()=>o("fact"),children:"Random Facts"}),L.jsx("button",{className:r==="trivia"?"active":"",onClick:()=>o("trivia"),children:"Trivia"})]}),L.jsxs("div",{className:"notepad-content",children:[V.length===0?L.jsx("p",{className:"notepad-empty",children:"Nothing saved yet!"}):L.jsx("ul",{children:V.map((Z,lt)=>L.jsx("li",{className:"notepad-item",onClick:()=>q(lt),children:Z},lt))}),L.jsxs("div",{className:"notepad-actions",style:{display:"flex",gap:"0.5rem",justifyContent:"flex-end",marginTop:"1rem"},children:[R.length>0&&L.jsxs("button",{onClick:x,title:`Undo Remove (${R.length})`,className:"undo-button",children:[L.jsx(Pp,{})," ",R.length>1&&L.jsx("span",{children:R.length})]}),L.jsx("button",{onClick:h,title:"Copy All",children:L.jsx(Fp,{})}),r==="joke"?L.jsx("button",{onClick:g,title:"Clear All",className:"clear-button",children:L.jsx(Cf,{})}):r==="fact"?L.jsx("button",{onClick:S,title:"Clear All",className:"clear-button",children:L.jsx(Cf,{})}):L.jsx("button",{onClick:A,title:"Clear All",className:"clear-button",children:L.jsx(Cf,{})})]})]})]})},t2=Vl(L.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}));function e2(){const{darkMode:u,toggleTheme:c}=it.useContext(Eh),[f,r]=it.useState("joke"),[o,h]=it.useState("any"),[g,S]=it.useState(""),[A,v]=it.useState(!1),[z,R]=it.useState(null),[M,X]=it.useState(""),[q,x]=it.useState(""),[V,Z]=it.useState([]),lt=[{id:"any",name:"Any"},{id:"programming",name:"Programming"},{id:"misc",name:"Misc"},{id:"pun",name:"Pun"},{id:"dark",name:"Dark"},{id:"dad",name:"Dad"}],F=()=>{tt([])},W=()=>{yt([])},[G,tt]=it.useState(()=>{const w=localStorage.getItem("savedJokes");return w?JSON.parse(w):[]}),[$,yt]=it.useState(()=>{const w=localStorage.getItem("savedFacts");return w?JSON.parse(w):[]}),[gt,m]=it.useState(()=>{const w=localStorage.getItem("savedTrivia");return w?JSON.parse(w):[]}),k=()=>{m([])},[P,st]=it.useState("joke"),[Ht,bt]=it.useState(!1),D=it.useRef(null);it.useEffect(()=>{function w(Y){D.current&&!D.current.contains(Y.target)&&bt(!1)}return document.addEventListener("mousedown",w),()=>{document.removeEventListener("mousedown",w)}},[]);const Q=async(w=0,Y=0)=>{v(!0),R(null);try{let K="",rt={};f==="joke"?o==="any"||o==="dad"?(K="https://icanhazdadjoke.com/",rt={Accept:"application/json"}):K=`https://v2.jokeapi.dev/joke/${o}?type=single`:f==="fact"?K="https://uselessfacts.jsph.pl/random.json?language=en":f==="trivia"&&(K="https://the-trivia-api.com/api/questions?limit=1");const ct=new AbortController,se=setTimeout(()=>ct.abort(),5e3),Ct=await fetch(K,{headers:rt,signal:ct.signal});if(clearTimeout(se),!Ct.ok)throw new Error(`API responded with status: ${Ct.status}`);const Qt=await Ct.json();if(f==="joke"){if(o==="any"||o==="dad")S(Qt.joke);else if(Qt.type==="single")S(Qt.joke);else if(Qt.type==="twopart")S(`${Qt.setup} ${Qt.delivery}`);else if(Qt.error)throw new Error(Qt.message||"Failed to fetch joke")}else if(f==="fact")S(Qt.text);else if(f==="trivia")if(Qt&&Qt.length>0){const tl=Qt[0];X(tl.question),x(tl.correctAnswer),Z(tl.incorrectAnswers),S(`Q: ${tl.question} A: ${tl.correctAnswer}`)}else throw new Error("Failed to fetch trivia")}catch(K){if(console.error("Fetch error:",K),w<2)return console.log(`Retrying fetch (attempt ${w+1})...`),v(!1),Q(w+1);R(K.message),f==="trivia"&&(X(""),x(""),Z([])),S("")}finally{v(!1)}};it.useEffect(()=>{f==="fact"&&h("any")},[f]),it.useEffect(()=>{S(""),f==="trivia"&&(X(""),x(""),Z([])),Q()},[f,o]);const et=()=>{g&&(f==="joke"&&!G.includes(g)?tt([...G,g]):f==="fact"&&!$.includes(g)?yt([...$,g]):f==="trivia"&&!gt.includes(g)&&m([...gt,g]))};it.useEffect(()=>{localStorage.setItem("savedJokes",JSON.stringify(G))},[G]),it.useEffect(()=>{localStorage.setItem("savedFacts",JSON.stringify($))},[$]),it.useEffect(()=>{localStorage.setItem("savedTrivia",JSON.stringify(gt))},[gt]);const Et=()=>{let w="";P==="joke"?w=G.join(`

`):P==="fact"?w=$.join(`

`):P==="trivia"&&(w=gt.join(`

`)),navigator.clipboard.writeText(w)},p=(w,Y)=>{if(w==="joke"){const K=[...G];K.splice(Y,1),tt(K)}else if(w==="fact"){const K=[...$];K.splice(Y,1),yt(K)}else if(w==="trivia"){const K=[...gt];K.splice(Y,1),m(K)}},j=(w,Y,K)=>{if(w==="joke"){const rt=[...G];rt.splice(K,0,Y),tt(rt)}else if(w==="fact"){const rt=[...$];rt.splice(K,0,Y),yt(rt)}else if(w==="trivia"){const rt=[...gt];rt.splice(K,0,Y),m(rt)}};return L.jsxs("div",{className:`app ${u?"dark-mode":"light-mode"}`,children:[L.jsx("div",{className:"theme-toggle",children:L.jsx("button",{onClick:c,className:"theme-button",children:u?L.jsx(kp,{}):L.jsx(Zp,{})})}),L.jsx("h1",{children:"The Grin Bin"}),L.jsxs("div",{className:"category-buttons",children:[L.jsxs("div",{className:"category-button-container",children:[L.jsxs("button",{className:`${f==="joke"?"active":""} jokes-button-with-dropdown`,onClick:()=>{r("joke"),st("joke")},children:["Jokes ",L.jsx(Kp,{style:{fontSize:"16px",width:"16px",height:"16px"}})]}),L.jsx("div",{className:"joke-dropdown",children:lt.map(w=>L.jsx("button",{className:`joke-dropdown-item ${o===w.id?"active":""}`,onClick:Y=>{Y.stopPropagation(),h(w.id)},children:w.name},w.id))})]}),L.jsx("button",{className:f==="fact"?"active":"",onClick:()=>{r("fact"),st("fact"),bt(!1)},children:"Random Fact"}),L.jsx("button",{className:f==="trivia"?"active":"",onClick:()=>{r("trivia"),st("trivia"),bt(!1)},children:"Trivia"})]}),L.jsx("div",{className:"content-container",style:{height:f==="trivia"?"400px":"250px"},children:L.jsx("div",{className:"card-wrapper",children:L.jsxs("div",{className:"card-transition",children:[f==="joke"&&g&&L.jsx($p,{text:g,onSave:et}),f==="fact"&&g&&L.jsx(Jp,{text:g,onSave:et}),f==="trivia"&&M&&L.jsx(Wp,{question:M,correctAnswer:q,incorrectAnswers:V,onSave:et}),!g&&L.jsx("div",{className:"empty-card"})]})})}),L.jsxs("button",{className:"refresh-button",onClick:Q,disabled:A,children:[L.jsx(t2,{style:{marginRight:"0.5rem"}})," Get Another"]}),L.jsx(Ip,{savedJokes:G,savedFacts:$,savedTrivia:gt,notepadTab:P,setNotepadTab:st,copyNotepad:Et,clearJokes:F,clearFacts:W,clearTrivia:k,removeItem:p,undoRemove:j})]})}Mg.createRoot(document.getElementById("root")).render(L.jsx(Ah.StrictMode,{children:L.jsx(Dg,{children:L.jsx(e2,{})})}));
