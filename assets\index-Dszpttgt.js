function pg(u,c){for(var f=0;f<c.length;f++){const r=c[f];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in u)){const h=Object.getOwnPropertyDescriptor(r,s);h&&Object.defineProperty(u,s,h.get?h:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const h of s)if(h.type==="childList")for(const v of h.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&r(v)}).observe(document,{childList:!0,subtree:!0});function f(s){const h={};return s.integrity&&(h.integrity=s.integrity),s.referrerPolicy&&(h.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?h.credentials="include":s.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function r(s){if(s.ep)return;s.ep=!0;const h=f(s);fetch(s.href,h)}})();function Sh(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var yf={exports:{}},lu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var G0;function bg(){if(G0)return lu;G0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function f(r,s,h){var v=null;if(h!==void 0&&(v=""+h),s.key!==void 0&&(v=""+s.key),"key"in s){h={};for(var S in s)S!=="key"&&(h[S]=s[S])}else h=s;return s=h.ref,{$$typeof:u,type:r,key:v,ref:s!==void 0?s:null,props:h}}return lu.Fragment=c,lu.jsx=f,lu.jsxs=f,lu}var w0;function Sg(){return w0||(w0=1,yf.exports=bg()),yf.exports}var ut=Sg(),gf={exports:{}},ct={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X0;function Tg(){if(X0)return ct;X0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),R=Symbol.iterator;function N(p){return p===null||typeof p!="object"?null:(p=R&&p[R]||p["@@iterator"],typeof p=="function"?p:null)}var Y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},q=Object.assign,_={};function X(p,j,Z){this.props=p,this.context=j,this.refs=_,this.updater=Z||Y}X.prototype.isReactComponent={},X.prototype.setState=function(p,j){if(typeof p!="object"&&typeof p!="function"&&p!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,p,j,"setState")},X.prototype.forceUpdate=function(p){this.updater.enqueueForceUpdate(this,p,"forceUpdate")};function K(){}K.prototype=X.prototype;function nt(p,j,Z){this.props=p,this.context=j,this.refs=_,this.updater=Z||Y}var Q=nt.prototype=new K;Q.constructor=nt,q(Q,X.prototype),Q.isPureReactComponent=!0;var k=Array.isArray,G={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function J(p,j,Z,L,tt,mt){return Z=mt.ref,{$$typeof:u,type:p,key:j,ref:Z!==void 0?Z:null,props:mt}}function bt(p,j){return J(p.type,j,void 0,void 0,void 0,p.props)}function Nt(p){return typeof p=="object"&&p!==null&&p.$$typeof===u}function m(p){var j={"=":"=0",":":"=2"};return"$"+p.replace(/[=:]/g,function(Z){return j[Z]})}var V=/\/+/g;function P(p,j){return typeof p=="object"&&p!==null&&p.key!=null?m(""+p.key):j.toString(36)}function dt(){}function qt(p){switch(p.status){case"fulfilled":return p.value;case"rejected":throw p.reason;default:switch(typeof p.status=="string"?p.then(dt,dt):(p.status="pending",p.then(function(j){p.status==="pending"&&(p.status="fulfilled",p.value=j)},function(j){p.status==="pending"&&(p.status="rejected",p.reason=j)})),p.status){case"fulfilled":return p.value;case"rejected":throw p.reason}}throw p}function xt(p,j,Z,L,tt){var mt=typeof p;(mt==="undefined"||mt==="boolean")&&(p=null);var it=!1;if(p===null)it=!0;else switch(mt){case"bigint":case"string":case"number":it=!0;break;case"object":switch(p.$$typeof){case u:case c:it=!0;break;case z:return it=p._init,xt(it(p._payload),j,Z,L,tt)}}if(it)return tt=tt(p),it=L===""?"."+P(p,0):L,k(tt)?(Z="",it!=null&&(Z=it.replace(V,"$&/")+"/"),xt(tt,j,Z,"",function(vl){return vl})):tt!=null&&(Nt(tt)&&(tt=bt(tt,Z+(tt.key==null||p&&p.key===tt.key?"":(""+tt.key).replace(V,"$&/")+"/")+it)),j.push(tt)),1;it=0;var ge=L===""?".":L+":";if(k(p))for(var Bt=0;Bt<p.length;Bt++)L=p[Bt],mt=ge+P(L,Bt),it+=xt(L,j,Z,mt,tt);else if(Bt=N(p),typeof Bt=="function")for(p=Bt.call(p),Bt=0;!(L=p.next()).done;)L=L.value,mt=ge+P(L,Bt++),it+=xt(L,j,Z,mt,tt);else if(mt==="object"){if(typeof p.then=="function")return xt(qt(p),j,Z,L,tt);throw j=String(p),Error("Objects are not valid as a React child (found: "+(j==="[object Object]"?"object with keys {"+Object.keys(p).join(", ")+"}":j)+"). If you meant to render a collection of children, use an array instead.")}return it}function M(p,j,Z){if(p==null)return p;var L=[],tt=0;return xt(p,L,"","",function(mt){return j.call(Z,mt,tt++)}),L}function w(p){if(p._status===-1){var j=p._result;j=j(),j.then(function(Z){(p._status===0||p._status===-1)&&(p._status=1,p._result=Z)},function(Z){(p._status===0||p._status===-1)&&(p._status=2,p._result=Z)}),p._status===-1&&(p._status=0,p._result=j)}if(p._status===1)return p._result.default;throw p._result}var I=typeof reportError=="function"?reportError:function(p){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var j=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof p=="object"&&p!==null&&typeof p.message=="string"?String(p.message):String(p),error:p});if(!window.dispatchEvent(j))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",p);return}console.error(p)};function zt(){}return ct.Children={map:M,forEach:function(p,j,Z){M(p,function(){j.apply(this,arguments)},Z)},count:function(p){var j=0;return M(p,function(){j++}),j},toArray:function(p){return M(p,function(j){return j})||[]},only:function(p){if(!Nt(p))throw Error("React.Children.only expected to receive a single React element child.");return p}},ct.Component=X,ct.Fragment=f,ct.Profiler=s,ct.PureComponent=nt,ct.StrictMode=r,ct.Suspense=A,ct.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,ct.__COMPILER_RUNTIME={__proto__:null,c:function(p){return G.H.useMemoCache(p)}},ct.cache=function(p){return function(){return p.apply(null,arguments)}},ct.cloneElement=function(p,j,Z){if(p==null)throw Error("The argument must be a React element, but you passed "+p+".");var L=q({},p.props),tt=p.key,mt=void 0;if(j!=null)for(it in j.ref!==void 0&&(mt=void 0),j.key!==void 0&&(tt=""+j.key),j)!F.call(j,it)||it==="key"||it==="__self"||it==="__source"||it==="ref"&&j.ref===void 0||(L[it]=j[it]);var it=arguments.length-2;if(it===1)L.children=Z;else if(1<it){for(var ge=Array(it),Bt=0;Bt<it;Bt++)ge[Bt]=arguments[Bt+2];L.children=ge}return J(p.type,tt,void 0,void 0,mt,L)},ct.createContext=function(p){return p={$$typeof:v,_currentValue:p,_currentValue2:p,_threadCount:0,Provider:null,Consumer:null},p.Provider=p,p.Consumer={$$typeof:h,_context:p},p},ct.createElement=function(p,j,Z){var L,tt={},mt=null;if(j!=null)for(L in j.key!==void 0&&(mt=""+j.key),j)F.call(j,L)&&L!=="key"&&L!=="__self"&&L!=="__source"&&(tt[L]=j[L]);var it=arguments.length-2;if(it===1)tt.children=Z;else if(1<it){for(var ge=Array(it),Bt=0;Bt<it;Bt++)ge[Bt]=arguments[Bt+2];tt.children=ge}if(p&&p.defaultProps)for(L in it=p.defaultProps,it)tt[L]===void 0&&(tt[L]=it[L]);return J(p,mt,void 0,void 0,null,tt)},ct.createRef=function(){return{current:null}},ct.forwardRef=function(p){return{$$typeof:S,render:p}},ct.isValidElement=Nt,ct.lazy=function(p){return{$$typeof:z,_payload:{_status:-1,_result:p},_init:w}},ct.memo=function(p,j){return{$$typeof:g,type:p,compare:j===void 0?null:j}},ct.startTransition=function(p){var j=G.T,Z={};G.T=Z;try{var L=p(),tt=G.S;tt!==null&&tt(Z,L),typeof L=="object"&&L!==null&&typeof L.then=="function"&&L.then(zt,I)}catch(mt){I(mt)}finally{G.T=j}},ct.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},ct.use=function(p){return G.H.use(p)},ct.useActionState=function(p,j,Z){return G.H.useActionState(p,j,Z)},ct.useCallback=function(p,j){return G.H.useCallback(p,j)},ct.useContext=function(p){return G.H.useContext(p)},ct.useDebugValue=function(){},ct.useDeferredValue=function(p,j){return G.H.useDeferredValue(p,j)},ct.useEffect=function(p,j,Z){var L=G.H;if(typeof Z=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return L.useEffect(p,j)},ct.useId=function(){return G.H.useId()},ct.useImperativeHandle=function(p,j,Z){return G.H.useImperativeHandle(p,j,Z)},ct.useInsertionEffect=function(p,j){return G.H.useInsertionEffect(p,j)},ct.useLayoutEffect=function(p,j){return G.H.useLayoutEffect(p,j)},ct.useMemo=function(p,j){return G.H.useMemo(p,j)},ct.useOptimistic=function(p,j){return G.H.useOptimistic(p,j)},ct.useReducer=function(p,j,Z){return G.H.useReducer(p,j,Z)},ct.useRef=function(p){return G.H.useRef(p)},ct.useState=function(p){return G.H.useState(p)},ct.useSyncExternalStore=function(p,j,Z){return G.H.useSyncExternalStore(p,j,Z)},ct.useTransition=function(){return G.H.useTransition()},ct.version="19.1.0",ct}var Q0;function Bf(){return Q0||(Q0=1,gf.exports=Tg()),gf.exports}var At=Bf();const Th=Sh(At),L0=pg({__proto__:null,default:Th},[At]);var vf={exports:{}},au={},pf={exports:{}},bf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var V0;function Ag(){return V0||(V0=1,function(u){function c(M,w){var I=M.length;M.push(w);t:for(;0<I;){var zt=I-1>>>1,p=M[zt];if(0<s(p,w))M[zt]=w,M[I]=p,I=zt;else break t}}function f(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var w=M[0],I=M.pop();if(I!==w){M[0]=I;t:for(var zt=0,p=M.length,j=p>>>1;zt<j;){var Z=2*(zt+1)-1,L=M[Z],tt=Z+1,mt=M[tt];if(0>s(L,I))tt<p&&0>s(mt,L)?(M[zt]=mt,M[tt]=I,zt=tt):(M[zt]=L,M[Z]=I,zt=Z);else if(tt<p&&0>s(mt,I))M[zt]=mt,M[tt]=I,zt=tt;else break t}}return w}function s(M,w){var I=M.sortIndex-w.sortIndex;return I!==0?I:M.id-w.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;u.unstable_now=function(){return h.now()}}else{var v=Date,S=v.now();u.unstable_now=function(){return v.now()-S}}var A=[],g=[],z=1,R=null,N=3,Y=!1,q=!1,_=!1,X=!1,K=typeof setTimeout=="function"?setTimeout:null,nt=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;function k(M){for(var w=f(g);w!==null;){if(w.callback===null)r(g);else if(w.startTime<=M)r(g),w.sortIndex=w.expirationTime,c(A,w);else break;w=f(g)}}function G(M){if(_=!1,k(M),!q)if(f(A)!==null)q=!0,F||(F=!0,P());else{var w=f(g);w!==null&&xt(G,w.startTime-M)}}var F=!1,J=-1,bt=5,Nt=-1;function m(){return X?!0:!(u.unstable_now()-Nt<bt)}function V(){if(X=!1,F){var M=u.unstable_now();Nt=M;var w=!0;try{t:{q=!1,_&&(_=!1,nt(J),J=-1),Y=!0;var I=N;try{e:{for(k(M),R=f(A);R!==null&&!(R.expirationTime>M&&m());){var zt=R.callback;if(typeof zt=="function"){R.callback=null,N=R.priorityLevel;var p=zt(R.expirationTime<=M);if(M=u.unstable_now(),typeof p=="function"){R.callback=p,k(M),w=!0;break e}R===f(A)&&r(A),k(M)}else r(A);R=f(A)}if(R!==null)w=!0;else{var j=f(g);j!==null&&xt(G,j.startTime-M),w=!1}}break t}finally{R=null,N=I,Y=!1}w=void 0}}finally{w?P():F=!1}}}var P;if(typeof Q=="function")P=function(){Q(V)};else if(typeof MessageChannel<"u"){var dt=new MessageChannel,qt=dt.port2;dt.port1.onmessage=V,P=function(){qt.postMessage(null)}}else P=function(){K(V,0)};function xt(M,w){J=K(function(){M(u.unstable_now())},w)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(M){M.callback=null},u.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):bt=0<M?Math.floor(1e3/M):5},u.unstable_getCurrentPriorityLevel=function(){return N},u.unstable_next=function(M){switch(N){case 1:case 2:case 3:var w=3;break;default:w=N}var I=N;N=w;try{return M()}finally{N=I}},u.unstable_requestPaint=function(){X=!0},u.unstable_runWithPriority=function(M,w){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var I=N;N=M;try{return w()}finally{N=I}},u.unstable_scheduleCallback=function(M,w,I){var zt=u.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?zt+I:zt):I=zt,M){case 1:var p=-1;break;case 2:p=250;break;case 5:p=1073741823;break;case 4:p=1e4;break;default:p=5e3}return p=I+p,M={id:z++,callback:w,priorityLevel:M,startTime:I,expirationTime:p,sortIndex:-1},I>zt?(M.sortIndex=I,c(g,M),f(A)===null&&M===f(g)&&(_?(nt(J),J=-1):_=!0,xt(G,I-zt))):(M.sortIndex=p,c(A,M),q||Y||(q=!0,F||(F=!0,P()))),M},u.unstable_shouldYield=m,u.unstable_wrapCallback=function(M){var w=N;return function(){var I=N;N=w;try{return M.apply(this,arguments)}finally{N=I}}}}(bf)),bf}var Z0;function Eg(){return Z0||(Z0=1,pf.exports=Ag()),pf.exports}var Sf={exports:{}},ce={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var K0;function Og(){if(K0)return ce;K0=1;var u=Bf();function c(A){var g="https://react.dev/errors/"+A;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)g+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+A+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var r={d:{f,r:function(){throw Error(c(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},s=Symbol.for("react.portal");function h(A,g,z){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:R==null?null:""+R,children:A,containerInfo:g,implementation:z}}var v=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(A,g){if(A==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return ce.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ce.createPortal=function(A,g){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(c(299));return h(A,g,null,z)},ce.flushSync=function(A){var g=v.T,z=r.p;try{if(v.T=null,r.p=2,A)return A()}finally{v.T=g,r.p=z,r.d.f()}},ce.preconnect=function(A,g){typeof A=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,r.d.C(A,g))},ce.prefetchDNS=function(A){typeof A=="string"&&r.d.D(A)},ce.preinit=function(A,g){if(typeof A=="string"&&g&&typeof g.as=="string"){var z=g.as,R=S(z,g.crossOrigin),N=typeof g.integrity=="string"?g.integrity:void 0,Y=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;z==="style"?r.d.S(A,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:R,integrity:N,fetchPriority:Y}):z==="script"&&r.d.X(A,{crossOrigin:R,integrity:N,fetchPriority:Y,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},ce.preinitModule=function(A,g){if(typeof A=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var z=S(g.as,g.crossOrigin);r.d.M(A,{crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&r.d.M(A)},ce.preload=function(A,g){if(typeof A=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var z=g.as,R=S(z,g.crossOrigin);r.d.L(A,z,{crossOrigin:R,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},ce.preloadModule=function(A,g){if(typeof A=="string")if(g){var z=S(g.as,g.crossOrigin);r.d.m(A,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else r.d.m(A)},ce.requestFormReset=function(A){r.d.r(A)},ce.unstable_batchedUpdates=function(A,g){return A(g)},ce.useFormState=function(A,g,z){return v.H.useFormState(A,g,z)},ce.useFormStatus=function(){return v.H.useHostTransitionStatus()},ce.version="19.1.0",ce}var k0;function _g(){if(k0)return Sf.exports;k0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),Sf.exports=Og(),Sf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $0;function Cg(){if($0)return au;$0=1;var u=Eg(),c=Bf(),f=_g();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function v(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(h(t)!==t)throw Error(r(188))}function A(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(r(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return S(n),t;if(i===a)return S(n),e;i=i.sibling}throw Error(r(188))}if(l.return!==a.return)l=n,a=i;else{for(var o=!1,d=n.child;d;){if(d===l){o=!0,l=n,a=i;break}if(d===a){o=!0,a=n,l=i;break}d=d.sibling}if(!o){for(d=i.child;d;){if(d===l){o=!0,l=i,a=n;break}if(d===a){o=!0,a=i,l=n;break}d=d.sibling}if(!o)throw Error(r(189))}}if(l.alternate!==a)throw Error(r(190))}if(l.tag!==3)throw Error(r(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,R=Symbol.for("react.element"),N=Symbol.for("react.transitional.element"),Y=Symbol.for("react.portal"),q=Symbol.for("react.fragment"),_=Symbol.for("react.strict_mode"),X=Symbol.for("react.profiler"),K=Symbol.for("react.provider"),nt=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),J=Symbol.for("react.memo"),bt=Symbol.for("react.lazy"),Nt=Symbol.for("react.activity"),m=Symbol.for("react.memo_cache_sentinel"),V=Symbol.iterator;function P(t){return t===null||typeof t!="object"?null:(t=V&&t[V]||t["@@iterator"],typeof t=="function"?t:null)}var dt=Symbol.for("react.client.reference");function qt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===dt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case q:return"Fragment";case X:return"Profiler";case _:return"StrictMode";case G:return"Suspense";case F:return"SuspenseList";case Nt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case Y:return"Portal";case Q:return(t.displayName||"Context")+".Provider";case nt:return(t._context.displayName||"Context")+".Consumer";case k:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case J:return e=t.displayName||null,e!==null?e:qt(t.type)||"Memo";case bt:e=t._payload,t=t._init;try{return qt(t(e))}catch{}}return null}var xt=Array.isArray,M=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,w=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I={pending:!1,data:null,method:null,action:null},zt=[],p=-1;function j(t){return{current:t}}function Z(t){0>p||(t.current=zt[p],zt[p]=null,p--)}function L(t,e){p++,zt[p]=t.current,t.current=e}var tt=j(null),mt=j(null),it=j(null),ge=j(null);function Bt(t,e){switch(L(it,e),L(mt,t),L(tt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?h0(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=h0(e),t=m0(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Z(tt),L(tt,t)}function vl(){Z(tt),Z(mt),Z(it)}function Ii(t){t.memoizedState!==null&&L(ge,t);var e=tt.current,l=m0(e,t.type);e!==l&&(L(mt,t),L(tt,l))}function gu(t){mt.current===t&&(Z(tt),Z(mt)),ge.current===t&&(Z(ge),Fn._currentValue=I)}var tc=Object.prototype.hasOwnProperty,ec=u.unstable_scheduleCallback,lc=u.unstable_cancelCallback,Wh=u.unstable_shouldYield,Fh=u.unstable_requestPaint,Le=u.unstable_now,Ph=u.unstable_getCurrentPriorityLevel,Kf=u.unstable_ImmediatePriority,kf=u.unstable_UserBlockingPriority,vu=u.unstable_NormalPriority,Ih=u.unstable_LowPriority,$f=u.unstable_IdlePriority,tm=u.log,em=u.unstable_setDisableYieldValue,nn=null,ve=null;function pl(t){if(typeof tm=="function"&&em(t),ve&&typeof ve.setStrictMode=="function")try{ve.setStrictMode(nn,t)}catch{}}var pe=Math.clz32?Math.clz32:nm,lm=Math.log,am=Math.LN2;function nm(t){return t>>>=0,t===0?32:31-(lm(t)/am|0)|0}var pu=256,bu=4194304;function Ll(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Su(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,i=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var d=a&134217727;return d!==0?(a=d&~i,a!==0?n=Ll(a):(o&=d,o!==0?n=Ll(o):l||(l=d&~t,l!==0&&(n=Ll(l))))):(d=a&~i,d!==0?n=Ll(d):o!==0?n=Ll(o):l||(l=a&~t,l!==0&&(n=Ll(l)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:n}function un(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function um(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jf(){var t=pu;return pu<<=1,(pu&4194048)===0&&(pu=256),t}function Wf(){var t=bu;return bu<<=1,(bu&62914560)===0&&(bu=4194304),t}function ac(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function cn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function im(t,e,l,a,n,i){var o=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var d=t.entanglements,y=t.expirationTimes,O=t.hiddenUpdates;for(l=o&~l;0<l;){var U=31-pe(l),H=1<<U;d[U]=0,y[U]=-1;var C=O[U];if(C!==null)for(O[U]=null,U=0;U<C.length;U++){var x=C[U];x!==null&&(x.lane&=-536870913)}l&=~H}a!==0&&Ff(t,a,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(o&~e))}function Ff(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-pe(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Pf(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-pe(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function nc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function uc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function If(){var t=w.p;return t!==0?t:(t=window.event,t===void 0?32:N0(t.type))}function cm(t,e){var l=w.p;try{return w.p=t,e()}finally{w.p=l}}var bl=Math.random().toString(36).slice(2),ue="__reactFiber$"+bl,oe="__reactProps$"+bl,ra="__reactContainer$"+bl,ic="__reactEvents$"+bl,rm="__reactListeners$"+bl,fm="__reactHandles$"+bl,to="__reactResources$"+bl,rn="__reactMarker$"+bl;function cc(t){delete t[ue],delete t[oe],delete t[ic],delete t[rm],delete t[fm]}function fa(t){var e=t[ue];if(e)return e;for(var l=t.parentNode;l;){if(e=l[ra]||l[ue]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=p0(t);t!==null;){if(l=t[ue])return l;t=p0(t)}return e}t=l,l=t.parentNode}return null}function oa(t){if(t=t[ue]||t[ra]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function sa(t){var e=t[to];return e||(e=t[to]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ft(t){t[rn]=!0}var eo=new Set,lo={};function Vl(t,e){da(t,e),da(t+"Capture",e)}function da(t,e){for(lo[t]=e,t=0;t<e.length;t++)eo.add(e[t])}var om=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ao={},no={};function sm(t){return tc.call(no,t)?!0:tc.call(ao,t)?!1:om.test(t)?no[t]=!0:(ao[t]=!0,!1)}function Tu(t,e,l){if(sm(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Au(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Ie(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var rc,uo;function ha(t){if(rc===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);rc=e&&e[1]||"",uo=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+rc+t+uo}var fc=!1;function oc(t,e){if(!t||fc)return"";fc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(x){var C=x}Reflect.construct(t,[],H)}else{try{H.call()}catch(x){C=x}t.call(H.prototype)}}else{try{throw Error()}catch(x){C=x}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(x){if(x&&C&&typeof x.stack=="string")return[x.stack,C.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),o=i[0],d=i[1];if(o&&d){var y=o.split(`
`),O=d.split(`
`);for(n=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;n<O.length&&!O[n].includes("DetermineComponentFrameRoot");)n++;if(a===y.length||n===O.length)for(a=y.length-1,n=O.length-1;1<=a&&0<=n&&y[a]!==O[n];)n--;for(;1<=a&&0<=n;a--,n--)if(y[a]!==O[n]){if(a!==1||n!==1)do if(a--,n--,0>n||y[a]!==O[n]){var U=`
`+y[a].replace(" at new "," at ");return t.displayName&&U.includes("<anonymous>")&&(U=U.replace("<anonymous>",t.displayName)),U}while(1<=a&&0<=n);break}}}finally{fc=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ha(l):""}function dm(t){switch(t.tag){case 26:case 27:case 5:return ha(t.type);case 16:return ha("Lazy");case 13:return ha("Suspense");case 19:return ha("SuspenseList");case 0:case 15:return oc(t.type,!1);case 11:return oc(t.type.render,!1);case 1:return oc(t.type,!0);case 31:return ha("Activity");default:return""}}function io(t){try{var e="";do e+=dm(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Re(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function co(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function hm(t){var e=co(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(o){a=""+o,i.call(this,o)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(o){a=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Eu(t){t._valueTracker||(t._valueTracker=hm(t))}function ro(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=co(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Ou(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var mm=/[\n"\\]/g;function Me(t){return t.replace(mm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function sc(t,e,l,a,n,i,o,d){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Re(e)):t.value!==""+Re(e)&&(t.value=""+Re(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?dc(t,o,Re(e)):l!=null?dc(t,o,Re(l)):a!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+Re(d):t.removeAttribute("name")}function fo(t,e,l,a,n,i,o,d){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+Re(l):"",e=e!=null?""+Re(e):l,d||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=d?t.checked:!!a,t.defaultChecked=!!a,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function dc(t,e,l){e==="number"&&Ou(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function ma(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+Re(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function oo(t,e,l){if(e!=null&&(e=""+Re(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+Re(l):""}function so(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(r(92));if(xt(a)){if(1<a.length)throw Error(r(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=Re(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ya(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var ym=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ho(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||ym.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function mo(t,e,l){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&ho(t,n,a)}else for(var i in e)e.hasOwnProperty(i)&&ho(t,i,e[i])}function hc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var gm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),vm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _u(t){return vm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var mc=null;function yc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ga=null,va=null;function yo(t){var e=oa(t);if(e&&(t=e.stateNode)){var l=t[oe]||null;t:switch(t=e.stateNode,e.type){case"input":if(sc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Me(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[oe]||null;if(!n)throw Error(r(90));sc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&ro(a)}break t;case"textarea":oo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&ma(t,!!l.multiple,e,!1)}}}var gc=!1;function go(t,e,l){if(gc)return t(e,l);gc=!0;try{var a=t(e);return a}finally{if(gc=!1,(ga!==null||va!==null)&&(oi(),ga&&(e=ga,t=va,va=ga=null,yo(e),t)))for(e=0;e<t.length;e++)yo(t[e])}}function on(t,e){var l=t.stateNode;if(l===null)return null;var a=l[oe]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(r(231,e,typeof l));return l}var tl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vc=!1;if(tl)try{var sn={};Object.defineProperty(sn,"passive",{get:function(){vc=!0}}),window.addEventListener("test",sn,sn),window.removeEventListener("test",sn,sn)}catch{vc=!1}var Sl=null,pc=null,Cu=null;function vo(){if(Cu)return Cu;var t,e=pc,l=e.length,a,n="value"in Sl?Sl.value:Sl.textContent,i=n.length;for(t=0;t<l&&e[t]===n[t];t++);var o=l-t;for(a=1;a<=o&&e[l-a]===n[i-a];a++);return Cu=n.slice(t,1<a?1-a:void 0)}function xu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function zu(){return!0}function po(){return!1}function se(t){function e(l,a,n,i,o){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(l=t[d],this[d]=l?l(i):i[d]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?zu:po,this.isPropagationStopped=po,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=zu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=zu)},persist:function(){},isPersistent:zu}),e}var Zl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ru=se(Zl),dn=z({},Zl,{view:0,detail:0}),pm=se(dn),bc,Sc,hn,Mu=z({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ac,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==hn&&(hn&&t.type==="mousemove"?(bc=t.screenX-hn.screenX,Sc=t.screenY-hn.screenY):Sc=bc=0,hn=t),bc)},movementY:function(t){return"movementY"in t?t.movementY:Sc}}),bo=se(Mu),bm=z({},Mu,{dataTransfer:0}),Sm=se(bm),Tm=z({},dn,{relatedTarget:0}),Tc=se(Tm),Am=z({},Zl,{animationName:0,elapsedTime:0,pseudoElement:0}),Em=se(Am),Om=z({},Zl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),_m=se(Om),Cm=z({},Zl,{data:0}),So=se(Cm),xm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},zm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Rm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Rm[t])?!!e[t]:!1}function Ac(){return Mm}var Dm=z({},dn,{key:function(t){if(t.key){var e=xm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=xu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?zm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ac,charCode:function(t){return t.type==="keypress"?xu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?xu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Um=se(Dm),Nm=z({},Mu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),To=se(Nm),Bm=z({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ac}),Hm=se(Bm),jm=z({},Zl,{propertyName:0,elapsedTime:0,pseudoElement:0}),qm=se(jm),Ym=z({},Mu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Gm=se(Ym),wm=z({},Zl,{newState:0,oldState:0}),Xm=se(wm),Qm=[9,13,27,32],Ec=tl&&"CompositionEvent"in window,mn=null;tl&&"documentMode"in document&&(mn=document.documentMode);var Lm=tl&&"TextEvent"in window&&!mn,Ao=tl&&(!Ec||mn&&8<mn&&11>=mn),Eo=" ",Oo=!1;function _o(t,e){switch(t){case"keyup":return Qm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Co(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var pa=!1;function Vm(t,e){switch(t){case"compositionend":return Co(e);case"keypress":return e.which!==32?null:(Oo=!0,Eo);case"textInput":return t=e.data,t===Eo&&Oo?null:t;default:return null}}function Zm(t,e){if(pa)return t==="compositionend"||!Ec&&_o(t,e)?(t=vo(),Cu=pc=Sl=null,pa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Ao&&e.locale!=="ko"?null:e.data;default:return null}}var Km={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function xo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Km[t.type]:e==="textarea"}function zo(t,e,l,a){ga?va?va.push(a):va=[a]:ga=a,e=gi(e,"onChange"),0<e.length&&(l=new Ru("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var yn=null,gn=null;function km(t){r0(t,0)}function Du(t){var e=fn(t);if(ro(e))return t}function Ro(t,e){if(t==="change")return e}var Mo=!1;if(tl){var Oc;if(tl){var _c="oninput"in document;if(!_c){var Do=document.createElement("div");Do.setAttribute("oninput","return;"),_c=typeof Do.oninput=="function"}Oc=_c}else Oc=!1;Mo=Oc&&(!document.documentMode||9<document.documentMode)}function Uo(){yn&&(yn.detachEvent("onpropertychange",No),gn=yn=null)}function No(t){if(t.propertyName==="value"&&Du(gn)){var e=[];zo(e,gn,t,yc(t)),go(km,e)}}function $m(t,e,l){t==="focusin"?(Uo(),yn=e,gn=l,yn.attachEvent("onpropertychange",No)):t==="focusout"&&Uo()}function Jm(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Du(gn)}function Wm(t,e){if(t==="click")return Du(e)}function Fm(t,e){if(t==="input"||t==="change")return Du(e)}function Pm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var be=typeof Object.is=="function"?Object.is:Pm;function vn(t,e){if(be(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!tc.call(e,n)||!be(t[n],e[n]))return!1}return!0}function Bo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Ho(t,e){var l=Bo(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Bo(l)}}function jo(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?jo(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function qo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ou(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Ou(t.document)}return e}function Cc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Im=tl&&"documentMode"in document&&11>=document.documentMode,ba=null,xc=null,pn=null,zc=!1;function Yo(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;zc||ba==null||ba!==Ou(a)||(a=ba,"selectionStart"in a&&Cc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),pn&&vn(pn,a)||(pn=a,a=gi(xc,"onSelect"),0<a.length&&(e=new Ru("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=ba)))}function Kl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Sa={animationend:Kl("Animation","AnimationEnd"),animationiteration:Kl("Animation","AnimationIteration"),animationstart:Kl("Animation","AnimationStart"),transitionrun:Kl("Transition","TransitionRun"),transitionstart:Kl("Transition","TransitionStart"),transitioncancel:Kl("Transition","TransitionCancel"),transitionend:Kl("Transition","TransitionEnd")},Rc={},Go={};tl&&(Go=document.createElement("div").style,"AnimationEvent"in window||(delete Sa.animationend.animation,delete Sa.animationiteration.animation,delete Sa.animationstart.animation),"TransitionEvent"in window||delete Sa.transitionend.transition);function kl(t){if(Rc[t])return Rc[t];if(!Sa[t])return t;var e=Sa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Go)return Rc[t]=e[l];return t}var wo=kl("animationend"),Xo=kl("animationiteration"),Qo=kl("animationstart"),ty=kl("transitionrun"),ey=kl("transitionstart"),ly=kl("transitioncancel"),Lo=kl("transitionend"),Vo=new Map,Mc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Mc.push("scrollEnd");function we(t,e){Vo.set(t,e),Vl(e,[t])}var Zo=new WeakMap;function De(t,e){if(typeof t=="object"&&t!==null){var l=Zo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:io(e)},Zo.set(t,e),e)}return{value:t,source:e,stack:io(e)}}var Ue=[],Ta=0,Dc=0;function Uu(){for(var t=Ta,e=Dc=Ta=0;e<t;){var l=Ue[e];Ue[e++]=null;var a=Ue[e];Ue[e++]=null;var n=Ue[e];Ue[e++]=null;var i=Ue[e];if(Ue[e++]=null,a!==null&&n!==null){var o=a.pending;o===null?n.next=n:(n.next=o.next,o.next=n),a.pending=n}i!==0&&Ko(l,n,i)}}function Nu(t,e,l,a){Ue[Ta++]=t,Ue[Ta++]=e,Ue[Ta++]=l,Ue[Ta++]=a,Dc|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Uc(t,e,l,a){return Nu(t,e,l,a),Bu(t)}function Aa(t,e){return Nu(t,null,null,e),Bu(t)}function Ko(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=t.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-pe(l),t=i.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),i):null}function Bu(t){if(50<Ln)throw Ln=0,Yr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ea={};function ay(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Se(t,e,l,a){return new ay(t,e,l,a)}function Nc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function el(t,e){var l=t.alternate;return l===null?(l=Se(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function ko(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Hu(t,e,l,a,n,i){var o=0;if(a=t,typeof t=="function")Nc(t)&&(o=1);else if(typeof t=="string")o=ug(t,l,tt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Nt:return t=Se(31,l,e,n),t.elementType=Nt,t.lanes=i,t;case q:return $l(l.children,n,i,e);case _:o=8,n|=24;break;case X:return t=Se(12,l,e,n|2),t.elementType=X,t.lanes=i,t;case G:return t=Se(13,l,e,n),t.elementType=G,t.lanes=i,t;case F:return t=Se(19,l,e,n),t.elementType=F,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case K:case Q:o=10;break t;case nt:o=9;break t;case k:o=11;break t;case J:o=14;break t;case bt:o=16,a=null;break t}o=29,l=Error(r(130,t===null?"null":typeof t,"")),a=null}return e=Se(o,l,e,n),e.elementType=t,e.type=a,e.lanes=i,e}function $l(t,e,l,a){return t=Se(7,t,a,e),t.lanes=l,t}function Bc(t,e,l){return t=Se(6,t,null,e),t.lanes=l,t}function Hc(t,e,l){return e=Se(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oa=[],_a=0,ju=null,qu=0,Ne=[],Be=0,Jl=null,ll=1,al="";function Wl(t,e){Oa[_a++]=qu,Oa[_a++]=ju,ju=t,qu=e}function $o(t,e,l){Ne[Be++]=ll,Ne[Be++]=al,Ne[Be++]=Jl,Jl=t;var a=ll;t=al;var n=32-pe(a)-1;a&=~(1<<n),l+=1;var i=32-pe(e)+n;if(30<i){var o=n-n%5;i=(a&(1<<o)-1).toString(32),a>>=o,n-=o,ll=1<<32-pe(e)+n|l<<n|a,al=i+t}else ll=1<<i|l<<n|a,al=t}function jc(t){t.return!==null&&(Wl(t,1),$o(t,1,0))}function qc(t){for(;t===ju;)ju=Oa[--_a],Oa[_a]=null,qu=Oa[--_a],Oa[_a]=null;for(;t===Jl;)Jl=Ne[--Be],Ne[Be]=null,al=Ne[--Be],Ne[Be]=null,ll=Ne[--Be],Ne[Be]=null}var fe=null,Gt=null,gt=!1,Fl=null,Ve=!1,Yc=Error(r(519));function Pl(t){var e=Error(r(418,""));throw Tn(De(e,t)),Yc}function Jo(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[ue]=t,e[oe]=a,l){case"dialog":st("cancel",e),st("close",e);break;case"iframe":case"object":case"embed":st("load",e);break;case"video":case"audio":for(l=0;l<Zn.length;l++)st(Zn[l],e);break;case"source":st("error",e);break;case"img":case"image":case"link":st("error",e),st("load",e);break;case"details":st("toggle",e);break;case"input":st("invalid",e),fo(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Eu(e);break;case"select":st("invalid",e);break;case"textarea":st("invalid",e),so(e,a.value,a.defaultValue,a.children),Eu(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||d0(e.textContent,l)?(a.popover!=null&&(st("beforetoggle",e),st("toggle",e)),a.onScroll!=null&&st("scroll",e),a.onScrollEnd!=null&&st("scrollend",e),a.onClick!=null&&(e.onclick=vi),e=!0):e=!1,e||Pl(t)}function Wo(t){for(fe=t.return;fe;)switch(fe.tag){case 5:case 13:Ve=!1;return;case 27:case 3:Ve=!0;return;default:fe=fe.return}}function bn(t){if(t!==fe)return!1;if(!gt)return Wo(t),gt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||tf(t.type,t.memoizedProps)),l=!l),l&&Gt&&Pl(t),Wo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Gt=Qe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Gt=null}}else e===27?(e=Gt,jl(t.type)?(t=nf,nf=null,Gt=t):Gt=e):Gt=fe?Qe(t.stateNode.nextSibling):null;return!0}function Sn(){Gt=fe=null,gt=!1}function Fo(){var t=Fl;return t!==null&&(me===null?me=t:me.push.apply(me,t),Fl=null),t}function Tn(t){Fl===null?Fl=[t]:Fl.push(t)}var Gc=j(null),Il=null,nl=null;function Tl(t,e,l){L(Gc,e._currentValue),e._currentValue=l}function ul(t){t._currentValue=Gc.current,Z(Gc)}function wc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Xc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var o=n.child;i=i.firstContext;t:for(;i!==null;){var d=i;i=n;for(var y=0;y<e.length;y++)if(d.context===e[y]){i.lanes|=l,d=i.alternate,d!==null&&(d.lanes|=l),wc(i.return,l,t),a||(o=null);break t}i=d.next}}else if(n.tag===18){if(o=n.return,o===null)throw Error(r(341));o.lanes|=l,i=o.alternate,i!==null&&(i.lanes|=l),wc(o,l,t),o=null}else o=n.child;if(o!==null)o.return=n;else for(o=n;o!==null;){if(o===t){o=null;break}if(n=o.sibling,n!==null){n.return=o.return,o=n;break}o=o.return}n=o}}function An(t,e,l,a){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var o=n.alternate;if(o===null)throw Error(r(387));if(o=o.memoizedProps,o!==null){var d=n.type;be(n.pendingProps.value,o.value)||(t!==null?t.push(d):t=[d])}}else if(n===ge.current){if(o=n.alternate,o===null)throw Error(r(387));o.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Fn):t=[Fn])}n=n.return}t!==null&&Xc(e,t,l,a),e.flags|=262144}function Yu(t){for(t=t.firstContext;t!==null;){if(!be(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ta(t){Il=t,nl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ie(t){return Po(Il,t)}function Gu(t,e){return Il===null&&ta(t),Po(t,e)}function Po(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},nl===null){if(t===null)throw Error(r(308));nl=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else nl=nl.next=e;return l}var ny=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},uy=u.unstable_scheduleCallback,iy=u.unstable_NormalPriority,Jt={$$typeof:Q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Qc(){return{controller:new ny,data:new Map,refCount:0}}function En(t){t.refCount--,t.refCount===0&&uy(iy,function(){t.controller.abort()})}var On=null,Lc=0,Ca=0,xa=null;function cy(t,e){if(On===null){var l=On=[];Lc=0,Ca=Zr(),xa={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Lc++,e.then(Io,Io),e}function Io(){if(--Lc===0&&On!==null){xa!==null&&(xa.status="fulfilled");var t=On;On=null,Ca=0,xa=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function ry(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var ts=M.S;M.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&cy(t,e),ts!==null&&ts(t,e)};var ea=j(null);function Vc(){var t=ea.current;return t!==null?t:Ut.pooledCache}function wu(t,e){e===null?L(ea,ea.current):L(ea,e.pool)}function es(){var t=Vc();return t===null?null:{parent:Jt._currentValue,pool:t}}var _n=Error(r(460)),ls=Error(r(474)),Xu=Error(r(542)),Zc={then:function(){}};function as(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Qu(){}function ns(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(Qu,Qu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,is(t),t;default:if(typeof e.status=="string")e.then(Qu,Qu);else{if(t=Ut,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,is(t),t}throw Cn=e,_n}}var Cn=null;function us(){if(Cn===null)throw Error(r(459));var t=Cn;return Cn=null,t}function is(t){if(t===_n||t===Xu)throw Error(r(483))}var Al=!1;function Kc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function kc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function El(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Ol(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(St&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=Bu(t),Ko(t,null,l),e}return Nu(t,a,e,l),Bu(t)}function xn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Pf(t,l)}}function $c(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var o={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=o:i=i.next=o,l=l.next}while(l!==null);i===null?n=i=e:i=i.next=e}else n=i=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var Jc=!1;function zn(){if(Jc){var t=xa;if(t!==null)throw t}}function Rn(t,e,l,a){Jc=!1;var n=t.updateQueue;Al=!1;var i=n.firstBaseUpdate,o=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var y=d,O=y.next;y.next=null,o===null?i=O:o.next=O,o=y;var U=t.alternate;U!==null&&(U=U.updateQueue,d=U.lastBaseUpdate,d!==o&&(d===null?U.firstBaseUpdate=O:d.next=O,U.lastBaseUpdate=y))}if(i!==null){var H=n.baseState;o=0,U=O=y=null,d=i;do{var C=d.lane&-536870913,x=C!==d.lane;if(x?(ht&C)===C:(a&C)===C){C!==0&&C===Ca&&(Jc=!0),U!==null&&(U=U.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var at=t,et=d;C=e;var _t=l;switch(et.tag){case 1:if(at=et.payload,typeof at=="function"){H=at.call(_t,H,C);break t}H=at;break t;case 3:at.flags=at.flags&-65537|128;case 0:if(at=et.payload,C=typeof at=="function"?at.call(_t,H,C):at,C==null)break t;H=z({},H,C);break t;case 2:Al=!0}}C=d.callback,C!==null&&(t.flags|=64,x&&(t.flags|=8192),x=n.callbacks,x===null?n.callbacks=[C]:x.push(C))}else x={lane:C,tag:d.tag,payload:d.payload,callback:d.callback,next:null},U===null?(O=U=x,y=H):U=U.next=x,o|=C;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;x=d,d=x.next,x.next=null,n.lastBaseUpdate=x,n.shared.pending=null}}while(!0);U===null&&(y=H),n.baseState=y,n.firstBaseUpdate=O,n.lastBaseUpdate=U,i===null&&(n.shared.lanes=0),Ul|=o,t.lanes=o,t.memoizedState=H}}function cs(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function rs(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)cs(l[t],e)}var za=j(null),Lu=j(0);function fs(t,e){t=dl,L(Lu,t),L(za,e),dl=t|e.baseLanes}function Wc(){L(Lu,dl),L(za,za.current)}function Fc(){dl=Lu.current,Z(za),Z(Lu)}var _l=0,rt=null,Et=null,Kt=null,Vu=!1,Ra=!1,la=!1,Zu=0,Mn=0,Ma=null,fy=0;function Lt(){throw Error(r(321))}function Pc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!be(t[l],e[l]))return!1;return!0}function Ic(t,e,l,a,n,i){return _l=i,rt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,M.H=t===null||t.memoizedState===null?Ks:ks,la=!1,i=l(a,n),la=!1,Ra&&(i=ss(e,l,a,n)),os(t),i}function os(t){M.H=Fu;var e=Et!==null&&Et.next!==null;if(_l=0,Kt=Et=rt=null,Vu=!1,Mn=0,Ma=null,e)throw Error(r(300));t===null||Pt||(t=t.dependencies,t!==null&&Yu(t)&&(Pt=!0))}function ss(t,e,l,a){rt=t;var n=0;do{if(Ra&&(Ma=null),Mn=0,Ra=!1,25<=n)throw Error(r(301));if(n+=1,Kt=Et=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}M.H=gy,i=e(l,a)}while(Ra);return i}function oy(){var t=M.H,e=t.useState()[0];return e=typeof e.then=="function"?Dn(e):e,t=t.useState()[0],(Et!==null?Et.memoizedState:null)!==t&&(rt.flags|=1024),e}function tr(){var t=Zu!==0;return Zu=0,t}function er(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function lr(t){if(Vu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Vu=!1}_l=0,Kt=Et=rt=null,Ra=!1,Mn=Zu=0,Ma=null}function de(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Kt===null?rt.memoizedState=Kt=t:Kt=Kt.next=t,Kt}function kt(){if(Et===null){var t=rt.alternate;t=t!==null?t.memoizedState:null}else t=Et.next;var e=Kt===null?rt.memoizedState:Kt.next;if(e!==null)Kt=e,Et=t;else{if(t===null)throw rt.alternate===null?Error(r(467)):Error(r(310));Et=t,t={memoizedState:Et.memoizedState,baseState:Et.baseState,baseQueue:Et.baseQueue,queue:Et.queue,next:null},Kt===null?rt.memoizedState=Kt=t:Kt=Kt.next=t}return Kt}function ar(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Dn(t){var e=Mn;return Mn+=1,Ma===null&&(Ma=[]),t=ns(Ma,t,e),e=rt,(Kt===null?e.memoizedState:Kt.next)===null&&(e=e.alternate,M.H=e===null||e.memoizedState===null?Ks:ks),t}function Ku(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Dn(t);if(t.$$typeof===Q)return ie(t)}throw Error(r(438,String(t)))}function nr(t){var e=null,l=rt.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=rt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=ar(),rt.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=m;return e.index++,l}function il(t,e){return typeof e=="function"?e(t):e}function ku(t){var e=kt();return ur(e,Et,t)}function ur(t,e,l){var a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=l;var n=t.baseQueue,i=a.pending;if(i!==null){if(n!==null){var o=n.next;n.next=i.next,i.next=o}e.baseQueue=n=i,a.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var d=o=null,y=null,O=e,U=!1;do{var H=O.lane&-536870913;if(H!==O.lane?(ht&H)===H:(_l&H)===H){var C=O.revertLane;if(C===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),H===Ca&&(U=!0);else if((_l&C)===C){O=O.next,C===Ca&&(U=!0);continue}else H={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=H,o=i):y=y.next=H,rt.lanes|=C,Ul|=C;H=O.action,la&&l(i,H),i=O.hasEagerState?O.eagerState:l(i,H)}else C={lane:H,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=C,o=i):y=y.next=C,rt.lanes|=H,Ul|=H;O=O.next}while(O!==null&&O!==e);if(y===null?o=i:y.next=d,!be(i,t.memoizedState)&&(Pt=!0,U&&(l=xa,l!==null)))throw l;t.memoizedState=i,t.baseState=o,t.baseQueue=y,a.lastRenderedState=i}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function ir(t){var e=kt(),l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,i=e.memoizedState;if(n!==null){l.pending=null;var o=n=n.next;do i=t(i,o.action),o=o.next;while(o!==n);be(i,e.memoizedState)||(Pt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,a]}function ds(t,e,l){var a=rt,n=kt(),i=gt;if(i){if(l===void 0)throw Error(r(407));l=l()}else l=e();var o=!be((Et||n).memoizedState,l);o&&(n.memoizedState=l,Pt=!0),n=n.queue;var d=ys.bind(null,a,n,t);if(Un(2048,8,d,[t]),n.getSnapshot!==e||o||Kt!==null&&Kt.memoizedState.tag&1){if(a.flags|=2048,Da(9,$u(),ms.bind(null,a,n,l,e),null),Ut===null)throw Error(r(349));i||(_l&124)!==0||hs(a,e,l)}return l}function hs(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=rt.updateQueue,e===null?(e=ar(),rt.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function ms(t,e,l,a){e.value=l,e.getSnapshot=a,gs(e)&&vs(t)}function ys(t,e,l){return l(function(){gs(e)&&vs(t)})}function gs(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!be(t,l)}catch{return!0}}function vs(t){var e=Aa(t,2);e!==null&&_e(e,t,2)}function cr(t){var e=de();if(typeof t=="function"){var l=t;if(t=l(),la){pl(!0);try{l()}finally{pl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:t},e}function ps(t,e,l,a){return t.baseState=l,ur(t,Et,typeof a=="function"?a:il)}function sy(t,e,l,a,n){if(Wu(t))throw Error(r(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){i.listeners.push(o)}};M.T!==null?l(!0):i.isTransition=!1,a(i),l=e.pending,l===null?(i.next=e.pending=i,bs(e,i)):(i.next=l.next,e.pending=l.next=i)}}function bs(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var i=M.T,o={};M.T=o;try{var d=l(n,a),y=M.S;y!==null&&y(o,d),Ss(t,e,d)}catch(O){rr(t,e,O)}finally{M.T=i}}else try{i=l(n,a),Ss(t,e,i)}catch(O){rr(t,e,O)}}function Ss(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Ts(t,e,a)},function(a){return rr(t,e,a)}):Ts(t,e,l)}function Ts(t,e,l){e.status="fulfilled",e.value=l,As(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,bs(t,l)))}function rr(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,As(e),e=e.next;while(e!==a)}t.action=null}function As(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Es(t,e){return e}function Os(t,e){if(gt){var l=Ut.formState;if(l!==null){t:{var a=rt;if(gt){if(Gt){e:{for(var n=Gt,i=Ve;n.nodeType!==8;){if(!i){n=null;break e}if(n=Qe(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Gt=Qe(n.nextSibling),a=n.data==="F!";break t}}Pl(a)}a=!1}a&&(e=l[0])}}return l=de(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Es,lastRenderedState:e},l.queue=a,l=Ls.bind(null,rt,a),a.dispatch=l,a=cr(!1),i=hr.bind(null,rt,!1,a.queue),a=de(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=sy.bind(null,rt,n,i,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function _s(t){var e=kt();return Cs(e,Et,t)}function Cs(t,e,l){if(e=ur(t,e,Es)[0],t=ku(il)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Dn(e)}catch(o){throw o===_n?Xu:o}else a=e;e=kt();var n=e.queue,i=n.dispatch;return l!==e.memoizedState&&(rt.flags|=2048,Da(9,$u(),dy.bind(null,n,l),null)),[a,i,t]}function dy(t,e){t.action=e}function xs(t){var e=kt(),l=Et;if(l!==null)return Cs(e,l,t);kt(),e=e.memoizedState,l=kt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function Da(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=rt.updateQueue,e===null&&(e=ar(),rt.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function $u(){return{destroy:void 0,resource:void 0}}function zs(){return kt().memoizedState}function Ju(t,e,l,a){var n=de();a=a===void 0?null:a,rt.flags|=t,n.memoizedState=Da(1|e,$u(),l,a)}function Un(t,e,l,a){var n=kt();a=a===void 0?null:a;var i=n.memoizedState.inst;Et!==null&&a!==null&&Pc(a,Et.memoizedState.deps)?n.memoizedState=Da(e,i,l,a):(rt.flags|=t,n.memoizedState=Da(1|e,i,l,a))}function Rs(t,e){Ju(8390656,8,t,e)}function Ms(t,e){Un(2048,8,t,e)}function Ds(t,e){return Un(4,2,t,e)}function Us(t,e){return Un(4,4,t,e)}function Ns(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Bs(t,e,l){l=l!=null?l.concat([t]):null,Un(4,4,Ns.bind(null,e,t),l)}function fr(){}function Hs(t,e){var l=kt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&Pc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function js(t,e){var l=kt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&Pc(e,a[1]))return a[0];if(a=t(),la){pl(!0);try{t()}finally{pl(!1)}}return l.memoizedState=[a,e],a}function or(t,e,l){return l===void 0||(_l&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Gd(),rt.lanes|=t,Ul|=t,l)}function qs(t,e,l,a){return be(l,e)?l:za.current!==null?(t=or(t,l,a),be(t,e)||(Pt=!0),t):(_l&42)===0?(Pt=!0,t.memoizedState=l):(t=Gd(),rt.lanes|=t,Ul|=t,e)}function Ys(t,e,l,a,n){var i=w.p;w.p=i!==0&&8>i?i:8;var o=M.T,d={};M.T=d,hr(t,!1,e,l);try{var y=n(),O=M.S;if(O!==null&&O(d,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var U=ry(y,a);Nn(t,e,U,Oe(t))}else Nn(t,e,a,Oe(t))}catch(H){Nn(t,e,{then:function(){},status:"rejected",reason:H},Oe())}finally{w.p=i,M.T=o}}function hy(){}function sr(t,e,l,a){if(t.tag!==5)throw Error(r(476));var n=Gs(t).queue;Ys(t,n,e,I,l===null?hy:function(){return ws(t),l(a)})}function Gs(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:I,baseState:I,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:I},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function ws(t){var e=Gs(t).next.queue;Nn(t,e,{},Oe())}function dr(){return ie(Fn)}function Xs(){return kt().memoizedState}function Qs(){return kt().memoizedState}function my(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=Oe();t=El(l);var a=Ol(e,t,l);a!==null&&(_e(a,e,l),xn(a,e,l)),e={cache:Qc()},t.payload=e;return}e=e.return}}function yy(t,e,l){var a=Oe();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Wu(t)?Vs(e,l):(l=Uc(t,e,l,a),l!==null&&(_e(l,t,a),Zs(l,e,a)))}function Ls(t,e,l){var a=Oe();Nn(t,e,l,a)}function Nn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Wu(t))Vs(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var o=e.lastRenderedState,d=i(o,l);if(n.hasEagerState=!0,n.eagerState=d,be(d,o))return Nu(t,e,n,0),Ut===null&&Uu(),!1}catch{}finally{}if(l=Uc(t,e,n,a),l!==null)return _e(l,t,a),Zs(l,e,a),!0}return!1}function hr(t,e,l,a){if(a={lane:2,revertLane:Zr(),action:a,hasEagerState:!1,eagerState:null,next:null},Wu(t)){if(e)throw Error(r(479))}else e=Uc(t,l,a,2),e!==null&&_e(e,t,2)}function Wu(t){var e=t.alternate;return t===rt||e!==null&&e===rt}function Vs(t,e){Ra=Vu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Zs(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Pf(t,l)}}var Fu={readContext:ie,use:Ku,useCallback:Lt,useContext:Lt,useEffect:Lt,useImperativeHandle:Lt,useLayoutEffect:Lt,useInsertionEffect:Lt,useMemo:Lt,useReducer:Lt,useRef:Lt,useState:Lt,useDebugValue:Lt,useDeferredValue:Lt,useTransition:Lt,useSyncExternalStore:Lt,useId:Lt,useHostTransitionStatus:Lt,useFormState:Lt,useActionState:Lt,useOptimistic:Lt,useMemoCache:Lt,useCacheRefresh:Lt},Ks={readContext:ie,use:Ku,useCallback:function(t,e){return de().memoizedState=[t,e===void 0?null:e],t},useContext:ie,useEffect:Rs,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Ju(4194308,4,Ns.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Ju(4194308,4,t,e)},useInsertionEffect:function(t,e){Ju(4,2,t,e)},useMemo:function(t,e){var l=de();e=e===void 0?null:e;var a=t();if(la){pl(!0);try{t()}finally{pl(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=de();if(l!==void 0){var n=l(e);if(la){pl(!0);try{l(e)}finally{pl(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=yy.bind(null,rt,t),[a.memoizedState,t]},useRef:function(t){var e=de();return t={current:t},e.memoizedState=t},useState:function(t){t=cr(t);var e=t.queue,l=Ls.bind(null,rt,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:fr,useDeferredValue:function(t,e){var l=de();return or(l,t,e)},useTransition:function(){var t=cr(!1);return t=Ys.bind(null,rt,t.queue,!0,!1),de().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=rt,n=de();if(gt){if(l===void 0)throw Error(r(407));l=l()}else{if(l=e(),Ut===null)throw Error(r(349));(ht&124)!==0||hs(a,e,l)}n.memoizedState=l;var i={value:l,getSnapshot:e};return n.queue=i,Rs(ys.bind(null,a,i,t),[t]),a.flags|=2048,Da(9,$u(),ms.bind(null,a,i,l,e),null),l},useId:function(){var t=de(),e=Ut.identifierPrefix;if(gt){var l=al,a=ll;l=(a&~(1<<32-pe(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=Zu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=fy++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:dr,useFormState:Os,useActionState:Os,useOptimistic:function(t){var e=de();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=hr.bind(null,rt,!0,l),l.dispatch=e,[t,e]},useMemoCache:nr,useCacheRefresh:function(){return de().memoizedState=my.bind(null,rt)}},ks={readContext:ie,use:Ku,useCallback:Hs,useContext:ie,useEffect:Ms,useImperativeHandle:Bs,useInsertionEffect:Ds,useLayoutEffect:Us,useMemo:js,useReducer:ku,useRef:zs,useState:function(){return ku(il)},useDebugValue:fr,useDeferredValue:function(t,e){var l=kt();return qs(l,Et.memoizedState,t,e)},useTransition:function(){var t=ku(il)[0],e=kt().memoizedState;return[typeof t=="boolean"?t:Dn(t),e]},useSyncExternalStore:ds,useId:Xs,useHostTransitionStatus:dr,useFormState:_s,useActionState:_s,useOptimistic:function(t,e){var l=kt();return ps(l,Et,t,e)},useMemoCache:nr,useCacheRefresh:Qs},gy={readContext:ie,use:Ku,useCallback:Hs,useContext:ie,useEffect:Ms,useImperativeHandle:Bs,useInsertionEffect:Ds,useLayoutEffect:Us,useMemo:js,useReducer:ir,useRef:zs,useState:function(){return ir(il)},useDebugValue:fr,useDeferredValue:function(t,e){var l=kt();return Et===null?or(l,t,e):qs(l,Et.memoizedState,t,e)},useTransition:function(){var t=ir(il)[0],e=kt().memoizedState;return[typeof t=="boolean"?t:Dn(t),e]},useSyncExternalStore:ds,useId:Xs,useHostTransitionStatus:dr,useFormState:xs,useActionState:xs,useOptimistic:function(t,e){var l=kt();return Et!==null?ps(l,Et,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:nr,useCacheRefresh:Qs},Ua=null,Bn=0;function Pu(t){var e=Bn;return Bn+=1,Ua===null&&(Ua=[]),ns(Ua,t,e)}function Hn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Iu(t,e){throw e.$$typeof===R?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function $s(t){var e=t._init;return e(t._payload)}function Js(t){function e(T,b){if(t){var E=T.deletions;E===null?(T.deletions=[b],T.flags|=16):E.push(b)}}function l(T,b){if(!t)return null;for(;b!==null;)e(T,b),b=b.sibling;return null}function a(T){for(var b=new Map;T!==null;)T.key!==null?b.set(T.key,T):b.set(T.index,T),T=T.sibling;return b}function n(T,b){return T=el(T,b),T.index=0,T.sibling=null,T}function i(T,b,E){return T.index=E,t?(E=T.alternate,E!==null?(E=E.index,E<b?(T.flags|=67108866,b):E):(T.flags|=67108866,b)):(T.flags|=1048576,b)}function o(T){return t&&T.alternate===null&&(T.flags|=67108866),T}function d(T,b,E,B){return b===null||b.tag!==6?(b=Bc(E,T.mode,B),b.return=T,b):(b=n(b,E),b.return=T,b)}function y(T,b,E,B){var $=E.type;return $===q?U(T,b,E.props.children,B,E.key):b!==null&&(b.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===bt&&$s($)===b.type)?(b=n(b,E.props),Hn(b,E),b.return=T,b):(b=Hu(E.type,E.key,E.props,null,T.mode,B),Hn(b,E),b.return=T,b)}function O(T,b,E,B){return b===null||b.tag!==4||b.stateNode.containerInfo!==E.containerInfo||b.stateNode.implementation!==E.implementation?(b=Hc(E,T.mode,B),b.return=T,b):(b=n(b,E.children||[]),b.return=T,b)}function U(T,b,E,B,$){return b===null||b.tag!==7?(b=$l(E,T.mode,B,$),b.return=T,b):(b=n(b,E),b.return=T,b)}function H(T,b,E){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Bc(""+b,T.mode,E),b.return=T,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case N:return E=Hu(b.type,b.key,b.props,null,T.mode,E),Hn(E,b),E.return=T,E;case Y:return b=Hc(b,T.mode,E),b.return=T,b;case bt:var B=b._init;return b=B(b._payload),H(T,b,E)}if(xt(b)||P(b))return b=$l(b,T.mode,E,null),b.return=T,b;if(typeof b.then=="function")return H(T,Pu(b),E);if(b.$$typeof===Q)return H(T,Gu(T,b),E);Iu(T,b)}return null}function C(T,b,E,B){var $=b!==null?b.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return $!==null?null:d(T,b,""+E,B);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case N:return E.key===$?y(T,b,E,B):null;case Y:return E.key===$?O(T,b,E,B):null;case bt:return $=E._init,E=$(E._payload),C(T,b,E,B)}if(xt(E)||P(E))return $!==null?null:U(T,b,E,B,null);if(typeof E.then=="function")return C(T,b,Pu(E),B);if(E.$$typeof===Q)return C(T,b,Gu(T,E),B);Iu(T,E)}return null}function x(T,b,E,B,$){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return T=T.get(E)||null,d(b,T,""+B,$);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case N:return T=T.get(B.key===null?E:B.key)||null,y(b,T,B,$);case Y:return T=T.get(B.key===null?E:B.key)||null,O(b,T,B,$);case bt:var ft=B._init;return B=ft(B._payload),x(T,b,E,B,$)}if(xt(B)||P(B))return T=T.get(E)||null,U(b,T,B,$,null);if(typeof B.then=="function")return x(T,b,E,Pu(B),$);if(B.$$typeof===Q)return x(T,b,E,Gu(b,B),$);Iu(b,B)}return null}function at(T,b,E,B){for(var $=null,ft=null,W=b,lt=b=0,te=null;W!==null&&lt<E.length;lt++){W.index>lt?(te=W,W=null):te=W.sibling;var yt=C(T,W,E[lt],B);if(yt===null){W===null&&(W=te);break}t&&W&&yt.alternate===null&&e(T,W),b=i(yt,b,lt),ft===null?$=yt:ft.sibling=yt,ft=yt,W=te}if(lt===E.length)return l(T,W),gt&&Wl(T,lt),$;if(W===null){for(;lt<E.length;lt++)W=H(T,E[lt],B),W!==null&&(b=i(W,b,lt),ft===null?$=W:ft.sibling=W,ft=W);return gt&&Wl(T,lt),$}for(W=a(W);lt<E.length;lt++)te=x(W,T,lt,E[lt],B),te!==null&&(t&&te.alternate!==null&&W.delete(te.key===null?lt:te.key),b=i(te,b,lt),ft===null?$=te:ft.sibling=te,ft=te);return t&&W.forEach(function(Xl){return e(T,Xl)}),gt&&Wl(T,lt),$}function et(T,b,E,B){if(E==null)throw Error(r(151));for(var $=null,ft=null,W=b,lt=b=0,te=null,yt=E.next();W!==null&&!yt.done;lt++,yt=E.next()){W.index>lt?(te=W,W=null):te=W.sibling;var Xl=C(T,W,yt.value,B);if(Xl===null){W===null&&(W=te);break}t&&W&&Xl.alternate===null&&e(T,W),b=i(Xl,b,lt),ft===null?$=Xl:ft.sibling=Xl,ft=Xl,W=te}if(yt.done)return l(T,W),gt&&Wl(T,lt),$;if(W===null){for(;!yt.done;lt++,yt=E.next())yt=H(T,yt.value,B),yt!==null&&(b=i(yt,b,lt),ft===null?$=yt:ft.sibling=yt,ft=yt);return gt&&Wl(T,lt),$}for(W=a(W);!yt.done;lt++,yt=E.next())yt=x(W,T,lt,yt.value,B),yt!==null&&(t&&yt.alternate!==null&&W.delete(yt.key===null?lt:yt.key),b=i(yt,b,lt),ft===null?$=yt:ft.sibling=yt,ft=yt);return t&&W.forEach(function(vg){return e(T,vg)}),gt&&Wl(T,lt),$}function _t(T,b,E,B){if(typeof E=="object"&&E!==null&&E.type===q&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case N:t:{for(var $=E.key;b!==null;){if(b.key===$){if($=E.type,$===q){if(b.tag===7){l(T,b.sibling),B=n(b,E.props.children),B.return=T,T=B;break t}}else if(b.elementType===$||typeof $=="object"&&$!==null&&$.$$typeof===bt&&$s($)===b.type){l(T,b.sibling),B=n(b,E.props),Hn(B,E),B.return=T,T=B;break t}l(T,b);break}else e(T,b);b=b.sibling}E.type===q?(B=$l(E.props.children,T.mode,B,E.key),B.return=T,T=B):(B=Hu(E.type,E.key,E.props,null,T.mode,B),Hn(B,E),B.return=T,T=B)}return o(T);case Y:t:{for($=E.key;b!==null;){if(b.key===$)if(b.tag===4&&b.stateNode.containerInfo===E.containerInfo&&b.stateNode.implementation===E.implementation){l(T,b.sibling),B=n(b,E.children||[]),B.return=T,T=B;break t}else{l(T,b);break}else e(T,b);b=b.sibling}B=Hc(E,T.mode,B),B.return=T,T=B}return o(T);case bt:return $=E._init,E=$(E._payload),_t(T,b,E,B)}if(xt(E))return at(T,b,E,B);if(P(E)){if($=P(E),typeof $!="function")throw Error(r(150));return E=$.call(E),et(T,b,E,B)}if(typeof E.then=="function")return _t(T,b,Pu(E),B);if(E.$$typeof===Q)return _t(T,b,Gu(T,E),B);Iu(T,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,b!==null&&b.tag===6?(l(T,b.sibling),B=n(b,E),B.return=T,T=B):(l(T,b),B=Bc(E,T.mode,B),B.return=T,T=B),o(T)):l(T,b)}return function(T,b,E,B){try{Bn=0;var $=_t(T,b,E,B);return Ua=null,$}catch(W){if(W===_n||W===Xu)throw W;var ft=Se(29,W,null,T.mode);return ft.lanes=B,ft.return=T,ft}finally{}}}var Na=Js(!0),Ws=Js(!1),He=j(null),Ze=null;function Cl(t){var e=t.alternate;L(Wt,Wt.current&1),L(He,t),Ze===null&&(e===null||za.current!==null||e.memoizedState!==null)&&(Ze=t)}function Fs(t){if(t.tag===22){if(L(Wt,Wt.current),L(He,t),Ze===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ze=t)}}else xl()}function xl(){L(Wt,Wt.current),L(He,He.current)}function cl(t){Z(He),Ze===t&&(Ze=null),Z(Wt)}var Wt=j(0);function ti(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||af(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function mr(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var yr={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=Oe(),n=El(a);n.payload=e,l!=null&&(n.callback=l),e=Ol(t,n,a),e!==null&&(_e(e,t,a),xn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=Oe(),n=El(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=Ol(t,n,a),e!==null&&(_e(e,t,a),xn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=Oe(),a=El(l);a.tag=2,e!=null&&(a.callback=e),e=Ol(t,a,l),e!==null&&(_e(e,t,l),xn(e,t,l))}};function Ps(t,e,l,a,n,i,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,i,o):e.prototype&&e.prototype.isPureReactComponent?!vn(l,a)||!vn(n,i):!0}function Is(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&yr.enqueueReplaceState(e,e.state,null)}function aa(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var ei=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function td(t){ei(t)}function ed(t){console.error(t)}function ld(t){ei(t)}function li(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ad(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function gr(t,e,l){return l=El(l),l.tag=3,l.payload={element:null},l.callback=function(){li(t,e)},l}function nd(t){return t=El(t),t.tag=3,t}function ud(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;t.payload=function(){return n(i)},t.callback=function(){ad(e,l,a)}}var o=l.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){ad(e,l,a),typeof n!="function"&&(Nl===null?Nl=new Set([this]):Nl.add(this));var d=a.stack;this.componentDidCatch(a.value,{componentStack:d!==null?d:""})})}function vy(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&An(e,l,n,!0),l=He.current,l!==null){switch(l.tag){case 13:return Ze===null?wr():l.alternate===null&&wt===0&&(wt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Zc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Qr(t,a,n)),!1;case 22:return l.flags|=65536,a===Zc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Qr(t,a,n)),!1}throw Error(r(435,l.tag))}return Qr(t,a,n),wr(),!1}if(gt)return e=He.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Yc&&(t=Error(r(422),{cause:a}),Tn(De(t,l)))):(a!==Yc&&(e=Error(r(423),{cause:a}),Tn(De(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=De(a,l),n=gr(t.stateNode,a,n),$c(t,n),wt!==4&&(wt=2)),!1;var i=Error(r(520),{cause:a});if(i=De(i,l),Qn===null?Qn=[i]:Qn.push(i),wt!==4&&(wt=2),e===null)return!0;a=De(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=gr(l.stateNode,a,t),$c(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Nl===null||!Nl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=nd(n),ud(n,t,l,a),$c(l,n),!1}l=l.return}while(l!==null);return!1}var id=Error(r(461)),Pt=!1;function ee(t,e,l,a){e.child=t===null?Ws(e,null,l,a):Na(e,t.child,l,a)}function cd(t,e,l,a,n){l=l.render;var i=e.ref;if("ref"in a){var o={};for(var d in a)d!=="ref"&&(o[d]=a[d])}else o=a;return ta(e),a=Ic(t,e,l,o,i,n),d=tr(),t!==null&&!Pt?(er(t,e,n),rl(t,e,n)):(gt&&d&&jc(e),e.flags|=1,ee(t,e,a,n),e.child)}function rd(t,e,l,a,n){if(t===null){var i=l.type;return typeof i=="function"&&!Nc(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,fd(t,e,i,a,n)):(t=Hu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Or(t,n)){var o=i.memoizedProps;if(l=l.compare,l=l!==null?l:vn,l(o,a)&&t.ref===e.ref)return rl(t,e,n)}return e.flags|=1,t=el(i,a),t.ref=e.ref,t.return=e,e.child=t}function fd(t,e,l,a,n){if(t!==null){var i=t.memoizedProps;if(vn(i,a)&&t.ref===e.ref)if(Pt=!1,e.pendingProps=a=i,Or(t,n))(t.flags&131072)!==0&&(Pt=!0);else return e.lanes=t.lanes,rl(t,e,n)}return vr(t,e,l,a,n)}function od(t,e,l){var a=e.pendingProps,n=a.children,i=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~a}else e.childLanes=0,e.child=null;return sd(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&wu(e,i!==null?i.cachePool:null),i!==null?fs(e,i):Wc(),Fs(e);else return e.lanes=e.childLanes=536870912,sd(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(wu(e,i.cachePool),fs(e,i),xl(),e.memoizedState=null):(t!==null&&wu(e,null),Wc(),xl());return ee(t,e,n,l),e.child}function sd(t,e,l,a){var n=Vc();return n=n===null?null:{parent:Jt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&wu(e,null),Wc(),Fs(e),t!==null&&An(t,e,a,!0),null}function ai(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(r(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function vr(t,e,l,a,n){return ta(e),l=Ic(t,e,l,a,void 0,n),a=tr(),t!==null&&!Pt?(er(t,e,n),rl(t,e,n)):(gt&&a&&jc(e),e.flags|=1,ee(t,e,l,n),e.child)}function dd(t,e,l,a,n,i){return ta(e),e.updateQueue=null,l=ss(e,a,l,n),os(t),a=tr(),t!==null&&!Pt?(er(t,e,i),rl(t,e,i)):(gt&&a&&jc(e),e.flags|=1,ee(t,e,l,i),e.child)}function hd(t,e,l,a,n){if(ta(e),e.stateNode===null){var i=Ea,o=l.contextType;typeof o=="object"&&o!==null&&(i=ie(o)),i=new l(a,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=yr,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=a,i.state=e.memoizedState,i.refs={},Kc(e),o=l.contextType,i.context=typeof o=="object"&&o!==null?ie(o):Ea,i.state=e.memoizedState,o=l.getDerivedStateFromProps,typeof o=="function"&&(mr(e,l,o,a),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(o=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),o!==i.state&&yr.enqueueReplaceState(i,i.state,null),Rn(e,a,i,n),zn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){i=e.stateNode;var d=e.memoizedProps,y=aa(l,d);i.props=y;var O=i.context,U=l.contextType;o=Ea,typeof U=="object"&&U!==null&&(o=ie(U));var H=l.getDerivedStateFromProps;U=typeof H=="function"||typeof i.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,U||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(d||O!==o)&&Is(e,i,a,o),Al=!1;var C=e.memoizedState;i.state=C,Rn(e,a,i,n),zn(),O=e.memoizedState,d||C!==O||Al?(typeof H=="function"&&(mr(e,l,H,a),O=e.memoizedState),(y=Al||Ps(e,l,y,a,C,O,o))?(U||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=O),i.props=a,i.state=O,i.context=o,a=y):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{i=e.stateNode,kc(t,e),o=e.memoizedProps,U=aa(l,o),i.props=U,H=e.pendingProps,C=i.context,O=l.contextType,y=Ea,typeof O=="object"&&O!==null&&(y=ie(O)),d=l.getDerivedStateFromProps,(O=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==H||C!==y)&&Is(e,i,a,y),Al=!1,C=e.memoizedState,i.state=C,Rn(e,a,i,n),zn();var x=e.memoizedState;o!==H||C!==x||Al||t!==null&&t.dependencies!==null&&Yu(t.dependencies)?(typeof d=="function"&&(mr(e,l,d,a),x=e.memoizedState),(U=Al||Ps(e,l,U,a,C,x,y)||t!==null&&t.dependencies!==null&&Yu(t.dependencies))?(O||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,x,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,x,y)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=x),i.props=a,i.state=x,i.context=y,a=U):(typeof i.componentDidUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),a=!1)}return i=a,ai(t,e),a=(e.flags&128)!==0,i||a?(i=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&a?(e.child=Na(e,t.child,null,n),e.child=Na(e,null,l,n)):ee(t,e,l,n),e.memoizedState=i.state,t=e.child):t=rl(t,e,n),t}function md(t,e,l,a){return Sn(),e.flags|=256,ee(t,e,l,a),e.child}var pr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function br(t){return{baseLanes:t,cachePool:es()}}function Sr(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=je),t}function yd(t,e,l){var a=e.pendingProps,n=!1,i=(e.flags&128)!==0,o;if((o=i)||(o=t!==null&&t.memoizedState===null?!1:(Wt.current&2)!==0),o&&(n=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(gt){if(n?Cl(e):xl(),gt){var d=Gt,y;if(y=d){t:{for(y=d,d=Ve;y.nodeType!==8;){if(!d){d=null;break t}if(y=Qe(y.nextSibling),y===null){d=null;break t}}d=y}d!==null?(e.memoizedState={dehydrated:d,treeContext:Jl!==null?{id:ll,overflow:al}:null,retryLane:536870912,hydrationErrors:null},y=Se(18,null,null,0),y.stateNode=d,y.return=e,e.child=y,fe=e,Gt=null,y=!0):y=!1}y||Pl(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return af(d)?e.lanes=32:e.lanes=536870912,null;cl(e)}return d=a.children,a=a.fallback,n?(xl(),n=e.mode,d=ni({mode:"hidden",children:d},n),a=$l(a,n,l,null),d.return=e,a.return=e,d.sibling=a,e.child=d,n=e.child,n.memoizedState=br(l),n.childLanes=Sr(t,o,l),e.memoizedState=pr,a):(Cl(e),Tr(e,d))}if(y=t.memoizedState,y!==null&&(d=y.dehydrated,d!==null)){if(i)e.flags&256?(Cl(e),e.flags&=-257,e=Ar(t,e,l)):e.memoizedState!==null?(xl(),e.child=t.child,e.flags|=128,e=null):(xl(),n=a.fallback,d=e.mode,a=ni({mode:"visible",children:a.children},d),n=$l(n,d,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Na(e,t.child,null,l),a=e.child,a.memoizedState=br(l),a.childLanes=Sr(t,o,l),e.memoizedState=pr,e=n);else if(Cl(e),af(d)){if(o=d.nextSibling&&d.nextSibling.dataset,o)var O=o.dgst;o=O,a=Error(r(419)),a.stack="",a.digest=o,Tn({value:a,source:null,stack:null}),e=Ar(t,e,l)}else if(Pt||An(t,e,l,!1),o=(l&t.childLanes)!==0,Pt||o){if(o=Ut,o!==null&&(a=l&-l,a=(a&42)!==0?1:nc(a),a=(a&(o.suspendedLanes|l))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,Aa(t,a),_e(o,t,a),id;d.data==="$?"||wr(),e=Ar(t,e,l)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,Gt=Qe(d.nextSibling),fe=e,gt=!0,Fl=null,Ve=!1,t!==null&&(Ne[Be++]=ll,Ne[Be++]=al,Ne[Be++]=Jl,ll=t.id,al=t.overflow,Jl=e),e=Tr(e,a.children),e.flags|=4096);return e}return n?(xl(),n=a.fallback,d=e.mode,y=t.child,O=y.sibling,a=el(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,O!==null?n=el(O,n):(n=$l(n,d,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,d=t.child.memoizedState,d===null?d=br(l):(y=d.cachePool,y!==null?(O=Jt._currentValue,y=y.parent!==O?{parent:O,pool:O}:y):y=es(),d={baseLanes:d.baseLanes|l,cachePool:y}),n.memoizedState=d,n.childLanes=Sr(t,o,l),e.memoizedState=pr,a):(Cl(e),l=t.child,t=l.sibling,l=el(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=l,e.memoizedState=null,l)}function Tr(t,e){return e=ni({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ni(t,e){return t=Se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Ar(t,e,l){return Na(e,t.child,null,l),t=Tr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function gd(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),wc(t.return,e,l)}function Er(t,e,l,a,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function vd(t,e,l){var a=e.pendingProps,n=a.revealOrder,i=a.tail;if(ee(t,e,a.children,l),a=Wt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&gd(t,l,e);else if(t.tag===19)gd(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(L(Wt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&ti(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Er(e,!1,n,l,i);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&ti(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Er(e,!0,l,null,i);break;case"together":Er(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function rl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),Ul|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(An(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,l=el(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=el(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Or(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Yu(t)))}function py(t,e,l){switch(e.tag){case 3:Bt(e,e.stateNode.containerInfo),Tl(e,Jt,t.memoizedState.cache),Sn();break;case 27:case 5:Ii(e);break;case 4:Bt(e,e.stateNode.containerInfo);break;case 10:Tl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(Cl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?yd(t,e,l):(Cl(e),t=rl(t,e,l),t!==null?t.sibling:null);Cl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(An(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return vd(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),L(Wt,Wt.current),a)break;return null;case 22:case 23:return e.lanes=0,od(t,e,l);case 24:Tl(e,Jt,t.memoizedState.cache)}return rl(t,e,l)}function pd(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Pt=!0;else{if(!Or(t,l)&&(e.flags&128)===0)return Pt=!1,py(t,e,l);Pt=(t.flags&131072)!==0}else Pt=!1,gt&&(e.flags&1048576)!==0&&$o(e,qu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Nc(a)?(t=aa(a,t),e.tag=1,e=hd(null,e,a,t,l)):(e.tag=0,e=vr(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===k){e.tag=11,e=cd(null,e,a,t,l);break t}else if(n===J){e.tag=14,e=rd(null,e,a,t,l);break t}}throw e=qt(a)||a,Error(r(306,e,""))}}return e;case 0:return vr(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=aa(a,e.pendingProps),hd(t,e,a,n,l);case 3:t:{if(Bt(e,e.stateNode.containerInfo),t===null)throw Error(r(387));a=e.pendingProps;var i=e.memoizedState;n=i.element,kc(t,e),Rn(e,a,null,l);var o=e.memoizedState;if(a=o.cache,Tl(e,Jt,a),a!==i.cache&&Xc(e,[Jt],l,!0),zn(),a=o.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=md(t,e,a,l);break t}else if(a!==n){n=De(Error(r(424)),e),Tn(n),e=md(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Gt=Qe(t.firstChild),fe=e,gt=!0,Fl=null,Ve=!0,l=Ws(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Sn(),a===n){e=rl(t,e,l);break t}ee(t,e,a,l)}e=e.child}return e;case 26:return ai(t,e),t===null?(l=A0(e.type,null,e.pendingProps,null))?e.memoizedState=l:gt||(l=e.type,t=e.pendingProps,a=pi(it.current).createElement(l),a[ue]=e,a[oe]=t,ae(a,l,t),Ft(a),e.stateNode=a):e.memoizedState=A0(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Ii(e),t===null&&gt&&(a=e.stateNode=b0(e.type,e.pendingProps,it.current),fe=e,Ve=!0,n=Gt,jl(e.type)?(nf=n,Gt=Qe(a.firstChild)):Gt=n),ee(t,e,e.pendingProps.children,l),ai(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&gt&&((n=a=Gt)&&(a=Ky(a,e.type,e.pendingProps,Ve),a!==null?(e.stateNode=a,fe=e,Gt=Qe(a.firstChild),Ve=!1,n=!0):n=!1),n||Pl(e)),Ii(e),n=e.type,i=e.pendingProps,o=t!==null?t.memoizedProps:null,a=i.children,tf(n,i)?a=null:o!==null&&tf(n,o)&&(e.flags|=32),e.memoizedState!==null&&(n=Ic(t,e,oy,null,null,l),Fn._currentValue=n),ai(t,e),ee(t,e,a,l),e.child;case 6:return t===null&&gt&&((t=l=Gt)&&(l=ky(l,e.pendingProps,Ve),l!==null?(e.stateNode=l,fe=e,Gt=null,t=!0):t=!1),t||Pl(e)),null;case 13:return yd(t,e,l);case 4:return Bt(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Na(e,null,a,l):ee(t,e,a,l),e.child;case 11:return cd(t,e,e.type,e.pendingProps,l);case 7:return ee(t,e,e.pendingProps,l),e.child;case 8:return ee(t,e,e.pendingProps.children,l),e.child;case 12:return ee(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,Tl(e,e.type,a.value),ee(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,ta(e),n=ie(n),a=a(n),e.flags|=1,ee(t,e,a,l),e.child;case 14:return rd(t,e,e.type,e.pendingProps,l);case 15:return fd(t,e,e.type,e.pendingProps,l);case 19:return vd(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=ni(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=el(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return od(t,e,l);case 24:return ta(e),a=ie(Jt),t===null?(n=Vc(),n===null&&(n=Ut,i=Qc(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),e.memoizedState={parent:a,cache:n},Kc(e),Tl(e,Jt,n)):((t.lanes&l)!==0&&(kc(t,e),Rn(e,null,null,l),zn()),n=t.memoizedState,i=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),Tl(e,Jt,a)):(a=i.cache,Tl(e,Jt,a),a!==n.cache&&Xc(e,[Jt],l,!0))),ee(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function fl(t){t.flags|=4}function bd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!x0(e)){if(e=He.current,e!==null&&((ht&4194048)===ht?Ze!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==Ze))throw Cn=Zc,ls;t.flags|=8192}}function ui(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Wf():536870912,t.lanes|=e,qa|=e)}function jn(t,e){if(!gt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Yt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function by(t,e,l){var a=e.pendingProps;switch(qc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Yt(e),null;case 1:return Yt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ul(Jt),vl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(bn(e)?fl(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Fo())),Yt(e),null;case 26:return l=e.memoizedState,t===null?(fl(e),l!==null?(Yt(e),bd(e,l)):(Yt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(fl(e),Yt(e),bd(e,l)):(Yt(e),e.flags&=-16777217):(t.memoizedProps!==a&&fl(e),Yt(e),e.flags&=-16777217),null;case 27:gu(e),l=it.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&fl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return Yt(e),null}t=tt.current,bn(e)?Jo(e):(t=b0(n,a,l),e.stateNode=t,fl(e))}return Yt(e),null;case 5:if(gu(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&fl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return Yt(e),null}if(t=tt.current,bn(e))Jo(e);else{switch(n=pi(it.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[ue]=e,t[oe]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(ae(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&fl(e)}}return Yt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&fl(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(r(166));if(t=it.current,bn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=fe,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[ue]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||d0(t.nodeValue,l)),t||Pl(e)}else t=pi(t).createTextNode(a),t[ue]=e,e.stateNode=t}return Yt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=bn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(r(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[ue]=e}else Sn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Yt(e),n=!1}else n=Fo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(cl(e),e):(cl(e),null)}if(cl(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),ui(e,e.updateQueue),Yt(e),null;case 4:return vl(),t===null&&Jr(e.stateNode.containerInfo),Yt(e),null;case 10:return ul(e.type),Yt(e),null;case 19:if(Z(Wt),n=e.memoizedState,n===null)return Yt(e),null;if(a=(e.flags&128)!==0,i=n.rendering,i===null)if(a)jn(n,!1);else{if(wt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=ti(t),i!==null){for(e.flags|=128,jn(n,!1),t=i.updateQueue,e.updateQueue=t,ui(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)ko(l,t),l=l.sibling;return L(Wt,Wt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Le()>ri&&(e.flags|=128,a=!0,jn(n,!1),e.lanes=4194304)}else{if(!a)if(t=ti(i),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ui(e,t),jn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!gt)return Yt(e),null}else 2*Le()-n.renderingStartTime>ri&&l!==536870912&&(e.flags|=128,a=!0,jn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Le(),e.sibling=null,t=Wt.current,L(Wt,a?t&1|2:t&1),e):(Yt(e),null);case 22:case 23:return cl(e),Fc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Yt(e),e.subtreeFlags&6&&(e.flags|=8192)):Yt(e),l=e.updateQueue,l!==null&&ui(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Z(ea),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),ul(Jt),Yt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function Sy(t,e){switch(qc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ul(Jt),vl(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return gu(e),null;case 13:if(cl(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));Sn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Z(Wt),null;case 4:return vl(),null;case 10:return ul(e.type),null;case 22:case 23:return cl(e),Fc(),t!==null&&Z(ea),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ul(Jt),null;case 25:return null;default:return null}}function Sd(t,e){switch(qc(e),e.tag){case 3:ul(Jt),vl();break;case 26:case 27:case 5:gu(e);break;case 4:vl();break;case 13:cl(e);break;case 19:Z(Wt);break;case 10:ul(e.type);break;case 22:case 23:cl(e),Fc(),t!==null&&Z(ea);break;case 24:ul(Jt)}}function qn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var i=l.create,o=l.inst;a=i(),o.destroy=a}l=l.next}while(l!==n)}}catch(d){Rt(e,e.return,d)}}function zl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){var o=a.inst,d=o.destroy;if(d!==void 0){o.destroy=void 0,n=e;var y=l,O=d;try{O()}catch(U){Rt(n,y,U)}}}a=a.next}while(a!==i)}}catch(U){Rt(e,e.return,U)}}function Td(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{rs(e,l)}catch(a){Rt(t,t.return,a)}}}function Ad(t,e,l){l.props=aa(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Rt(t,e,a)}}function Yn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Rt(t,e,n)}}function Ke(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Rt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Rt(t,e,n)}else l.current=null}function Ed(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Rt(t,t.return,n)}}function _r(t,e,l){try{var a=t.stateNode;Xy(a,t.type,l,e),a[oe]=e}catch(n){Rt(t,t.return,n)}}function Od(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&jl(t.type)||t.tag===4}function Cr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Od(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&jl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function xr(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=vi));else if(a!==4&&(a===27&&jl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(xr(t,e,l),t=t.sibling;t!==null;)xr(t,e,l),t=t.sibling}function ii(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&jl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(ii(t,e,l),t=t.sibling;t!==null;)ii(t,e,l),t=t.sibling}function _d(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);ae(e,a,l),e[ue]=t,e[oe]=l}catch(i){Rt(t,t.return,i)}}var ol=!1,Vt=!1,zr=!1,Cd=typeof WeakSet=="function"?WeakSet:Set,It=null;function Ty(t,e){if(t=t.containerInfo,Pr=Oi,t=qo(t),Cc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var o=0,d=-1,y=-1,O=0,U=0,H=t,C=null;e:for(;;){for(var x;H!==l||n!==0&&H.nodeType!==3||(d=o+n),H!==i||a!==0&&H.nodeType!==3||(y=o+a),H.nodeType===3&&(o+=H.nodeValue.length),(x=H.firstChild)!==null;)C=H,H=x;for(;;){if(H===t)break e;if(C===l&&++O===n&&(d=o),C===i&&++U===a&&(y=o),(x=H.nextSibling)!==null)break;H=C,C=H.parentNode}H=x}l=d===-1||y===-1?null:{start:d,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(Ir={focusedElem:t,selectionRange:l},Oi=!1,It=e;It!==null;)if(e=It,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,It=t;else for(;It!==null;){switch(e=It,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var at=aa(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(at,i),a.__reactInternalSnapshotBeforeUpdate=t}catch(et){Rt(l,l.return,et)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)lf(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":lf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,It=t;break}It=e.return}}function xd(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Rl(t,l),a&4&&qn(5,l);break;case 1:if(Rl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(o){Rt(l,l.return,o)}else{var n=aa(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){Rt(l,l.return,o)}}a&64&&Td(l),a&512&&Yn(l,l.return);break;case 3:if(Rl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{rs(t,e)}catch(o){Rt(l,l.return,o)}}break;case 27:e===null&&a&4&&_d(l);case 26:case 5:Rl(t,l),e===null&&a&4&&Ed(l),a&512&&Yn(l,l.return);break;case 12:Rl(t,l);break;case 13:Rl(t,l),a&4&&Md(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=My.bind(null,l),$y(t,l))));break;case 22:if(a=l.memoizedState!==null||ol,!a){e=e!==null&&e.memoizedState!==null||Vt,n=ol;var i=Vt;ol=a,(Vt=e)&&!i?Ml(t,l,(l.subtreeFlags&8772)!==0):Rl(t,l),ol=n,Vt=i}break;case 30:break;default:Rl(t,l)}}function zd(t){var e=t.alternate;e!==null&&(t.alternate=null,zd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&cc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ht=null,he=!1;function sl(t,e,l){for(l=l.child;l!==null;)Rd(t,e,l),l=l.sibling}function Rd(t,e,l){if(ve&&typeof ve.onCommitFiberUnmount=="function")try{ve.onCommitFiberUnmount(nn,l)}catch{}switch(l.tag){case 26:Vt||Ke(l,e),sl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Vt||Ke(l,e);var a=Ht,n=he;jl(l.type)&&(Ht=l.stateNode,he=!1),sl(t,e,l),kn(l.stateNode),Ht=a,he=n;break;case 5:Vt||Ke(l,e);case 6:if(a=Ht,n=he,Ht=null,sl(t,e,l),Ht=a,he=n,Ht!==null)if(he)try{(Ht.nodeType===9?Ht.body:Ht.nodeName==="HTML"?Ht.ownerDocument.body:Ht).removeChild(l.stateNode)}catch(i){Rt(l,e,i)}else try{Ht.removeChild(l.stateNode)}catch(i){Rt(l,e,i)}break;case 18:Ht!==null&&(he?(t=Ht,v0(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),eu(t)):v0(Ht,l.stateNode));break;case 4:a=Ht,n=he,Ht=l.stateNode.containerInfo,he=!0,sl(t,e,l),Ht=a,he=n;break;case 0:case 11:case 14:case 15:Vt||zl(2,l,e),Vt||zl(4,l,e),sl(t,e,l);break;case 1:Vt||(Ke(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Ad(l,e,a)),sl(t,e,l);break;case 21:sl(t,e,l);break;case 22:Vt=(a=Vt)||l.memoizedState!==null,sl(t,e,l),Vt=a;break;default:sl(t,e,l)}}function Md(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{eu(t)}catch(l){Rt(e,e.return,l)}}function Ay(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Cd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Cd),e;default:throw Error(r(435,t.tag))}}function Rr(t,e){var l=Ay(t);e.forEach(function(a){var n=Dy.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function Te(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=t,o=e,d=o;t:for(;d!==null;){switch(d.tag){case 27:if(jl(d.type)){Ht=d.stateNode,he=!1;break t}break;case 5:Ht=d.stateNode,he=!1;break t;case 3:case 4:Ht=d.stateNode.containerInfo,he=!0;break t}d=d.return}if(Ht===null)throw Error(r(160));Rd(i,o,n),Ht=null,he=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Dd(e,t),e=e.sibling}var Xe=null;function Dd(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Te(e,t),Ae(t),a&4&&(zl(3,t,t.return),qn(3,t),zl(5,t,t.return));break;case 1:Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),a&64&&ol&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Xe;if(Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[rn]||i[ue]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),ae(i,a,l),i[ue]=t,Ft(i),a=i;break t;case"link":var o=_0("link","href",n).get(a+(l.href||""));if(o){for(var d=0;d<o.length;d++)if(i=o[d],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){o.splice(d,1);break e}}i=n.createElement(a),ae(i,a,l),n.head.appendChild(i);break;case"meta":if(o=_0("meta","content",n).get(a+(l.content||""))){for(d=0;d<o.length;d++)if(i=o[d],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){o.splice(d,1);break e}}i=n.createElement(a),ae(i,a,l),n.head.appendChild(i);break;default:throw Error(r(468,a))}i[ue]=t,Ft(i),a=i}t.stateNode=a}else C0(n,t.type,t.stateNode);else t.stateNode=O0(n,a,t.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?C0(n,t.type,t.stateNode):O0(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&_r(t,t.memoizedProps,l.memoizedProps)}break;case 27:Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),l!==null&&a&4&&_r(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),t.flags&32){n=t.stateNode;try{ya(n,"")}catch(x){Rt(t,t.return,x)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,_r(t,n,l!==null?l.memoizedProps:n)),a&1024&&(zr=!0);break;case 6:if(Te(e,t),Ae(t),a&4){if(t.stateNode===null)throw Error(r(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(x){Rt(t,t.return,x)}}break;case 3:if(Ti=null,n=Xe,Xe=bi(e.containerInfo),Te(e,t),Xe=n,Ae(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{eu(e.containerInfo)}catch(x){Rt(t,t.return,x)}zr&&(zr=!1,Ud(t));break;case 4:a=Xe,Xe=bi(t.stateNode.containerInfo),Te(e,t),Ae(t),Xe=a;break;case 12:Te(e,t),Ae(t);break;case 13:Te(e,t),Ae(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Hr=Le()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Rr(t,a)));break;case 22:n=t.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,O=ol,U=Vt;if(ol=O||n,Vt=U||y,Te(e,t),Vt=U,ol=O,Ae(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||y||ol||Vt||na(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){y=l=e;try{if(i=y.stateNode,n)o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{d=y.stateNode;var H=y.memoizedProps.style,C=H!=null&&H.hasOwnProperty("display")?H.display:null;d.style.display=C==null||typeof C=="boolean"?"":(""+C).trim()}}catch(x){Rt(y,y.return,x)}}}else if(e.tag===6){if(l===null){y=e;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(x){Rt(y,y.return,x)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Rr(t,l))));break;case 19:Te(e,t),Ae(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Rr(t,a)));break;case 30:break;case 21:break;default:Te(e,t),Ae(t)}}function Ae(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Od(a)){l=a;break}a=a.return}if(l==null)throw Error(r(160));switch(l.tag){case 27:var n=l.stateNode,i=Cr(t);ii(t,i,n);break;case 5:var o=l.stateNode;l.flags&32&&(ya(o,""),l.flags&=-33);var d=Cr(t);ii(t,d,o);break;case 3:case 4:var y=l.stateNode.containerInfo,O=Cr(t);xr(t,O,y);break;default:throw Error(r(161))}}catch(U){Rt(t,t.return,U)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ud(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Ud(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Rl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)xd(t,e.alternate,e),e=e.sibling}function na(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:zl(4,e,e.return),na(e);break;case 1:Ke(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Ad(e,e.return,l),na(e);break;case 27:kn(e.stateNode);case 26:case 5:Ke(e,e.return),na(e);break;case 22:e.memoizedState===null&&na(e);break;case 30:na(e);break;default:na(e)}t=t.sibling}}function Ml(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,i=e,o=i.flags;switch(i.tag){case 0:case 11:case 15:Ml(n,i,l),qn(4,i);break;case 1:if(Ml(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(O){Rt(a,a.return,O)}if(a=i,n=a.updateQueue,n!==null){var d=a.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)cs(y[n],d)}catch(O){Rt(a,a.return,O)}}l&&o&64&&Td(i),Yn(i,i.return);break;case 27:_d(i);case 26:case 5:Ml(n,i,l),l&&a===null&&o&4&&Ed(i),Yn(i,i.return);break;case 12:Ml(n,i,l);break;case 13:Ml(n,i,l),l&&o&4&&Md(n,i);break;case 22:i.memoizedState===null&&Ml(n,i,l),Yn(i,i.return);break;case 30:break;default:Ml(n,i,l)}e=e.sibling}}function Mr(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&En(l))}function Dr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&En(t))}function ke(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Nd(t,e,l,a),e=e.sibling}function Nd(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:ke(t,e,l,a),n&2048&&qn(9,e);break;case 1:ke(t,e,l,a);break;case 3:ke(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&En(t)));break;case 12:if(n&2048){ke(t,e,l,a),t=e.stateNode;try{var i=e.memoizedProps,o=i.id,d=i.onPostCommit;typeof d=="function"&&d(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){Rt(e,e.return,y)}}else ke(t,e,l,a);break;case 13:ke(t,e,l,a);break;case 23:break;case 22:i=e.stateNode,o=e.alternate,e.memoizedState!==null?i._visibility&2?ke(t,e,l,a):Gn(t,e):i._visibility&2?ke(t,e,l,a):(i._visibility|=2,Ba(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Mr(o,e);break;case 24:ke(t,e,l,a),n&2048&&Dr(e.alternate,e);break;default:ke(t,e,l,a)}}function Ba(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,o=e,d=l,y=a,O=o.flags;switch(o.tag){case 0:case 11:case 15:Ba(i,o,d,y,n),qn(8,o);break;case 23:break;case 22:var U=o.stateNode;o.memoizedState!==null?U._visibility&2?Ba(i,o,d,y,n):Gn(i,o):(U._visibility|=2,Ba(i,o,d,y,n)),n&&O&2048&&Mr(o.alternate,o);break;case 24:Ba(i,o,d,y,n),n&&O&2048&&Dr(o.alternate,o);break;default:Ba(i,o,d,y,n)}e=e.sibling}}function Gn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Gn(l,a),n&2048&&Mr(a.alternate,a);break;case 24:Gn(l,a),n&2048&&Dr(a.alternate,a);break;default:Gn(l,a)}e=e.sibling}}var wn=8192;function Ha(t){if(t.subtreeFlags&wn)for(t=t.child;t!==null;)Bd(t),t=t.sibling}function Bd(t){switch(t.tag){case 26:Ha(t),t.flags&wn&&t.memoizedState!==null&&cg(Xe,t.memoizedState,t.memoizedProps);break;case 5:Ha(t);break;case 3:case 4:var e=Xe;Xe=bi(t.stateNode.containerInfo),Ha(t),Xe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=wn,wn=16777216,Ha(t),wn=e):Ha(t));break;default:Ha(t)}}function Hd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Xn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];It=a,qd(a,t)}Hd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)jd(t),t=t.sibling}function jd(t){switch(t.tag){case 0:case 11:case 15:Xn(t),t.flags&2048&&zl(9,t,t.return);break;case 3:Xn(t);break;case 12:Xn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ci(t)):Xn(t);break;default:Xn(t)}}function ci(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];It=a,qd(a,t)}Hd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:zl(8,e,e.return),ci(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,ci(e));break;default:ci(e)}t=t.sibling}}function qd(t,e){for(;It!==null;){var l=It;switch(l.tag){case 0:case 11:case 15:zl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:En(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,It=a;else t:for(l=t;It!==null;){a=It;var n=a.sibling,i=a.return;if(zd(a),a===l){It=null;break t}if(n!==null){n.return=i,It=n;break t}It=i}}}var Ey={getCacheForType:function(t){var e=ie(Jt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Oy=typeof WeakMap=="function"?WeakMap:Map,St=0,Ut=null,ot=null,ht=0,Tt=0,Ee=null,Dl=!1,ja=!1,Ur=!1,dl=0,wt=0,Ul=0,ua=0,Nr=0,je=0,qa=0,Qn=null,me=null,Br=!1,Hr=0,ri=1/0,fi=null,Nl=null,le=0,Bl=null,Ya=null,Ga=0,jr=0,qr=null,Yd=null,Ln=0,Yr=null;function Oe(){if((St&2)!==0&&ht!==0)return ht&-ht;if(M.T!==null){var t=Ca;return t!==0?t:Zr()}return If()}function Gd(){je===0&&(je=(ht&536870912)===0||gt?Jf():536870912);var t=He.current;return t!==null&&(t.flags|=32),je}function _e(t,e,l){(t===Ut&&(Tt===2||Tt===9)||t.cancelPendingCommit!==null)&&(wa(t,0),Hl(t,ht,je,!1)),cn(t,l),((St&2)===0||t!==Ut)&&(t===Ut&&((St&2)===0&&(ua|=l),wt===4&&Hl(t,ht,je,!1)),$e(t))}function wd(t,e,l){if((St&6)!==0)throw Error(r(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||un(t,e),n=a?xy(t,e):Xr(t,e,!0),i=a;do{if(n===0){ja&&!a&&Hl(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!_y(l)){n=Xr(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var d=t;n=Qn;var y=d.current.memoizedState.isDehydrated;if(y&&(wa(d,o).flags|=256),o=Xr(d,o,!1),o!==2){if(Ur&&!y){d.errorRecoveryDisabledLanes|=i,ua|=i,n=4;break t}i=me,me=n,i!==null&&(me===null?me=i:me.push.apply(me,i))}n=o}if(i=!1,n!==2)continue}}if(n===1){wa(t,0),Hl(t,e,0,!0);break}t:{switch(a=t,i=n,i){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Hl(a,e,je,!Dl);break t;case 2:me=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(n=Hr+300-Le(),10<n)){if(Hl(a,e,je,!Dl),Su(a,0,!0)!==0)break t;a.timeoutHandle=y0(Xd.bind(null,a,l,me,fi,Br,e,je,ua,qa,Dl,i,2,-0,0),n);break t}Xd(a,l,me,fi,Br,e,je,ua,qa,Dl,i,0,-0,0)}}break}while(!0);$e(t)}function Xd(t,e,l,a,n,i,o,d,y,O,U,H,C,x){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Wn={stylesheets:null,count:0,unsuspend:ig},Bd(e),H=rg(),H!==null)){t.cancelPendingCommit=H($d.bind(null,t,e,i,l,a,n,o,d,y,U,1,C,x)),Hl(t,i,o,!O);return}$d(t,e,i,l,a,n,o,d,y)}function _y(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!be(i(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Hl(t,e,l,a){e&=~Nr,e&=~ua,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var i=31-pe(n),o=1<<i;a[i]=-1,n&=~o}l!==0&&Ff(t,l,e)}function oi(){return(St&6)===0?(Vn(0),!1):!0}function Gr(){if(ot!==null){if(Tt===0)var t=ot.return;else t=ot,nl=Il=null,lr(t),Ua=null,Bn=0,t=ot;for(;t!==null;)Sd(t.alternate,t),t=t.return;ot=null}}function wa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Ly(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Gr(),Ut=t,ot=l=el(t.current,null),ht=e,Tt=0,Ee=null,Dl=!1,ja=un(t,e),Ur=!1,qa=je=Nr=ua=Ul=wt=0,me=Qn=null,Br=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-pe(a),i=1<<n;e|=t[n],a&=~i}return dl=e,Uu(),l}function Qd(t,e){rt=null,M.H=Fu,e===_n||e===Xu?(e=us(),Tt=3):e===ls?(e=us(),Tt=4):Tt=e===id?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ee=e,ot===null&&(wt=1,li(t,De(e,t.current)))}function Ld(){var t=M.H;return M.H=Fu,t===null?Fu:t}function Vd(){var t=M.A;return M.A=Ey,t}function wr(){wt=4,Dl||(ht&4194048)!==ht&&He.current!==null||(ja=!0),(Ul&134217727)===0&&(ua&134217727)===0||Ut===null||Hl(Ut,ht,je,!1)}function Xr(t,e,l){var a=St;St|=2;var n=Ld(),i=Vd();(Ut!==t||ht!==e)&&(fi=null,wa(t,e)),e=!1;var o=wt;t:do try{if(Tt!==0&&ot!==null){var d=ot,y=Ee;switch(Tt){case 8:Gr(),o=6;break t;case 3:case 2:case 9:case 6:He.current===null&&(e=!0);var O=Tt;if(Tt=0,Ee=null,Xa(t,d,y,O),l&&ja){o=0;break t}break;default:O=Tt,Tt=0,Ee=null,Xa(t,d,y,O)}}Cy(),o=wt;break}catch(U){Qd(t,U)}while(!0);return e&&t.shellSuspendCounter++,nl=Il=null,St=a,M.H=n,M.A=i,ot===null&&(Ut=null,ht=0,Uu()),o}function Cy(){for(;ot!==null;)Zd(ot)}function xy(t,e){var l=St;St|=2;var a=Ld(),n=Vd();Ut!==t||ht!==e?(fi=null,ri=Le()+500,wa(t,e)):ja=un(t,e);t:do try{if(Tt!==0&&ot!==null){e=ot;var i=Ee;e:switch(Tt){case 1:Tt=0,Ee=null,Xa(t,e,i,1);break;case 2:case 9:if(as(i)){Tt=0,Ee=null,Kd(e);break}e=function(){Tt!==2&&Tt!==9||Ut!==t||(Tt=7),$e(t)},i.then(e,e);break t;case 3:Tt=7;break t;case 4:Tt=5;break t;case 7:as(i)?(Tt=0,Ee=null,Kd(e)):(Tt=0,Ee=null,Xa(t,e,i,7));break;case 5:var o=null;switch(ot.tag){case 26:o=ot.memoizedState;case 5:case 27:var d=ot;if(!o||x0(o)){Tt=0,Ee=null;var y=d.sibling;if(y!==null)ot=y;else{var O=d.return;O!==null?(ot=O,si(O)):ot=null}break e}}Tt=0,Ee=null,Xa(t,e,i,5);break;case 6:Tt=0,Ee=null,Xa(t,e,i,6);break;case 8:Gr(),wt=6;break t;default:throw Error(r(462))}}zy();break}catch(U){Qd(t,U)}while(!0);return nl=Il=null,M.H=a,M.A=n,St=l,ot!==null?0:(Ut=null,ht=0,Uu(),wt)}function zy(){for(;ot!==null&&!Wh();)Zd(ot)}function Zd(t){var e=pd(t.alternate,t,dl);t.memoizedProps=t.pendingProps,e===null?si(t):ot=e}function Kd(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=dd(l,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=dd(l,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:lr(e);default:Sd(l,e),e=ot=ko(e,dl),e=pd(l,e,dl)}t.memoizedProps=t.pendingProps,e===null?si(t):ot=e}function Xa(t,e,l,a){nl=Il=null,lr(e),Ua=null,Bn=0;var n=e.return;try{if(vy(t,n,e,l,ht)){wt=1,li(t,De(l,t.current)),ot=null;return}}catch(i){if(n!==null)throw ot=n,i;wt=1,li(t,De(l,t.current)),ot=null;return}e.flags&32768?(gt||a===1?t=!0:ja||(ht&536870912)!==0?t=!1:(Dl=t=!0,(a===2||a===9||a===3||a===6)&&(a=He.current,a!==null&&a.tag===13&&(a.flags|=16384))),kd(e,t)):si(e)}function si(t){var e=t;do{if((e.flags&32768)!==0){kd(e,Dl);return}t=e.return;var l=by(e.alternate,e,dl);if(l!==null){ot=l;return}if(e=e.sibling,e!==null){ot=e;return}ot=e=t}while(e!==null);wt===0&&(wt=5)}function kd(t,e){do{var l=Sy(t.alternate,t);if(l!==null){l.flags&=32767,ot=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ot=t;return}ot=t=l}while(t!==null);wt=6,ot=null}function $d(t,e,l,a,n,i,o,d,y){t.cancelPendingCommit=null;do di();while(le!==0);if((St&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(i=e.lanes|e.childLanes,i|=Dc,im(t,l,i,o,d,y),t===Ut&&(ot=Ut=null,ht=0),Ya=e,Bl=t,Ga=l,jr=i,qr=n,Yd=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Uy(vu,function(){return Id(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=M.T,M.T=null,n=w.p,w.p=2,o=St,St|=4;try{Ty(t,e,l)}finally{St=o,w.p=n,M.T=a}}le=1,Jd(),Wd(),Fd()}}function Jd(){if(le===1){le=0;var t=Bl,e=Ya,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=M.T,M.T=null;var a=w.p;w.p=2;var n=St;St|=4;try{Dd(e,t);var i=Ir,o=qo(t.containerInfo),d=i.focusedElem,y=i.selectionRange;if(o!==d&&d&&d.ownerDocument&&jo(d.ownerDocument.documentElement,d)){if(y!==null&&Cc(d)){var O=y.start,U=y.end;if(U===void 0&&(U=O),"selectionStart"in d)d.selectionStart=O,d.selectionEnd=Math.min(U,d.value.length);else{var H=d.ownerDocument||document,C=H&&H.defaultView||window;if(C.getSelection){var x=C.getSelection(),at=d.textContent.length,et=Math.min(y.start,at),_t=y.end===void 0?et:Math.min(y.end,at);!x.extend&&et>_t&&(o=_t,_t=et,et=o);var T=Ho(d,et),b=Ho(d,_t);if(T&&b&&(x.rangeCount!==1||x.anchorNode!==T.node||x.anchorOffset!==T.offset||x.focusNode!==b.node||x.focusOffset!==b.offset)){var E=H.createRange();E.setStart(T.node,T.offset),x.removeAllRanges(),et>_t?(x.addRange(E),x.extend(b.node,b.offset)):(E.setEnd(b.node,b.offset),x.addRange(E))}}}}for(H=[],x=d;x=x.parentNode;)x.nodeType===1&&H.push({element:x,left:x.scrollLeft,top:x.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<H.length;d++){var B=H[d];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}Oi=!!Pr,Ir=Pr=null}finally{St=n,w.p=a,M.T=l}}t.current=e,le=2}}function Wd(){if(le===2){le=0;var t=Bl,e=Ya,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=M.T,M.T=null;var a=w.p;w.p=2;var n=St;St|=4;try{xd(t,e.alternate,e)}finally{St=n,w.p=a,M.T=l}}le=3}}function Fd(){if(le===4||le===3){le=0,Fh();var t=Bl,e=Ya,l=Ga,a=Yd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?le=5:(le=0,Ya=Bl=null,Pd(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Nl=null),uc(l),e=e.stateNode,ve&&typeof ve.onCommitFiberRoot=="function")try{ve.onCommitFiberRoot(nn,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=M.T,n=w.p,w.p=2,M.T=null;try{for(var i=t.onRecoverableError,o=0;o<a.length;o++){var d=a[o];i(d.value,{componentStack:d.stack})}}finally{M.T=e,w.p=n}}(Ga&3)!==0&&di(),$e(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Yr?Ln++:(Ln=0,Yr=t):Ln=0,Vn(0)}}function Pd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,En(e)))}function di(t){return Jd(),Wd(),Fd(),Id()}function Id(){if(le!==5)return!1;var t=Bl,e=jr;jr=0;var l=uc(Ga),a=M.T,n=w.p;try{w.p=32>l?32:l,M.T=null,l=qr,qr=null;var i=Bl,o=Ga;if(le=0,Ya=Bl=null,Ga=0,(St&6)!==0)throw Error(r(331));var d=St;if(St|=4,jd(i.current),Nd(i,i.current,o,l),St=d,Vn(0,!1),ve&&typeof ve.onPostCommitFiberRoot=="function")try{ve.onPostCommitFiberRoot(nn,i)}catch{}return!0}finally{w.p=n,M.T=a,Pd(t,e)}}function t0(t,e,l){e=De(l,e),e=gr(t.stateNode,e,2),t=Ol(t,e,2),t!==null&&(cn(t,2),$e(t))}function Rt(t,e,l){if(t.tag===3)t0(t,t,l);else for(;e!==null;){if(e.tag===3){t0(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Nl===null||!Nl.has(a))){t=De(l,t),l=nd(2),a=Ol(e,l,2),a!==null&&(ud(l,a,e,t),cn(a,2),$e(a));break}}e=e.return}}function Qr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new Oy;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Ur=!0,n.add(l),t=Ry.bind(null,t,e,l),e.then(t,t))}function Ry(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Ut===t&&(ht&l)===l&&(wt===4||wt===3&&(ht&62914560)===ht&&300>Le()-Hr?(St&2)===0&&wa(t,0):Nr|=l,qa===ht&&(qa=0)),$e(t)}function e0(t,e){e===0&&(e=Wf()),t=Aa(t,e),t!==null&&(cn(t,e),$e(t))}function My(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),e0(t,l)}function Dy(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(r(314))}a!==null&&a.delete(e),e0(t,l)}function Uy(t,e){return ec(t,e)}var hi=null,Qa=null,Lr=!1,mi=!1,Vr=!1,ia=0;function $e(t){t!==Qa&&t.next===null&&(Qa===null?hi=Qa=t:Qa=Qa.next=t),mi=!0,Lr||(Lr=!0,By())}function Vn(t,e){if(!Vr&&mi){Vr=!0;do for(var l=!1,a=hi;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var o=a.suspendedLanes,d=a.pingedLanes;i=(1<<31-pe(42|t)+1)-1,i&=n&~(o&~d),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,u0(a,i))}else i=ht,i=Su(a,a===Ut?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||un(a,i)||(l=!0,u0(a,i));a=a.next}while(l);Vr=!1}}function Ny(){l0()}function l0(){mi=Lr=!1;var t=0;ia!==0&&(Qy()&&(t=ia),ia=0);for(var e=Le(),l=null,a=hi;a!==null;){var n=a.next,i=a0(a,e);i===0?(a.next=null,l===null?hi=n:l.next=n,n===null&&(Qa=l)):(l=a,(t!==0||(i&3)!==0)&&(mi=!0)),a=n}Vn(t)}function a0(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var o=31-pe(i),d=1<<o,y=n[o];y===-1?((d&l)===0||(d&a)!==0)&&(n[o]=um(d,e)):y<=e&&(t.expiredLanes|=d),i&=~d}if(e=Ut,l=ht,l=Su(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(Tt===2||Tt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&lc(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||un(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&lc(a),uc(l)){case 2:case 8:l=kf;break;case 32:l=vu;break;case 268435456:l=$f;break;default:l=vu}return a=n0.bind(null,t),l=ec(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&lc(a),t.callbackPriority=2,t.callbackNode=null,2}function n0(t,e){if(le!==0&&le!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(di()&&t.callbackNode!==l)return null;var a=ht;return a=Su(t,t===Ut?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(wd(t,a,e),a0(t,Le()),t.callbackNode!=null&&t.callbackNode===l?n0.bind(null,t):null)}function u0(t,e){if(di())return null;wd(t,e,!0)}function By(){Vy(function(){(St&6)!==0?ec(Kf,Ny):l0()})}function Zr(){return ia===0&&(ia=Jf()),ia}function i0(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_u(""+t)}function c0(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Hy(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var i=i0((n[oe]||null).action),o=a.submitter;o&&(e=(e=o[oe]||null)?i0(e.formAction):o.getAttribute("formAction"),e!==null&&(i=e,o=null));var d=new Ru("action","action",null,a,n);t.push({event:d,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ia!==0){var y=o?c0(n,o):new FormData(n);sr(l,{pending:!0,data:y,method:n.method,action:i},null,y)}}else typeof i=="function"&&(d.preventDefault(),y=o?c0(n,o):new FormData(n),sr(l,{pending:!0,data:y,method:n.method,action:i},i,y))},currentTarget:n}]})}}for(var Kr=0;Kr<Mc.length;Kr++){var kr=Mc[Kr],jy=kr.toLowerCase(),qy=kr[0].toUpperCase()+kr.slice(1);we(jy,"on"+qy)}we(wo,"onAnimationEnd"),we(Xo,"onAnimationIteration"),we(Qo,"onAnimationStart"),we("dblclick","onDoubleClick"),we("focusin","onFocus"),we("focusout","onBlur"),we(ty,"onTransitionRun"),we(ey,"onTransitionStart"),we(ly,"onTransitionCancel"),we(Lo,"onTransitionEnd"),da("onMouseEnter",["mouseout","mouseover"]),da("onMouseLeave",["mouseout","mouseover"]),da("onPointerEnter",["pointerout","pointerover"]),da("onPointerLeave",["pointerout","pointerover"]),Vl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Vl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Vl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Vl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Vl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Vl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Zn));function r0(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var i=void 0;if(e)for(var o=a.length-1;0<=o;o--){var d=a[o],y=d.instance,O=d.currentTarget;if(d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(U){ei(U)}n.currentTarget=null,i=y}else for(o=0;o<a.length;o++){if(d=a[o],y=d.instance,O=d.currentTarget,d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(U){ei(U)}n.currentTarget=null,i=y}}}}function st(t,e){var l=e[ic];l===void 0&&(l=e[ic]=new Set);var a=t+"__bubble";l.has(a)||(f0(e,t,2,!1),l.add(a))}function $r(t,e,l){var a=0;e&&(a|=4),f0(l,t,a,e)}var yi="_reactListening"+Math.random().toString(36).slice(2);function Jr(t){if(!t[yi]){t[yi]=!0,eo.forEach(function(l){l!=="selectionchange"&&(Yy.has(l)||$r(l,!1,t),$r(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[yi]||(e[yi]=!0,$r("selectionchange",!1,e))}}function f0(t,e,l,a){switch(N0(e)){case 2:var n=sg;break;case 8:n=dg;break;default:n=of}l=n.bind(null,e,l,t),n=void 0,!vc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function Wr(t,e,l,a,n){var i=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var o=a.tag;if(o===3||o===4){var d=a.stateNode.containerInfo;if(d===n)break;if(o===4)for(o=a.return;o!==null;){var y=o.tag;if((y===3||y===4)&&o.stateNode.containerInfo===n)return;o=o.return}for(;d!==null;){if(o=fa(d),o===null)return;if(y=o.tag,y===5||y===6||y===26||y===27){a=i=o;continue t}d=d.parentNode}}a=a.return}go(function(){var O=i,U=yc(l),H=[];t:{var C=Vo.get(t);if(C!==void 0){var x=Ru,at=t;switch(t){case"keypress":if(xu(l)===0)break t;case"keydown":case"keyup":x=Um;break;case"focusin":at="focus",x=Tc;break;case"focusout":at="blur",x=Tc;break;case"beforeblur":case"afterblur":x=Tc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=bo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=Sm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=Hm;break;case wo:case Xo:case Qo:x=Em;break;case Lo:x=qm;break;case"scroll":case"scrollend":x=pm;break;case"wheel":x=Gm;break;case"copy":case"cut":case"paste":x=_m;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=To;break;case"toggle":case"beforetoggle":x=Xm}var et=(e&4)!==0,_t=!et&&(t==="scroll"||t==="scrollend"),T=et?C!==null?C+"Capture":null:C;et=[];for(var b=O,E;b!==null;){var B=b;if(E=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||E===null||T===null||(B=on(b,T),B!=null&&et.push(Kn(b,B,E))),_t)break;b=b.return}0<et.length&&(C=new x(C,at,null,l,U),H.push({event:C,listeners:et}))}}if((e&7)===0){t:{if(C=t==="mouseover"||t==="pointerover",x=t==="mouseout"||t==="pointerout",C&&l!==mc&&(at=l.relatedTarget||l.fromElement)&&(fa(at)||at[ra]))break t;if((x||C)&&(C=U.window===U?U:(C=U.ownerDocument)?C.defaultView||C.parentWindow:window,x?(at=l.relatedTarget||l.toElement,x=O,at=at?fa(at):null,at!==null&&(_t=h(at),et=at.tag,at!==_t||et!==5&&et!==27&&et!==6)&&(at=null)):(x=null,at=O),x!==at)){if(et=bo,B="onMouseLeave",T="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(et=To,B="onPointerLeave",T="onPointerEnter",b="pointer"),_t=x==null?C:fn(x),E=at==null?C:fn(at),C=new et(B,b+"leave",x,l,U),C.target=_t,C.relatedTarget=E,B=null,fa(U)===O&&(et=new et(T,b+"enter",at,l,U),et.target=E,et.relatedTarget=_t,B=et),_t=B,x&&at)e:{for(et=x,T=at,b=0,E=et;E;E=La(E))b++;for(E=0,B=T;B;B=La(B))E++;for(;0<b-E;)et=La(et),b--;for(;0<E-b;)T=La(T),E--;for(;b--;){if(et===T||T!==null&&et===T.alternate)break e;et=La(et),T=La(T)}et=null}else et=null;x!==null&&o0(H,C,x,et,!1),at!==null&&_t!==null&&o0(H,_t,at,et,!0)}}t:{if(C=O?fn(O):window,x=C.nodeName&&C.nodeName.toLowerCase(),x==="select"||x==="input"&&C.type==="file")var $=Ro;else if(xo(C))if(Mo)$=Fm;else{$=Jm;var ft=$m}else x=C.nodeName,!x||x.toLowerCase()!=="input"||C.type!=="checkbox"&&C.type!=="radio"?O&&hc(O.elementType)&&($=Ro):$=Wm;if($&&($=$(t,O))){zo(H,$,l,U);break t}ft&&ft(t,C,O),t==="focusout"&&O&&C.type==="number"&&O.memoizedProps.value!=null&&dc(C,"number",C.value)}switch(ft=O?fn(O):window,t){case"focusin":(xo(ft)||ft.contentEditable==="true")&&(ba=ft,xc=O,pn=null);break;case"focusout":pn=xc=ba=null;break;case"mousedown":zc=!0;break;case"contextmenu":case"mouseup":case"dragend":zc=!1,Yo(H,l,U);break;case"selectionchange":if(Im)break;case"keydown":case"keyup":Yo(H,l,U)}var W;if(Ec)t:{switch(t){case"compositionstart":var lt="onCompositionStart";break t;case"compositionend":lt="onCompositionEnd";break t;case"compositionupdate":lt="onCompositionUpdate";break t}lt=void 0}else pa?_o(t,l)&&(lt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(lt="onCompositionStart");lt&&(Ao&&l.locale!=="ko"&&(pa||lt!=="onCompositionStart"?lt==="onCompositionEnd"&&pa&&(W=vo()):(Sl=U,pc="value"in Sl?Sl.value:Sl.textContent,pa=!0)),ft=gi(O,lt),0<ft.length&&(lt=new So(lt,t,null,l,U),H.push({event:lt,listeners:ft}),W?lt.data=W:(W=Co(l),W!==null&&(lt.data=W)))),(W=Lm?Vm(t,l):Zm(t,l))&&(lt=gi(O,"onBeforeInput"),0<lt.length&&(ft=new So("onBeforeInput","beforeinput",null,l,U),H.push({event:ft,listeners:lt}),ft.data=W)),Hy(H,t,O,l,U)}r0(H,e)})}function Kn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function gi(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=on(t,l),n!=null&&a.unshift(Kn(t,n,i)),n=on(t,e),n!=null&&a.push(Kn(t,n,i))),t.tag===3)return a;t=t.return}return[]}function La(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function o0(t,e,l,a,n){for(var i=e._reactName,o=[];l!==null&&l!==a;){var d=l,y=d.alternate,O=d.stateNode;if(d=d.tag,y!==null&&y===a)break;d!==5&&d!==26&&d!==27||O===null||(y=O,n?(O=on(l,i),O!=null&&o.unshift(Kn(l,O,y))):n||(O=on(l,i),O!=null&&o.push(Kn(l,O,y)))),l=l.return}o.length!==0&&t.push({event:e,listeners:o})}var Gy=/\r\n?/g,wy=/\u0000|\uFFFD/g;function s0(t){return(typeof t=="string"?t:""+t).replace(Gy,`
`).replace(wy,"")}function d0(t,e){return e=s0(e),s0(t)===e}function vi(){}function Ot(t,e,l,a,n,i){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ya(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ya(t,""+a);break;case"className":Au(t,"class",a);break;case"tabIndex":Au(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Au(t,l,a);break;case"style":mo(t,a,i);break;case"data":if(e!=="object"){Au(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&Ot(t,e,"name",n.name,n,null),Ot(t,e,"formEncType",n.formEncType,n,null),Ot(t,e,"formMethod",n.formMethod,n,null),Ot(t,e,"formTarget",n.formTarget,n,null)):(Ot(t,e,"encType",n.encType,n,null),Ot(t,e,"method",n.method,n,null),Ot(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=vi);break;case"onScroll":a!=null&&st("scroll",t);break;case"onScrollEnd":a!=null&&st("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=_u(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":st("beforetoggle",t),st("toggle",t),Tu(t,"popover",a);break;case"xlinkActuate":Ie(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ie(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ie(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ie(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ie(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ie(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Tu(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=gm.get(l)||l,Tu(t,l,a))}}function Fr(t,e,l,a,n,i){switch(l){case"style":mo(t,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ya(t,a):(typeof a=="number"||typeof a=="bigint")&&ya(t,""+a);break;case"onScroll":a!=null&&st("scroll",t);break;case"onScrollEnd":a!=null&&st("scrollend",t);break;case"onClick":a!=null&&(t.onclick=vi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!lo.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),i=t[oe]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Tu(t,l,a)}}}function ae(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":st("error",t),st("load",t);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var o=l[i];if(o!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Ot(t,e,i,o,l,null)}}n&&Ot(t,e,"srcSet",l.srcSet,l,null),a&&Ot(t,e,"src",l.src,l,null);return;case"input":st("invalid",t);var d=i=o=n=null,y=null,O=null;for(a in l)if(l.hasOwnProperty(a)){var U=l[a];if(U!=null)switch(a){case"name":n=U;break;case"type":o=U;break;case"checked":y=U;break;case"defaultChecked":O=U;break;case"value":i=U;break;case"defaultValue":d=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(r(137,e));break;default:Ot(t,e,a,U,l,null)}}fo(t,i,d,y,O,o,n,!1),Eu(t);return;case"select":st("invalid",t),a=o=i=null;for(n in l)if(l.hasOwnProperty(n)&&(d=l[n],d!=null))switch(n){case"value":i=d;break;case"defaultValue":o=d;break;case"multiple":a=d;default:Ot(t,e,n,d,l,null)}e=i,l=o,t.multiple=!!a,e!=null?ma(t,!!a,e,!1):l!=null&&ma(t,!!a,l,!0);return;case"textarea":st("invalid",t),i=n=a=null;for(o in l)if(l.hasOwnProperty(o)&&(d=l[o],d!=null))switch(o){case"value":a=d;break;case"defaultValue":n=d;break;case"children":i=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(r(91));break;default:Ot(t,e,o,d,l,null)}so(t,a,n,i),Eu(t);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Ot(t,e,y,a,l,null)}return;case"dialog":st("beforetoggle",t),st("toggle",t),st("cancel",t),st("close",t);break;case"iframe":case"object":st("load",t);break;case"video":case"audio":for(a=0;a<Zn.length;a++)st(Zn[a],t);break;case"image":st("error",t),st("load",t);break;case"details":st("toggle",t);break;case"embed":case"source":case"link":st("error",t),st("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in l)if(l.hasOwnProperty(O)&&(a=l[O],a!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Ot(t,e,O,a,l,null)}return;default:if(hc(e)){for(U in l)l.hasOwnProperty(U)&&(a=l[U],a!==void 0&&Fr(t,e,U,a,l,void 0));return}}for(d in l)l.hasOwnProperty(d)&&(a=l[d],a!=null&&Ot(t,e,d,a,l,null))}function Xy(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,o=null,d=null,y=null,O=null,U=null;for(x in l){var H=l[x];if(l.hasOwnProperty(x)&&H!=null)switch(x){case"checked":break;case"value":break;case"defaultValue":y=H;default:a.hasOwnProperty(x)||Ot(t,e,x,null,a,H)}}for(var C in a){var x=a[C];if(H=l[C],a.hasOwnProperty(C)&&(x!=null||H!=null))switch(C){case"type":i=x;break;case"name":n=x;break;case"checked":O=x;break;case"defaultChecked":U=x;break;case"value":o=x;break;case"defaultValue":d=x;break;case"children":case"dangerouslySetInnerHTML":if(x!=null)throw Error(r(137,e));break;default:x!==H&&Ot(t,e,C,x,a,H)}}sc(t,o,d,y,O,U,i,n);return;case"select":x=o=d=C=null;for(i in l)if(y=l[i],l.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":x=y;default:a.hasOwnProperty(i)||Ot(t,e,i,null,a,y)}for(n in a)if(i=a[n],y=l[n],a.hasOwnProperty(n)&&(i!=null||y!=null))switch(n){case"value":C=i;break;case"defaultValue":d=i;break;case"multiple":o=i;default:i!==y&&Ot(t,e,n,i,a,y)}e=d,l=o,a=x,C!=null?ma(t,!!l,C,!1):!!a!=!!l&&(e!=null?ma(t,!!l,e,!0):ma(t,!!l,l?[]:"",!1));return;case"textarea":x=C=null;for(d in l)if(n=l[d],l.hasOwnProperty(d)&&n!=null&&!a.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Ot(t,e,d,null,a,n)}for(o in a)if(n=a[o],i=l[o],a.hasOwnProperty(o)&&(n!=null||i!=null))switch(o){case"value":C=n;break;case"defaultValue":x=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==i&&Ot(t,e,o,n,a,i)}oo(t,C,x);return;case"option":for(var at in l)if(C=l[at],l.hasOwnProperty(at)&&C!=null&&!a.hasOwnProperty(at))switch(at){case"selected":t.selected=!1;break;default:Ot(t,e,at,null,a,C)}for(y in a)if(C=a[y],x=l[y],a.hasOwnProperty(y)&&C!==x&&(C!=null||x!=null))switch(y){case"selected":t.selected=C&&typeof C!="function"&&typeof C!="symbol";break;default:Ot(t,e,y,C,a,x)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var et in l)C=l[et],l.hasOwnProperty(et)&&C!=null&&!a.hasOwnProperty(et)&&Ot(t,e,et,null,a,C);for(O in a)if(C=a[O],x=l[O],a.hasOwnProperty(O)&&C!==x&&(C!=null||x!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(r(137,e));break;default:Ot(t,e,O,C,a,x)}return;default:if(hc(e)){for(var _t in l)C=l[_t],l.hasOwnProperty(_t)&&C!==void 0&&!a.hasOwnProperty(_t)&&Fr(t,e,_t,void 0,a,C);for(U in a)C=a[U],x=l[U],!a.hasOwnProperty(U)||C===x||C===void 0&&x===void 0||Fr(t,e,U,C,a,x);return}}for(var T in l)C=l[T],l.hasOwnProperty(T)&&C!=null&&!a.hasOwnProperty(T)&&Ot(t,e,T,null,a,C);for(H in a)C=a[H],x=l[H],!a.hasOwnProperty(H)||C===x||C==null&&x==null||Ot(t,e,H,C,a,x)}var Pr=null,Ir=null;function pi(t){return t.nodeType===9?t:t.ownerDocument}function h0(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function m0(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function tf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ef=null;function Qy(){var t=window.event;return t&&t.type==="popstate"?t===ef?!1:(ef=t,!0):(ef=null,!1)}var y0=typeof setTimeout=="function"?setTimeout:void 0,Ly=typeof clearTimeout=="function"?clearTimeout:void 0,g0=typeof Promise=="function"?Promise:void 0,Vy=typeof queueMicrotask=="function"?queueMicrotask:typeof g0<"u"?function(t){return g0.resolve(null).then(t).catch(Zy)}:y0;function Zy(t){setTimeout(function(){throw t})}function jl(t){return t==="head"}function v0(t,e){var l=e,a=0,n=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var o=t.ownerDocument;if(l&1&&kn(o.documentElement),l&2&&kn(o.body),l&4)for(l=o.head,kn(l),o=l.firstChild;o;){var d=o.nextSibling,y=o.nodeName;o[rn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&o.rel.toLowerCase()==="stylesheet"||l.removeChild(o),o=d}}if(n===0){t.removeChild(i),eu(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);eu(e)}function lf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":lf(l),cc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Ky(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[rn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Qe(t.nextSibling),t===null)break}return null}function ky(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Qe(t.nextSibling),t===null))return null;return t}function af(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function $y(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Qe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var nf=null;function p0(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function b0(t,e,l){switch(e=pi(l),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function kn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);cc(t)}var qe=new Map,S0=new Set;function bi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var hl=w.d;w.d={f:Jy,r:Wy,D:Fy,C:Py,L:Iy,m:tg,X:lg,S:eg,M:ag};function Jy(){var t=hl.f(),e=oi();return t||e}function Wy(t){var e=oa(t);e!==null&&e.tag===5&&e.type==="form"?ws(e):hl.r(t)}var Va=typeof document>"u"?null:document;function T0(t,e,l){var a=Va;if(a&&typeof e=="string"&&e){var n=Me(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),S0.has(n)||(S0.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),ae(e,"link",t),Ft(e),a.head.appendChild(e)))}}function Fy(t){hl.D(t),T0("dns-prefetch",t,null)}function Py(t,e){hl.C(t,e),T0("preconnect",t,e)}function Iy(t,e,l){hl.L(t,e,l);var a=Va;if(a&&t&&e){var n='link[rel="preload"][as="'+Me(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+Me(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+Me(l.imageSizes)+'"]')):n+='[href="'+Me(t)+'"]';var i=n;switch(e){case"style":i=Za(t);break;case"script":i=Ka(t)}qe.has(i)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),qe.set(i,t),a.querySelector(n)!==null||e==="style"&&a.querySelector($n(i))||e==="script"&&a.querySelector(Jn(i))||(e=a.createElement("link"),ae(e,"link",t),Ft(e),a.head.appendChild(e)))}}function tg(t,e){hl.m(t,e);var l=Va;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+Me(a)+'"][href="'+Me(t)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ka(t)}if(!qe.has(i)&&(t=z({rel:"modulepreload",href:t},e),qe.set(i,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Jn(i)))return}a=l.createElement("link"),ae(a,"link",t),Ft(a),l.head.appendChild(a)}}}function eg(t,e,l){hl.S(t,e,l);var a=Va;if(a&&t){var n=sa(a).hoistableStyles,i=Za(t);e=e||"default";var o=n.get(i);if(!o){var d={loading:0,preload:null};if(o=a.querySelector($n(i)))d.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=qe.get(i))&&uf(t,l);var y=o=a.createElement("link");Ft(y),ae(y,"link",t),y._p=new Promise(function(O,U){y.onload=O,y.onerror=U}),y.addEventListener("load",function(){d.loading|=1}),y.addEventListener("error",function(){d.loading|=2}),d.loading|=4,Si(o,e,a)}o={type:"stylesheet",instance:o,count:1,state:d},n.set(i,o)}}}function lg(t,e){hl.X(t,e);var l=Va;if(l&&t){var a=sa(l).hoistableScripts,n=Ka(t),i=a.get(n);i||(i=l.querySelector(Jn(n)),i||(t=z({src:t,async:!0},e),(e=qe.get(n))&&cf(t,e),i=l.createElement("script"),Ft(i),ae(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function ag(t,e){hl.M(t,e);var l=Va;if(l&&t){var a=sa(l).hoistableScripts,n=Ka(t),i=a.get(n);i||(i=l.querySelector(Jn(n)),i||(t=z({src:t,async:!0,type:"module"},e),(e=qe.get(n))&&cf(t,e),i=l.createElement("script"),Ft(i),ae(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function A0(t,e,l,a){var n=(n=it.current)?bi(n):null;if(!n)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Za(l.href),l=sa(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Za(l.href);var i=sa(n).hoistableStyles,o=i.get(t);if(o||(n=n.ownerDocument||n,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,o),(i=n.querySelector($n(t)))&&!i._p&&(o.instance=i,o.state.loading=5),qe.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},qe.set(t,l),i||ng(n,t,l,o.state))),e&&a===null)throw Error(r(528,""));return o}if(e&&a!==null)throw Error(r(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ka(l),l=sa(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Za(t){return'href="'+Me(t)+'"'}function $n(t){return'link[rel="stylesheet"]['+t+"]"}function E0(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function ng(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),ae(e,"link",l),Ft(e),t.head.appendChild(e))}function Ka(t){return'[src="'+Me(t)+'"]'}function Jn(t){return"script[async]"+t}function O0(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+Me(l.href)+'"]');if(a)return e.instance=a,Ft(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Ft(a),ae(a,"style",n),Si(a,l.precedence,t),e.instance=a;case"stylesheet":n=Za(l.href);var i=t.querySelector($n(n));if(i)return e.state.loading|=4,e.instance=i,Ft(i),i;a=E0(l),(n=qe.get(n))&&uf(a,n),i=(t.ownerDocument||t).createElement("link"),Ft(i);var o=i;return o._p=new Promise(function(d,y){o.onload=d,o.onerror=y}),ae(i,"link",a),e.state.loading|=4,Si(i,l.precedence,t),e.instance=i;case"script":return i=Ka(l.src),(n=t.querySelector(Jn(i)))?(e.instance=n,Ft(n),n):(a=l,(n=qe.get(i))&&(a=z({},l),cf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Ft(n),ae(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Si(a,l.precedence,t));return e.instance}function Si(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,o=0;o<a.length;o++){var d=a[o];if(d.dataset.precedence===e)i=d;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function uf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function cf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ti=null;function _0(t,e,l){if(Ti===null){var a=new Map,n=Ti=new Map;n.set(l,a)}else n=Ti,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var i=l[n];if(!(i[rn]||i[ue]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var o=i.getAttribute(e)||"";o=t+o;var d=a.get(o);d?d.push(i):a.set(o,[i])}}return a}function C0(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function ug(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function x0(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Wn=null;function ig(){}function cg(t,e,l){if(Wn===null)throw Error(r(475));var a=Wn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Za(l.href),i=t.querySelector($n(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Ai.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=i,Ft(i);return}i=t.ownerDocument||t,l=E0(l),(n=qe.get(n))&&uf(l,n),i=i.createElement("link"),Ft(i);var o=i;o._p=new Promise(function(d,y){o.onload=d,o.onerror=y}),ae(i,"link",l),e.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Ai.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function rg(){if(Wn===null)throw Error(r(475));var t=Wn;return t.stylesheets&&t.count===0&&rf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&rf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Ai(){if(this.count--,this.count===0){if(this.stylesheets)rf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ei=null;function rf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ei=new Map,e.forEach(fg,t),Ei=null,Ai.call(t))}function fg(t,e){if(!(e.state.loading&4)){var l=Ei.get(t);if(l)var a=l.get(null);else{l=new Map,Ei.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var o=n[i];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(l.set(o.dataset.precedence,o),a=o)}a&&l.set(null,a)}n=e.instance,o=n.getAttribute("data-precedence"),i=l.get(o)||a,i===a&&l.set(null,n),l.set(o,n),this.count++,a=Ai.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Fn={$$typeof:Q,Provider:null,Consumer:null,_currentValue:I,_currentValue2:I,_threadCount:0};function og(t,e,l,a,n,i,o,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ac(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ac(0),this.hiddenUpdates=ac(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function z0(t,e,l,a,n,i,o,d,y,O,U,H){return t=new og(t,e,l,o,d,y,O,H),e=1,i===!0&&(e|=24),i=Se(3,null,null,e),t.current=i,i.stateNode=t,e=Qc(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:e},Kc(i),t}function R0(t){return t?(t=Ea,t):Ea}function M0(t,e,l,a,n,i){n=R0(n),a.context===null?a.context=n:a.pendingContext=n,a=El(e),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=Ol(t,a,e),l!==null&&(_e(l,t,e),xn(l,t,e))}function D0(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function ff(t,e){D0(t,e),(t=t.alternate)&&D0(t,e)}function U0(t){if(t.tag===13){var e=Aa(t,67108864);e!==null&&_e(e,t,67108864),ff(t,67108864)}}var Oi=!0;function sg(t,e,l,a){var n=M.T;M.T=null;var i=w.p;try{w.p=2,of(t,e,l,a)}finally{w.p=i,M.T=n}}function dg(t,e,l,a){var n=M.T;M.T=null;var i=w.p;try{w.p=8,of(t,e,l,a)}finally{w.p=i,M.T=n}}function of(t,e,l,a){if(Oi){var n=sf(a);if(n===null)Wr(t,e,a,_i,l),B0(t,a);else if(mg(n,t,e,l,a))a.stopPropagation();else if(B0(t,a),e&4&&-1<hg.indexOf(t)){for(;n!==null;){var i=oa(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var o=Ll(i.pendingLanes);if(o!==0){var d=i;for(d.pendingLanes|=2,d.entangledLanes|=2;o;){var y=1<<31-pe(o);d.entanglements[1]|=y,o&=~y}$e(i),(St&6)===0&&(ri=Le()+500,Vn(0))}}break;case 13:d=Aa(i,2),d!==null&&_e(d,i,2),oi(),ff(i,2)}if(i=sf(a),i===null&&Wr(t,e,a,_i,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else Wr(t,e,a,null,l)}}function sf(t){return t=yc(t),df(t)}var _i=null;function df(t){if(_i=null,t=fa(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=v(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _i=t,null}function N0(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ph()){case Kf:return 2;case kf:return 8;case vu:case Ih:return 32;case $f:return 268435456;default:return 32}default:return 32}}var hf=!1,ql=null,Yl=null,Gl=null,Pn=new Map,In=new Map,wl=[],hg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function B0(t,e){switch(t){case"focusin":case"focusout":ql=null;break;case"dragenter":case"dragleave":Yl=null;break;case"mouseover":case"mouseout":Gl=null;break;case"pointerover":case"pointerout":Pn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":In.delete(e.pointerId)}}function tu(t,e,l,a,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},e!==null&&(e=oa(e),e!==null&&U0(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function mg(t,e,l,a,n){switch(e){case"focusin":return ql=tu(ql,t,e,l,a,n),!0;case"dragenter":return Yl=tu(Yl,t,e,l,a,n),!0;case"mouseover":return Gl=tu(Gl,t,e,l,a,n),!0;case"pointerover":var i=n.pointerId;return Pn.set(i,tu(Pn.get(i)||null,t,e,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,In.set(i,tu(In.get(i)||null,t,e,l,a,n)),!0}return!1}function H0(t){var e=fa(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=v(l),e!==null){t.blockedOn=e,cm(t.priority,function(){if(l.tag===13){var a=Oe();a=nc(a);var n=Aa(l,a);n!==null&&_e(n,l,a),ff(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ci(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=sf(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);mc=a,l.target.dispatchEvent(a),mc=null}else return e=oa(l),e!==null&&U0(e),t.blockedOn=l,!1;e.shift()}return!0}function j0(t,e,l){Ci(t)&&l.delete(e)}function yg(){hf=!1,ql!==null&&Ci(ql)&&(ql=null),Yl!==null&&Ci(Yl)&&(Yl=null),Gl!==null&&Ci(Gl)&&(Gl=null),Pn.forEach(j0),In.forEach(j0)}function xi(t,e){t.blockedOn===e&&(t.blockedOn=null,hf||(hf=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,yg)))}var zi=null;function q0(t){zi!==t&&(zi=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){zi===t&&(zi=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(df(a||l)===null)continue;break}var i=oa(l);i!==null&&(t.splice(e,3),e-=3,sr(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function eu(t){function e(y){return xi(y,t)}ql!==null&&xi(ql,t),Yl!==null&&xi(Yl,t),Gl!==null&&xi(Gl,t),Pn.forEach(e),In.forEach(e);for(var l=0;l<wl.length;l++){var a=wl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<wl.length&&(l=wl[0],l.blockedOn===null);)H0(l),l.blockedOn===null&&wl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],o=n[oe]||null;if(typeof i=="function")o||q0(l);else if(o){var d=null;if(i&&i.hasAttribute("formAction")){if(n=i,o=i[oe]||null)d=o.formAction;else if(df(n)!==null)continue}else d=o.action;typeof d=="function"?l[a+1]=d:(l.splice(a,3),a-=3),q0(l)}}}function mf(t){this._internalRoot=t}Ri.prototype.render=mf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var l=e.current,a=Oe();M0(l,a,t,e,null,null)},Ri.prototype.unmount=mf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;M0(t.current,2,null,t,null,null),oi(),e[ra]=null}};function Ri(t){this._internalRoot=t}Ri.prototype.unstable_scheduleHydration=function(t){if(t){var e=If();t={blockedOn:null,target:t,priority:e};for(var l=0;l<wl.length&&e!==0&&e<wl[l].priority;l++);wl.splice(l,0,t),l===0&&H0(t)}};var Y0=c.version;if(Y0!=="19.1.0")throw Error(r(527,Y0,"19.1.0"));w.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=A(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var gg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Mi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mi.isDisabled&&Mi.supportsFiber)try{nn=Mi.inject(gg),ve=Mi}catch{}}return au.createRoot=function(t,e){if(!s(t))throw Error(r(299));var l=!1,a="",n=td,i=ed,o=ld,d=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=z0(t,1,!1,null,null,l,a,n,i,o,d,null),t[ra]=e.current,Jr(t),new mf(e)},au.hydrateRoot=function(t,e,l){if(!s(t))throw Error(r(299));var a=!1,n="",i=td,o=ed,d=ld,y=null,O=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(o=l.onCaughtError),l.onRecoverableError!==void 0&&(d=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(O=l.formState)),e=z0(t,1,!0,e,l??null,a,n,i,o,d,y,O),e.context=R0(null),l=e.current,a=Oe(),a=nc(a),n=El(a),n.callback=null,Ol(l,n,a),l=a,e.current.lanes=l,cn(e,l),$e(e),t[ra]=e.current,Jr(t),new Ri(e)},au.version="19.1.0",au}var J0;function xg(){if(J0)return vf.exports;J0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),vf.exports=Cg(),vf.exports}var zg=xg();const Rg=Sh(zg),W0=u=>u,Mg=()=>{let u=W0;return{configure(c){u=c},generate(c){return u(c)},reset(){u=W0}}},Dg=Mg();function ca(u,...c){const f=new URL(`https://mui.com/production-error/?code=${u}`);return c.forEach(r=>f.searchParams.append("args[]",r)),`Minified MUI error #${u}; visit ${f} for the full message.`}function tn(u){if(typeof u!="string")throw new Error(ca(7));return u.charAt(0).toUpperCase()+u.slice(1)}function Ah(u){var c,f,r="";if(typeof u=="string"||typeof u=="number")r+=u;else if(typeof u=="object")if(Array.isArray(u)){var s=u.length;for(c=0;c<s;c++)u[c]&&(f=Ah(u[c]))&&(r&&(r+=" "),r+=f)}else for(f in u)u[f]&&(r&&(r+=" "),r+=f);return r}function Ug(){for(var u,c,f=0,r="",s=arguments.length;f<s;f++)(u=arguments[f])&&(c=Ah(u))&&(r&&(r+=" "),r+=c);return r}function Ng(u,c,f=void 0){const r={};for(const s in u){const h=u[s];let v="",S=!0;for(let A=0;A<h.length;A+=1){const g=h[A];g&&(v+=(S===!0?"":" ")+c(g),S=!1,f&&f[g]&&(v+=" "+f[g]))}r[s]=v}return r}var Tf={exports:{}},Ct={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var F0;function Bg(){if(F0)return Ct;F0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),g=Symbol.for("react.suspense_list"),z=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),N=Symbol.for("react.view_transition"),Y=Symbol.for("react.client.reference");function q(_){if(typeof _=="object"&&_!==null){var X=_.$$typeof;switch(X){case u:switch(_=_.type,_){case f:case s:case r:case A:case g:case N:return _;default:switch(_=_&&_.$$typeof,_){case v:case S:case R:case z:return _;case h:return _;default:return X}}case c:return X}}}return Ct.ContextConsumer=h,Ct.ContextProvider=v,Ct.Element=u,Ct.ForwardRef=S,Ct.Fragment=f,Ct.Lazy=R,Ct.Memo=z,Ct.Portal=c,Ct.Profiler=s,Ct.StrictMode=r,Ct.Suspense=A,Ct.SuspenseList=g,Ct.isContextConsumer=function(_){return q(_)===h},Ct.isContextProvider=function(_){return q(_)===v},Ct.isElement=function(_){return typeof _=="object"&&_!==null&&_.$$typeof===u},Ct.isForwardRef=function(_){return q(_)===S},Ct.isFragment=function(_){return q(_)===f},Ct.isLazy=function(_){return q(_)===R},Ct.isMemo=function(_){return q(_)===z},Ct.isPortal=function(_){return q(_)===c},Ct.isProfiler=function(_){return q(_)===s},Ct.isStrictMode=function(_){return q(_)===r},Ct.isSuspense=function(_){return q(_)===A},Ct.isSuspenseList=function(_){return q(_)===g},Ct.isValidElementType=function(_){return typeof _=="string"||typeof _=="function"||_===f||_===s||_===r||_===A||_===g||typeof _=="object"&&_!==null&&(_.$$typeof===R||_.$$typeof===z||_.$$typeof===v||_.$$typeof===h||_.$$typeof===S||_.$$typeof===Y||_.getModuleId!==void 0)},Ct.typeOf=q,Ct}var P0;function Hg(){return P0||(P0=1,Tf.exports=Bg()),Tf.exports}var Eh=Hg();function yl(u){if(typeof u!="object"||u===null)return!1;const c=Object.getPrototypeOf(u);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(Symbol.toStringTag in u)&&!(Symbol.iterator in u)}function Oh(u){if(At.isValidElement(u)||Eh.isValidElementType(u)||!yl(u))return u;const c={};return Object.keys(u).forEach(f=>{c[f]=Oh(u[f])}),c}function xe(u,c,f={clone:!0}){const r=f.clone?{...u}:u;return yl(u)&&yl(c)&&Object.keys(c).forEach(s=>{At.isValidElement(c[s])||Eh.isValidElementType(c[s])?r[s]=c[s]:yl(c[s])&&Object.prototype.hasOwnProperty.call(u,s)&&yl(u[s])?r[s]=xe(u[s],c[s],f):f.clone?r[s]=yl(c[s])?Oh(c[s]):c[s]:r[s]=c[s]}),r}function ru(u,c){return c?xe(u,c,{clone:!1}):u}function jg(u,c){if(!u.containerQueries)return c;const f=Object.keys(c).filter(r=>r.startsWith("@container")).sort((r,s)=>{var v,S;const h=/min-width:\s*([0-9.]+)/;return+(((v=r.match(h))==null?void 0:v[1])||0)-+(((S=s.match(h))==null?void 0:S[1])||0)});return f.length?f.reduce((r,s)=>{const h=c[s];return delete r[s],r[s]=h,r},{...c}):c}function qg(u,c){return c==="@"||c.startsWith("@")&&(u.some(f=>c.startsWith(`@${f}`))||!!c.match(/^@\d/))}function Yg(u,c){const f=c.match(/^@([^/]+)?\/?(.+)?$/);if(!f)return null;const[,r,s]=f,h=Number.isNaN(+r)?r||0:+r;return u.containerQueries(s).up(h)}function Gg(u){const c=(h,v)=>h.replace("@media",v?`@container ${v}`:"@container");function f(h,v){h.up=(...S)=>c(u.breakpoints.up(...S),v),h.down=(...S)=>c(u.breakpoints.down(...S),v),h.between=(...S)=>c(u.breakpoints.between(...S),v),h.only=(...S)=>c(u.breakpoints.only(...S),v),h.not=(...S)=>{const A=c(u.breakpoints.not(...S),v);return A.includes("not all and")?A.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):A}}const r={},s=h=>(f(r,h),r);return f(s),{...u,containerQueries:s}}const wi={xs:0,sm:600,md:900,lg:1200,xl:1536},I0={keys:["xs","sm","md","lg","xl"],up:u=>`@media (min-width:${wi[u]}px)`},wg={containerQueries:u=>({up:c=>{let f=typeof c=="number"?c:wi[c]||c;return typeof f=="number"&&(f=`${f}px`),u?`@container ${u} (min-width:${f})`:`@container (min-width:${f})`}})};function gl(u,c,f){const r=u.theme||{};if(Array.isArray(c)){const h=r.breakpoints||I0;return c.reduce((v,S,A)=>(v[h.up(h.keys[A])]=f(c[A]),v),{})}if(typeof c=="object"){const h=r.breakpoints||I0;return Object.keys(c).reduce((v,S)=>{if(qg(h.keys,S)){const A=Yg(r.containerQueries?r:wg,S);A&&(v[A]=f(c[S],S))}else if(Object.keys(h.values||wi).includes(S)){const A=h.up(S);v[A]=f(c[S],S)}else{const A=S;v[A]=c[A]}return v},{})}return f(c)}function Xg(u={}){var f;return((f=u.keys)==null?void 0:f.reduce((r,s)=>{const h=u.up(s);return r[h]={},r},{}))||{}}function Qg(u,c){return u.reduce((f,r)=>{const s=f[r];return(!s||Object.keys(s).length===0)&&delete f[r],f},c)}function Xi(u,c,f=!0){if(!c||typeof c!="string")return null;if(u&&u.vars&&f){const r=`vars.${c}`.split(".").reduce((s,h)=>s&&s[h]?s[h]:null,u);if(r!=null)return r}return c.split(".").reduce((r,s)=>r&&r[s]!=null?r[s]:null,u)}function Yi(u,c,f,r=f){let s;return typeof u=="function"?s=u(f):Array.isArray(u)?s=u[f]||r:s=Xi(u,f)||r,c&&(s=c(s,r,u)),s}function Zt(u){const{prop:c,cssProperty:f=u.prop,themeKey:r,transform:s}=u,h=v=>{if(v[c]==null)return null;const S=v[c],A=v.theme,g=Xi(A,r)||{};return gl(v,S,R=>{let N=Yi(g,s,R);return R===N&&typeof R=="string"&&(N=Yi(g,s,`${c}${R==="default"?"":tn(R)}`,R)),f===!1?N:{[f]:N}})};return h.propTypes={},h.filterProps=[c],h}function Lg(u){const c={};return f=>(c[f]===void 0&&(c[f]=u(f)),c[f])}const Vg={m:"margin",p:"padding"},Zg={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},th={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Kg=Lg(u=>{if(u.length>2)if(th[u])u=th[u];else return[u];const[c,f]=u.split(""),r=Vg[c],s=Zg[f]||"";return Array.isArray(s)?s.map(h=>r+h):[r+s]}),Hf=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],jf=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Hf,...jf];function hu(u,c,f,r){const s=Xi(u,c,!0)??f;return typeof s=="number"||typeof s=="string"?h=>typeof h=="string"?h:typeof s=="string"?s.startsWith("var(")&&h===0?0:s.startsWith("var(")&&h===1?s:`calc(${h} * ${s})`:s*h:Array.isArray(s)?h=>{if(typeof h=="string")return h;const v=Math.abs(h),S=s[v];return h>=0?S:typeof S=="number"?-S:typeof S=="string"&&S.startsWith("var(")?`calc(-1 * ${S})`:`-${S}`}:typeof s=="function"?s:()=>{}}function qf(u){return hu(u,"spacing",8)}function mu(u,c){return typeof c=="string"||c==null?c:u(c)}function kg(u,c){return f=>u.reduce((r,s)=>(r[s]=mu(c,f),r),{})}function $g(u,c,f,r){if(!c.includes(f))return null;const s=Kg(f),h=kg(s,r),v=u[f];return gl(u,v,h)}function _h(u,c){const f=qf(u.theme);return Object.keys(u).map(r=>$g(u,c,r,f)).reduce(ru,{})}function Xt(u){return _h(u,Hf)}Xt.propTypes={};Xt.filterProps=Hf;function Qt(u){return _h(u,jf)}Qt.propTypes={};Qt.filterProps=jf;function Qi(...u){const c=u.reduce((r,s)=>(s.filterProps.forEach(h=>{r[h]=s}),r),{}),f=r=>Object.keys(r).reduce((s,h)=>c[h]?ru(s,c[h](r)):s,{});return f.propTypes={},f.filterProps=u.reduce((r,s)=>r.concat(s.filterProps),[]),f}function Ye(u){return typeof u!="number"?u:`${u}px solid`}function Ge(u,c){return Zt({prop:u,themeKey:"borders",transform:c})}const Jg=Ge("border",Ye),Wg=Ge("borderTop",Ye),Fg=Ge("borderRight",Ye),Pg=Ge("borderBottom",Ye),Ig=Ge("borderLeft",Ye),t1=Ge("borderColor"),e1=Ge("borderTopColor"),l1=Ge("borderRightColor"),a1=Ge("borderBottomColor"),n1=Ge("borderLeftColor"),u1=Ge("outline",Ye),i1=Ge("outlineColor"),Li=u=>{if(u.borderRadius!==void 0&&u.borderRadius!==null){const c=hu(u.theme,"shape.borderRadius",4),f=r=>({borderRadius:mu(c,r)});return gl(u,u.borderRadius,f)}return null};Li.propTypes={};Li.filterProps=["borderRadius"];Qi(Jg,Wg,Fg,Pg,Ig,t1,e1,l1,a1,n1,Li,u1,i1);const Vi=u=>{if(u.gap!==void 0&&u.gap!==null){const c=hu(u.theme,"spacing",8),f=r=>({gap:mu(c,r)});return gl(u,u.gap,f)}return null};Vi.propTypes={};Vi.filterProps=["gap"];const Zi=u=>{if(u.columnGap!==void 0&&u.columnGap!==null){const c=hu(u.theme,"spacing",8),f=r=>({columnGap:mu(c,r)});return gl(u,u.columnGap,f)}return null};Zi.propTypes={};Zi.filterProps=["columnGap"];const Ki=u=>{if(u.rowGap!==void 0&&u.rowGap!==null){const c=hu(u.theme,"spacing",8),f=r=>({rowGap:mu(c,r)});return gl(u,u.rowGap,f)}return null};Ki.propTypes={};Ki.filterProps=["rowGap"];const c1=Zt({prop:"gridColumn"}),r1=Zt({prop:"gridRow"}),f1=Zt({prop:"gridAutoFlow"}),o1=Zt({prop:"gridAutoColumns"}),s1=Zt({prop:"gridAutoRows"}),d1=Zt({prop:"gridTemplateColumns"}),h1=Zt({prop:"gridTemplateRows"}),m1=Zt({prop:"gridTemplateAreas"}),y1=Zt({prop:"gridArea"});Qi(Vi,Zi,Ki,c1,r1,f1,o1,s1,d1,h1,m1,y1);function Pa(u,c){return c==="grey"?c:u}const g1=Zt({prop:"color",themeKey:"palette",transform:Pa}),v1=Zt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Pa}),p1=Zt({prop:"backgroundColor",themeKey:"palette",transform:Pa});Qi(g1,v1,p1);function Ce(u){return u<=1&&u!==0?`${u*100}%`:u}const b1=Zt({prop:"width",transform:Ce}),Yf=u=>{if(u.maxWidth!==void 0&&u.maxWidth!==null){const c=f=>{var s,h,v,S,A;const r=((v=(h=(s=u.theme)==null?void 0:s.breakpoints)==null?void 0:h.values)==null?void 0:v[f])||wi[f];return r?((A=(S=u.theme)==null?void 0:S.breakpoints)==null?void 0:A.unit)!=="px"?{maxWidth:`${r}${u.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:Ce(f)}};return gl(u,u.maxWidth,c)}return null};Yf.filterProps=["maxWidth"];const S1=Zt({prop:"minWidth",transform:Ce}),T1=Zt({prop:"height",transform:Ce}),A1=Zt({prop:"maxHeight",transform:Ce}),E1=Zt({prop:"minHeight",transform:Ce});Zt({prop:"size",cssProperty:"width",transform:Ce});Zt({prop:"size",cssProperty:"height",transform:Ce});const O1=Zt({prop:"boxSizing"});Qi(b1,Yf,S1,T1,A1,E1,O1);const ki={border:{themeKey:"borders",transform:Ye},borderTop:{themeKey:"borders",transform:Ye},borderRight:{themeKey:"borders",transform:Ye},borderBottom:{themeKey:"borders",transform:Ye},borderLeft:{themeKey:"borders",transform:Ye},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Ye},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Li},color:{themeKey:"palette",transform:Pa},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Pa},backgroundColor:{themeKey:"palette",transform:Pa},p:{style:Qt},pt:{style:Qt},pr:{style:Qt},pb:{style:Qt},pl:{style:Qt},px:{style:Qt},py:{style:Qt},padding:{style:Qt},paddingTop:{style:Qt},paddingRight:{style:Qt},paddingBottom:{style:Qt},paddingLeft:{style:Qt},paddingX:{style:Qt},paddingY:{style:Qt},paddingInline:{style:Qt},paddingInlineStart:{style:Qt},paddingInlineEnd:{style:Qt},paddingBlock:{style:Qt},paddingBlockStart:{style:Qt},paddingBlockEnd:{style:Qt},m:{style:Xt},mt:{style:Xt},mr:{style:Xt},mb:{style:Xt},ml:{style:Xt},mx:{style:Xt},my:{style:Xt},margin:{style:Xt},marginTop:{style:Xt},marginRight:{style:Xt},marginBottom:{style:Xt},marginLeft:{style:Xt},marginX:{style:Xt},marginY:{style:Xt},marginInline:{style:Xt},marginInlineStart:{style:Xt},marginInlineEnd:{style:Xt},marginBlock:{style:Xt},marginBlockStart:{style:Xt},marginBlockEnd:{style:Xt},displayPrint:{cssProperty:!1,transform:u=>({"@media print":{display:u}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Vi},rowGap:{style:Ki},columnGap:{style:Zi},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ce},maxWidth:{style:Yf},minWidth:{transform:Ce},height:{transform:Ce},maxHeight:{transform:Ce},minHeight:{transform:Ce},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function _1(...u){const c=u.reduce((r,s)=>r.concat(Object.keys(s)),[]),f=new Set(c);return u.every(r=>f.size===Object.keys(r).length)}function C1(u,c){return typeof u=="function"?u(c):u}function x1(){function u(f,r,s,h){const v={[f]:r,theme:s},S=h[f];if(!S)return{[f]:r};const{cssProperty:A=f,themeKey:g,transform:z,style:R}=S;if(r==null)return null;if(g==="typography"&&r==="inherit")return{[f]:r};const N=Xi(s,g)||{};return R?R(v):gl(v,r,q=>{let _=Yi(N,z,q);return q===_&&typeof q=="string"&&(_=Yi(N,z,`${f}${q==="default"?"":tn(q)}`,q)),A===!1?_:{[A]:_}})}function c(f){const{sx:r,theme:s={}}=f||{};if(!r)return null;const h=s.unstable_sxConfig??ki;function v(S){let A=S;if(typeof S=="function")A=S(s);else if(typeof S!="object")return S;if(!A)return null;const g=Xg(s.breakpoints),z=Object.keys(g);let R=g;return Object.keys(A).forEach(N=>{const Y=C1(A[N],s);if(Y!=null)if(typeof Y=="object")if(h[N])R=ru(R,u(N,Y,s,h));else{const q=gl({theme:s},Y,_=>({[N]:_}));_1(q,Y)?R[N]=c({sx:Y,theme:s}):R=ru(R,q)}else R=ru(R,u(N,Y,s,h))}),jg(s,Qg(z,R))}return Array.isArray(r)?r.map(v):v(r)}return c}const en=x1();en.filterProps=["sx"];function Cf(){return Cf=Object.assign?Object.assign.bind():function(u){for(var c=1;c<arguments.length;c++){var f=arguments[c];for(var r in f)({}).hasOwnProperty.call(f,r)&&(u[r]=f[r])}return u},Cf.apply(null,arguments)}function z1(u){if(u.sheet)return u.sheet;for(var c=0;c<document.styleSheets.length;c++)if(document.styleSheets[c].ownerNode===u)return document.styleSheets[c]}function R1(u){var c=document.createElement("style");return c.setAttribute("data-emotion",u.key),u.nonce!==void 0&&c.setAttribute("nonce",u.nonce),c.appendChild(document.createTextNode("")),c.setAttribute("data-s",""),c}var M1=function(){function u(f){var r=this;this._insertTag=function(s){var h;r.tags.length===0?r.insertionPoint?h=r.insertionPoint.nextSibling:r.prepend?h=r.container.firstChild:h=r.before:h=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(s,h),r.tags.push(s)},this.isSpeedy=f.speedy===void 0?!0:f.speedy,this.tags=[],this.ctr=0,this.nonce=f.nonce,this.key=f.key,this.container=f.container,this.prepend=f.prepend,this.insertionPoint=f.insertionPoint,this.before=null}var c=u.prototype;return c.hydrate=function(r){r.forEach(this._insertTag)},c.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(R1(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var h=z1(s);try{h.insertRule(r,h.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(r));this.ctr++},c.flush=function(){this.tags.forEach(function(r){var s;return(s=r.parentNode)==null?void 0:s.removeChild(r)}),this.tags=[],this.ctr=0},u}(),re="-ms-",Gi="-moz-",vt="-webkit-",Ch="comm",Gf="rule",wf="decl",D1="@import",xh="@keyframes",U1="@layer",N1=Math.abs,$i=String.fromCharCode,B1=Object.assign;function H1(u,c){return ne(u,0)^45?(((c<<2^ne(u,0))<<2^ne(u,1))<<2^ne(u,2))<<2^ne(u,3):0}function zh(u){return u.trim()}function j1(u,c){return(u=c.exec(u))?u[0]:u}function pt(u,c,f){return u.replace(c,f)}function xf(u,c){return u.indexOf(c)}function ne(u,c){return u.charCodeAt(c)|0}function fu(u,c,f){return u.slice(c,f)}function We(u){return u.length}function Xf(u){return u.length}function Di(u,c){return c.push(u),u}function q1(u,c){return u.map(c).join("")}var Ji=1,ln=1,Rh=0,ye=0,$t=0,an="";function Wi(u,c,f,r,s,h,v){return{value:u,root:c,parent:f,type:r,props:s,children:h,line:Ji,column:ln,length:v,return:""}}function nu(u,c){return B1(Wi("",null,null,"",null,null,0),u,{length:-u.length},c)}function Y1(){return $t}function G1(){return $t=ye>0?ne(an,--ye):0,ln--,$t===10&&(ln=1,Ji--),$t}function ze(){return $t=ye<Rh?ne(an,ye++):0,ln++,$t===10&&(ln=1,Ji++),$t}function Pe(){return ne(an,ye)}function Bi(){return ye}function yu(u,c){return fu(an,u,c)}function ou(u){switch(u){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Mh(u){return Ji=ln=1,Rh=We(an=u),ye=0,[]}function Dh(u){return an="",u}function Hi(u){return zh(yu(ye-1,zf(u===91?u+2:u===40?u+1:u)))}function w1(u){for(;($t=Pe())&&$t<33;)ze();return ou(u)>2||ou($t)>3?"":" "}function X1(u,c){for(;--c&&ze()&&!($t<48||$t>102||$t>57&&$t<65||$t>70&&$t<97););return yu(u,Bi()+(c<6&&Pe()==32&&ze()==32))}function zf(u){for(;ze();)switch($t){case u:return ye;case 34:case 39:u!==34&&u!==39&&zf($t);break;case 40:u===41&&zf(u);break;case 92:ze();break}return ye}function Q1(u,c){for(;ze()&&u+$t!==57;)if(u+$t===84&&Pe()===47)break;return"/*"+yu(c,ye-1)+"*"+$i(u===47?u:ze())}function L1(u){for(;!ou(Pe());)ze();return yu(u,ye)}function V1(u){return Dh(ji("",null,null,null,[""],u=Mh(u),0,[0],u))}function ji(u,c,f,r,s,h,v,S,A){for(var g=0,z=0,R=v,N=0,Y=0,q=0,_=1,X=1,K=1,nt=0,Q="",k=s,G=h,F=r,J=Q;X;)switch(q=nt,nt=ze()){case 40:if(q!=108&&ne(J,R-1)==58){xf(J+=pt(Hi(nt),"&","&\f"),"&\f")!=-1&&(K=-1);break}case 34:case 39:case 91:J+=Hi(nt);break;case 9:case 10:case 13:case 32:J+=w1(q);break;case 92:J+=X1(Bi()-1,7);continue;case 47:switch(Pe()){case 42:case 47:Di(Z1(Q1(ze(),Bi()),c,f),A);break;default:J+="/"}break;case 123*_:S[g++]=We(J)*K;case 125*_:case 59:case 0:switch(nt){case 0:case 125:X=0;case 59+z:K==-1&&(J=pt(J,/\f/g,"")),Y>0&&We(J)-R&&Di(Y>32?lh(J+";",r,f,R-1):lh(pt(J," ","")+";",r,f,R-2),A);break;case 59:J+=";";default:if(Di(F=eh(J,c,f,g,z,s,S,Q,k=[],G=[],R),h),nt===123)if(z===0)ji(J,c,F,F,k,h,R,S,G);else switch(N===99&&ne(J,3)===110?100:N){case 100:case 108:case 109:case 115:ji(u,F,F,r&&Di(eh(u,F,F,0,0,s,S,Q,s,k=[],R),G),s,G,R,S,r?k:G);break;default:ji(J,F,F,F,[""],G,0,S,G)}}g=z=Y=0,_=K=1,Q=J="",R=v;break;case 58:R=1+We(J),Y=q;default:if(_<1){if(nt==123)--_;else if(nt==125&&_++==0&&G1()==125)continue}switch(J+=$i(nt),nt*_){case 38:K=z>0?1:(J+="\f",-1);break;case 44:S[g++]=(We(J)-1)*K,K=1;break;case 64:Pe()===45&&(J+=Hi(ze())),N=Pe(),z=R=We(Q=J+=L1(Bi())),nt++;break;case 45:q===45&&We(J)==2&&(_=0)}}return h}function eh(u,c,f,r,s,h,v,S,A,g,z){for(var R=s-1,N=s===0?h:[""],Y=Xf(N),q=0,_=0,X=0;q<r;++q)for(var K=0,nt=fu(u,R+1,R=N1(_=v[q])),Q=u;K<Y;++K)(Q=zh(_>0?N[K]+" "+nt:pt(nt,/&\f/g,N[K])))&&(A[X++]=Q);return Wi(u,c,f,s===0?Gf:S,A,g,z)}function Z1(u,c,f){return Wi(u,c,f,Ch,$i(Y1()),fu(u,2,-2),0)}function lh(u,c,f,r){return Wi(u,c,f,wf,fu(u,0,r),fu(u,r+1,-1),r)}function Ia(u,c){for(var f="",r=Xf(u),s=0;s<r;s++)f+=c(u[s],s,u,c)||"";return f}function K1(u,c,f,r){switch(u.type){case U1:if(u.children.length)break;case D1:case wf:return u.return=u.return||u.value;case Ch:return"";case xh:return u.return=u.value+"{"+Ia(u.children,r)+"}";case Gf:u.value=u.props.join(",")}return We(f=Ia(u.children,r))?u.return=u.value+"{"+f+"}":""}function k1(u){var c=Xf(u);return function(f,r,s,h){for(var v="",S=0;S<c;S++)v+=u[S](f,r,s,h)||"";return v}}function $1(u){return function(c){c.root||(c=c.return)&&u(c)}}function Uh(u){var c=Object.create(null);return function(f){return c[f]===void 0&&(c[f]=u(f)),c[f]}}var J1=function(c,f,r){for(var s=0,h=0;s=h,h=Pe(),s===38&&h===12&&(f[r]=1),!ou(h);)ze();return yu(c,ye)},W1=function(c,f){var r=-1,s=44;do switch(ou(s)){case 0:s===38&&Pe()===12&&(f[r]=1),c[r]+=J1(ye-1,f,r);break;case 2:c[r]+=Hi(s);break;case 4:if(s===44){c[++r]=Pe()===58?"&\f":"",f[r]=c[r].length;break}default:c[r]+=$i(s)}while(s=ze());return c},F1=function(c,f){return Dh(W1(Mh(c),f))},ah=new WeakMap,P1=function(c){if(!(c.type!=="rule"||!c.parent||c.length<1)){for(var f=c.value,r=c.parent,s=c.column===r.column&&c.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(c.props.length===1&&f.charCodeAt(0)!==58&&!ah.get(r))&&!s){ah.set(c,!0);for(var h=[],v=F1(f,h),S=r.props,A=0,g=0;A<v.length;A++)for(var z=0;z<S.length;z++,g++)c.props[g]=h[A]?v[A].replace(/&\f/g,S[z]):S[z]+" "+v[A]}}},I1=function(c){if(c.type==="decl"){var f=c.value;f.charCodeAt(0)===108&&f.charCodeAt(2)===98&&(c.return="",c.value="")}};function Nh(u,c){switch(H1(u,c)){case 5103:return vt+"print-"+u+u;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return vt+u+u;case 5349:case 4246:case 4810:case 6968:case 2756:return vt+u+Gi+u+re+u+u;case 6828:case 4268:return vt+u+re+u+u;case 6165:return vt+u+re+"flex-"+u+u;case 5187:return vt+u+pt(u,/(\w+).+(:[^]+)/,vt+"box-$1$2"+re+"flex-$1$2")+u;case 5443:return vt+u+re+"flex-item-"+pt(u,/flex-|-self/,"")+u;case 4675:return vt+u+re+"flex-line-pack"+pt(u,/align-content|flex-|-self/,"")+u;case 5548:return vt+u+re+pt(u,"shrink","negative")+u;case 5292:return vt+u+re+pt(u,"basis","preferred-size")+u;case 6060:return vt+"box-"+pt(u,"-grow","")+vt+u+re+pt(u,"grow","positive")+u;case 4554:return vt+pt(u,/([^-])(transform)/g,"$1"+vt+"$2")+u;case 6187:return pt(pt(pt(u,/(zoom-|grab)/,vt+"$1"),/(image-set)/,vt+"$1"),u,"")+u;case 5495:case 3959:return pt(u,/(image-set\([^]*)/,vt+"$1$`$1");case 4968:return pt(pt(u,/(.+:)(flex-)?(.*)/,vt+"box-pack:$3"+re+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+vt+u+u;case 4095:case 3583:case 4068:case 2532:return pt(u,/(.+)-inline(.+)/,vt+"$1$2")+u;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(We(u)-1-c>6)switch(ne(u,c+1)){case 109:if(ne(u,c+4)!==45)break;case 102:return pt(u,/(.+:)(.+)-([^]+)/,"$1"+vt+"$2-$3$1"+Gi+(ne(u,c+3)==108?"$3":"$2-$3"))+u;case 115:return~xf(u,"stretch")?Nh(pt(u,"stretch","fill-available"),c)+u:u}break;case 4949:if(ne(u,c+1)!==115)break;case 6444:switch(ne(u,We(u)-3-(~xf(u,"!important")&&10))){case 107:return pt(u,":",":"+vt)+u;case 101:return pt(u,/(.+:)([^;!]+)(;|!.+)?/,"$1"+vt+(ne(u,14)===45?"inline-":"")+"box$3$1"+vt+"$2$3$1"+re+"$2box$3")+u}break;case 5936:switch(ne(u,c+11)){case 114:return vt+u+re+pt(u,/[svh]\w+-[tblr]{2}/,"tb")+u;case 108:return vt+u+re+pt(u,/[svh]\w+-[tblr]{2}/,"tb-rl")+u;case 45:return vt+u+re+pt(u,/[svh]\w+-[tblr]{2}/,"lr")+u}return vt+u+re+u+u}return u}var tv=function(c,f,r,s){if(c.length>-1&&!c.return)switch(c.type){case wf:c.return=Nh(c.value,c.length);break;case xh:return Ia([nu(c,{value:pt(c.value,"@","@"+vt)})],s);case Gf:if(c.length)return q1(c.props,function(h){switch(j1(h,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Ia([nu(c,{props:[pt(h,/:(read-\w+)/,":"+Gi+"$1")]})],s);case"::placeholder":return Ia([nu(c,{props:[pt(h,/:(plac\w+)/,":"+vt+"input-$1")]}),nu(c,{props:[pt(h,/:(plac\w+)/,":"+Gi+"$1")]}),nu(c,{props:[pt(h,/:(plac\w+)/,re+"input-$1")]})],s)}return""})}},ev=[tv],lv=function(c){var f=c.key;if(f==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(_){var X=_.getAttribute("data-emotion");X.indexOf(" ")!==-1&&(document.head.appendChild(_),_.setAttribute("data-s",""))})}var s=c.stylisPlugins||ev,h={},v,S=[];v=c.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+f+' "]'),function(_){for(var X=_.getAttribute("data-emotion").split(" "),K=1;K<X.length;K++)h[X[K]]=!0;S.push(_)});var A,g=[P1,I1];{var z,R=[K1,$1(function(_){z.insert(_)})],N=k1(g.concat(s,R)),Y=function(X){return Ia(V1(X),N)};A=function(X,K,nt,Q){z=nt,Y(X?X+"{"+K.styles+"}":K.styles),Q&&(q.inserted[K.name]=!0)}}var q={key:f,sheet:new M1({key:f,container:v,nonce:c.nonce,speedy:c.speedy,prepend:c.prepend,insertionPoint:c.insertionPoint}),nonce:c.nonce,inserted:h,registered:{},insert:A};return q.sheet.hydrate(S),q},av=!0;function nv(u,c,f){var r="";return f.split(" ").forEach(function(s){u[s]!==void 0?c.push(u[s]+";"):s&&(r+=s+" ")}),r}var Bh=function(c,f,r){var s=c.key+"-"+f.name;(r===!1||av===!1)&&c.registered[s]===void 0&&(c.registered[s]=f.styles)},uv=function(c,f,r){Bh(c,f,r);var s=c.key+"-"+f.name;if(c.inserted[f.name]===void 0){var h=f;do c.insert(f===h?"."+s:"",h,c.sheet,!0),h=h.next;while(h!==void 0)}};function iv(u){for(var c=0,f,r=0,s=u.length;s>=4;++r,s-=4)f=u.charCodeAt(r)&255|(u.charCodeAt(++r)&255)<<8|(u.charCodeAt(++r)&255)<<16|(u.charCodeAt(++r)&255)<<24,f=(f&65535)*1540483477+((f>>>16)*59797<<16),f^=f>>>24,c=(f&65535)*1540483477+((f>>>16)*59797<<16)^(c&65535)*1540483477+((c>>>16)*59797<<16);switch(s){case 3:c^=(u.charCodeAt(r+2)&255)<<16;case 2:c^=(u.charCodeAt(r+1)&255)<<8;case 1:c^=u.charCodeAt(r)&255,c=(c&65535)*1540483477+((c>>>16)*59797<<16)}return c^=c>>>13,c=(c&65535)*1540483477+((c>>>16)*59797<<16),((c^c>>>15)>>>0).toString(36)}var cv={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},rv=/[A-Z]|^ms/g,fv=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Hh=function(c){return c.charCodeAt(1)===45},nh=function(c){return c!=null&&typeof c!="boolean"},Af=Uh(function(u){return Hh(u)?u:u.replace(rv,"-$&").toLowerCase()}),uh=function(c,f){switch(c){case"animation":case"animationName":if(typeof f=="string")return f.replace(fv,function(r,s,h){return Fe={name:s,styles:h,next:Fe},s})}return cv[c]!==1&&!Hh(c)&&typeof f=="number"&&f!==0?f+"px":f};function su(u,c,f){if(f==null)return"";var r=f;if(r.__emotion_styles!==void 0)return r;switch(typeof f){case"boolean":return"";case"object":{var s=f;if(s.anim===1)return Fe={name:s.name,styles:s.styles,next:Fe},s.name;var h=f;if(h.styles!==void 0){var v=h.next;if(v!==void 0)for(;v!==void 0;)Fe={name:v.name,styles:v.styles,next:Fe},v=v.next;var S=h.styles+";";return S}return ov(u,c,f)}case"function":{if(u!==void 0){var A=Fe,g=f(u);return Fe=A,su(u,c,g)}break}}var z=f;if(c==null)return z;var R=c[z];return R!==void 0?R:z}function ov(u,c,f){var r="";if(Array.isArray(f))for(var s=0;s<f.length;s++)r+=su(u,c,f[s])+";";else for(var h in f){var v=f[h];if(typeof v!="object"){var S=v;c!=null&&c[S]!==void 0?r+=h+"{"+c[S]+"}":nh(S)&&(r+=Af(h)+":"+uh(h,S)+";")}else if(Array.isArray(v)&&typeof v[0]=="string"&&(c==null||c[v[0]]===void 0))for(var A=0;A<v.length;A++)nh(v[A])&&(r+=Af(h)+":"+uh(h,v[A])+";");else{var g=su(u,c,v);switch(h){case"animation":case"animationName":{r+=Af(h)+":"+g+";";break}default:r+=h+"{"+g+"}"}}}return r}var ih=/label:\s*([^\s;{]+)\s*(;|$)/g,Fe;function jh(u,c,f){if(u.length===1&&typeof u[0]=="object"&&u[0]!==null&&u[0].styles!==void 0)return u[0];var r=!0,s="";Fe=void 0;var h=u[0];if(h==null||h.raw===void 0)r=!1,s+=su(f,c,h);else{var v=h;s+=v[0]}for(var S=1;S<u.length;S++)if(s+=su(f,c,u[S]),r){var A=h;s+=A[S]}ih.lastIndex=0;for(var g="",z;(z=ih.exec(s))!==null;)g+="-"+z[1];var R=iv(s)+g;return{name:R,styles:s,next:Fe}}var sv=function(c){return c()},dv=L0.useInsertionEffect?L0.useInsertionEffect:!1,hv=dv||sv,qh=At.createContext(typeof HTMLElement<"u"?lv({key:"css"}):null);qh.Provider;var mv=function(c){return At.forwardRef(function(f,r){var s=At.useContext(qh);return c(f,s,r)})},yv=At.createContext({}),gv=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,vv=Uh(function(u){return gv.test(u)||u.charCodeAt(0)===111&&u.charCodeAt(1)===110&&u.charCodeAt(2)<91}),pv=vv,bv=function(c){return c!=="theme"},ch=function(c){return typeof c=="string"&&c.charCodeAt(0)>96?pv:bv},rh=function(c,f,r){var s;if(f){var h=f.shouldForwardProp;s=c.__emotion_forwardProp&&h?function(v){return c.__emotion_forwardProp(v)&&h(v)}:h}return typeof s!="function"&&r&&(s=c.__emotion_forwardProp),s},Sv=function(c){var f=c.cache,r=c.serialized,s=c.isStringTag;return Bh(f,r,s),hv(function(){return uv(f,r,s)}),null},Tv=function u(c,f){var r=c.__emotion_real===c,s=r&&c.__emotion_base||c,h,v;f!==void 0&&(h=f.label,v=f.target);var S=rh(c,f,r),A=S||ch(s),g=!A("as");return function(){var z=arguments,R=r&&c.__emotion_styles!==void 0?c.__emotion_styles.slice(0):[];if(h!==void 0&&R.push("label:"+h+";"),z[0]==null||z[0].raw===void 0)R.push.apply(R,z);else{var N=z[0];R.push(N[0]);for(var Y=z.length,q=1;q<Y;q++)R.push(z[q],N[q])}var _=mv(function(X,K,nt){var Q=g&&X.as||s,k="",G=[],F=X;if(X.theme==null){F={};for(var J in X)F[J]=X[J];F.theme=At.useContext(yv)}typeof X.className=="string"?k=nv(K.registered,G,X.className):X.className!=null&&(k=X.className+" ");var bt=jh(R.concat(G),K.registered,F);k+=K.key+"-"+bt.name,v!==void 0&&(k+=" "+v);var Nt=g&&S===void 0?ch(Q):A,m={};for(var V in X)g&&V==="as"||Nt(V)&&(m[V]=X[V]);return m.className=k,nt&&(m.ref=nt),At.createElement(At.Fragment,null,At.createElement(Sv,{cache:K,serialized:bt,isStringTag:typeof Q=="string"}),At.createElement(Q,m))});return _.displayName=h!==void 0?h:"Styled("+(typeof s=="string"?s:s.displayName||s.name||"Component")+")",_.defaultProps=c.defaultProps,_.__emotion_real=_,_.__emotion_base=s,_.__emotion_styles=R,_.__emotion_forwardProp=S,Object.defineProperty(_,"toString",{value:function(){return"."+v}}),_.withComponent=function(X,K){var nt=u(X,Cf({},f,K,{shouldForwardProp:rh(_,K,!0)}));return nt.apply(void 0,R)},_}},Av=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Rf=Tv.bind(null);Av.forEach(function(u){Rf[u]=Rf(u)});function Ev(u,c){return Rf(u,c)}function Ov(u,c){Array.isArray(u.__emotion_styles)&&(u.__emotion_styles=c(u.__emotion_styles))}const fh=[];function oh(u){return fh[0]=u,jh(fh)}const _v=u=>{const c=Object.keys(u).map(f=>({key:f,val:u[f]}))||[];return c.sort((f,r)=>f.val-r.val),c.reduce((f,r)=>({...f,[r.key]:r.val}),{})};function Cv(u){const{values:c={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:f="px",step:r=5,...s}=u,h=_v(c),v=Object.keys(h);function S(N){return`@media (min-width:${typeof c[N]=="number"?c[N]:N}${f})`}function A(N){return`@media (max-width:${(typeof c[N]=="number"?c[N]:N)-r/100}${f})`}function g(N,Y){const q=v.indexOf(Y);return`@media (min-width:${typeof c[N]=="number"?c[N]:N}${f}) and (max-width:${(q!==-1&&typeof c[v[q]]=="number"?c[v[q]]:Y)-r/100}${f})`}function z(N){return v.indexOf(N)+1<v.length?g(N,v[v.indexOf(N)+1]):S(N)}function R(N){const Y=v.indexOf(N);return Y===0?S(v[1]):Y===v.length-1?A(v[Y]):g(N,v[v.indexOf(N)+1]).replace("@media","@media not all and")}return{keys:v,values:h,up:S,down:A,between:g,only:z,not:R,unit:f,...s}}const xv={borderRadius:4};function Yh(u=8,c=qf({spacing:u})){if(u.mui)return u;const f=(...r)=>(r.length===0?[1]:r).map(h=>{const v=c(h);return typeof v=="number"?`${v}px`:v}).join(" ");return f.mui=!0,f}function zv(u,c){var r;const f=this;if(f.vars){if(!((r=f.colorSchemes)!=null&&r[u])||typeof f.getColorSchemeSelector!="function")return{};let s=f.getColorSchemeSelector(u);return s==="&"?c:((s.includes("data-")||s.includes("."))&&(s=`*:where(${s.replace(/\s*&$/,"")}) &`),{[s]:c})}return f.palette.mode===u?c:{}}function Gh(u={},...c){const{breakpoints:f={},palette:r={},spacing:s,shape:h={},...v}=u,S=Cv(f),A=Yh(s);let g=xe({breakpoints:S,direction:"ltr",components:{},palette:{mode:"light",...r},spacing:A,shape:{...xv,...h}},v);return g=Gg(g),g.applyStyles=zv,g=c.reduce((z,R)=>xe(z,R),g),g.unstable_sxConfig={...ki,...v==null?void 0:v.unstable_sxConfig},g.unstable_sx=function(R){return en({sx:R,theme:this})},g}const Rv={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function wh(u,c,f="Mui"){const r=Rv[c];return r?`${f}-${r}`:`${Dg.generate(u)}-${c}`}function Mv(u,c,f="Mui"){const r={};return c.forEach(s=>{r[s]=wh(u,s,f)}),r}function Xh(u){const{variants:c,...f}=u,r={variants:c,style:oh(f),isProcessed:!0};return r.style===f||c&&c.forEach(s=>{typeof s.style!="function"&&(s.style=oh(s.style))}),r}const Dv=Gh();function Ef(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}function Uv(u){return u?(c,f)=>f[u]:null}function Nv(u,c,f){u.theme=jv(u.theme)?f:u.theme[c]||u.theme}function qi(u,c){const f=typeof c=="function"?c(u):c;if(Array.isArray(f))return f.flatMap(r=>qi(u,r));if(Array.isArray(f==null?void 0:f.variants)){let r;if(f.isProcessed)r=f.style;else{const{variants:s,...h}=f;r=h}return Qh(u,f.variants,[r])}return f!=null&&f.isProcessed?f.style:f}function Qh(u,c,f=[]){var s;let r;t:for(let h=0;h<c.length;h+=1){const v=c[h];if(typeof v.props=="function"){if(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),!v.props(r))continue}else for(const S in v.props)if(u[S]!==v.props[S]&&((s=u.ownerState)==null?void 0:s[S])!==v.props[S])continue t;typeof v.style=="function"?(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),f.push(v.style(r))):f.push(v.style)}return f}function Bv(u={}){const{themeId:c,defaultTheme:f=Dv,rootShouldForwardProp:r=Ef,slotShouldForwardProp:s=Ef}=u;function h(S){Nv(S,c,f)}return(S,A={})=>{Ov(S,G=>G.filter(F=>F!==en));const{name:g,slot:z,skipVariantsResolver:R,skipSx:N,overridesResolver:Y=Uv(Yv(z)),...q}=A,_=R!==void 0?R:z&&z!=="Root"&&z!=="root"||!1,X=N||!1;let K=Ef;z==="Root"||z==="root"?K=r:z?K=s:qv(S)&&(K=void 0);const nt=Ev(S,{shouldForwardProp:K,label:Hv(),...q}),Q=G=>{if(G.__emotion_real===G)return G;if(typeof G=="function")return function(J){return qi(J,G)};if(yl(G)){const F=Xh(G);return F.variants?function(bt){return qi(bt,F)}:F.style}return G},k=(...G)=>{const F=[],J=G.map(Q),bt=[];if(F.push(h),g&&Y&&bt.push(function(P){var M,w;const qt=(w=(M=P.theme.components)==null?void 0:M[g])==null?void 0:w.styleOverrides;if(!qt)return null;const xt={};for(const I in qt)xt[I]=qi(P,qt[I]);return Y(P,xt)}),g&&!_&&bt.push(function(P){var xt,M;const dt=P.theme,qt=(M=(xt=dt==null?void 0:dt.components)==null?void 0:xt[g])==null?void 0:M.variants;return qt?Qh(P,qt):null}),X||bt.push(en),Array.isArray(J[0])){const V=J.shift(),P=new Array(F.length).fill(""),dt=new Array(bt.length).fill("");let qt;qt=[...P,...V,...dt],qt.raw=[...P,...V.raw,...dt],F.unshift(qt)}const Nt=[...F,...J,...bt],m=nt(...Nt);return S.muiName&&(m.muiName=S.muiName),m};return nt.withConfig&&(k.withConfig=nt.withConfig),k}}function Hv(u,c){return void 0}function jv(u){for(const c in u)return!1;return!0}function qv(u){return typeof u=="string"&&u.charCodeAt(0)>96}function Yv(u){return u&&u.charAt(0).toLowerCase()+u.slice(1)}function Mf(u,c){const f={...c};for(const r in u)if(Object.prototype.hasOwnProperty.call(u,r)){const s=r;if(s==="components"||s==="slots")f[s]={...u[s],...f[s]};else if(s==="componentsProps"||s==="slotProps"){const h=u[s],v=c[s];if(!v)f[s]=h||{};else if(!h)f[s]=v;else{f[s]={...v};for(const S in h)if(Object.prototype.hasOwnProperty.call(h,S)){const A=S;f[s][A]=Mf(h[A],v[A])}}}else f[s]===void 0&&(f[s]=u[s])}return f}function Gv(u,c=Number.MIN_SAFE_INTEGER,f=Number.MAX_SAFE_INTEGER){return Math.max(c,Math.min(u,f))}function Qf(u,c=0,f=1){return Gv(u,c,f)}function wv(u){u=u.slice(1);const c=new RegExp(`.{1,${u.length>=6?2:1}}`,"g");let f=u.match(c);return f&&f[0].length===1&&(f=f.map(r=>r+r)),f?`rgb${f.length===4?"a":""}(${f.map((r,s)=>s<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function Ql(u){if(u.type)return u;if(u.charAt(0)==="#")return Ql(wv(u));const c=u.indexOf("("),f=u.substring(0,c);if(!["rgb","rgba","hsl","hsla","color"].includes(f))throw new Error(ca(9,u));let r=u.substring(c+1,u.length-1),s;if(f==="color"){if(r=r.split(" "),s=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(s))throw new Error(ca(10,s))}else r=r.split(",");return r=r.map(h=>parseFloat(h)),{type:f,values:r,colorSpace:s}}const Xv=u=>{const c=Ql(u);return c.values.slice(0,3).map((f,r)=>c.type.includes("hsl")&&r!==0?`${f}%`:f).join(" ")},iu=(u,c)=>{try{return Xv(u)}catch{return u}};function Fi(u){const{type:c,colorSpace:f}=u;let{values:r}=u;return c.includes("rgb")?r=r.map((s,h)=>h<3?parseInt(s,10):s):c.includes("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),c.includes("color")?r=`${f} ${r.join(" ")}`:r=`${r.join(", ")}`,`${c}(${r})`}function Lh(u){u=Ql(u);const{values:c}=u,f=c[0],r=c[1]/100,s=c[2]/100,h=r*Math.min(s,1-s),v=(g,z=(g+f/30)%12)=>s-h*Math.max(Math.min(z-3,9-z,1),-1);let S="rgb";const A=[Math.round(v(0)*255),Math.round(v(8)*255),Math.round(v(4)*255)];return u.type==="hsla"&&(S+="a",A.push(c[3])),Fi({type:S,values:A})}function Df(u){u=Ql(u);let c=u.type==="hsl"||u.type==="hsla"?Ql(Lh(u)).values:u.values;return c=c.map(f=>(u.type!=="color"&&(f/=255),f<=.03928?f/12.92:((f+.055)/1.055)**2.4)),Number((.2126*c[0]+.7152*c[1]+.0722*c[2]).toFixed(3))}function Qv(u,c){const f=Df(u),r=Df(c);return(Math.max(f,r)+.05)/(Math.min(f,r)+.05)}function Lv(u,c){return u=Ql(u),c=Qf(c),(u.type==="rgb"||u.type==="hsl")&&(u.type+="a"),u.type==="color"?u.values[3]=`/${c}`:u.values[3]=c,Fi(u)}function Ui(u,c,f){try{return Lv(u,c)}catch{return u}}function Lf(u,c){if(u=Ql(u),c=Qf(c),u.type.includes("hsl"))u.values[2]*=1-c;else if(u.type.includes("rgb")||u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]*=1-c;return Fi(u)}function Mt(u,c,f){try{return Lf(u,c)}catch{return u}}function Vf(u,c){if(u=Ql(u),c=Qf(c),u.type.includes("hsl"))u.values[2]+=(100-u.values[2])*c;else if(u.type.includes("rgb"))for(let f=0;f<3;f+=1)u.values[f]+=(255-u.values[f])*c;else if(u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]+=(1-u.values[f])*c;return Fi(u)}function Dt(u,c,f){try{return Vf(u,c)}catch{return u}}function Vv(u,c=.15){return Df(u)>.5?Lf(u,c):Vf(u,c)}function Ni(u,c,f){try{return Vv(u,c)}catch{return u}}const Zv=At.createContext(void 0);function Kv(u){const{theme:c,name:f,props:r}=u;if(!c||!c.components||!c.components[f])return r;const s=c.components[f];return s.defaultProps?Mf(s.defaultProps,r):!s.styleOverrides&&!s.variants?Mf(s,r):r}function kv({props:u,name:c}){const f=At.useContext(Zv);return Kv({props:u,name:c,theme:{components:f}})}const sh={theme:void 0};function $v(u){let c,f;return function(s){let h=c;return(h===void 0||s.theme!==f)&&(sh.theme=s.theme,h=Xh(u(sh)),c=h,f=s.theme),h}}function Jv(u=""){function c(...r){if(!r.length)return"";const s=r[0];return typeof s=="string"&&!s.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${u?`${u}-`:""}${s}${c(...r.slice(1))})`:`, ${s}`}return(r,...s)=>`var(--${u?`${u}-`:""}${r}${c(...s)})`}const dh=(u,c,f,r=[])=>{let s=u;c.forEach((h,v)=>{v===c.length-1?Array.isArray(s)?s[Number(h)]=f:s&&typeof s=="object"&&(s[h]=f):s&&typeof s=="object"&&(s[h]||(s[h]=r.includes(h)?[]:{}),s=s[h])})},Wv=(u,c,f)=>{function r(s,h=[],v=[]){Object.entries(s).forEach(([S,A])=>{(!f||f&&!f([...h,S]))&&A!=null&&(typeof A=="object"&&Object.keys(A).length>0?r(A,[...h,S],Array.isArray(A)?[...v,S]:v):c([...h,S],A,v))})}r(u)},Fv=(u,c)=>typeof c=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(r=>u.includes(r))||u[u.length-1].toLowerCase().includes("opacity")?c:`${c}px`:c;function Of(u,c){const{prefix:f,shouldSkipGeneratingVar:r}=c||{},s={},h={},v={};return Wv(u,(S,A,g)=>{if((typeof A=="string"||typeof A=="number")&&(!r||!r(S,A))){const z=`--${f?`${f}-`:""}${S.join("-")}`,R=Fv(S,A);Object.assign(s,{[z]:R}),dh(h,S,`var(${z})`,g),dh(v,S,`var(${z}, ${R})`,g)}},S=>S[0]==="vars"),{css:s,vars:h,varsWithDefaults:v}}function Pv(u,c={}){const{getSelector:f=X,disableCssColorScheme:r,colorSchemeSelector:s}=c,{colorSchemes:h={},components:v,defaultColorScheme:S="light",...A}=u,{vars:g,css:z,varsWithDefaults:R}=Of(A,c);let N=R;const Y={},{[S]:q,..._}=h;if(Object.entries(_||{}).forEach(([Q,k])=>{const{vars:G,css:F,varsWithDefaults:J}=Of(k,c);N=xe(N,J),Y[Q]={css:F,vars:G}}),q){const{css:Q,vars:k,varsWithDefaults:G}=Of(q,c);N=xe(N,G),Y[S]={css:Q,vars:k}}function X(Q,k){var F,J;let G=s;if(s==="class"&&(G=".%s"),s==="data"&&(G="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(G=`[${s}="%s"]`),Q){if(G==="media")return u.defaultColorScheme===Q?":root":{[`@media (prefers-color-scheme: ${((J=(F=h[Q])==null?void 0:F.palette)==null?void 0:J.mode)||Q})`]:{":root":k}};if(G)return u.defaultColorScheme===Q?`:root, ${G.replace("%s",String(Q))}`:G.replace("%s",String(Q))}return":root"}return{vars:N,generateThemeVars:()=>{let Q={...g};return Object.entries(Y).forEach(([,{vars:k}])=>{Q=xe(Q,k)}),Q},generateStyleSheets:()=>{var bt,Nt;const Q=[],k=u.defaultColorScheme||"light";function G(m,V){Object.keys(V).length&&Q.push(typeof m=="string"?{[m]:{...V}}:m)}G(f(void 0,{...z}),z);const{[k]:F,...J}=Y;if(F){const{css:m}=F,V=(Nt=(bt=h[k])==null?void 0:bt.palette)==null?void 0:Nt.mode,P=!r&&V?{colorScheme:V,...m}:{...m};G(f(k,{...P}),P)}return Object.entries(J).forEach(([m,{css:V}])=>{var qt,xt;const P=(xt=(qt=h[m])==null?void 0:qt.palette)==null?void 0:xt.mode,dt=!r&&P?{colorScheme:P,...V}:{...V};G(f(m,{...dt}),dt)}),Q}}}function Iv(u){return function(f){return u==="media"?`@media (prefers-color-scheme: ${f})`:u?u.startsWith("data-")&&!u.includes("%s")?`[${u}="${f}"] &`:u==="class"?`.${f} &`:u==="data"?`[data-${f}] &`:`${u.replace("%s",f)} &`:"&"}}const du={black:"#000",white:"#fff"},tp={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},ka={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},$a={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},uu={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Ja={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Wa={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Fa={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function Vh(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:du.white,default:du.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const ep=Vh();function Zh(){return{text:{primary:du.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:du.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const hh=Zh();function mh(u,c,f,r){const s=r.light||r,h=r.dark||r*1.5;u[c]||(u.hasOwnProperty(f)?u[c]=u[f]:c==="light"?u.light=Vf(u.main,s):c==="dark"&&(u.dark=Lf(u.main,h)))}function lp(u="light"){return u==="dark"?{main:Ja[200],light:Ja[50],dark:Ja[400]}:{main:Ja[700],light:Ja[400],dark:Ja[800]}}function ap(u="light"){return u==="dark"?{main:ka[200],light:ka[50],dark:ka[400]}:{main:ka[500],light:ka[300],dark:ka[700]}}function np(u="light"){return u==="dark"?{main:$a[500],light:$a[300],dark:$a[700]}:{main:$a[700],light:$a[400],dark:$a[800]}}function up(u="light"){return u==="dark"?{main:Wa[400],light:Wa[300],dark:Wa[700]}:{main:Wa[700],light:Wa[500],dark:Wa[900]}}function ip(u="light"){return u==="dark"?{main:Fa[400],light:Fa[300],dark:Fa[700]}:{main:Fa[800],light:Fa[500],dark:Fa[900]}}function cp(u="light"){return u==="dark"?{main:uu[400],light:uu[300],dark:uu[700]}:{main:"#ed6c02",light:uu[500],dark:uu[900]}}function Zf(u){const{mode:c="light",contrastThreshold:f=3,tonalOffset:r=.2,...s}=u,h=u.primary||lp(c),v=u.secondary||ap(c),S=u.error||np(c),A=u.info||up(c),g=u.success||ip(c),z=u.warning||cp(c);function R(_){return Qv(_,hh.text.primary)>=f?hh.text.primary:ep.text.primary}const N=({color:_,name:X,mainShade:K=500,lightShade:nt=300,darkShade:Q=700})=>{if(_={..._},!_.main&&_[K]&&(_.main=_[K]),!_.hasOwnProperty("main"))throw new Error(ca(11,X?` (${X})`:"",K));if(typeof _.main!="string")throw new Error(ca(12,X?` (${X})`:"",JSON.stringify(_.main)));return mh(_,"light",nt,r),mh(_,"dark",Q,r),_.contrastText||(_.contrastText=R(_.main)),_};let Y;return c==="light"?Y=Vh():c==="dark"&&(Y=Zh()),xe({common:{...du},mode:c,primary:N({color:h,name:"primary"}),secondary:N({color:v,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:N({color:S,name:"error"}),warning:N({color:z,name:"warning"}),info:N({color:A,name:"info"}),success:N({color:g,name:"success"}),grey:tp,contrastThreshold:f,getContrastText:R,augmentColor:N,tonalOffset:r,...Y},s)}function rp(u){const c={};return Object.entries(u).forEach(r=>{const[s,h]=r;typeof h=="object"&&(c[s]=`${h.fontStyle?`${h.fontStyle} `:""}${h.fontVariant?`${h.fontVariant} `:""}${h.fontWeight?`${h.fontWeight} `:""}${h.fontStretch?`${h.fontStretch} `:""}${h.fontSize||""}${h.lineHeight?`/${h.lineHeight} `:""}${h.fontFamily||""}`)}),c}function fp(u,c){return{toolbar:{minHeight:56,[u.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[u.up("sm")]:{minHeight:64}},...c}}function op(u){return Math.round(u*1e5)/1e5}const yh={textTransform:"uppercase"},gh='"Roboto", "Helvetica", "Arial", sans-serif';function sp(u,c){const{fontFamily:f=gh,fontSize:r=14,fontWeightLight:s=300,fontWeightRegular:h=400,fontWeightMedium:v=500,fontWeightBold:S=700,htmlFontSize:A=16,allVariants:g,pxToRem:z,...R}=typeof c=="function"?c(u):c,N=r/14,Y=z||(X=>`${X/A*N}rem`),q=(X,K,nt,Q,k)=>({fontFamily:f,fontWeight:X,fontSize:Y(K),lineHeight:nt,...f===gh?{letterSpacing:`${op(Q/K)}em`}:{},...k,...g}),_={h1:q(s,96,1.167,-1.5),h2:q(s,60,1.2,-.5),h3:q(h,48,1.167,0),h4:q(h,34,1.235,.25),h5:q(h,24,1.334,0),h6:q(v,20,1.6,.15),subtitle1:q(h,16,1.75,.15),subtitle2:q(v,14,1.57,.1),body1:q(h,16,1.5,.15),body2:q(h,14,1.43,.15),button:q(v,14,1.75,.4,yh),caption:q(h,12,1.66,.4),overline:q(h,12,2.66,1,yh),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return xe({htmlFontSize:A,pxToRem:Y,fontFamily:f,fontSize:r,fontWeightLight:s,fontWeightRegular:h,fontWeightMedium:v,fontWeightBold:S,..._},R,{clone:!1})}const dp=.2,hp=.14,mp=.12;function jt(...u){return[`${u[0]}px ${u[1]}px ${u[2]}px ${u[3]}px rgba(0,0,0,${dp})`,`${u[4]}px ${u[5]}px ${u[6]}px ${u[7]}px rgba(0,0,0,${hp})`,`${u[8]}px ${u[9]}px ${u[10]}px ${u[11]}px rgba(0,0,0,${mp})`].join(",")}const yp=["none",jt(0,2,1,-1,0,1,1,0,0,1,3,0),jt(0,3,1,-2,0,2,2,0,0,1,5,0),jt(0,3,3,-2,0,3,4,0,0,1,8,0),jt(0,2,4,-1,0,4,5,0,0,1,10,0),jt(0,3,5,-1,0,5,8,0,0,1,14,0),jt(0,3,5,-1,0,6,10,0,0,1,18,0),jt(0,4,5,-2,0,7,10,1,0,2,16,1),jt(0,5,5,-3,0,8,10,1,0,3,14,2),jt(0,5,6,-3,0,9,12,1,0,3,16,2),jt(0,6,6,-3,0,10,14,1,0,4,18,3),jt(0,6,7,-4,0,11,15,1,0,4,20,3),jt(0,7,8,-4,0,12,17,2,0,5,22,4),jt(0,7,8,-4,0,13,19,2,0,5,24,4),jt(0,7,9,-4,0,14,21,2,0,5,26,4),jt(0,8,9,-5,0,15,22,2,0,6,28,5),jt(0,8,10,-5,0,16,24,2,0,6,30,5),jt(0,8,11,-5,0,17,26,2,0,6,32,5),jt(0,9,11,-5,0,18,28,2,0,7,34,6),jt(0,9,12,-6,0,19,29,2,0,7,36,6),jt(0,10,13,-6,0,20,31,3,0,8,38,7),jt(0,10,13,-6,0,21,33,3,0,8,40,7),jt(0,10,14,-6,0,22,35,3,0,8,42,7),jt(0,11,14,-7,0,23,36,3,0,9,44,8),jt(0,11,15,-7,0,24,38,3,0,9,46,8)],gp={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},vp={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function vh(u){return`${Math.round(u)}ms`}function pp(u){if(!u)return 0;const c=u/36;return Math.min(Math.round((4+15*c**.25+c/5)*10),3e3)}function bp(u){const c={...gp,...u.easing},f={...vp,...u.duration};return{getAutoHeightDuration:pp,create:(s=["all"],h={})=>{const{duration:v=f.standard,easing:S=c.easeInOut,delay:A=0,...g}=h;return(Array.isArray(s)?s:[s]).map(z=>`${z} ${typeof v=="string"?v:vh(v)} ${S} ${typeof A=="string"?A:vh(A)}`).join(",")},...u,easing:c,duration:f}}const Sp={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Tp(u){return yl(u)||typeof u>"u"||typeof u=="string"||typeof u=="boolean"||typeof u=="number"||Array.isArray(u)}function Kh(u={}){const c={...u};function f(r){const s=Object.entries(r);for(let h=0;h<s.length;h++){const[v,S]=s[h];!Tp(S)||v.startsWith("unstable_")?delete r[v]:yl(S)&&(r[v]={...S},f(r[v]))}}return f(c),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(c,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Uf(u={},...c){const{breakpoints:f,mixins:r={},spacing:s,palette:h={},transitions:v={},typography:S={},shape:A,...g}=u;if(u.vars&&u.generateThemeVars===void 0)throw new Error(ca(20));const z=Zf(h),R=Gh(u);let N=xe(R,{mixins:fp(R.breakpoints,r),palette:z,shadows:yp.slice(),typography:sp(z,S),transitions:bp(v),zIndex:{...Sp}});return N=xe(N,g),N=c.reduce((Y,q)=>xe(Y,q),N),N.unstable_sxConfig={...ki,...g==null?void 0:g.unstable_sxConfig},N.unstable_sx=function(q){return en({sx:q,theme:this})},N.toRuntimeSource=Kh,N}function Ap(u){let c;return u<1?c=5.11916*u**2:c=4.5*Math.log(u+1)+2,Math.round(c*10)/1e3}const Ep=[...Array(25)].map((u,c)=>{if(c===0)return"none";const f=Ap(c);return`linear-gradient(rgba(255 255 255 / ${f}), rgba(255 255 255 / ${f}))`});function kh(u){return{inputPlaceholder:u==="dark"?.5:.42,inputUnderline:u==="dark"?.7:.42,switchTrackDisabled:u==="dark"?.2:.12,switchTrack:u==="dark"?.3:.38}}function $h(u){return u==="dark"?Ep:[]}function Op(u){const{palette:c={mode:"light"},opacity:f,overlays:r,...s}=u,h=Zf(c);return{palette:h,opacity:{...kh(h.mode),...f},overlays:r||$h(h.mode),...s}}function _p(u){var c;return!!u[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!u[0].match(/sxConfig$/)||u[0]==="palette"&&!!((c=u[1])!=null&&c.match(/(mode|contrastThreshold|tonalOffset)/))}const Cp=u=>[...[...Array(25)].map((c,f)=>`--${u?`${u}-`:""}overlays-${f}`),`--${u?`${u}-`:""}palette-AppBar-darkBg`,`--${u?`${u}-`:""}palette-AppBar-darkColor`],xp=u=>(c,f)=>{const r=u.rootSelector||":root",s=u.colorSchemeSelector;let h=s;if(s==="class"&&(h=".%s"),s==="data"&&(h="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(h=`[${s}="%s"]`),u.defaultColorScheme===c){if(c==="dark"){const v={};return Cp(u.cssVarPrefix).forEach(S=>{v[S]=f[S],delete f[S]}),h==="media"?{[r]:f,"@media (prefers-color-scheme: dark)":{[r]:v}}:h?{[h.replace("%s",c)]:v,[`${r}, ${h.replace("%s",c)}`]:f}:{[r]:{...f,...v}}}if(h&&h!=="media")return`${r}, ${h.replace("%s",String(c))}`}else if(c){if(h==="media")return{[`@media (prefers-color-scheme: ${String(c)})`]:{[r]:f}};if(h)return h.replace("%s",String(c))}return r};function zp(u,c){c.forEach(f=>{u[f]||(u[f]={})})}function D(u,c,f){!u[c]&&f&&(u[c]=f)}function cu(u){return typeof u!="string"||!u.startsWith("hsl")?u:Lh(u)}function ml(u,c){`${c}Channel`in u||(u[`${c}Channel`]=iu(cu(u[c])))}function Rp(u){return typeof u=="number"?`${u}px`:typeof u=="string"||typeof u=="function"||Array.isArray(u)?u:"8px"}const Je=u=>{try{return u()}catch{}},Mp=(u="mui")=>Jv(u);function _f(u,c,f,r){if(!c)return;c=c===!0?{}:c;const s=r==="dark"?"dark":"light";if(!f){u[r]=Op({...c,palette:{mode:s,...c==null?void 0:c.palette}});return}const{palette:h,...v}=Uf({...f,palette:{mode:s,...c==null?void 0:c.palette}});return u[r]={...c,palette:h,opacity:{...kh(s),...c==null?void 0:c.opacity},overlays:(c==null?void 0:c.overlays)||$h(s)},v}function Dp(u={},...c){const{colorSchemes:f={light:!0},defaultColorScheme:r,disableCssColorScheme:s=!1,cssVarPrefix:h="mui",shouldSkipGeneratingVar:v=_p,colorSchemeSelector:S=f.light&&f.dark?"media":void 0,rootSelector:A=":root",...g}=u,z=Object.keys(f)[0],R=r||(f.light&&z!=="light"?"light":z),N=Mp(h),{[R]:Y,light:q,dark:_,...X}=f,K={...X};let nt=Y;if((R==="dark"&&!("dark"in f)||R==="light"&&!("light"in f))&&(nt=!0),!nt)throw new Error(ca(21,R));const Q=_f(K,nt,g,R);q&&!K.light&&_f(K,q,void 0,"light"),_&&!K.dark&&_f(K,_,void 0,"dark");let k={defaultColorScheme:R,...Q,cssVarPrefix:h,colorSchemeSelector:S,rootSelector:A,getCssVar:N,colorSchemes:K,font:{...rp(Q.typography),...Q.font},spacing:Rp(g.spacing)};Object.keys(k.colorSchemes).forEach(Nt=>{const m=k.colorSchemes[Nt].palette,V=P=>{const dt=P.split("-"),qt=dt[1],xt=dt[2];return N(P,m[qt][xt])};if(m.mode==="light"&&(D(m.common,"background","#fff"),D(m.common,"onBackground","#000")),m.mode==="dark"&&(D(m.common,"background","#000"),D(m.common,"onBackground","#fff")),zp(m,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),m.mode==="light"){D(m.Alert,"errorColor",Mt(m.error.light,.6)),D(m.Alert,"infoColor",Mt(m.info.light,.6)),D(m.Alert,"successColor",Mt(m.success.light,.6)),D(m.Alert,"warningColor",Mt(m.warning.light,.6)),D(m.Alert,"errorFilledBg",V("palette-error-main")),D(m.Alert,"infoFilledBg",V("palette-info-main")),D(m.Alert,"successFilledBg",V("palette-success-main")),D(m.Alert,"warningFilledBg",V("palette-warning-main")),D(m.Alert,"errorFilledColor",Je(()=>m.getContrastText(m.error.main))),D(m.Alert,"infoFilledColor",Je(()=>m.getContrastText(m.info.main))),D(m.Alert,"successFilledColor",Je(()=>m.getContrastText(m.success.main))),D(m.Alert,"warningFilledColor",Je(()=>m.getContrastText(m.warning.main))),D(m.Alert,"errorStandardBg",Dt(m.error.light,.9)),D(m.Alert,"infoStandardBg",Dt(m.info.light,.9)),D(m.Alert,"successStandardBg",Dt(m.success.light,.9)),D(m.Alert,"warningStandardBg",Dt(m.warning.light,.9)),D(m.Alert,"errorIconColor",V("palette-error-main")),D(m.Alert,"infoIconColor",V("palette-info-main")),D(m.Alert,"successIconColor",V("palette-success-main")),D(m.Alert,"warningIconColor",V("palette-warning-main")),D(m.AppBar,"defaultBg",V("palette-grey-100")),D(m.Avatar,"defaultBg",V("palette-grey-400")),D(m.Button,"inheritContainedBg",V("palette-grey-300")),D(m.Button,"inheritContainedHoverBg",V("palette-grey-A100")),D(m.Chip,"defaultBorder",V("palette-grey-400")),D(m.Chip,"defaultAvatarColor",V("palette-grey-700")),D(m.Chip,"defaultIconColor",V("palette-grey-700")),D(m.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),D(m.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),D(m.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),D(m.LinearProgress,"primaryBg",Dt(m.primary.main,.62)),D(m.LinearProgress,"secondaryBg",Dt(m.secondary.main,.62)),D(m.LinearProgress,"errorBg",Dt(m.error.main,.62)),D(m.LinearProgress,"infoBg",Dt(m.info.main,.62)),D(m.LinearProgress,"successBg",Dt(m.success.main,.62)),D(m.LinearProgress,"warningBg",Dt(m.warning.main,.62)),D(m.Skeleton,"bg",`rgba(${V("palette-text-primaryChannel")} / 0.11)`),D(m.Slider,"primaryTrack",Dt(m.primary.main,.62)),D(m.Slider,"secondaryTrack",Dt(m.secondary.main,.62)),D(m.Slider,"errorTrack",Dt(m.error.main,.62)),D(m.Slider,"infoTrack",Dt(m.info.main,.62)),D(m.Slider,"successTrack",Dt(m.success.main,.62)),D(m.Slider,"warningTrack",Dt(m.warning.main,.62));const P=Ni(m.background.default,.8);D(m.SnackbarContent,"bg",P),D(m.SnackbarContent,"color",Je(()=>m.getContrastText(P))),D(m.SpeedDialAction,"fabHoverBg",Ni(m.background.paper,.15)),D(m.StepConnector,"border",V("palette-grey-400")),D(m.StepContent,"border",V("palette-grey-400")),D(m.Switch,"defaultColor",V("palette-common-white")),D(m.Switch,"defaultDisabledColor",V("palette-grey-100")),D(m.Switch,"primaryDisabledColor",Dt(m.primary.main,.62)),D(m.Switch,"secondaryDisabledColor",Dt(m.secondary.main,.62)),D(m.Switch,"errorDisabledColor",Dt(m.error.main,.62)),D(m.Switch,"infoDisabledColor",Dt(m.info.main,.62)),D(m.Switch,"successDisabledColor",Dt(m.success.main,.62)),D(m.Switch,"warningDisabledColor",Dt(m.warning.main,.62)),D(m.TableCell,"border",Dt(Ui(m.divider,1),.88)),D(m.Tooltip,"bg",Ui(m.grey[700],.92))}if(m.mode==="dark"){D(m.Alert,"errorColor",Dt(m.error.light,.6)),D(m.Alert,"infoColor",Dt(m.info.light,.6)),D(m.Alert,"successColor",Dt(m.success.light,.6)),D(m.Alert,"warningColor",Dt(m.warning.light,.6)),D(m.Alert,"errorFilledBg",V("palette-error-dark")),D(m.Alert,"infoFilledBg",V("palette-info-dark")),D(m.Alert,"successFilledBg",V("palette-success-dark")),D(m.Alert,"warningFilledBg",V("palette-warning-dark")),D(m.Alert,"errorFilledColor",Je(()=>m.getContrastText(m.error.dark))),D(m.Alert,"infoFilledColor",Je(()=>m.getContrastText(m.info.dark))),D(m.Alert,"successFilledColor",Je(()=>m.getContrastText(m.success.dark))),D(m.Alert,"warningFilledColor",Je(()=>m.getContrastText(m.warning.dark))),D(m.Alert,"errorStandardBg",Mt(m.error.light,.9)),D(m.Alert,"infoStandardBg",Mt(m.info.light,.9)),D(m.Alert,"successStandardBg",Mt(m.success.light,.9)),D(m.Alert,"warningStandardBg",Mt(m.warning.light,.9)),D(m.Alert,"errorIconColor",V("palette-error-main")),D(m.Alert,"infoIconColor",V("palette-info-main")),D(m.Alert,"successIconColor",V("palette-success-main")),D(m.Alert,"warningIconColor",V("palette-warning-main")),D(m.AppBar,"defaultBg",V("palette-grey-900")),D(m.AppBar,"darkBg",V("palette-background-paper")),D(m.AppBar,"darkColor",V("palette-text-primary")),D(m.Avatar,"defaultBg",V("palette-grey-600")),D(m.Button,"inheritContainedBg",V("palette-grey-800")),D(m.Button,"inheritContainedHoverBg",V("palette-grey-700")),D(m.Chip,"defaultBorder",V("palette-grey-700")),D(m.Chip,"defaultAvatarColor",V("palette-grey-300")),D(m.Chip,"defaultIconColor",V("palette-grey-300")),D(m.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),D(m.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),D(m.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),D(m.LinearProgress,"primaryBg",Mt(m.primary.main,.5)),D(m.LinearProgress,"secondaryBg",Mt(m.secondary.main,.5)),D(m.LinearProgress,"errorBg",Mt(m.error.main,.5)),D(m.LinearProgress,"infoBg",Mt(m.info.main,.5)),D(m.LinearProgress,"successBg",Mt(m.success.main,.5)),D(m.LinearProgress,"warningBg",Mt(m.warning.main,.5)),D(m.Skeleton,"bg",`rgba(${V("palette-text-primaryChannel")} / 0.13)`),D(m.Slider,"primaryTrack",Mt(m.primary.main,.5)),D(m.Slider,"secondaryTrack",Mt(m.secondary.main,.5)),D(m.Slider,"errorTrack",Mt(m.error.main,.5)),D(m.Slider,"infoTrack",Mt(m.info.main,.5)),D(m.Slider,"successTrack",Mt(m.success.main,.5)),D(m.Slider,"warningTrack",Mt(m.warning.main,.5));const P=Ni(m.background.default,.98);D(m.SnackbarContent,"bg",P),D(m.SnackbarContent,"color",Je(()=>m.getContrastText(P))),D(m.SpeedDialAction,"fabHoverBg",Ni(m.background.paper,.15)),D(m.StepConnector,"border",V("palette-grey-600")),D(m.StepContent,"border",V("palette-grey-600")),D(m.Switch,"defaultColor",V("palette-grey-300")),D(m.Switch,"defaultDisabledColor",V("palette-grey-600")),D(m.Switch,"primaryDisabledColor",Mt(m.primary.main,.55)),D(m.Switch,"secondaryDisabledColor",Mt(m.secondary.main,.55)),D(m.Switch,"errorDisabledColor",Mt(m.error.main,.55)),D(m.Switch,"infoDisabledColor",Mt(m.info.main,.55)),D(m.Switch,"successDisabledColor",Mt(m.success.main,.55)),D(m.Switch,"warningDisabledColor",Mt(m.warning.main,.55)),D(m.TableCell,"border",Mt(Ui(m.divider,1),.68)),D(m.Tooltip,"bg",Ui(m.grey[700],.92))}ml(m.background,"default"),ml(m.background,"paper"),ml(m.common,"background"),ml(m.common,"onBackground"),ml(m,"divider"),Object.keys(m).forEach(P=>{const dt=m[P];P!=="tonalOffset"&&dt&&typeof dt=="object"&&(dt.main&&D(m[P],"mainChannel",iu(cu(dt.main))),dt.light&&D(m[P],"lightChannel",iu(cu(dt.light))),dt.dark&&D(m[P],"darkChannel",iu(cu(dt.dark))),dt.contrastText&&D(m[P],"contrastTextChannel",iu(cu(dt.contrastText))),P==="text"&&(ml(m[P],"primary"),ml(m[P],"secondary")),P==="action"&&(dt.active&&ml(m[P],"active"),dt.selected&&ml(m[P],"selected")))})}),k=c.reduce((Nt,m)=>xe(Nt,m),k);const G={prefix:h,disableCssColorScheme:s,shouldSkipGeneratingVar:v,getSelector:xp(k)},{vars:F,generateThemeVars:J,generateStyleSheets:bt}=Pv(k,G);return k.vars=F,Object.entries(k.colorSchemes[k.defaultColorScheme]).forEach(([Nt,m])=>{k[Nt]=m}),k.generateThemeVars=J,k.generateStyleSheets=bt,k.generateSpacing=function(){return Yh(g.spacing,qf(this))},k.getColorSchemeSelector=Iv(S),k.spacing=k.generateSpacing(),k.shouldSkipGeneratingVar=v,k.unstable_sxConfig={...ki,...g==null?void 0:g.unstable_sxConfig},k.unstable_sx=function(m){return en({sx:m,theme:this})},k.toRuntimeSource=Kh,k}function ph(u,c,f){u.colorSchemes&&f&&(u.colorSchemes[c]={...f!==!0&&f,palette:Zf({...f===!0?{}:f.palette,mode:c})})}function Up(u={},...c){const{palette:f,cssVariables:r=!1,colorSchemes:s=f?void 0:{light:!0},defaultColorScheme:h=f==null?void 0:f.mode,...v}=u,S=h||"light",A=s==null?void 0:s[S],g={...s,...f?{[S]:{...typeof A!="boolean"&&A,palette:f}}:void 0};if(r===!1){if(!("colorSchemes"in u))return Uf(u,...c);let z=f;"palette"in u||g[S]&&(g[S]!==!0?z=g[S].palette:S==="dark"&&(z={mode:"dark"}));const R=Uf({...u,palette:z},...c);return R.defaultColorScheme=S,R.colorSchemes=g,R.palette.mode==="light"&&(R.colorSchemes.light={...g.light!==!0&&g.light,palette:R.palette},ph(R,"dark",g.dark)),R.palette.mode==="dark"&&(R.colorSchemes.dark={...g.dark!==!0&&g.dark,palette:R.palette},ph(R,"light",g.light)),R}return!f&&!("light"in g)&&S==="light"&&(g.light=!0),Dp({...v,colorSchemes:g,defaultColorScheme:S,...typeof r!="boolean"&&r},...c)}const Np=Up(),Bp="$$material";function Hp(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}const jp=u=>Hp(u)&&u!=="classes",qp=Bv({themeId:Bp,defaultTheme:Np,rootShouldForwardProp:jp}),Yp=$v;function Gp(u){return kv(u)}function wp(u){return wh("MuiSvgIcon",u)}Mv("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Xp=u=>{const{color:c,fontSize:f,classes:r}=u,s={root:["root",c!=="inherit"&&`color${tn(c)}`,`fontSize${tn(f)}`]};return Ng(s,wp,r)},Qp=qp("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(u,c)=>{const{ownerState:f}=u;return[c.root,f.color!=="inherit"&&c[`color${tn(f.color)}`],c[`fontSize${tn(f.fontSize)}`]]}})(Yp(({theme:u})=>{var c,f,r,s,h,v,S,A,g,z,R,N,Y,q;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(s=(c=u.transitions)==null?void 0:c.create)==null?void 0:s.call(c,"fill",{duration:(r=(f=(u.vars??u).transitions)==null?void 0:f.duration)==null?void 0:r.shorter}),variants:[{props:_=>!_.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((v=(h=u.typography)==null?void 0:h.pxToRem)==null?void 0:v.call(h,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((A=(S=u.typography)==null?void 0:S.pxToRem)==null?void 0:A.call(S,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((z=(g=u.typography)==null?void 0:g.pxToRem)==null?void 0:z.call(g,35))||"2.1875rem"}},...Object.entries((u.vars??u).palette).filter(([,_])=>_&&_.main).map(([_])=>{var X,K;return{props:{color:_},style:{color:(K=(X=(u.vars??u).palette)==null?void 0:X[_])==null?void 0:K.main}}}),{props:{color:"action"},style:{color:(N=(R=(u.vars??u).palette)==null?void 0:R.action)==null?void 0:N.active}},{props:{color:"disabled"},style:{color:(q=(Y=(u.vars??u).palette)==null?void 0:Y.action)==null?void 0:q.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Nf=At.forwardRef(function(c,f){const r=Gp({props:c,name:"MuiSvgIcon"}),{children:s,className:h,color:v="inherit",component:S="svg",fontSize:A="medium",htmlColor:g,inheritViewBox:z=!1,titleAccess:R,viewBox:N="0 0 24 24",...Y}=r,q=At.isValidElement(s)&&s.type==="svg",_={...r,color:v,component:S,fontSize:A,instanceFontSize:c.fontSize,inheritViewBox:z,viewBox:N,hasSvgAsChild:q},X={};z||(X.viewBox=N);const K=Xp(_);return ut.jsxs(Qp,{as:S,className:Ug(K.root,h),focusable:"false",color:g,"aria-hidden":R?void 0:!0,role:R?"img":void 0,ref:f,...X,...Y,...q&&s.props,ownerState:_,children:[q?s.props.children:s,R?ut.jsx("title",{children:R}):null]})});Nf.muiName="SvgIcon";function Pi(u,c){function f(r,s){return ut.jsx(Nf,{"data-testid":void 0,ref:s,...r,children:u})}return f.muiName=Nf.muiName,At.memo(At.forwardRef(f))}const Jh=Pi(ut.jsx("path",{d:"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7zm-6 .67 2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"})),Lp=({text:u,onSave:c})=>ut.jsxs("div",{className:"joke-card card",children:[ut.jsx("p",{children:u}),ut.jsx("button",{className:"save-button",onClick:c,children:ut.jsx(Jh,{})})]}),Vp=({text:u,onSave:c})=>ut.jsxs("div",{className:"fact-card card",children:[ut.jsx("p",{children:u}),ut.jsx("button",{className:"save-button",onClick:c,children:ut.jsx(Jh,{})})]}),Zp=Pi(ut.jsx("path",{d:"M18 2H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h9c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 14H9V4h9zM3 15v-2h2v2zm0-5.5h2v2H3zM10 20h2v2h-2zm-7-1.5v-2h2v2zM5 22c-1.1 0-2-.9-2-2h2zm3.5 0h-2v-2h2zm5 0v-2h2c0 1.1-.9 2-2 2M5 6v2H3c0-1.1.9-2 2-2"})),bh=Pi(ut.jsx("path",{d:"M5 13h14v-2H5zm-2 4h14v-2H3zM7 7v2h14V7z"})),Kp=({savedJokes:u,savedFacts:c,notepadTab:f,setNotepadTab:r,copyNotepad:s,clearJokes:h,clearFacts:v})=>{const S=f==="joke"?u:c;return ut.jsxs("div",{className:"notepad",children:[ut.jsxs("div",{className:"notepad-tabs",children:[ut.jsx("button",{className:f==="joke"?"active":"",onClick:()=>r("joke"),children:"Dad Jokes"}),ut.jsx("button",{className:f==="fact"?"active":"",onClick:()=>r("fact"),children:"Random Facts"})]}),ut.jsxs("div",{className:"notepad-content",children:[S.length===0?ut.jsx("p",{className:"notepad-empty",children:"Nothing saved yet!"}):ut.jsx("ul",{children:S.map((A,g)=>ut.jsx("li",{children:A},g))}),ut.jsxs("div",{className:"notepad-copy-button",style:{display:"flex",gap:"0.5rem",justifyContent:"flex-end",marginTop:"1rem"},children:[ut.jsx("button",{onClick:s,title:"Copy All",children:ut.jsx(Zp,{})}),f==="joke"?ut.jsx("button",{onClick:h,title:"Clear All",className:"clear-button",children:ut.jsx(bh,{})}):ut.jsx("button",{onClick:v,title:"Clear All",className:"clear-button",children:ut.jsx(bh,{})})]})]})]})},kp=Pi(ut.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}));function $p(){const[u,c]=At.useState("joke"),[f,r]=At.useState(""),[s,h]=At.useState(!1),[v,S]=At.useState(null),A=()=>{R([])},g=()=>{Y([])},[z,R]=At.useState(()=>{const Q=localStorage.getItem("savedJokes");return Q?JSON.parse(Q):[]}),[N,Y]=At.useState(()=>{const Q=localStorage.getItem("savedFacts");return Q?JSON.parse(Q):[]}),[q,_]=At.useState("joke"),X=async()=>{h(!0),S(null);try{let Q="",k={};u==="joke"?(Q="https://icanhazdadjoke.com/",k={Accept:"application/json"}):u==="fact"&&(Q="https://uselessfacts.jsph.pl/random.json?language=en");const F=await(await fetch(Q,{headers:k})).json();u==="joke"?r(F.joke):u==="fact"&&r(F.text)}catch{S("Failed to fetch 😢")}finally{h(!1)}};At.useEffect(()=>{r(""),X()},[u]);const K=()=>{f&&(u==="joke"&&!z.includes(f)?R([...z,f]):u==="fact"&&!N.includes(f)&&Y([...N,f]))};At.useEffect(()=>{localStorage.setItem("savedJokes",JSON.stringify(z))},[z]),At.useEffect(()=>{localStorage.setItem("savedFacts",JSON.stringify(N))},[N]);const nt=()=>{const Q=q==="joke"?z.join(`

`):N.join(`

`);navigator.clipboard.writeText(Q)};return ut.jsxs("div",{className:"app",children:[ut.jsx("h1",{children:"The Grin Bin"}),ut.jsxs("div",{className:"category-buttons",children:[ut.jsx("button",{className:u==="joke"?"active":"",onClick:()=>{c("joke"),_("joke")},children:"Dad Joke"}),ut.jsx("button",{className:u==="fact"?"active":"",onClick:()=>{c("fact"),_("fact")},children:"Random Fact"})]}),v&&ut.jsx("p",{children:v}),ut.jsx("div",{className:"card-wrapper",children:!s&&f&&ut.jsxs("div",{className:"card-transition",children:[u==="joke"&&ut.jsx(Lp,{text:f,onSave:K}),u==="fact"&&ut.jsx(Vp,{text:f,onSave:K})]},u)}),ut.jsxs("button",{className:"refresh-button",onClick:X,children:[ut.jsx(kp,{style:{marginRight:"0.5rem"}})," Get Another"]}),ut.jsx(Kp,{savedJokes:z,savedFacts:N,notepadTab:q,setNotepadTab:_,copyNotepad:nt,clearJokes:A,clearFacts:g})]})}Rg.createRoot(document.getElementById("root")).render(ut.jsx(Th.StrictMode,{children:ut.jsx($p,{})}));
