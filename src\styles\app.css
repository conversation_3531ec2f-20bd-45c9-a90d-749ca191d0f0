:root {
    --bg-light: #f0ead6;
    --text-light: #0d47a1;
    --card-joke-light: #e3f2fd;
    --card-fact-light: #cfdb69;
    --card-trivia-light: #e6f7ff;
    --notepad-bg-light: #fffbea;
    --notepad-content-light: #fff;

    --bg-dark: #1a1a1a;
    --text-dark: #e3f2fd;
    --card-joke-dark: #0d47a1;
    --card-fact-dark: #4a5d23;
    --card-trivia-dark: #0a3d91;
    --notepad-bg-dark: #2d2d2d;
    --notepad-content-dark: #3a3a3a;
    --card-riddle-light: #fff9c4;
    /* Light yellow */
    --card-riddle-dark: #827717;
    /* Dark yellow/olive */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

html,
body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}

#root {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

h1 {
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    color: black;
    margin-bottom: 1rem;
}

.dark-mode h1 {
    color: var(--text-dark);
}

.logo-container {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-logo {
    height: 65px;
    width: auto;
    transition: opacity 0.3s ease;
}

.main-logo:hover {
    opacity: 0.8;
}

.app {
    font-family: sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 2rem;
    min-height: 100vh;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.app>*:not(.app-footer) {
    flex-shrink: 0;
}

.app>.app-footer {
    margin-top: auto;
}

.light-mode {
    background-color: var(--bg-light);
    color: var(--text-light);
}

.dark-mode {
    background-color: var(--bg-dark);
    color: var(--text-dark);
}

.theme-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.theme-button {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.category-buttons {
    margin-top: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 500px;
}

.category-buttons button {
    margin: 0 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    background-color: #e0e0e0;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    height: 40px;
    /* Increased height */
    display: inline-flex;
    align-items: center;
    min-width: 110px;
    /* Increased minimum width */
    justify-content: center;
    flex: 1;
    /* Make buttons take equal space */
}

/* Mobile-specific styles */
@media (max-width: 600px) {
    .category-buttons {
        width: 95%;
    }

    .category-buttons button {
        min-width: 0;
        /* Remove min-width constraint on mobile */
        padding: 0.5rem;
        margin: 0 0.25rem;
        font-size: 0.9rem;
        height: 44px;
        /* Even taller on mobile for better touch targets */
        flex: 1;
        /* Make buttons take equal space */
    }
}

/* Extra small screens */
@media (max-width: 360px) {
    .category-buttons button {
        padding: 0.5rem 0.3rem;
        font-size: 0.8rem;
    }
}

.jokes-button-with-dropdown {
    height: 32px;
}

.category-buttons button.active {
    background-color: #ffd966;

}

.joke-card {
    background: var(--card-joke-light);
    color: var(--text-light);
    padding: 1.5rem;
    margin: 1.5rem auto;
    width: 100%;
    max-width: 500px;
    min-height: 150px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-sizing: border-box;
}

.dark-mode .joke-card {
    background: var(--card-joke-dark);
    color: var(--text-dark);
}



.fact-card {
    background: var(--card-fact-light);
    color: var(--text-light);
    padding: 1.5rem;
    margin: 1.5rem auto;
    width: 100%;
    max-width: 500px;
    min-height: 150px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    box-sizing: border-box;
}

.dark-mode .fact-card {
    background: var(--card-fact-dark);
    color: var(--text-dark);
}



.card-wrapper {

    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.joke-card,
.fact-card,
.trivia-card {
    animation: fadeIn 0.4s ease-in-out;
    width: 100%;
    max-width: 500px;
}

.card-transition {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards;
    position: absolute;
    width: 100%;
    max-width: 500px;
    left: 0;
    right: 0;
    margin: 0 auto;
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.refresh-button {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    background-color: #5095ff;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.refresh-button:hover {
    background-color: #77a4e9;
    transform: translateY(-2px);
}

.refresh-button:active {
    background-color: #233e66;
    transform: scale(0.98);
}


.notepad {
    margin-top: 2rem;
    margin-bottom: 2rem;
    background: var(--notepad-bg-light);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 600px;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.dark-mode .notepad {
    background: var(--notepad-bg-dark);
}

.notepad-tabs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.notepad-tabs button {
    background-color: #e0e0e0;
    border: none;
    padding: 0.5rem 0.75rem;
    margin: 0;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
    flex: 1;
    min-width: 0;
    white-space: nowrap;
}

@media (max-width: 600px) {
    .notepad {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }

    .notepad-tabs {
        flex-wrap: wrap;
    }

    .notepad-tabs button {
        font-size: 0.8rem;
        padding: 0.4rem 0.5rem;
        flex-basis: calc(50% - 0.5rem);
        margin-bottom: 0.5rem;
    }
}

.notepad-tabs button.active {
    background-color: #ffd966;
}

.notepad-content {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    background: var(--notepad-content-light);
}

.dark-mode .notepad-content {
    background: var(--notepad-content-dark);
    border-color: #444;
}

.notepad-content ul {
    list-style-type: disc;
    padding-left: 1.2rem;
}

.notepad-content li {
    margin-bottom: 0.75rem;
    color: var(--text-light);
}

.dark-mode .notepad-content li {
    color: var(--text-dark);
}

.notepad-content button {
    margin-top: 1rem;
    padding: 0.2rem 0.4rem 0.2rem 0.4rem;
    font-size: 0.85rem;
    background-color: #5095ff;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}


.notepad-content button:hover {
    background-color: #77a4e9;
    transform: translateY(-2px);
}

.notepad-content button:active {
    background-color: #233e66;
    transform: scale(0.98);
}

.notepad-empty {
    color: #999;
    font-style: italic;
    text-align: center;
}

.notepad-actions {
    display: flex;
    justify-content: flex-end;
}

/* Add these styles for the notepad items */
.notepad-item {
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.notepad-item:hover {
    background-color: rgba(80, 149, 255, 0.1);
    color: #5095ff;
    transform: translateX(4px);
}

.notepad-item:hover::after {
    content: "× Remove";
    position: absolute;
    right: 0.5rem;
    font-size: 0.8rem;
    color: #721c24;
    background-color: rgba(248, 215, 218, 0.7);
    padding: 0.1rem 0.3rem;
    border-radius: 4px;
}

.dark-mode .notepad-item:hover {
    background-color: rgba(119, 164, 233, 0.1);
    color: #77a4e9;
}

.dark-mode .notepad-item:hover::after {
    color: #f8d7da;
    background-color: rgba(114, 28, 36, 0.7);
}

.undo-button {
    background-color: #f8d7da;
    color: #721c24;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.undo-button span {
    font-size: 0.8rem;
    background-color: #721c24;
    color: #f8d7da;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dark-mode .undo-button {
    background-color: #721c24;
    color: #f8d7da;
}

.dark-mode .undo-button:hover {
    background-color: #f8d7da;
    color: #721c24;
}

.dark-mode .undo-button span {
    background-color: #f8d7da;
    color: #721c24;
}

.card {
    position: relative;
}

.card-actions {
    display: flex;
    position: absolute;
    bottom: 5px;
    right: 10px;
    gap: 16px;
}

.trivia-card .card-actions {
    gap: 20px;
    bottom: 1px;
    right: 15px;
}

.share-button {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.share-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .share-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.save-button {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.save-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .save-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}


.dark-mode .save-button {
    color: #e3f2fd;
}

.dark-mode .save-button:hover {
    color: #ffffff;
    background-color: rgba(227, 242, 253, 0.1);
}

.dark-mode .category-buttons button {
    background-color: #444;
    color: #fff;
}

.dark-mode .category-buttons button.active {
    background-color: #ffd966;
    color: #000;
}

.dark-mode .notepad-tabs button {
    background-color: #444;
    color: #fff;
}

.dark-mode .notepad-tabs button.active {
    background-color: #ffd966;
    color: #000;
}


.joke-categories {
    margin: 1rem 0;
}

.category-select {
    padding: 0.5rem;
    border-radius: 6px;
    border: 1px solid #ccc;
    background-color: white;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 1rem;
    cursor: pointer;
    min-width: 150px;
    text-align: center;
}

.dark-mode .category-select {
    background-color: #333;
    color: #fff;
    border-color: #555;
}

.category-select:focus {
    outline: none;
    border-color: #5095ff;
    box-shadow: 0 0 0 2px rgba(80, 149, 255, 0.2);
}

.dark-mode .category-select:focus {
    border-color: #77a4e9;
    box-shadow: 0 0 0 2px rgba(119, 164, 233, 0.2);
}


.trivia-card {
    background: rgb(233, 218, 255);
    padding: 1.5rem;
    margin: 1.5rem auto;
    width: 100%;
    max-width: 500px;
    min-height: 300px;
    height: auto;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
    box-sizing: border-box;
    position: relative;
    padding-bottom: 3rem;
}


.trivia-card .save-button {
    position: absolute;
    bottom: 1px;
    right: 45px;
    z-index: 10;
}

.dark-mode .trivia-card {
    background: rgb(86, 61, 124);
    color: var(--text-dark);
}

.trivia-question {
    margin-bottom: 1.5rem;
    width: 100%;
}

.trivia-question h3 {
    font-size: 1rem;

}

.trivia-answers {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
    margin-top: auto;
    margin-bottom: 1rem;
}

.answer-button {
    padding: 0.6rem 0.8rem;
    border: 2px solid #0d47a1;
    border-radius: 8px;
    background: white;
    color: #0d47a1;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.dark-mode .answer-button {
    background: #1a1a1a;
    color: #e6f7ff;
    border-color: #e6f7ff;
}

.answer-button:hover:not(:disabled) {
    background: #e3f2fd;
    transform: translateY(-2px);
}

.dark-mode .answer-button:hover:not(:disabled) {
    background: #0d47a1;
}

.answer-button.correct {
    background: #d4edda;
    color: #155724;
    border-color: #155724;
    font-weight: bold;
}

.dark-mode .answer-button.correct {
    background: #155724;
    color: #d4edda;
    border-color: #d4edda;
}

.answer-button.incorrect {
    background: #f8d7da;
    color: #721c24;
    border-color: #721c24;
}

.dark-mode .answer-button.incorrect {
    background: #721c24;
    color: #f8d7da;
    border-color: #f8d7da;
}

.answer-button.faded {
    opacity: 0.6;
}

.trivia-result {
    margin-top: 1rem;
    padding: 0.5rem;
    width: 100%;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.correct-message {
    color: #155724;
    font-weight: bold;
}

.dark-mode .correct-message {
    color: #d4edda;
}

.incorrect-message {
    color: #721c24;
}

.dark-mode .incorrect-message {
    color: #f8d7da;
}

.error-message {
    color: #721c24;
    margin: 1rem 0;
}

.dark-mode .error-message {
    color: #f8d7da;
}

@media (max-width: 600px) {
    .trivia-card {
        width: 100%;
        min-height: 300px;
        padding: 1.25rem;
    }

    .trivia-question h3 {
        font-size: 1.1rem;
    }

    .answer-button {
        padding: 0.5rem 0.7rem;
        font-size: 0.85rem;
    }
}


.empty-card {
    min-height: 300px;
    width: 100%;
    max-width: 500px;
    margin: 1.5rem auto;
}


.content-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin: 1rem 0;
    position: relative;
    height: 400px;
    transition: height 0.3s ease-in-out;
}

.joke-card,
.fact-card {
    min-height: 150px;
}

.trivia-card {
    max-height: 380px;
    min-height: 300px;
}

.card-wrapper {
    width: 100%;
    max-width: 500px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}


.trivia-card {
    max-height: 380px;
}



/* Riddle Card Styles */
.riddle-card {
    background: var(--card-riddle-light);
    color: var(--text-light);
    padding: 1.5rem;
    margin: 1.5rem auto;
    width: 100%;
    max-width: 500px;
    min-height: 200px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    text-align: center;
    box-sizing: border-box;
    position: relative;
    padding-bottom: 3rem;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.riddle-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.dark-mode .riddle-card {
    background: var(--card-riddle-dark);
    color: var(--text-dark);
}

.riddle-question {
    margin-bottom: 1.5rem;
    width: 100%;
}

.riddle-question h3 {
    font-size: 0.95rem;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400;
    /* Add normal font weight to match other cards */
}

.riddle-answer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: auto;
    margin-bottom: 1rem;
    width: 100%;
}

.answer-reveal-button {
    padding: 0.6rem 1.2rem;
    background-color: #5095ff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: "Josefin Sans", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    font-weight: 600;
    transition: all 0.3s ease, transform 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.dark-mode .answer-reveal-button {
    background-color: #77a4e9;
    color: #1a1a1a;
}

.answer-reveal-button:hover {
    background-color: #77a4e9;
    transform: translateY(-2px);
}

.answer-reveal-button:active {
    background-color: #233e66;
    transform: scale(0.98);
}

.dark-mode .answer-reveal-button:hover {
    background-color: #bbdefb;
}

.dark-mode .answer-reveal-button:active {
    background-color: #5095ff;
}

.answer-text {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--notepad-content-light);
    border-radius: 8px;
    width: 100%;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark-mode .answer-text {
    background-color: var(--notepad-content-dark);
    color: var(--text-dark);
}

.riddle-card .card-actions {
    display: flex;
    position: absolute;
    bottom: 5px;
    right: 10px;
    gap: 16px;
}

.riddle-card .save-button {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.riddle-card .save-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .riddle-card .save-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.riddle-card .share-button {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.riddle-card .share-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .riddle-card .share-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--notepad-content-light);
    border-radius: 12px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.dark-mode .modal-content {
    background: var(--notepad-content-dark);
    color: var(--text-dark);
}

/* Submission form styles */
.submission-form {
    position: relative;
}

.submission-form h2 {
    text-align: center;
    margin-bottom: 0.5rem;
}

.submission-note {
    text-align: center;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    color: #666;
}

.dark-mode .submission-note {
    color: #aaa;
}

.close-button {
    position: absolute;
    top: 0;
    right: 0;
    background: transparent;
    border: none;
    cursor: pointer;
    color: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Only adjust the positioning on mobile */
@media (max-width: 600px) {


    .submission-form h2 {
        padding-right: 2rem;
        /* Add padding to prevent overlap with close button on mobile */
    }
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid #ccc;
    font-family: inherit;
    background: white;
}

.dark-mode .form-group input,
.dark-mode .form-group select,
.dark-mode .form-group textarea {
    background: #333;
    color: white;
    border-color: #555;
}

.incorrect-answer-input {
    margin-bottom: 0.5rem;
}

.submit-button {
    padding: 0.75rem 1.5rem;
    background: #5095ff;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
    display: block;
    margin: 0 auto;
}

.dark-mode .submit-button {
    background: #77a4e9;
    color: #1a1a1a;
}

.submit-button:hover:not(:disabled) {
    transform: translateY(-2px);
    background: #77a4e9;
}

.dark-mode .submit-button:hover:not(:disabled) {
    background: #bbdefb;
}

.submit-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.success-message {
    color: #155724;
    text-align: center;
    margin-top: 1rem;
    padding: 0.5rem;
    background-color: #d4edda;
    border-radius: 4px;
}

.dark-mode .success-message {
    color: #d4edda;
    background-color: #155724;
}

.error-message {
    color: #721c24;
    text-align: center;
    margin-top: 1rem;
    padding: 0.5rem;
    background-color: #f8d7da;
    border-radius: 4px;
}

.dark-mode .error-message {
    color: #f8d7da;
    background-color: #721c24;
}

/* App actions container */
.app-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;

}

@media (max-width: 600px) {
    .app-actions {
        flex-direction: column;
        align-items: center;
    }

    .app-actions button {
        width: 100%;
        max-width: 300px;
    }
}

/* User content styles */
.user-content-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.user-content-item {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark-mode .user-content-item {
    background: #333;
}

/* Content layout with voting */
.content-main {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.content-text {
    flex: 1;
    min-width: 0;
    /* Allows text to wrap properly */
}

/* Voting buttons styles */
.voting-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.25rem;
    min-width: 100px;
    padding: 0.25rem;
    flex-shrink: 0;
    /* Prevents voting buttons from shrinking */
}

.vote-button {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
    background: transparent;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 0.25rem 0.375rem;
    cursor: pointer;
    transition: all 0.15s ease;
    min-width: 40px;
    color: #888;
    font-size: 0.75rem;
}

.vote-button:hover {
    border-color: #bbb;
    background: rgba(0, 0, 0, 0.03);
    color: #666;
}

.vote-button.upvote.active {
    border-color: #66bb6a;
    background: rgba(102, 187, 106, 0.08);
    color: #4caf50;
}

.vote-button.downvote.active {
    border-color: #ef5350;
    background: rgba(239, 83, 80, 0.08);
    color: #f44336;
}

.vote-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.vote-count {
    font-size: 0.7rem;
    font-weight: 500;
    line-height: 1;
}

.net-votes {
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-weight: 500;
    font-size: 0.75rem;
    text-align: center;
    min-width: 30px;
    line-height: 1.2;
}

.net-score.positive {
    color: #4caf50;
    background: rgba(76, 175, 80, 0.06);
}

.net-score.negative {
    color: #f44336;
    background: rgba(244, 67, 54, 0.06);
}

.net-score.neutral {
    color: #888;
    background: rgba(0, 0, 0, 0.03);
}

/* Dark mode voting styles */
.dark-mode .vote-button {
    border-color: #444;
    color: #aaa;
}

.dark-mode .vote-button:hover {
    border-color: #666;
    background: rgba(255, 255, 255, 0.03);
    color: #ccc;
}

.dark-mode .vote-button.upvote.active {
    border-color: #66bb6a;
    background: rgba(102, 187, 106, 0.12);
    color: #66bb6a;
}

.dark-mode .vote-button.downvote.active {
    border-color: #ef5350;
    background: rgba(239, 83, 80, 0.12);
    color: #ef5350;
}

.dark-mode .net-score.positive {
    color: #66bb6a;
    background: rgba(102, 187, 106, 0.12);
}

.dark-mode .net-score.negative {
    color: #ef5350;
    background: rgba(239, 83, 80, 0.12);
}

.dark-mode .net-score.neutral {
    color: #aaa;
    background: rgba(255, 255, 255, 0.03);
}

/* Responsive voting layout */
@media (max-width: 768px) {
    .content-main {
        gap: 0.5rem;
        /* Keep side-by-side layout on mobile too */
    }

    .voting-buttons {
        min-width: 85px;
        /* Slightly smaller on mobile */
        padding: 0.125rem;
        gap: 0.125rem;
    }

    .vote-button {
        min-width: 30px;
        padding: 0.15rem 0.25rem;
    }

    .vote-count {
        font-size: 0.65rem;
    }

    .net-votes {
        padding: 0.1rem 0.15rem;
    }

    .net-score {
        font-size: 0.65rem;
    }
}

.content-author {
    font-size: 0.8rem;
    font-style: italic;
    margin-top: 0.5rem;
    text-align: left;
    color: #666;
}

.dark-mode .content-author {
    color: #aaa;
}

.loading-message,
.no-content-message {
    text-align: center;
    padding: 2rem 0;
}

.user-content-button {
    padding: 0.75rem 1.5rem;
    background: #4caf50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s;
}

.dark-mode .user-content-button {
    background: #2e7d32;
}

.user-content-button:hover:not(:disabled) {
    transform: translateY(-2px);
    background: #66bb6a;
}

.dark-mode .user-content-button:hover:not(:disabled) {
    background: #43a047;
}

.user-content-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Refined styles for the top actions */
.top-actions {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
}

/* Make the buttons in top-actions more compact and elegant */
.top-actions button {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    min-width: 120px;
    transition: all 0.2s ease;
}

.top-actions button:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.top-actions button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Style the submit button */
.submit-button {
    background: #ff9800;
    color: white;
    border: none;
    cursor: pointer;
}

.submit-button:hover {
    background: #f57c00;
}

/* Style the user content button */
.user-content-button {
    background: #4caf50;
    color: white;
    border: none;
    cursor: pointer;
}

.user-content-button:hover {
    background: #388e3c;
}

.user-content-button:disabled {
    background: #a5d6a7;
    cursor: not-allowed;
    opacity: 0.7;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Dark mode adjustments */
.dark-mode .submit-button {
    background: #ff9800;
    color: #1a1a1a;
}

.dark-mode .submit-button:hover {
    background: #ffb74d;
}

.dark-mode .user-content-button {
    background: #4caf50;
    color: #1a1a1a;
}

.dark-mode .user-content-button:hover {
    background: #81c784;
}

.dark-mode .user-content-button:disabled {
    background: #2e7d32;
    color: rgba(255, 255, 255, 0.7);
    opacity: 0.7;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    .top-actions {
        flex-direction: row;
        width: 100%;
        padding: 0 1rem;
        gap: 0.5rem;
    }

    .top-actions button {
        flex: 1;
        font-size: 0.75rem;
        padding: 0.4rem 0.5rem;
        min-width: 0;
    }
}