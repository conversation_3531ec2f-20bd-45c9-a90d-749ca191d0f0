import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App'
import { ThemeProvider } from './context/ThemeContext'
import { getFirestore, collection, getDocs, query, limit as firestoreLimit } from 'firebase/firestore';
import { getApp } from 'firebase/app';

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </React.StrictMode>
)

try {
  const db = getFirestore(getApp());
  console.log('Testing Firebase connection...');

  // Test query to verify connection
  const testQuery = async () => {
    try {
      const q = query(collection(db, 'userSubmissions'), firestoreLimit(1));
      const snapshot = await getDocs(q);
      console.log('Firebase connection successful. Documents found:', snapshot.size);
    } catch (error) {
      console.error('Firebase test query failed:', error);
    }
  };

  testQuery();
} catch (error) {
  console.error('Error accessing Firebase:', error);
}



