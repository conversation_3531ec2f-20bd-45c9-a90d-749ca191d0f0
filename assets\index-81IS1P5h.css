*{margin:0;padding:0;box-sizing:border-box}html,body{width:100%;height:100%;margin:0;padding:0}.app{font-family:sans-serif;display:flex;flex-direction:column;align-items:center;justify-content:flex-start;padding:2rem;background-color:#f0ead6;min-height:100vh}.category-buttons{margin-top:1rem;margin-bottom:1rem}.category-buttons button{margin:0 .5rem;padding:.5rem 1rem;border:none;background-color:#e0e0e0;border-radius:6px;cursor:pointer;font-size:1rem}.category-buttons button.active{background-color:#ffd966}.joke-card{background:#e3f2fd;color:#0d47a1;padding:1.5rem;margin:1.5rem auto;width:100%;max-width:500px;min-height:150px;border-radius:12px;box-shadow:0 4px 6px #0000001a;display:flex;align-items:center;justify-content:center;text-align:center;box-sizing:border-box}.pun-card{background:#019b41;color:#0d47a1;padding:1.5rem;margin:1.5rem auto;width:500px;min-height:150px;border-radius:12px;box-shadow:0 4px 6px #0000001a;display:flex;align-items:center;justify-content:center;text-align:center}.fact-card{background:#cfdb69;color:#0d47a1;padding:1.5rem;margin:1.5rem auto;width:100%;max-width:500px;min-height:150px;border-radius:12px;box-shadow:0 4px 6px #0000001a;display:flex;align-items:center;justify-content:center;text-align:center;box-sizing:border-box}.card-wrapper{min-height:200px;display:flex;align-items:center;justify-content:center;margin-bottom:1rem}.joke-card{animation:fadeIn .4s ease-in-out}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.card-transition{opacity:0;transform:translateY(20px);animation:fadeInUp .5s ease forwards}@keyframes fadeInUp{to{opacity:1;transform:translateY(0)}}.refresh-button{margin-top:.5rem;padding:.75rem 1.5rem;font-size:1.1rem;font-weight:600;color:#fff;background-color:#5095ff;border:none;border-radius:8px;cursor:pointer;transition:background-color .3s ease,transform .2s ease;box-shadow:0 4px 8px #00000026}.refresh-button:hover{background-color:#77a4e9;transform:translateY(-2px)}.refresh-button:active{background-color:#233e66;transform:scale(.98)}
