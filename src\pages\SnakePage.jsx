import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SnakeGame from '../components/SnakeGame';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import '../styles/app.css';
import '../styles/game-page.css';

const SnakePage = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const handleNavigate = (page) => {
    navigate(`/${page}`);
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>

      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div className="game-page-container">
        <div className="game-header">
          <button
            className="back-button"
            onClick={() => navigate('/classic-games')}
          >
            <ArrowBackIcon /> Back to Games
          </button>
          <h1 className="game-title">🐍 Snake</h1>
          <p className="game-description">
            Guide the snake to eat food and grow longer without hitting the walls or yourself!
            Use arrow keys or WASD on desktop, or use the on-screen controls and swipe gestures on mobile.
            The classic arcade game that never gets old - how long can you make your snake?
          </p>
        </div>

        <div className="game-container">
          <SnakeGame />
        </div>

        <div className="game-instructions">
          <h3>How to Play:</h3>
          <ul>
            <li>🎮🖱️ Use arrow keys, WASD, or on-screen controls to move</li>
            <li>📱 Swipe on the game area for mobile control</li>
            <li>🍎 Eat red food to grow your snake and score points</li>
            <li>⚠️ Don't hit walls or your own body!</li>
            <li>🏆 Try to beat your high score and grow as long as possible</li>
            <li>💾 Your best score is automatically saved</li>
          </ul>
        </div>
      </div>

      <Footer onNavigate={handleNavigate} />
      <CookieConsent />
    </div>
  );
};

export default SnakePage;
