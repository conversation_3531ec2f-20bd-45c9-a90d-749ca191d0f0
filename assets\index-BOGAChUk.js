function Ag(u,c){for(var f=0;f<c.length;f++){const r=c[f];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in u)){const h=Object.getOwnPropertyDescriptor(r,o);h&&Object.defineProperty(u,o,h.get?h:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const h of o)if(h.type==="childList")for(const v of h.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&r(v)}).observe(document,{childList:!0,subtree:!0});function f(o){const h={};return o.integrity&&(h.integrity=o.integrity),o.referrerPolicy&&(h.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?h.credentials="include":o.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function r(o){if(o.ep)return;o.ep=!0;const h=f(o);fetch(o.href,h)}})();function xh(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var Sf={exports:{}},su={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var V0;function Eg(){if(V0)return su;V0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function f(r,o,h){var v=null;if(h!==void 0&&(v=""+h),o.key!==void 0&&(v=""+o.key),"key"in o){h={};for(var S in o)S!=="key"&&(h[S]=o[S])}else h=o;return o=h.ref,{$$typeof:u,type:r,key:v,ref:o!==void 0?o:null,props:h}}return su.Fragment=c,su.jsx=f,su.jsxs=f,su}var k0;function xg(){return k0||(k0=1,Sf.exports=Eg()),Sf.exports}var q=xg(),Tf={exports:{}},st={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Z0;function Og(){if(Z0)return st;Z0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),R=Symbol.iterator;function U(p){return p===null||typeof p!="object"?null:(p=R&&p[R]||p["@@iterator"],typeof p=="function"?p:null)}var Y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,O={};function L(p,H,Z){this.props=p,this.context=H,this.refs=O,this.updater=Z||Y}L.prototype.isReactComponent={},L.prototype.setState=function(p,H){if(typeof p!="object"&&typeof p!="function"&&p!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,p,H,"setState")},L.prototype.forceUpdate=function(p){this.updater.enqueueForceUpdate(this,p,"forceUpdate")};function $(){}$.prototype=L.prototype;function ut(p,H,Z){this.props=p,this.context=H,this.refs=O,this.updater=Z||Y}var k=ut.prototype=new $;k.constructor=ut,w(k,L.prototype),k.isPureReactComponent=!0;var K=Array.isArray,Q={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function W(p,H,Z,V,et,vt){return Z=vt.ref,{$$typeof:u,type:p,key:H,ref:Z!==void 0?Z:null,props:vt}}function ot(p,H){return W(p.type,H,void 0,void 0,void 0,p.props)}function xt(p){return typeof p=="object"&&p!==null&&p.$$typeof===u}function m(p){var H={"=":"=0",":":"=2"};return"$"+p.replace(/[=:]/g,function(Z){return H[Z]})}var X=/\/+/g;function I(p,H){return typeof p=="object"&&p!==null&&p.key!=null?m(""+p.key):H.toString(36)}function ft(){}function Rt(p){switch(p.status){case"fulfilled":return p.value;case"rejected":throw p.reason;default:switch(typeof p.status=="string"?p.then(ft,ft):(p.status="pending",p.then(function(H){p.status==="pending"&&(p.status="fulfilled",p.value=H)},function(H){p.status==="pending"&&(p.status="rejected",p.reason=H)})),p.status){case"fulfilled":return p.value;case"rejected":throw p.reason}}throw p}function Ot(p,H,Z,V,et){var vt=typeof p;(vt==="undefined"||vt==="boolean")&&(p=null);var rt=!1;if(p===null)rt=!0;else switch(vt){case"bigint":case"string":case"number":rt=!0;break;case"object":switch(p.$$typeof){case u:case c:rt=!0;break;case z:return rt=p._init,Ot(rt(p._payload),H,Z,V,et)}}if(rt)return et=et(p),rt=V===""?"."+I(p,0):V,K(et)?(Z="",rt!=null&&(Z=rt.replace(X,"$&/")+"/"),Ot(et,H,Z,"",function(pt){return pt})):et!=null&&(xt(et)&&(et=ot(et,Z+(et.key==null||p&&p.key===et.key?"":(""+et.key).replace(X,"$&/")+"/")+rt)),H.push(et)),1;rt=0;var it=V===""?".":V+":";if(K(p))for(var ht=0;ht<p.length;ht++)V=p[ht],vt=it+I(V,ht),rt+=Ot(V,H,Z,vt,et);else if(ht=U(p),typeof ht=="function")for(p=ht.call(p),ht=0;!(V=p.next()).done;)V=V.value,vt=it+I(V,ht++),rt+=Ot(V,H,Z,vt,et);else if(vt==="object"){if(typeof p.then=="function")return Ot(Rt(p),H,Z,V,et);throw H=String(p),Error("Objects are not valid as a React child (found: "+(H==="[object Object]"?"object with keys {"+Object.keys(p).join(", ")+"}":H)+"). If you meant to render a collection of children, use an array instead.")}return rt}function M(p,H,Z){if(p==null)return p;var V=[],et=0;return Ot(p,V,"","",function(vt){return H.call(Z,vt,et++)}),V}function G(p){if(p._status===-1){var H=p._result;H=H(),H.then(function(Z){(p._status===0||p._status===-1)&&(p._status=1,p._result=Z)},function(Z){(p._status===0||p._status===-1)&&(p._status=2,p._result=Z)}),p._status===-1&&(p._status=0,p._result=H)}if(p._status===1)return p._result.default;throw p._result}var tt=typeof reportError=="function"?reportError:function(p){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var H=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof p=="object"&&p!==null&&typeof p.message=="string"?String(p.message):String(p),error:p});if(!window.dispatchEvent(H))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",p);return}console.error(p)};function Ct(){}return st.Children={map:M,forEach:function(p,H,Z){M(p,function(){H.apply(this,arguments)},Z)},count:function(p){var H=0;return M(p,function(){H++}),H},toArray:function(p){return M(p,function(H){return H})||[]},only:function(p){if(!xt(p))throw Error("React.Children.only expected to receive a single React element child.");return p}},st.Component=L,st.Fragment=f,st.Profiler=o,st.PureComponent=ut,st.StrictMode=r,st.Suspense=A,st.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Q,st.__COMPILER_RUNTIME={__proto__:null,c:function(p){return Q.H.useMemoCache(p)}},st.cache=function(p){return function(){return p.apply(null,arguments)}},st.cloneElement=function(p,H,Z){if(p==null)throw Error("The argument must be a React element, but you passed "+p+".");var V=w({},p.props),et=p.key,vt=void 0;if(H!=null)for(rt in H.ref!==void 0&&(vt=void 0),H.key!==void 0&&(et=""+H.key),H)!F.call(H,rt)||rt==="key"||rt==="__self"||rt==="__source"||rt==="ref"&&H.ref===void 0||(V[rt]=H[rt]);var rt=arguments.length-2;if(rt===1)V.children=Z;else if(1<rt){for(var it=Array(rt),ht=0;ht<rt;ht++)it[ht]=arguments[ht+2];V.children=it}return W(p.type,et,void 0,void 0,vt,V)},st.createContext=function(p){return p={$$typeof:v,_currentValue:p,_currentValue2:p,_threadCount:0,Provider:null,Consumer:null},p.Provider=p,p.Consumer={$$typeof:h,_context:p},p},st.createElement=function(p,H,Z){var V,et={},vt=null;if(H!=null)for(V in H.key!==void 0&&(vt=""+H.key),H)F.call(H,V)&&V!=="key"&&V!=="__self"&&V!=="__source"&&(et[V]=H[V]);var rt=arguments.length-2;if(rt===1)et.children=Z;else if(1<rt){for(var it=Array(rt),ht=0;ht<rt;ht++)it[ht]=arguments[ht+2];et.children=it}if(p&&p.defaultProps)for(V in rt=p.defaultProps,rt)et[V]===void 0&&(et[V]=rt[V]);return W(p,vt,void 0,void 0,null,et)},st.createRef=function(){return{current:null}},st.forwardRef=function(p){return{$$typeof:S,render:p}},st.isValidElement=xt,st.lazy=function(p){return{$$typeof:z,_payload:{_status:-1,_result:p},_init:G}},st.memo=function(p,H){return{$$typeof:g,type:p,compare:H===void 0?null:H}},st.startTransition=function(p){var H=Q.T,Z={};Q.T=Z;try{var V=p(),et=Q.S;et!==null&&et(Z,V),typeof V=="object"&&V!==null&&typeof V.then=="function"&&V.then(Ct,tt)}catch(vt){tt(vt)}finally{Q.T=H}},st.unstable_useCacheRefresh=function(){return Q.H.useCacheRefresh()},st.use=function(p){return Q.H.use(p)},st.useActionState=function(p,H,Z){return Q.H.useActionState(p,H,Z)},st.useCallback=function(p,H){return Q.H.useCallback(p,H)},st.useContext=function(p){return Q.H.useContext(p)},st.useDebugValue=function(){},st.useDeferredValue=function(p,H){return Q.H.useDeferredValue(p,H)},st.useEffect=function(p,H,Z){var V=Q.H;if(typeof Z=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return V.useEffect(p,H)},st.useId=function(){return Q.H.useId()},st.useImperativeHandle=function(p,H,Z){return Q.H.useImperativeHandle(p,H,Z)},st.useInsertionEffect=function(p,H){return Q.H.useInsertionEffect(p,H)},st.useLayoutEffect=function(p,H){return Q.H.useLayoutEffect(p,H)},st.useMemo=function(p,H){return Q.H.useMemo(p,H)},st.useOptimistic=function(p,H){return Q.H.useOptimistic(p,H)},st.useReducer=function(p,H,Z){return Q.H.useReducer(p,H,Z)},st.useRef=function(p){return Q.H.useRef(p)},st.useState=function(p){return Q.H.useState(p)},st.useSyncExternalStore=function(p,H,Z){return Q.H.useSyncExternalStore(p,H,Z)},st.useTransition=function(){return Q.H.useTransition()},st.version="19.1.0",st}var K0;function Yf(){return K0||(K0=1,Tf.exports=Og()),Tf.exports}var lt=Yf();const Oh=xh(lt),$0=Ag({__proto__:null,default:Oh},[lt]);var Af={exports:{}},ou={},Ef={exports:{}},xf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var J0;function Cg(){return J0||(J0=1,function(u){function c(M,G){var tt=M.length;M.push(G);t:for(;0<tt;){var Ct=tt-1>>>1,p=M[Ct];if(0<o(p,G))M[Ct]=G,M[tt]=p,tt=Ct;else break t}}function f(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var G=M[0],tt=M.pop();if(tt!==G){M[0]=tt;t:for(var Ct=0,p=M.length,H=p>>>1;Ct<H;){var Z=2*(Ct+1)-1,V=M[Z],et=Z+1,vt=M[et];if(0>o(V,tt))et<p&&0>o(vt,V)?(M[Ct]=vt,M[et]=tt,Ct=et):(M[Ct]=V,M[Z]=tt,Ct=Z);else if(et<p&&0>o(vt,tt))M[Ct]=vt,M[et]=tt,Ct=et;else break t}}return G}function o(M,G){var tt=M.sortIndex-G.sortIndex;return tt!==0?tt:M.id-G.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;u.unstable_now=function(){return h.now()}}else{var v=Date,S=v.now();u.unstable_now=function(){return v.now()-S}}var A=[],g=[],z=1,R=null,U=3,Y=!1,w=!1,O=!1,L=!1,$=typeof setTimeout=="function"?setTimeout:null,ut=typeof clearTimeout=="function"?clearTimeout:null,k=typeof setImmediate<"u"?setImmediate:null;function K(M){for(var G=f(g);G!==null;){if(G.callback===null)r(g);else if(G.startTime<=M)r(g),G.sortIndex=G.expirationTime,c(A,G);else break;G=f(g)}}function Q(M){if(O=!1,K(M),!w)if(f(A)!==null)w=!0,F||(F=!0,I());else{var G=f(g);G!==null&&Ot(Q,G.startTime-M)}}var F=!1,W=-1,ot=5,xt=-1;function m(){return L?!0:!(u.unstable_now()-xt<ot)}function X(){if(L=!1,F){var M=u.unstable_now();xt=M;var G=!0;try{t:{w=!1,O&&(O=!1,ut(W),W=-1),Y=!0;var tt=U;try{e:{for(K(M),R=f(A);R!==null&&!(R.expirationTime>M&&m());){var Ct=R.callback;if(typeof Ct=="function"){R.callback=null,U=R.priorityLevel;var p=Ct(R.expirationTime<=M);if(M=u.unstable_now(),typeof p=="function"){R.callback=p,K(M),G=!0;break e}R===f(A)&&r(A),K(M)}else r(A);R=f(A)}if(R!==null)G=!0;else{var H=f(g);H!==null&&Ot(Q,H.startTime-M),G=!1}}break t}finally{R=null,U=tt,Y=!1}G=void 0}}finally{G?I():F=!1}}}var I;if(typeof k=="function")I=function(){k(X)};else if(typeof MessageChannel<"u"){var ft=new MessageChannel,Rt=ft.port2;ft.port1.onmessage=X,I=function(){Rt.postMessage(null)}}else I=function(){$(X,0)};function Ot(M,G){W=$(function(){M(u.unstable_now())},G)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(M){M.callback=null},u.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ot=0<M?Math.floor(1e3/M):5},u.unstable_getCurrentPriorityLevel=function(){return U},u.unstable_next=function(M){switch(U){case 1:case 2:case 3:var G=3;break;default:G=U}var tt=U;U=G;try{return M()}finally{U=tt}},u.unstable_requestPaint=function(){L=!0},u.unstable_runWithPriority=function(M,G){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var tt=U;U=M;try{return G()}finally{U=tt}},u.unstable_scheduleCallback=function(M,G,tt){var Ct=u.unstable_now();switch(typeof tt=="object"&&tt!==null?(tt=tt.delay,tt=typeof tt=="number"&&0<tt?Ct+tt:Ct):tt=Ct,M){case 1:var p=-1;break;case 2:p=250;break;case 5:p=1073741823;break;case 4:p=1e4;break;default:p=5e3}return p=tt+p,M={id:z++,callback:G,priorityLevel:M,startTime:tt,expirationTime:p,sortIndex:-1},tt>Ct?(M.sortIndex=tt,c(g,M),f(A)===null&&M===f(g)&&(O?(ut(W),W=-1):O=!0,Ot(Q,tt-Ct))):(M.sortIndex=p,c(A,M),w||Y||(w=!0,F||(F=!0,I()))),M},u.unstable_shouldYield=m,u.unstable_wrapCallback=function(M){var G=U;return function(){var tt=U;U=G;try{return M.apply(this,arguments)}finally{U=tt}}}}(xf)),xf}var W0;function _g(){return W0||(W0=1,Ef.exports=Cg()),Ef.exports}var Of={exports:{}},se={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var F0;function zg(){if(F0)return se;F0=1;var u=Yf();function c(A){var g="https://react.dev/errors/"+A;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)g+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+A+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var r={d:{f,r:function(){throw Error(c(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},o=Symbol.for("react.portal");function h(A,g,z){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:R==null?null:""+R,children:A,containerInfo:g,implementation:z}}var v=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(A,g){if(A==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return se.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,se.createPortal=function(A,g){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(c(299));return h(A,g,null,z)},se.flushSync=function(A){var g=v.T,z=r.p;try{if(v.T=null,r.p=2,A)return A()}finally{v.T=g,r.p=z,r.d.f()}},se.preconnect=function(A,g){typeof A=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,r.d.C(A,g))},se.prefetchDNS=function(A){typeof A=="string"&&r.d.D(A)},se.preinit=function(A,g){if(typeof A=="string"&&g&&typeof g.as=="string"){var z=g.as,R=S(z,g.crossOrigin),U=typeof g.integrity=="string"?g.integrity:void 0,Y=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;z==="style"?r.d.S(A,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:R,integrity:U,fetchPriority:Y}):z==="script"&&r.d.X(A,{crossOrigin:R,integrity:U,fetchPriority:Y,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},se.preinitModule=function(A,g){if(typeof A=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var z=S(g.as,g.crossOrigin);r.d.M(A,{crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&r.d.M(A)},se.preload=function(A,g){if(typeof A=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var z=g.as,R=S(z,g.crossOrigin);r.d.L(A,z,{crossOrigin:R,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},se.preloadModule=function(A,g){if(typeof A=="string")if(g){var z=S(g.as,g.crossOrigin);r.d.m(A,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:z,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else r.d.m(A)},se.requestFormReset=function(A){r.d.r(A)},se.unstable_batchedUpdates=function(A,g){return A(g)},se.useFormState=function(A,g,z){return v.H.useFormState(A,g,z)},se.useFormStatus=function(){return v.H.useHostTransitionStatus()},se.version="19.1.0",se}var P0;function Ch(){if(P0)return Of.exports;P0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),Of.exports=zg(),Of.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var I0;function Rg(){if(I0)return ou;I0=1;var u=_g(),c=Yf(),f=Ch();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function v(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(h(t)!==t)throw Error(r(188))}function A(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(r(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return S(n),t;if(i===a)return S(n),e;i=i.sibling}throw Error(r(188))}if(l.return!==a.return)l=n,a=i;else{for(var s=!1,d=n.child;d;){if(d===l){s=!0,l=n,a=i;break}if(d===a){s=!0,a=n,l=i;break}d=d.sibling}if(!s){for(d=i.child;d;){if(d===l){s=!0,l=i,a=n;break}if(d===a){s=!0,a=i,l=n;break}d=d.sibling}if(!s)throw Error(r(189))}}if(l.alternate!==a)throw Error(r(190))}if(l.tag!==3)throw Error(r(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,R=Symbol.for("react.element"),U=Symbol.for("react.transitional.element"),Y=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),O=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),$=Symbol.for("react.provider"),ut=Symbol.for("react.consumer"),k=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),Q=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),ot=Symbol.for("react.lazy"),xt=Symbol.for("react.activity"),m=Symbol.for("react.memo_cache_sentinel"),X=Symbol.iterator;function I(t){return t===null||typeof t!="object"?null:(t=X&&t[X]||t["@@iterator"],typeof t=="function"?t:null)}var ft=Symbol.for("react.client.reference");function Rt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ft?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case w:return"Fragment";case L:return"Profiler";case O:return"StrictMode";case Q:return"Suspense";case F:return"SuspenseList";case xt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case Y:return"Portal";case k:return(t.displayName||"Context")+".Provider";case ut:return(t._context.displayName||"Context")+".Consumer";case K:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case W:return e=t.displayName||null,e!==null?e:Rt(t.type)||"Memo";case ot:e=t._payload,t=t._init;try{return Rt(t(e))}catch{}}return null}var Ot=Array.isArray,M=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,tt={pending:!1,data:null,method:null,action:null},Ct=[],p=-1;function H(t){return{current:t}}function Z(t){0>p||(t.current=Ct[p],Ct[p]=null,p--)}function V(t,e){p++,Ct[p]=t.current,t.current=e}var et=H(null),vt=H(null),rt=H(null),it=H(null);function ht(t,e){switch(V(rt,e),V(vt,t),V(et,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?p0(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=p0(e),t=b0(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Z(et),V(et,t)}function pt(){Z(et),Z(vt),Z(rt)}function It(t){t.memoizedState!==null&&V(it,t);var e=et.current,l=b0(e,t.type);e!==l&&(V(vt,t),V(et,l))}function Kl(t){vt.current===t&&(Z(et),Z(vt)),it.current===t&&(Z(it),uu._currentValue=tt)}var dn=Object.prototype.hasOwnProperty,$l=u.unstable_scheduleCallback,he=u.unstable_cancelCallback,Jl=u.unstable_shouldYield,em=u.unstable_requestPaint,Ke=u.unstable_now,lm=u.unstable_getCurrentPriorityLevel,Ff=u.unstable_ImmediatePriority,Pf=u.unstable_UserBlockingPriority,xu=u.unstable_NormalPriority,am=u.unstable_LowPriority,If=u.unstable_IdlePriority,nm=u.log,um=u.unstable_setDisableYieldValue,hn=null,Se=null;function Tl(t){if(typeof nm=="function"&&um(t),Se&&typeof Se.setStrictMode=="function")try{Se.setStrictMode(hn,t)}catch{}}var Te=Math.clz32?Math.clz32:rm,im=Math.log,cm=Math.LN2;function rm(t){return t>>>=0,t===0?32:31-(im(t)/cm|0)|0}var Ou=256,Cu=4194304;function Wl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function _u(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,i=t.suspendedLanes,s=t.pingedLanes;t=t.warmLanes;var d=a&134217727;return d!==0?(a=d&~i,a!==0?n=Wl(a):(s&=d,s!==0?n=Wl(s):l||(l=d&~t,l!==0&&(n=Wl(l))))):(d=a&~i,d!==0?n=Wl(d):s!==0?n=Wl(s):l||(l=a&~t,l!==0&&(n=Wl(l)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:n}function mn(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function fm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ts(){var t=Ou;return Ou<<=1,(Ou&4194048)===0&&(Ou=256),t}function es(){var t=Cu;return Cu<<=1,(Cu&62914560)===0&&(Cu=4194304),t}function rc(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function yn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function sm(t,e,l,a,n,i){var s=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var d=t.entanglements,y=t.expirationTimes,x=t.hiddenUpdates;for(l=s&~l;0<l;){var N=31-Te(l),B=1<<N;d[N]=0,y[N]=-1;var C=x[N];if(C!==null)for(x[N]=null,N=0;N<C.length;N++){var _=C[N];_!==null&&(_.lane&=-536870913)}l&=~B}a!==0&&ls(t,a,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(s&~e))}function ls(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-Te(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function as(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-Te(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function fc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function sc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function ns(){var t=G.p;return t!==0?t:(t=window.event,t===void 0?32:q0(t.type))}function om(t,e){var l=G.p;try{return G.p=t,e()}finally{G.p=l}}var Al=Math.random().toString(36).slice(2),re="__reactFiber$"+Al,me="__reactProps$"+Al,ya="__reactContainer$"+Al,oc="__reactEvents$"+Al,dm="__reactListeners$"+Al,hm="__reactHandles$"+Al,us="__reactResources$"+Al,gn="__reactMarker$"+Al;function dc(t){delete t[re],delete t[me],delete t[oc],delete t[dm],delete t[hm]}function ga(t){var e=t[re];if(e)return e;for(var l=t.parentNode;l;){if(e=l[ya]||l[re]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=E0(t);t!==null;){if(l=t[re])return l;t=E0(t)}return e}t=l,l=t.parentNode}return null}function va(t){if(t=t[re]||t[ya]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function vn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function pa(t){var e=t[us];return e||(e=t[us]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function te(t){t[gn]=!0}var is=new Set,cs={};function Fl(t,e){ba(t,e),ba(t+"Capture",e)}function ba(t,e){for(cs[t]=e,t=0;t<e.length;t++)is.add(e[t])}var mm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),rs={},fs={};function ym(t){return dn.call(fs,t)?!0:dn.call(rs,t)?!1:mm.test(t)?fs[t]=!0:(rs[t]=!0,!1)}function zu(t,e,l){if(ym(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Ru(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function al(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var hc,ss;function Sa(t){if(hc===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);hc=e&&e[1]||"",ss=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+hc+t+ss}var mc=!1;function yc(t,e){if(!t||mc)return"";mc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var B=function(){throw Error()};if(Object.defineProperty(B.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(B,[])}catch(_){var C=_}Reflect.construct(t,[],B)}else{try{B.call()}catch(_){C=_}t.call(B.prototype)}}else{try{throw Error()}catch(_){C=_}(B=t())&&typeof B.catch=="function"&&B.catch(function(){})}}catch(_){if(_&&C&&typeof _.stack=="string")return[_.stack,C.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),s=i[0],d=i[1];if(s&&d){var y=s.split(`
`),x=d.split(`
`);for(n=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;n<x.length&&!x[n].includes("DetermineComponentFrameRoot");)n++;if(a===y.length||n===x.length)for(a=y.length-1,n=x.length-1;1<=a&&0<=n&&y[a]!==x[n];)n--;for(;1<=a&&0<=n;a--,n--)if(y[a]!==x[n]){if(a!==1||n!==1)do if(a--,n--,0>n||y[a]!==x[n]){var N=`
`+y[a].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=a&&0<=n);break}}}finally{mc=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?Sa(l):""}function gm(t){switch(t.tag){case 26:case 27:case 5:return Sa(t.type);case 16:return Sa("Lazy");case 13:return Sa("Suspense");case 19:return Sa("SuspenseList");case 0:case 15:return yc(t.type,!1);case 11:return yc(t.type.render,!1);case 1:return yc(t.type,!0);case 31:return Sa("Activity");default:return""}}function os(t){try{var e="";do e+=gm(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Ue(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ds(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function vm(t){var e=ds(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(s){a=""+s,i.call(this,s)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(s){a=""+s},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Mu(t){t._valueTracker||(t._valueTracker=vm(t))}function hs(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=ds(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Du(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var pm=/[\n"\\]/g;function je(t){return t.replace(pm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gc(t,e,l,a,n,i,s,d){t.name="",s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"?t.type=s:t.removeAttribute("type"),e!=null?s==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Ue(e)):t.value!==""+Ue(e)&&(t.value=""+Ue(e)):s!=="submit"&&s!=="reset"||t.removeAttribute("value"),e!=null?vc(t,s,Ue(e)):l!=null?vc(t,s,Ue(l)):a!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+Ue(d):t.removeAttribute("name")}function ms(t,e,l,a,n,i,s,d){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+Ue(l):"",e=e!=null?""+Ue(e):l,d||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=d?t.checked:!!a,t.defaultChecked=!!a,s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(t.name=s)}function vc(t,e,l){e==="number"&&Du(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function Ta(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+Ue(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function ys(t,e,l){if(e!=null&&(e=""+Ue(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+Ue(l):""}function gs(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(r(92));if(Ot(a)){if(1<a.length)throw Error(r(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=Ue(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function Aa(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var bm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function vs(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||bm.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function ps(t,e,l){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&vs(t,n,a)}else for(var i in e)e.hasOwnProperty(i)&&vs(t,i,e[i])}function pc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Sm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Tm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Nu(t){return Tm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var bc=null;function Sc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Ea=null,xa=null;function bs(t){var e=va(t);if(e&&(t=e.stateNode)){var l=t[me]||null;t:switch(t=e.stateNode,e.type){case"input":if(gc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+je(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[me]||null;if(!n)throw Error(r(90));gc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&hs(a)}break t;case"textarea":ys(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&Ta(t,!!l.multiple,e,!1)}}}var Tc=!1;function Ss(t,e,l){if(Tc)return t(e,l);Tc=!0;try{var a=t(e);return a}finally{if(Tc=!1,(Ea!==null||xa!==null)&&(vi(),Ea&&(e=Ea,t=xa,xa=Ea=null,bs(e),t)))for(e=0;e<t.length;e++)bs(t[e])}}function pn(t,e){var l=t.stateNode;if(l===null)return null;var a=l[me]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(r(231,e,typeof l));return l}var nl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ac=!1;if(nl)try{var bn={};Object.defineProperty(bn,"passive",{get:function(){Ac=!0}}),window.addEventListener("test",bn,bn),window.removeEventListener("test",bn,bn)}catch{Ac=!1}var El=null,Ec=null,Uu=null;function Ts(){if(Uu)return Uu;var t,e=Ec,l=e.length,a,n="value"in El?El.value:El.textContent,i=n.length;for(t=0;t<l&&e[t]===n[t];t++);var s=l-t;for(a=1;a<=s&&e[l-a]===n[i-a];a++);return Uu=n.slice(t,1<a?1-a:void 0)}function ju(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Bu(){return!0}function As(){return!1}function ye(t){function e(l,a,n,i,s){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(l=t[d],this[d]=l?l(i):i[d]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Bu:As,this.isPropagationStopped=As,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Bu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Bu)},persist:function(){},isPersistent:Bu}),e}var Pl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Hu=ye(Pl),Sn=z({},Pl,{view:0,detail:0}),Am=ye(Sn),xc,Oc,Tn,wu=z({},Sn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_c,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Tn&&(Tn&&t.type==="mousemove"?(xc=t.screenX-Tn.screenX,Oc=t.screenY-Tn.screenY):Oc=xc=0,Tn=t),xc)},movementY:function(t){return"movementY"in t?t.movementY:Oc}}),Es=ye(wu),Em=z({},wu,{dataTransfer:0}),xm=ye(Em),Om=z({},Sn,{relatedTarget:0}),Cc=ye(Om),Cm=z({},Pl,{animationName:0,elapsedTime:0,pseudoElement:0}),_m=ye(Cm),zm=z({},Pl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Rm=ye(zm),Mm=z({},Pl,{data:0}),xs=ye(Mm),Dm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Nm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Um={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function jm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Um[t])?!!e[t]:!1}function _c(){return jm}var Bm=z({},Sn,{key:function(t){if(t.key){var e=Dm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ju(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Nm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_c,charCode:function(t){return t.type==="keypress"?ju(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ju(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Hm=ye(Bm),wm=z({},wu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Os=ye(wm),qm=z({},Sn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_c}),Ym=ye(qm),Gm=z({},Pl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qm=ye(Gm),Xm=z({},wu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Lm=ye(Xm),Vm=z({},Pl,{newState:0,oldState:0}),km=ye(Vm),Zm=[9,13,27,32],zc=nl&&"CompositionEvent"in window,An=null;nl&&"documentMode"in document&&(An=document.documentMode);var Km=nl&&"TextEvent"in window&&!An,Cs=nl&&(!zc||An&&8<An&&11>=An),_s=" ",zs=!1;function Rs(t,e){switch(t){case"keyup":return Zm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ms(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Oa=!1;function $m(t,e){switch(t){case"compositionend":return Ms(e);case"keypress":return e.which!==32?null:(zs=!0,_s);case"textInput":return t=e.data,t===_s&&zs?null:t;default:return null}}function Jm(t,e){if(Oa)return t==="compositionend"||!zc&&Rs(t,e)?(t=Ts(),Uu=Ec=El=null,Oa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Cs&&e.locale!=="ko"?null:e.data;default:return null}}var Wm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ds(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Wm[t.type]:e==="textarea"}function Ns(t,e,l,a){Ea?xa?xa.push(a):xa=[a]:Ea=a,e=Ei(e,"onChange"),0<e.length&&(l=new Hu("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var En=null,xn=null;function Fm(t){h0(t,0)}function qu(t){var e=vn(t);if(hs(e))return t}function Us(t,e){if(t==="change")return e}var js=!1;if(nl){var Rc;if(nl){var Mc="oninput"in document;if(!Mc){var Bs=document.createElement("div");Bs.setAttribute("oninput","return;"),Mc=typeof Bs.oninput=="function"}Rc=Mc}else Rc=!1;js=Rc&&(!document.documentMode||9<document.documentMode)}function Hs(){En&&(En.detachEvent("onpropertychange",ws),xn=En=null)}function ws(t){if(t.propertyName==="value"&&qu(xn)){var e=[];Ns(e,xn,t,Sc(t)),Ss(Fm,e)}}function Pm(t,e,l){t==="focusin"?(Hs(),En=e,xn=l,En.attachEvent("onpropertychange",ws)):t==="focusout"&&Hs()}function Im(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return qu(xn)}function ty(t,e){if(t==="click")return qu(e)}function ey(t,e){if(t==="input"||t==="change")return qu(e)}function ly(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ae=typeof Object.is=="function"?Object.is:ly;function On(t,e){if(Ae(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!dn.call(e,n)||!Ae(t[n],e[n]))return!1}return!0}function qs(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Ys(t,e){var l=qs(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=qs(l)}}function Gs(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Gs(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Qs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Du(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Du(t.document)}return e}function Dc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var ay=nl&&"documentMode"in document&&11>=document.documentMode,Ca=null,Nc=null,Cn=null,Uc=!1;function Xs(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Uc||Ca==null||Ca!==Du(a)||(a=Ca,"selectionStart"in a&&Dc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Cn&&On(Cn,a)||(Cn=a,a=Ei(Nc,"onSelect"),0<a.length&&(e=new Hu("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=Ca)))}function Il(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var _a={animationend:Il("Animation","AnimationEnd"),animationiteration:Il("Animation","AnimationIteration"),animationstart:Il("Animation","AnimationStart"),transitionrun:Il("Transition","TransitionRun"),transitionstart:Il("Transition","TransitionStart"),transitioncancel:Il("Transition","TransitionCancel"),transitionend:Il("Transition","TransitionEnd")},jc={},Ls={};nl&&(Ls=document.createElement("div").style,"AnimationEvent"in window||(delete _a.animationend.animation,delete _a.animationiteration.animation,delete _a.animationstart.animation),"TransitionEvent"in window||delete _a.transitionend.transition);function ta(t){if(jc[t])return jc[t];if(!_a[t])return t;var e=_a[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Ls)return jc[t]=e[l];return t}var Vs=ta("animationend"),ks=ta("animationiteration"),Zs=ta("animationstart"),ny=ta("transitionrun"),uy=ta("transitionstart"),iy=ta("transitioncancel"),Ks=ta("transitionend"),$s=new Map,Bc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Bc.push("scrollEnd");function Ve(t,e){$s.set(t,e),Fl(e,[t])}var Js=new WeakMap;function Be(t,e){if(typeof t=="object"&&t!==null){var l=Js.get(t);return l!==void 0?l:(e={value:t,source:e,stack:os(e)},Js.set(t,e),e)}return{value:t,source:e,stack:os(e)}}var He=[],za=0,Hc=0;function Yu(){for(var t=za,e=Hc=za=0;e<t;){var l=He[e];He[e++]=null;var a=He[e];He[e++]=null;var n=He[e];He[e++]=null;var i=He[e];if(He[e++]=null,a!==null&&n!==null){var s=a.pending;s===null?n.next=n:(n.next=s.next,s.next=n),a.pending=n}i!==0&&Ws(l,n,i)}}function Gu(t,e,l,a){He[za++]=t,He[za++]=e,He[za++]=l,He[za++]=a,Hc|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function wc(t,e,l,a){return Gu(t,e,l,a),Qu(t)}function Ra(t,e){return Gu(t,null,null,e),Qu(t)}function Ws(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=t.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-Te(l),t=i.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),i):null}function Qu(t){if(50<Fn)throw Fn=0,Lr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ma={};function cy(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ee(t,e,l,a){return new cy(t,e,l,a)}function qc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function ul(t,e){var l=t.alternate;return l===null?(l=Ee(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Fs(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xu(t,e,l,a,n,i){var s=0;if(a=t,typeof t=="function")qc(t)&&(s=1);else if(typeof t=="string")s=fg(t,l,et.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case xt:return t=Ee(31,l,e,n),t.elementType=xt,t.lanes=i,t;case w:return ea(l.children,n,i,e);case O:s=8,n|=24;break;case L:return t=Ee(12,l,e,n|2),t.elementType=L,t.lanes=i,t;case Q:return t=Ee(13,l,e,n),t.elementType=Q,t.lanes=i,t;case F:return t=Ee(19,l,e,n),t.elementType=F,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case $:case k:s=10;break t;case ut:s=9;break t;case K:s=11;break t;case W:s=14;break t;case ot:s=16,a=null;break t}s=29,l=Error(r(130,t===null?"null":typeof t,"")),a=null}return e=Ee(s,l,e,n),e.elementType=t,e.type=a,e.lanes=i,e}function ea(t,e,l,a){return t=Ee(7,t,a,e),t.lanes=l,t}function Yc(t,e,l){return t=Ee(6,t,null,e),t.lanes=l,t}function Gc(t,e,l){return e=Ee(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Da=[],Na=0,Lu=null,Vu=0,we=[],qe=0,la=null,il=1,cl="";function aa(t,e){Da[Na++]=Vu,Da[Na++]=Lu,Lu=t,Vu=e}function Ps(t,e,l){we[qe++]=il,we[qe++]=cl,we[qe++]=la,la=t;var a=il;t=cl;var n=32-Te(a)-1;a&=~(1<<n),l+=1;var i=32-Te(e)+n;if(30<i){var s=n-n%5;i=(a&(1<<s)-1).toString(32),a>>=s,n-=s,il=1<<32-Te(e)+n|l<<n|a,cl=i+t}else il=1<<i|l<<n|a,cl=t}function Qc(t){t.return!==null&&(aa(t,1),Ps(t,1,0))}function Xc(t){for(;t===Lu;)Lu=Da[--Na],Da[Na]=null,Vu=Da[--Na],Da[Na]=null;for(;t===la;)la=we[--qe],we[qe]=null,cl=we[--qe],we[qe]=null,il=we[--qe],we[qe]=null}var de=null,Qt=null,Tt=!1,na=null,$e=!1,Lc=Error(r(519));function ua(t){var e=Error(r(418,""));throw Rn(Be(e,t)),Lc}function Is(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[re]=t,e[me]=a,l){case"dialog":gt("cancel",e),gt("close",e);break;case"iframe":case"object":case"embed":gt("load",e);break;case"video":case"audio":for(l=0;l<In.length;l++)gt(In[l],e);break;case"source":gt("error",e);break;case"img":case"image":case"link":gt("error",e),gt("load",e);break;case"details":gt("toggle",e);break;case"input":gt("invalid",e),ms(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Mu(e);break;case"select":gt("invalid",e);break;case"textarea":gt("invalid",e),gs(e,a.value,a.defaultValue,a.children),Mu(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||v0(e.textContent,l)?(a.popover!=null&&(gt("beforetoggle",e),gt("toggle",e)),a.onScroll!=null&&gt("scroll",e),a.onScrollEnd!=null&&gt("scrollend",e),a.onClick!=null&&(e.onclick=xi),e=!0):e=!1,e||ua(t)}function to(t){for(de=t.return;de;)switch(de.tag){case 5:case 13:$e=!1;return;case 27:case 3:$e=!0;return;default:de=de.return}}function _n(t){if(t!==de)return!1;if(!Tt)return to(t),Tt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||uf(t.type,t.memoizedProps)),l=!l),l&&Qt&&ua(t),to(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Qt=Ze(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Qt=null}}else e===27?(e=Qt,Yl(t.type)?(t=sf,sf=null,Qt=t):Qt=e):Qt=de?Ze(t.stateNode.nextSibling):null;return!0}function zn(){Qt=de=null,Tt=!1}function eo(){var t=na;return t!==null&&(pe===null?pe=t:pe.push.apply(pe,t),na=null),t}function Rn(t){na===null?na=[t]:na.push(t)}var Vc=H(null),ia=null,rl=null;function xl(t,e,l){V(Vc,e._currentValue),e._currentValue=l}function fl(t){t._currentValue=Vc.current,Z(Vc)}function kc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Zc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var s=n.child;i=i.firstContext;t:for(;i!==null;){var d=i;i=n;for(var y=0;y<e.length;y++)if(d.context===e[y]){i.lanes|=l,d=i.alternate,d!==null&&(d.lanes|=l),kc(i.return,l,t),a||(s=null);break t}i=d.next}}else if(n.tag===18){if(s=n.return,s===null)throw Error(r(341));s.lanes|=l,i=s.alternate,i!==null&&(i.lanes|=l),kc(s,l,t),s=null}else s=n.child;if(s!==null)s.return=n;else for(s=n;s!==null;){if(s===t){s=null;break}if(n=s.sibling,n!==null){n.return=s.return,s=n;break}s=s.return}n=s}}function Mn(t,e,l,a){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var s=n.alternate;if(s===null)throw Error(r(387));if(s=s.memoizedProps,s!==null){var d=n.type;Ae(n.pendingProps.value,s.value)||(t!==null?t.push(d):t=[d])}}else if(n===it.current){if(s=n.alternate,s===null)throw Error(r(387));s.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(uu):t=[uu])}n=n.return}t!==null&&Zc(e,t,l,a),e.flags|=262144}function ku(t){for(t=t.firstContext;t!==null;){if(!Ae(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ca(t){ia=t,rl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function fe(t){return lo(ia,t)}function Zu(t,e){return ia===null&&ca(t),lo(t,e)}function lo(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},rl===null){if(t===null)throw Error(r(308));rl=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else rl=rl.next=e;return l}var ry=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},fy=u.unstable_scheduleCallback,sy=u.unstable_NormalPriority,Ft={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Kc(){return{controller:new ry,data:new Map,refCount:0}}function Dn(t){t.refCount--,t.refCount===0&&fy(sy,function(){t.controller.abort()})}var Nn=null,$c=0,Ua=0,ja=null;function oy(t,e){if(Nn===null){var l=Nn=[];$c=0,Ua=Wr(),ja={status:"pending",value:void 0,then:function(a){l.push(a)}}}return $c++,e.then(ao,ao),e}function ao(){if(--$c===0&&Nn!==null){ja!==null&&(ja.status="fulfilled");var t=Nn;Nn=null,Ua=0,ja=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function dy(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var no=M.S;M.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&oy(t,e),no!==null&&no(t,e)};var ra=H(null);function Jc(){var t=ra.current;return t!==null?t:wt.pooledCache}function Ku(t,e){e===null?V(ra,ra.current):V(ra,e.pool)}function uo(){var t=Jc();return t===null?null:{parent:Ft._currentValue,pool:t}}var Un=Error(r(460)),io=Error(r(474)),$u=Error(r(542)),Wc={then:function(){}};function co(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ju(){}function ro(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(Ju,Ju),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,so(t),t;default:if(typeof e.status=="string")e.then(Ju,Ju);else{if(t=wt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,so(t),t}throw jn=e,Un}}var jn=null;function fo(){if(jn===null)throw Error(r(459));var t=jn;return jn=null,t}function so(t){if(t===Un||t===$u)throw Error(r(483))}var Ol=!1;function Fc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Pc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Cl(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function _l(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(_t&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=Qu(t),Ws(t,null,l),e}return Gu(t,a,e,l),Qu(t)}function Bn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,as(t,l)}}function Ic(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var s={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=s:i=i.next=s,l=l.next}while(l!==null);i===null?n=i=e:i=i.next=e}else n=i=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var tr=!1;function Hn(){if(tr){var t=ja;if(t!==null)throw t}}function wn(t,e,l,a){tr=!1;var n=t.updateQueue;Ol=!1;var i=n.firstBaseUpdate,s=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var y=d,x=y.next;y.next=null,s===null?i=x:s.next=x,s=y;var N=t.alternate;N!==null&&(N=N.updateQueue,d=N.lastBaseUpdate,d!==s&&(d===null?N.firstBaseUpdate=x:d.next=x,N.lastBaseUpdate=y))}if(i!==null){var B=n.baseState;s=0,N=x=y=null,d=i;do{var C=d.lane&-536870913,_=C!==d.lane;if(_?(bt&C)===C:(a&C)===C){C!==0&&C===Ua&&(tr=!0),N!==null&&(N=N.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var ct=t,at=d;C=e;var Nt=l;switch(at.tag){case 1:if(ct=at.payload,typeof ct=="function"){B=ct.call(Nt,B,C);break t}B=ct;break t;case 3:ct.flags=ct.flags&-65537|128;case 0:if(ct=at.payload,C=typeof ct=="function"?ct.call(Nt,B,C):ct,C==null)break t;B=z({},B,C);break t;case 2:Ol=!0}}C=d.callback,C!==null&&(t.flags|=64,_&&(t.flags|=8192),_=n.callbacks,_===null?n.callbacks=[C]:_.push(C))}else _={lane:C,tag:d.tag,payload:d.payload,callback:d.callback,next:null},N===null?(x=N=_,y=B):N=N.next=_,s|=C;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;_=d,d=_.next,_.next=null,n.lastBaseUpdate=_,n.shared.pending=null}}while(!0);N===null&&(y=B),n.baseState=y,n.firstBaseUpdate=x,n.lastBaseUpdate=N,i===null&&(n.shared.lanes=0),Bl|=s,t.lanes=s,t.memoizedState=B}}function oo(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function ho(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)oo(l[t],e)}var Ba=H(null),Wu=H(0);function mo(t,e){t=gl,V(Wu,t),V(Ba,e),gl=t|e.baseLanes}function er(){V(Wu,gl),V(Ba,Ba.current)}function lr(){gl=Wu.current,Z(Ba),Z(Wu)}var zl=0,dt=null,Mt=null,$t=null,Fu=!1,Ha=!1,fa=!1,Pu=0,qn=0,wa=null,hy=0;function kt(){throw Error(r(321))}function ar(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!Ae(t[l],e[l]))return!1;return!0}function nr(t,e,l,a,n,i){return zl=i,dt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,M.H=t===null||t.memoizedState===null?Fo:Po,fa=!1,i=l(a,n),fa=!1,Ha&&(i=go(e,l,a,n)),yo(t),i}function yo(t){M.H=ni;var e=Mt!==null&&Mt.next!==null;if(zl=0,$t=Mt=dt=null,Fu=!1,qn=0,wa=null,e)throw Error(r(300));t===null||ee||(t=t.dependencies,t!==null&&ku(t)&&(ee=!0))}function go(t,e,l,a){dt=t;var n=0;do{if(Ha&&(wa=null),qn=0,Ha=!1,25<=n)throw Error(r(301));if(n+=1,$t=Mt=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}M.H=Sy,i=e(l,a)}while(Ha);return i}function my(){var t=M.H,e=t.useState()[0];return e=typeof e.then=="function"?Yn(e):e,t=t.useState()[0],(Mt!==null?Mt.memoizedState:null)!==t&&(dt.flags|=1024),e}function ur(){var t=Pu!==0;return Pu=0,t}function ir(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function cr(t){if(Fu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Fu=!1}zl=0,$t=Mt=dt=null,Ha=!1,qn=Pu=0,wa=null}function ge(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $t===null?dt.memoizedState=$t=t:$t=$t.next=t,$t}function Jt(){if(Mt===null){var t=dt.alternate;t=t!==null?t.memoizedState:null}else t=Mt.next;var e=$t===null?dt.memoizedState:$t.next;if(e!==null)$t=e,Mt=t;else{if(t===null)throw dt.alternate===null?Error(r(467)):Error(r(310));Mt=t,t={memoizedState:Mt.memoizedState,baseState:Mt.baseState,baseQueue:Mt.baseQueue,queue:Mt.queue,next:null},$t===null?dt.memoizedState=$t=t:$t=$t.next=t}return $t}function rr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Yn(t){var e=qn;return qn+=1,wa===null&&(wa=[]),t=ro(wa,t,e),e=dt,($t===null?e.memoizedState:$t.next)===null&&(e=e.alternate,M.H=e===null||e.memoizedState===null?Fo:Po),t}function Iu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Yn(t);if(t.$$typeof===k)return fe(t)}throw Error(r(438,String(t)))}function fr(t){var e=null,l=dt.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=dt.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=rr(),dt.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=m;return e.index++,l}function sl(t,e){return typeof e=="function"?e(t):e}function ti(t){var e=Jt();return sr(e,Mt,t)}function sr(t,e,l){var a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=l;var n=t.baseQueue,i=a.pending;if(i!==null){if(n!==null){var s=n.next;n.next=i.next,i.next=s}e.baseQueue=n=i,a.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var d=s=null,y=null,x=e,N=!1;do{var B=x.lane&-536870913;if(B!==x.lane?(bt&B)===B:(zl&B)===B){var C=x.revertLane;if(C===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null}),B===Ua&&(N=!0);else if((zl&C)===C){x=x.next,C===Ua&&(N=!0);continue}else B={lane:0,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},y===null?(d=y=B,s=i):y=y.next=B,dt.lanes|=C,Bl|=C;B=x.action,fa&&l(i,B),i=x.hasEagerState?x.eagerState:l(i,B)}else C={lane:B,revertLane:x.revertLane,action:x.action,hasEagerState:x.hasEagerState,eagerState:x.eagerState,next:null},y===null?(d=y=C,s=i):y=y.next=C,dt.lanes|=B,Bl|=B;x=x.next}while(x!==null&&x!==e);if(y===null?s=i:y.next=d,!Ae(i,t.memoizedState)&&(ee=!0,N&&(l=ja,l!==null)))throw l;t.memoizedState=i,t.baseState=s,t.baseQueue=y,a.lastRenderedState=i}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function or(t){var e=Jt(),l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,i=e.memoizedState;if(n!==null){l.pending=null;var s=n=n.next;do i=t(i,s.action),s=s.next;while(s!==n);Ae(i,e.memoizedState)||(ee=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,a]}function vo(t,e,l){var a=dt,n=Jt(),i=Tt;if(i){if(l===void 0)throw Error(r(407));l=l()}else l=e();var s=!Ae((Mt||n).memoizedState,l);s&&(n.memoizedState=l,ee=!0),n=n.queue;var d=So.bind(null,a,n,t);if(Gn(2048,8,d,[t]),n.getSnapshot!==e||s||$t!==null&&$t.memoizedState.tag&1){if(a.flags|=2048,qa(9,ei(),bo.bind(null,a,n,l,e),null),wt===null)throw Error(r(349));i||(zl&124)!==0||po(a,e,l)}return l}function po(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=dt.updateQueue,e===null?(e=rr(),dt.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function bo(t,e,l,a){e.value=l,e.getSnapshot=a,To(e)&&Ao(t)}function So(t,e,l){return l(function(){To(e)&&Ao(t)})}function To(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!Ae(t,l)}catch{return!0}}function Ao(t){var e=Ra(t,2);e!==null&&ze(e,t,2)}function dr(t){var e=ge();if(typeof t=="function"){var l=t;if(t=l(),fa){Tl(!0);try{l()}finally{Tl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:t},e}function Eo(t,e,l,a){return t.baseState=l,sr(t,Mt,typeof a=="function"?a:sl)}function yy(t,e,l,a,n){if(ai(t))throw Error(r(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(s){i.listeners.push(s)}};M.T!==null?l(!0):i.isTransition=!1,a(i),l=e.pending,l===null?(i.next=e.pending=i,xo(e,i)):(i.next=l.next,e.pending=l.next=i)}}function xo(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var i=M.T,s={};M.T=s;try{var d=l(n,a),y=M.S;y!==null&&y(s,d),Oo(t,e,d)}catch(x){hr(t,e,x)}finally{M.T=i}}else try{i=l(n,a),Oo(t,e,i)}catch(x){hr(t,e,x)}}function Oo(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Co(t,e,a)},function(a){return hr(t,e,a)}):Co(t,e,l)}function Co(t,e,l){e.status="fulfilled",e.value=l,_o(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,xo(t,l)))}function hr(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,_o(e),e=e.next;while(e!==a)}t.action=null}function _o(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function zo(t,e){return e}function Ro(t,e){if(Tt){var l=wt.formState;if(l!==null){t:{var a=dt;if(Tt){if(Qt){e:{for(var n=Qt,i=$e;n.nodeType!==8;){if(!i){n=null;break e}if(n=Ze(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Qt=Ze(n.nextSibling),a=n.data==="F!";break t}}ua(a)}a=!1}a&&(e=l[0])}}return l=ge(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:zo,lastRenderedState:e},l.queue=a,l=$o.bind(null,dt,a),a.dispatch=l,a=dr(!1),i=pr.bind(null,dt,!1,a.queue),a=ge(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=yy.bind(null,dt,n,i,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Mo(t){var e=Jt();return Do(e,Mt,t)}function Do(t,e,l){if(e=sr(t,e,zo)[0],t=ti(sl)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Yn(e)}catch(s){throw s===Un?$u:s}else a=e;e=Jt();var n=e.queue,i=n.dispatch;return l!==e.memoizedState&&(dt.flags|=2048,qa(9,ei(),gy.bind(null,n,l),null)),[a,i,t]}function gy(t,e){t.action=e}function No(t){var e=Jt(),l=Mt;if(l!==null)return Do(e,l,t);Jt(),e=e.memoizedState,l=Jt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function qa(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=dt.updateQueue,e===null&&(e=rr(),dt.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function ei(){return{destroy:void 0,resource:void 0}}function Uo(){return Jt().memoizedState}function li(t,e,l,a){var n=ge();a=a===void 0?null:a,dt.flags|=t,n.memoizedState=qa(1|e,ei(),l,a)}function Gn(t,e,l,a){var n=Jt();a=a===void 0?null:a;var i=n.memoizedState.inst;Mt!==null&&a!==null&&ar(a,Mt.memoizedState.deps)?n.memoizedState=qa(e,i,l,a):(dt.flags|=t,n.memoizedState=qa(1|e,i,l,a))}function jo(t,e){li(8390656,8,t,e)}function Bo(t,e){Gn(2048,8,t,e)}function Ho(t,e){return Gn(4,2,t,e)}function wo(t,e){return Gn(4,4,t,e)}function qo(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Yo(t,e,l){l=l!=null?l.concat([t]):null,Gn(4,4,qo.bind(null,e,t),l)}function mr(){}function Go(t,e){var l=Jt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&ar(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Qo(t,e){var l=Jt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&ar(e,a[1]))return a[0];if(a=t(),fa){Tl(!0);try{t()}finally{Tl(!1)}}return l.memoizedState=[a,e],a}function yr(t,e,l){return l===void 0||(zl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Vd(),dt.lanes|=t,Bl|=t,l)}function Xo(t,e,l,a){return Ae(l,e)?l:Ba.current!==null?(t=yr(t,l,a),Ae(t,e)||(ee=!0),t):(zl&42)===0?(ee=!0,t.memoizedState=l):(t=Vd(),dt.lanes|=t,Bl|=t,e)}function Lo(t,e,l,a,n){var i=G.p;G.p=i!==0&&8>i?i:8;var s=M.T,d={};M.T=d,pr(t,!1,e,l);try{var y=n(),x=M.S;if(x!==null&&x(d,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var N=dy(y,a);Qn(t,e,N,_e(t))}else Qn(t,e,a,_e(t))}catch(B){Qn(t,e,{then:function(){},status:"rejected",reason:B},_e())}finally{G.p=i,M.T=s}}function vy(){}function gr(t,e,l,a){if(t.tag!==5)throw Error(r(476));var n=Vo(t).queue;Lo(t,n,e,tt,l===null?vy:function(){return ko(t),l(a)})}function Vo(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:tt,baseState:tt,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:tt},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function ko(t){var e=Vo(t).next.queue;Qn(t,e,{},_e())}function vr(){return fe(uu)}function Zo(){return Jt().memoizedState}function Ko(){return Jt().memoizedState}function py(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=_e();t=Cl(l);var a=_l(e,t,l);a!==null&&(ze(a,e,l),Bn(a,e,l)),e={cache:Kc()},t.payload=e;return}e=e.return}}function by(t,e,l){var a=_e();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},ai(t)?Jo(e,l):(l=wc(t,e,l,a),l!==null&&(ze(l,t,a),Wo(l,e,a)))}function $o(t,e,l){var a=_e();Qn(t,e,l,a)}function Qn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(ai(t))Jo(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var s=e.lastRenderedState,d=i(s,l);if(n.hasEagerState=!0,n.eagerState=d,Ae(d,s))return Gu(t,e,n,0),wt===null&&Yu(),!1}catch{}finally{}if(l=wc(t,e,n,a),l!==null)return ze(l,t,a),Wo(l,e,a),!0}return!1}function pr(t,e,l,a){if(a={lane:2,revertLane:Wr(),action:a,hasEagerState:!1,eagerState:null,next:null},ai(t)){if(e)throw Error(r(479))}else e=wc(t,l,a,2),e!==null&&ze(e,t,2)}function ai(t){var e=t.alternate;return t===dt||e!==null&&e===dt}function Jo(t,e){Ha=Fu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Wo(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,as(t,l)}}var ni={readContext:fe,use:Iu,useCallback:kt,useContext:kt,useEffect:kt,useImperativeHandle:kt,useLayoutEffect:kt,useInsertionEffect:kt,useMemo:kt,useReducer:kt,useRef:kt,useState:kt,useDebugValue:kt,useDeferredValue:kt,useTransition:kt,useSyncExternalStore:kt,useId:kt,useHostTransitionStatus:kt,useFormState:kt,useActionState:kt,useOptimistic:kt,useMemoCache:kt,useCacheRefresh:kt},Fo={readContext:fe,use:Iu,useCallback:function(t,e){return ge().memoizedState=[t,e===void 0?null:e],t},useContext:fe,useEffect:jo,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,li(4194308,4,qo.bind(null,e,t),l)},useLayoutEffect:function(t,e){return li(4194308,4,t,e)},useInsertionEffect:function(t,e){li(4,2,t,e)},useMemo:function(t,e){var l=ge();e=e===void 0?null:e;var a=t();if(fa){Tl(!0);try{t()}finally{Tl(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=ge();if(l!==void 0){var n=l(e);if(fa){Tl(!0);try{l(e)}finally{Tl(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=by.bind(null,dt,t),[a.memoizedState,t]},useRef:function(t){var e=ge();return t={current:t},e.memoizedState=t},useState:function(t){t=dr(t);var e=t.queue,l=$o.bind(null,dt,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:mr,useDeferredValue:function(t,e){var l=ge();return yr(l,t,e)},useTransition:function(){var t=dr(!1);return t=Lo.bind(null,dt,t.queue,!0,!1),ge().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=dt,n=ge();if(Tt){if(l===void 0)throw Error(r(407));l=l()}else{if(l=e(),wt===null)throw Error(r(349));(bt&124)!==0||po(a,e,l)}n.memoizedState=l;var i={value:l,getSnapshot:e};return n.queue=i,jo(So.bind(null,a,i,t),[t]),a.flags|=2048,qa(9,ei(),bo.bind(null,a,i,l,e),null),l},useId:function(){var t=ge(),e=wt.identifierPrefix;if(Tt){var l=cl,a=il;l=(a&~(1<<32-Te(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=Pu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=hy++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:vr,useFormState:Ro,useActionState:Ro,useOptimistic:function(t){var e=ge();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=pr.bind(null,dt,!0,l),l.dispatch=e,[t,e]},useMemoCache:fr,useCacheRefresh:function(){return ge().memoizedState=py.bind(null,dt)}},Po={readContext:fe,use:Iu,useCallback:Go,useContext:fe,useEffect:Bo,useImperativeHandle:Yo,useInsertionEffect:Ho,useLayoutEffect:wo,useMemo:Qo,useReducer:ti,useRef:Uo,useState:function(){return ti(sl)},useDebugValue:mr,useDeferredValue:function(t,e){var l=Jt();return Xo(l,Mt.memoizedState,t,e)},useTransition:function(){var t=ti(sl)[0],e=Jt().memoizedState;return[typeof t=="boolean"?t:Yn(t),e]},useSyncExternalStore:vo,useId:Zo,useHostTransitionStatus:vr,useFormState:Mo,useActionState:Mo,useOptimistic:function(t,e){var l=Jt();return Eo(l,Mt,t,e)},useMemoCache:fr,useCacheRefresh:Ko},Sy={readContext:fe,use:Iu,useCallback:Go,useContext:fe,useEffect:Bo,useImperativeHandle:Yo,useInsertionEffect:Ho,useLayoutEffect:wo,useMemo:Qo,useReducer:or,useRef:Uo,useState:function(){return or(sl)},useDebugValue:mr,useDeferredValue:function(t,e){var l=Jt();return Mt===null?yr(l,t,e):Xo(l,Mt.memoizedState,t,e)},useTransition:function(){var t=or(sl)[0],e=Jt().memoizedState;return[typeof t=="boolean"?t:Yn(t),e]},useSyncExternalStore:vo,useId:Zo,useHostTransitionStatus:vr,useFormState:No,useActionState:No,useOptimistic:function(t,e){var l=Jt();return Mt!==null?Eo(l,Mt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:fr,useCacheRefresh:Ko},Ya=null,Xn=0;function ui(t){var e=Xn;return Xn+=1,Ya===null&&(Ya=[]),ro(Ya,t,e)}function Ln(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ii(t,e){throw e.$$typeof===R?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Io(t){var e=t._init;return e(t._payload)}function td(t){function e(T,b){if(t){var E=T.deletions;E===null?(T.deletions=[b],T.flags|=16):E.push(b)}}function l(T,b){if(!t)return null;for(;b!==null;)e(T,b),b=b.sibling;return null}function a(T){for(var b=new Map;T!==null;)T.key!==null?b.set(T.key,T):b.set(T.index,T),T=T.sibling;return b}function n(T,b){return T=ul(T,b),T.index=0,T.sibling=null,T}function i(T,b,E){return T.index=E,t?(E=T.alternate,E!==null?(E=E.index,E<b?(T.flags|=67108866,b):E):(T.flags|=67108866,b)):(T.flags|=1048576,b)}function s(T){return t&&T.alternate===null&&(T.flags|=67108866),T}function d(T,b,E,j){return b===null||b.tag!==6?(b=Yc(E,T.mode,j),b.return=T,b):(b=n(b,E),b.return=T,b)}function y(T,b,E,j){var J=E.type;return J===w?N(T,b,E.props.children,j,E.key):b!==null&&(b.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===ot&&Io(J)===b.type)?(b=n(b,E.props),Ln(b,E),b.return=T,b):(b=Xu(E.type,E.key,E.props,null,T.mode,j),Ln(b,E),b.return=T,b)}function x(T,b,E,j){return b===null||b.tag!==4||b.stateNode.containerInfo!==E.containerInfo||b.stateNode.implementation!==E.implementation?(b=Gc(E,T.mode,j),b.return=T,b):(b=n(b,E.children||[]),b.return=T,b)}function N(T,b,E,j,J){return b===null||b.tag!==7?(b=ea(E,T.mode,j,J),b.return=T,b):(b=n(b,E),b.return=T,b)}function B(T,b,E){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Yc(""+b,T.mode,E),b.return=T,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case U:return E=Xu(b.type,b.key,b.props,null,T.mode,E),Ln(E,b),E.return=T,E;case Y:return b=Gc(b,T.mode,E),b.return=T,b;case ot:var j=b._init;return b=j(b._payload),B(T,b,E)}if(Ot(b)||I(b))return b=ea(b,T.mode,E,null),b.return=T,b;if(typeof b.then=="function")return B(T,ui(b),E);if(b.$$typeof===k)return B(T,Zu(T,b),E);ii(T,b)}return null}function C(T,b,E,j){var J=b!==null?b.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return J!==null?null:d(T,b,""+E,j);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case U:return E.key===J?y(T,b,E,j):null;case Y:return E.key===J?x(T,b,E,j):null;case ot:return J=E._init,E=J(E._payload),C(T,b,E,j)}if(Ot(E)||I(E))return J!==null?null:N(T,b,E,j,null);if(typeof E.then=="function")return C(T,b,ui(E),j);if(E.$$typeof===k)return C(T,b,Zu(T,E),j);ii(T,E)}return null}function _(T,b,E,j,J){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return T=T.get(E)||null,d(b,T,""+j,J);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case U:return T=T.get(j.key===null?E:j.key)||null,y(b,T,j,J);case Y:return T=T.get(j.key===null?E:j.key)||null,x(b,T,j,J);case ot:var mt=j._init;return j=mt(j._payload),_(T,b,E,j,J)}if(Ot(j)||I(j))return T=T.get(E)||null,N(b,T,j,J,null);if(typeof j.then=="function")return _(T,b,E,ui(j),J);if(j.$$typeof===k)return _(T,b,E,Zu(b,j),J);ii(b,j)}return null}function ct(T,b,E,j){for(var J=null,mt=null,P=b,nt=b=0,ae=null;P!==null&&nt<E.length;nt++){P.index>nt?(ae=P,P=null):ae=P.sibling;var St=C(T,P,E[nt],j);if(St===null){P===null&&(P=ae);break}t&&P&&St.alternate===null&&e(T,P),b=i(St,b,nt),mt===null?J=St:mt.sibling=St,mt=St,P=ae}if(nt===E.length)return l(T,P),Tt&&aa(T,nt),J;if(P===null){for(;nt<E.length;nt++)P=B(T,E[nt],j),P!==null&&(b=i(P,b,nt),mt===null?J=P:mt.sibling=P,mt=P);return Tt&&aa(T,nt),J}for(P=a(P);nt<E.length;nt++)ae=_(P,T,nt,E[nt],j),ae!==null&&(t&&ae.alternate!==null&&P.delete(ae.key===null?nt:ae.key),b=i(ae,b,nt),mt===null?J=ae:mt.sibling=ae,mt=ae);return t&&P.forEach(function(Vl){return e(T,Vl)}),Tt&&aa(T,nt),J}function at(T,b,E,j){if(E==null)throw Error(r(151));for(var J=null,mt=null,P=b,nt=b=0,ae=null,St=E.next();P!==null&&!St.done;nt++,St=E.next()){P.index>nt?(ae=P,P=null):ae=P.sibling;var Vl=C(T,P,St.value,j);if(Vl===null){P===null&&(P=ae);break}t&&P&&Vl.alternate===null&&e(T,P),b=i(Vl,b,nt),mt===null?J=Vl:mt.sibling=Vl,mt=Vl,P=ae}if(St.done)return l(T,P),Tt&&aa(T,nt),J;if(P===null){for(;!St.done;nt++,St=E.next())St=B(T,St.value,j),St!==null&&(b=i(St,b,nt),mt===null?J=St:mt.sibling=St,mt=St);return Tt&&aa(T,nt),J}for(P=a(P);!St.done;nt++,St=E.next())St=_(P,T,nt,St.value,j),St!==null&&(t&&St.alternate!==null&&P.delete(St.key===null?nt:St.key),b=i(St,b,nt),mt===null?J=St:mt.sibling=St,mt=St);return t&&P.forEach(function(Tg){return e(T,Tg)}),Tt&&aa(T,nt),J}function Nt(T,b,E,j){if(typeof E=="object"&&E!==null&&E.type===w&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case U:t:{for(var J=E.key;b!==null;){if(b.key===J){if(J=E.type,J===w){if(b.tag===7){l(T,b.sibling),j=n(b,E.props.children),j.return=T,T=j;break t}}else if(b.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===ot&&Io(J)===b.type){l(T,b.sibling),j=n(b,E.props),Ln(j,E),j.return=T,T=j;break t}l(T,b);break}else e(T,b);b=b.sibling}E.type===w?(j=ea(E.props.children,T.mode,j,E.key),j.return=T,T=j):(j=Xu(E.type,E.key,E.props,null,T.mode,j),Ln(j,E),j.return=T,T=j)}return s(T);case Y:t:{for(J=E.key;b!==null;){if(b.key===J)if(b.tag===4&&b.stateNode.containerInfo===E.containerInfo&&b.stateNode.implementation===E.implementation){l(T,b.sibling),j=n(b,E.children||[]),j.return=T,T=j;break t}else{l(T,b);break}else e(T,b);b=b.sibling}j=Gc(E,T.mode,j),j.return=T,T=j}return s(T);case ot:return J=E._init,E=J(E._payload),Nt(T,b,E,j)}if(Ot(E))return ct(T,b,E,j);if(I(E)){if(J=I(E),typeof J!="function")throw Error(r(150));return E=J.call(E),at(T,b,E,j)}if(typeof E.then=="function")return Nt(T,b,ui(E),j);if(E.$$typeof===k)return Nt(T,b,Zu(T,E),j);ii(T,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,b!==null&&b.tag===6?(l(T,b.sibling),j=n(b,E),j.return=T,T=j):(l(T,b),j=Yc(E,T.mode,j),j.return=T,T=j),s(T)):l(T,b)}return function(T,b,E,j){try{Xn=0;var J=Nt(T,b,E,j);return Ya=null,J}catch(P){if(P===Un||P===$u)throw P;var mt=Ee(29,P,null,T.mode);return mt.lanes=j,mt.return=T,mt}finally{}}}var Ga=td(!0),ed=td(!1),Ye=H(null),Je=null;function Rl(t){var e=t.alternate;V(Pt,Pt.current&1),V(Ye,t),Je===null&&(e===null||Ba.current!==null||e.memoizedState!==null)&&(Je=t)}function ld(t){if(t.tag===22){if(V(Pt,Pt.current),V(Ye,t),Je===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Je=t)}}else Ml()}function Ml(){V(Pt,Pt.current),V(Ye,Ye.current)}function ol(t){Z(Ye),Je===t&&(Je=null),Z(Pt)}var Pt=H(0);function ci(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||ff(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function br(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var Sr={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=_e(),n=Cl(a);n.payload=e,l!=null&&(n.callback=l),e=_l(t,n,a),e!==null&&(ze(e,t,a),Bn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=_e(),n=Cl(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=_l(t,n,a),e!==null&&(ze(e,t,a),Bn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=_e(),a=Cl(l);a.tag=2,e!=null&&(a.callback=e),e=_l(t,a,l),e!==null&&(ze(e,t,l),Bn(e,t,l))}};function ad(t,e,l,a,n,i,s){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,i,s):e.prototype&&e.prototype.isPureReactComponent?!On(l,a)||!On(n,i):!0}function nd(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&Sr.enqueueReplaceState(e,e.state,null)}function sa(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var ri=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function ud(t){ri(t)}function id(t){console.error(t)}function cd(t){ri(t)}function fi(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function rd(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function Tr(t,e,l){return l=Cl(l),l.tag=3,l.payload={element:null},l.callback=function(){fi(t,e)},l}function fd(t){return t=Cl(t),t.tag=3,t}function sd(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;t.payload=function(){return n(i)},t.callback=function(){rd(e,l,a)}}var s=l.stateNode;s!==null&&typeof s.componentDidCatch=="function"&&(t.callback=function(){rd(e,l,a),typeof n!="function"&&(Hl===null?Hl=new Set([this]):Hl.add(this));var d=a.stack;this.componentDidCatch(a.value,{componentStack:d!==null?d:""})})}function Ty(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&Mn(e,l,n,!0),l=Ye.current,l!==null){switch(l.tag){case 13:return Je===null?kr():l.alternate===null&&Xt===0&&(Xt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Wc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Kr(t,a,n)),!1;case 22:return l.flags|=65536,a===Wc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Kr(t,a,n)),!1}throw Error(r(435,l.tag))}return Kr(t,a,n),kr(),!1}if(Tt)return e=Ye.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Lc&&(t=Error(r(422),{cause:a}),Rn(Be(t,l)))):(a!==Lc&&(e=Error(r(423),{cause:a}),Rn(Be(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=Be(a,l),n=Tr(t.stateNode,a,n),Ic(t,n),Xt!==4&&(Xt=2)),!1;var i=Error(r(520),{cause:a});if(i=Be(i,l),Wn===null?Wn=[i]:Wn.push(i),Xt!==4&&(Xt=2),e===null)return!0;a=Be(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=Tr(l.stateNode,a,t),Ic(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Hl===null||!Hl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=fd(n),sd(n,t,l,a),Ic(l,n),!1}l=l.return}while(l!==null);return!1}var od=Error(r(461)),ee=!1;function ne(t,e,l,a){e.child=t===null?ed(e,null,l,a):Ga(e,t.child,l,a)}function dd(t,e,l,a,n){l=l.render;var i=e.ref;if("ref"in a){var s={};for(var d in a)d!=="ref"&&(s[d]=a[d])}else s=a;return ca(e),a=nr(t,e,l,s,i,n),d=ur(),t!==null&&!ee?(ir(t,e,n),dl(t,e,n)):(Tt&&d&&Qc(e),e.flags|=1,ne(t,e,a,n),e.child)}function hd(t,e,l,a,n){if(t===null){var i=l.type;return typeof i=="function"&&!qc(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,md(t,e,i,a,n)):(t=Xu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Rr(t,n)){var s=i.memoizedProps;if(l=l.compare,l=l!==null?l:On,l(s,a)&&t.ref===e.ref)return dl(t,e,n)}return e.flags|=1,t=ul(i,a),t.ref=e.ref,t.return=e,e.child=t}function md(t,e,l,a,n){if(t!==null){var i=t.memoizedProps;if(On(i,a)&&t.ref===e.ref)if(ee=!1,e.pendingProps=a=i,Rr(t,n))(t.flags&131072)!==0&&(ee=!0);else return e.lanes=t.lanes,dl(t,e,n)}return Ar(t,e,l,a,n)}function yd(t,e,l){var a=e.pendingProps,n=a.children,i=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~a}else e.childLanes=0,e.child=null;return gd(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Ku(e,i!==null?i.cachePool:null),i!==null?mo(e,i):er(),ld(e);else return e.lanes=e.childLanes=536870912,gd(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(Ku(e,i.cachePool),mo(e,i),Ml(),e.memoizedState=null):(t!==null&&Ku(e,null),er(),Ml());return ne(t,e,n,l),e.child}function gd(t,e,l,a){var n=Jc();return n=n===null?null:{parent:Ft._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&Ku(e,null),er(),ld(e),t!==null&&Mn(t,e,a,!0),null}function si(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(r(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function Ar(t,e,l,a,n){return ca(e),l=nr(t,e,l,a,void 0,n),a=ur(),t!==null&&!ee?(ir(t,e,n),dl(t,e,n)):(Tt&&a&&Qc(e),e.flags|=1,ne(t,e,l,n),e.child)}function vd(t,e,l,a,n,i){return ca(e),e.updateQueue=null,l=go(e,a,l,n),yo(t),a=ur(),t!==null&&!ee?(ir(t,e,i),dl(t,e,i)):(Tt&&a&&Qc(e),e.flags|=1,ne(t,e,l,i),e.child)}function pd(t,e,l,a,n){if(ca(e),e.stateNode===null){var i=Ma,s=l.contextType;typeof s=="object"&&s!==null&&(i=fe(s)),i=new l(a,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Sr,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=a,i.state=e.memoizedState,i.refs={},Fc(e),s=l.contextType,i.context=typeof s=="object"&&s!==null?fe(s):Ma,i.state=e.memoizedState,s=l.getDerivedStateFromProps,typeof s=="function"&&(br(e,l,s,a),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(s=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),s!==i.state&&Sr.enqueueReplaceState(i,i.state,null),wn(e,a,i,n),Hn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){i=e.stateNode;var d=e.memoizedProps,y=sa(l,d);i.props=y;var x=i.context,N=l.contextType;s=Ma,typeof N=="object"&&N!==null&&(s=fe(N));var B=l.getDerivedStateFromProps;N=typeof B=="function"||typeof i.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,N||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(d||x!==s)&&nd(e,i,a,s),Ol=!1;var C=e.memoizedState;i.state=C,wn(e,a,i,n),Hn(),x=e.memoizedState,d||C!==x||Ol?(typeof B=="function"&&(br(e,l,B,a),x=e.memoizedState),(y=Ol||ad(e,l,y,a,C,x,s))?(N||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=x),i.props=a,i.state=x,i.context=s,a=y):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{i=e.stateNode,Pc(t,e),s=e.memoizedProps,N=sa(l,s),i.props=N,B=e.pendingProps,C=i.context,x=l.contextType,y=Ma,typeof x=="object"&&x!==null&&(y=fe(x)),d=l.getDerivedStateFromProps,(x=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==B||C!==y)&&nd(e,i,a,y),Ol=!1,C=e.memoizedState,i.state=C,wn(e,a,i,n),Hn();var _=e.memoizedState;s!==B||C!==_||Ol||t!==null&&t.dependencies!==null&&ku(t.dependencies)?(typeof d=="function"&&(br(e,l,d,a),_=e.memoizedState),(N=Ol||ad(e,l,N,a,C,_,y)||t!==null&&t.dependencies!==null&&ku(t.dependencies))?(x||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,_,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,_,y)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=_),i.props=a,i.state=_,i.context=y,a=N):(typeof i.componentDidUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),a=!1)}return i=a,si(t,e),a=(e.flags&128)!==0,i||a?(i=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&a?(e.child=Ga(e,t.child,null,n),e.child=Ga(e,null,l,n)):ne(t,e,l,n),e.memoizedState=i.state,t=e.child):t=dl(t,e,n),t}function bd(t,e,l,a){return zn(),e.flags|=256,ne(t,e,l,a),e.child}var Er={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function xr(t){return{baseLanes:t,cachePool:uo()}}function Or(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=Ge),t}function Sd(t,e,l){var a=e.pendingProps,n=!1,i=(e.flags&128)!==0,s;if((s=i)||(s=t!==null&&t.memoizedState===null?!1:(Pt.current&2)!==0),s&&(n=!0,e.flags&=-129),s=(e.flags&32)!==0,e.flags&=-33,t===null){if(Tt){if(n?Rl(e):Ml(),Tt){var d=Qt,y;if(y=d){t:{for(y=d,d=$e;y.nodeType!==8;){if(!d){d=null;break t}if(y=Ze(y.nextSibling),y===null){d=null;break t}}d=y}d!==null?(e.memoizedState={dehydrated:d,treeContext:la!==null?{id:il,overflow:cl}:null,retryLane:536870912,hydrationErrors:null},y=Ee(18,null,null,0),y.stateNode=d,y.return=e,e.child=y,de=e,Qt=null,y=!0):y=!1}y||ua(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return ff(d)?e.lanes=32:e.lanes=536870912,null;ol(e)}return d=a.children,a=a.fallback,n?(Ml(),n=e.mode,d=oi({mode:"hidden",children:d},n),a=ea(a,n,l,null),d.return=e,a.return=e,d.sibling=a,e.child=d,n=e.child,n.memoizedState=xr(l),n.childLanes=Or(t,s,l),e.memoizedState=Er,a):(Rl(e),Cr(e,d))}if(y=t.memoizedState,y!==null&&(d=y.dehydrated,d!==null)){if(i)e.flags&256?(Rl(e),e.flags&=-257,e=_r(t,e,l)):e.memoizedState!==null?(Ml(),e.child=t.child,e.flags|=128,e=null):(Ml(),n=a.fallback,d=e.mode,a=oi({mode:"visible",children:a.children},d),n=ea(n,d,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ga(e,t.child,null,l),a=e.child,a.memoizedState=xr(l),a.childLanes=Or(t,s,l),e.memoizedState=Er,e=n);else if(Rl(e),ff(d)){if(s=d.nextSibling&&d.nextSibling.dataset,s)var x=s.dgst;s=x,a=Error(r(419)),a.stack="",a.digest=s,Rn({value:a,source:null,stack:null}),e=_r(t,e,l)}else if(ee||Mn(t,e,l,!1),s=(l&t.childLanes)!==0,ee||s){if(s=wt,s!==null&&(a=l&-l,a=(a&42)!==0?1:fc(a),a=(a&(s.suspendedLanes|l))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,Ra(t,a),ze(s,t,a),od;d.data==="$?"||kr(),e=_r(t,e,l)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,Qt=Ze(d.nextSibling),de=e,Tt=!0,na=null,$e=!1,t!==null&&(we[qe++]=il,we[qe++]=cl,we[qe++]=la,il=t.id,cl=t.overflow,la=e),e=Cr(e,a.children),e.flags|=4096);return e}return n?(Ml(),n=a.fallback,d=e.mode,y=t.child,x=y.sibling,a=ul(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,x!==null?n=ul(x,n):(n=ea(n,d,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,d=t.child.memoizedState,d===null?d=xr(l):(y=d.cachePool,y!==null?(x=Ft._currentValue,y=y.parent!==x?{parent:x,pool:x}:y):y=uo(),d={baseLanes:d.baseLanes|l,cachePool:y}),n.memoizedState=d,n.childLanes=Or(t,s,l),e.memoizedState=Er,a):(Rl(e),l=t.child,t=l.sibling,l=ul(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(s=e.deletions,s===null?(e.deletions=[t],e.flags|=16):s.push(t)),e.child=l,e.memoizedState=null,l)}function Cr(t,e){return e=oi({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function oi(t,e){return t=Ee(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function _r(t,e,l){return Ga(e,t.child,null,l),t=Cr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Td(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),kc(t.return,e,l)}function zr(t,e,l,a,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function Ad(t,e,l){var a=e.pendingProps,n=a.revealOrder,i=a.tail;if(ne(t,e,a.children,l),a=Pt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Td(t,l,e);else if(t.tag===19)Td(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(V(Pt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&ci(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),zr(e,!1,n,l,i);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&ci(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}zr(e,!0,l,null,i);break;case"together":zr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function dl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),Bl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(Mn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,l=ul(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=ul(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Rr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&ku(t)))}function Ay(t,e,l){switch(e.tag){case 3:ht(e,e.stateNode.containerInfo),xl(e,Ft,t.memoizedState.cache),zn();break;case 27:case 5:It(e);break;case 4:ht(e,e.stateNode.containerInfo);break;case 10:xl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(Rl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?Sd(t,e,l):(Rl(e),t=dl(t,e,l),t!==null?t.sibling:null);Rl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(Mn(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return Ad(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),V(Pt,Pt.current),a)break;return null;case 22:case 23:return e.lanes=0,yd(t,e,l);case 24:xl(e,Ft,t.memoizedState.cache)}return dl(t,e,l)}function Ed(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)ee=!0;else{if(!Rr(t,l)&&(e.flags&128)===0)return ee=!1,Ay(t,e,l);ee=(t.flags&131072)!==0}else ee=!1,Tt&&(e.flags&1048576)!==0&&Ps(e,Vu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")qc(a)?(t=sa(a,t),e.tag=1,e=pd(null,e,a,t,l)):(e.tag=0,e=Ar(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===K){e.tag=11,e=dd(null,e,a,t,l);break t}else if(n===W){e.tag=14,e=hd(null,e,a,t,l);break t}}throw e=Rt(a)||a,Error(r(306,e,""))}}return e;case 0:return Ar(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=sa(a,e.pendingProps),pd(t,e,a,n,l);case 3:t:{if(ht(e,e.stateNode.containerInfo),t===null)throw Error(r(387));a=e.pendingProps;var i=e.memoizedState;n=i.element,Pc(t,e),wn(e,a,null,l);var s=e.memoizedState;if(a=s.cache,xl(e,Ft,a),a!==i.cache&&Zc(e,[Ft],l,!0),Hn(),a=s.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:s.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=bd(t,e,a,l);break t}else if(a!==n){n=Be(Error(r(424)),e),Rn(n),e=bd(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Qt=Ze(t.firstChild),de=e,Tt=!0,na=null,$e=!0,l=ed(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(zn(),a===n){e=dl(t,e,l);break t}ne(t,e,a,l)}e=e.child}return e;case 26:return si(t,e),t===null?(l=_0(e.type,null,e.pendingProps,null))?e.memoizedState=l:Tt||(l=e.type,t=e.pendingProps,a=Oi(rt.current).createElement(l),a[re]=e,a[me]=t,ie(a,l,t),te(a),e.stateNode=a):e.memoizedState=_0(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return It(e),t===null&&Tt&&(a=e.stateNode=x0(e.type,e.pendingProps,rt.current),de=e,$e=!0,n=Qt,Yl(e.type)?(sf=n,Qt=Ze(a.firstChild)):Qt=n),ne(t,e,e.pendingProps.children,l),si(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&Tt&&((n=a=Qt)&&(a=Wy(a,e.type,e.pendingProps,$e),a!==null?(e.stateNode=a,de=e,Qt=Ze(a.firstChild),$e=!1,n=!0):n=!1),n||ua(e)),It(e),n=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,a=i.children,uf(n,i)?a=null:s!==null&&uf(n,s)&&(e.flags|=32),e.memoizedState!==null&&(n=nr(t,e,my,null,null,l),uu._currentValue=n),si(t,e),ne(t,e,a,l),e.child;case 6:return t===null&&Tt&&((t=l=Qt)&&(l=Fy(l,e.pendingProps,$e),l!==null?(e.stateNode=l,de=e,Qt=null,t=!0):t=!1),t||ua(e)),null;case 13:return Sd(t,e,l);case 4:return ht(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ga(e,null,a,l):ne(t,e,a,l),e.child;case 11:return dd(t,e,e.type,e.pendingProps,l);case 7:return ne(t,e,e.pendingProps,l),e.child;case 8:return ne(t,e,e.pendingProps.children,l),e.child;case 12:return ne(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,xl(e,e.type,a.value),ne(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,ca(e),n=fe(n),a=a(n),e.flags|=1,ne(t,e,a,l),e.child;case 14:return hd(t,e,e.type,e.pendingProps,l);case 15:return md(t,e,e.type,e.pendingProps,l);case 19:return Ad(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=oi(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=ul(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return yd(t,e,l);case 24:return ca(e),a=fe(Ft),t===null?(n=Jc(),n===null&&(n=wt,i=Kc(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),e.memoizedState={parent:a,cache:n},Fc(e),xl(e,Ft,n)):((t.lanes&l)!==0&&(Pc(t,e),wn(e,null,null,l),Hn()),n=t.memoizedState,i=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),xl(e,Ft,a)):(a=i.cache,xl(e,Ft,a),a!==n.cache&&Zc(e,[Ft],l,!0))),ne(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function hl(t){t.flags|=4}function xd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!N0(e)){if(e=Ye.current,e!==null&&((bt&4194048)===bt?Je!==null:(bt&62914560)!==bt&&(bt&536870912)===0||e!==Je))throw jn=Wc,io;t.flags|=8192}}function di(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?es():536870912,t.lanes|=e,Va|=e)}function Vn(t,e){if(!Tt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Gt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function Ey(t,e,l){var a=e.pendingProps;switch(Xc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Gt(e),null;case 1:return Gt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),fl(Ft),pt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(_n(e)?hl(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,eo())),Gt(e),null;case 26:return l=e.memoizedState,t===null?(hl(e),l!==null?(Gt(e),xd(e,l)):(Gt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(hl(e),Gt(e),xd(e,l)):(Gt(e),e.flags&=-16777217):(t.memoizedProps!==a&&hl(e),Gt(e),e.flags&=-16777217),null;case 27:Kl(e),l=rt.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&hl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return Gt(e),null}t=et.current,_n(e)?Is(e):(t=x0(n,a,l),e.stateNode=t,hl(e))}return Gt(e),null;case 5:if(Kl(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&hl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return Gt(e),null}if(t=et.current,_n(e))Is(e);else{switch(n=Oi(rt.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[re]=e,t[me]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(ie(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&hl(e)}}return Gt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&hl(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(r(166));if(t=rt.current,_n(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=de,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[re]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||v0(t.nodeValue,l)),t||ua(e)}else t=Oi(t).createTextNode(a),t[re]=e,e.stateNode=t}return Gt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=_n(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(r(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[re]=e}else zn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Gt(e),n=!1}else n=eo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(ol(e),e):(ol(e),null)}if(ol(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),di(e,e.updateQueue),Gt(e),null;case 4:return pt(),t===null&&tf(e.stateNode.containerInfo),Gt(e),null;case 10:return fl(e.type),Gt(e),null;case 19:if(Z(Pt),n=e.memoizedState,n===null)return Gt(e),null;if(a=(e.flags&128)!==0,i=n.rendering,i===null)if(a)Vn(n,!1);else{if(Xt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=ci(t),i!==null){for(e.flags|=128,Vn(n,!1),t=i.updateQueue,e.updateQueue=t,di(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Fs(l,t),l=l.sibling;return V(Pt,Pt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Ke()>yi&&(e.flags|=128,a=!0,Vn(n,!1),e.lanes=4194304)}else{if(!a)if(t=ci(i),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,di(e,t),Vn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!Tt)return Gt(e),null}else 2*Ke()-n.renderingStartTime>yi&&l!==536870912&&(e.flags|=128,a=!0,Vn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Ke(),e.sibling=null,t=Pt.current,V(Pt,a?t&1|2:t&1),e):(Gt(e),null);case 22:case 23:return ol(e),lr(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Gt(e),e.subtreeFlags&6&&(e.flags|=8192)):Gt(e),l=e.updateQueue,l!==null&&di(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Z(ra),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),fl(Ft),Gt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function xy(t,e){switch(Xc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return fl(Ft),pt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return Kl(e),null;case 13:if(ol(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));zn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Z(Pt),null;case 4:return pt(),null;case 10:return fl(e.type),null;case 22:case 23:return ol(e),lr(),t!==null&&Z(ra),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return fl(Ft),null;case 25:return null;default:return null}}function Od(t,e){switch(Xc(e),e.tag){case 3:fl(Ft),pt();break;case 26:case 27:case 5:Kl(e);break;case 4:pt();break;case 13:ol(e);break;case 19:Z(Pt);break;case 10:fl(e.type);break;case 22:case 23:ol(e),lr(),t!==null&&Z(ra);break;case 24:fl(Ft)}}function kn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var i=l.create,s=l.inst;a=i(),s.destroy=a}l=l.next}while(l!==n)}}catch(d){jt(e,e.return,d)}}function Dl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){var s=a.inst,d=s.destroy;if(d!==void 0){s.destroy=void 0,n=e;var y=l,x=d;try{x()}catch(N){jt(n,y,N)}}}a=a.next}while(a!==i)}}catch(N){jt(e,e.return,N)}}function Cd(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{ho(e,l)}catch(a){jt(t,t.return,a)}}}function _d(t,e,l){l.props=sa(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){jt(t,e,a)}}function Zn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){jt(t,e,n)}}function We(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){jt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){jt(t,e,n)}else l.current=null}function zd(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){jt(t,t.return,n)}}function Mr(t,e,l){try{var a=t.stateNode;ky(a,t.type,l,e),a[me]=e}catch(n){jt(t,t.return,n)}}function Rd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Yl(t.type)||t.tag===4}function Dr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Rd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Yl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Nr(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=xi));else if(a!==4&&(a===27&&Yl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(Nr(t,e,l),t=t.sibling;t!==null;)Nr(t,e,l),t=t.sibling}function hi(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&Yl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(hi(t,e,l),t=t.sibling;t!==null;)hi(t,e,l),t=t.sibling}function Md(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);ie(e,a,l),e[re]=t,e[me]=l}catch(i){jt(t,t.return,i)}}var ml=!1,Zt=!1,Ur=!1,Dd=typeof WeakSet=="function"?WeakSet:Set,le=null;function Oy(t,e){if(t=t.containerInfo,af=Di,t=Qs(t),Dc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var s=0,d=-1,y=-1,x=0,N=0,B=t,C=null;e:for(;;){for(var _;B!==l||n!==0&&B.nodeType!==3||(d=s+n),B!==i||a!==0&&B.nodeType!==3||(y=s+a),B.nodeType===3&&(s+=B.nodeValue.length),(_=B.firstChild)!==null;)C=B,B=_;for(;;){if(B===t)break e;if(C===l&&++x===n&&(d=s),C===i&&++N===a&&(y=s),(_=B.nextSibling)!==null)break;B=C,C=B.parentNode}B=_}l=d===-1||y===-1?null:{start:d,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(nf={focusedElem:t,selectionRange:l},Di=!1,le=e;le!==null;)if(e=le,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,le=t;else for(;le!==null;){switch(e=le,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var ct=sa(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(ct,i),a.__reactInternalSnapshotBeforeUpdate=t}catch(at){jt(l,l.return,at)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)rf(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":rf(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,le=t;break}le=e.return}}function Nd(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Nl(t,l),a&4&&kn(5,l);break;case 1:if(Nl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(s){jt(l,l.return,s)}else{var n=sa(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(s){jt(l,l.return,s)}}a&64&&Cd(l),a&512&&Zn(l,l.return);break;case 3:if(Nl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{ho(t,e)}catch(s){jt(l,l.return,s)}}break;case 27:e===null&&a&4&&Md(l);case 26:case 5:Nl(t,l),e===null&&a&4&&zd(l),a&512&&Zn(l,l.return);break;case 12:Nl(t,l);break;case 13:Nl(t,l),a&4&&Bd(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=jy.bind(null,l),Py(t,l))));break;case 22:if(a=l.memoizedState!==null||ml,!a){e=e!==null&&e.memoizedState!==null||Zt,n=ml;var i=Zt;ml=a,(Zt=e)&&!i?Ul(t,l,(l.subtreeFlags&8772)!==0):Nl(t,l),ml=n,Zt=i}break;case 30:break;default:Nl(t,l)}}function Ud(t){var e=t.alternate;e!==null&&(t.alternate=null,Ud(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&dc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var qt=null,ve=!1;function yl(t,e,l){for(l=l.child;l!==null;)jd(t,e,l),l=l.sibling}function jd(t,e,l){if(Se&&typeof Se.onCommitFiberUnmount=="function")try{Se.onCommitFiberUnmount(hn,l)}catch{}switch(l.tag){case 26:Zt||We(l,e),yl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Zt||We(l,e);var a=qt,n=ve;Yl(l.type)&&(qt=l.stateNode,ve=!1),yl(t,e,l),eu(l.stateNode),qt=a,ve=n;break;case 5:Zt||We(l,e);case 6:if(a=qt,n=ve,qt=null,yl(t,e,l),qt=a,ve=n,qt!==null)if(ve)try{(qt.nodeType===9?qt.body:qt.nodeName==="HTML"?qt.ownerDocument.body:qt).removeChild(l.stateNode)}catch(i){jt(l,e,i)}else try{qt.removeChild(l.stateNode)}catch(i){jt(l,e,i)}break;case 18:qt!==null&&(ve?(t=qt,A0(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),fu(t)):A0(qt,l.stateNode));break;case 4:a=qt,n=ve,qt=l.stateNode.containerInfo,ve=!0,yl(t,e,l),qt=a,ve=n;break;case 0:case 11:case 14:case 15:Zt||Dl(2,l,e),Zt||Dl(4,l,e),yl(t,e,l);break;case 1:Zt||(We(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&_d(l,e,a)),yl(t,e,l);break;case 21:yl(t,e,l);break;case 22:Zt=(a=Zt)||l.memoizedState!==null,yl(t,e,l),Zt=a;break;default:yl(t,e,l)}}function Bd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{fu(t)}catch(l){jt(e,e.return,l)}}function Cy(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Dd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Dd),e;default:throw Error(r(435,t.tag))}}function jr(t,e){var l=Cy(t);e.forEach(function(a){var n=By.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function xe(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=t,s=e,d=s;t:for(;d!==null;){switch(d.tag){case 27:if(Yl(d.type)){qt=d.stateNode,ve=!1;break t}break;case 5:qt=d.stateNode,ve=!1;break t;case 3:case 4:qt=d.stateNode.containerInfo,ve=!0;break t}d=d.return}if(qt===null)throw Error(r(160));jd(i,s,n),qt=null,ve=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Hd(e,t),e=e.sibling}var ke=null;function Hd(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:xe(e,t),Oe(t),a&4&&(Dl(3,t,t.return),kn(3,t),Dl(5,t,t.return));break;case 1:xe(e,t),Oe(t),a&512&&(Zt||l===null||We(l,l.return)),a&64&&ml&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=ke;if(xe(e,t),Oe(t),a&512&&(Zt||l===null||We(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[gn]||i[re]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),ie(i,a,l),i[re]=t,te(i),a=i;break t;case"link":var s=M0("link","href",n).get(a+(l.href||""));if(s){for(var d=0;d<s.length;d++)if(i=s[d],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){s.splice(d,1);break e}}i=n.createElement(a),ie(i,a,l),n.head.appendChild(i);break;case"meta":if(s=M0("meta","content",n).get(a+(l.content||""))){for(d=0;d<s.length;d++)if(i=s[d],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){s.splice(d,1);break e}}i=n.createElement(a),ie(i,a,l),n.head.appendChild(i);break;default:throw Error(r(468,a))}i[re]=t,te(i),a=i}t.stateNode=a}else D0(n,t.type,t.stateNode);else t.stateNode=R0(n,a,t.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?D0(n,t.type,t.stateNode):R0(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Mr(t,t.memoizedProps,l.memoizedProps)}break;case 27:xe(e,t),Oe(t),a&512&&(Zt||l===null||We(l,l.return)),l!==null&&a&4&&Mr(t,t.memoizedProps,l.memoizedProps);break;case 5:if(xe(e,t),Oe(t),a&512&&(Zt||l===null||We(l,l.return)),t.flags&32){n=t.stateNode;try{Aa(n,"")}catch(_){jt(t,t.return,_)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Mr(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Ur=!0);break;case 6:if(xe(e,t),Oe(t),a&4){if(t.stateNode===null)throw Error(r(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(_){jt(t,t.return,_)}}break;case 3:if(zi=null,n=ke,ke=Ci(e.containerInfo),xe(e,t),ke=n,Oe(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{fu(e.containerInfo)}catch(_){jt(t,t.return,_)}Ur&&(Ur=!1,wd(t));break;case 4:a=ke,ke=Ci(t.stateNode.containerInfo),xe(e,t),Oe(t),ke=a;break;case 12:xe(e,t),Oe(t);break;case 13:xe(e,t),Oe(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Gr=Ke()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,jr(t,a)));break;case 22:n=t.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,x=ml,N=Zt;if(ml=x||n,Zt=N||y,xe(e,t),Zt=N,ml=x,Oe(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||y||ml||Zt||oa(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){y=l=e;try{if(i=y.stateNode,n)s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none";else{d=y.stateNode;var B=y.memoizedProps.style,C=B!=null&&B.hasOwnProperty("display")?B.display:null;d.style.display=C==null||typeof C=="boolean"?"":(""+C).trim()}}catch(_){jt(y,y.return,_)}}}else if(e.tag===6){if(l===null){y=e;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(_){jt(y,y.return,_)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,jr(t,l))));break;case 19:xe(e,t),Oe(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,jr(t,a)));break;case 30:break;case 21:break;default:xe(e,t),Oe(t)}}function Oe(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Rd(a)){l=a;break}a=a.return}if(l==null)throw Error(r(160));switch(l.tag){case 27:var n=l.stateNode,i=Dr(t);hi(t,i,n);break;case 5:var s=l.stateNode;l.flags&32&&(Aa(s,""),l.flags&=-33);var d=Dr(t);hi(t,d,s);break;case 3:case 4:var y=l.stateNode.containerInfo,x=Dr(t);Nr(t,x,y);break;default:throw Error(r(161))}}catch(N){jt(t,t.return,N)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function wd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;wd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Nl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Nd(t,e.alternate,e),e=e.sibling}function oa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Dl(4,e,e.return),oa(e);break;case 1:We(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&_d(e,e.return,l),oa(e);break;case 27:eu(e.stateNode);case 26:case 5:We(e,e.return),oa(e);break;case 22:e.memoizedState===null&&oa(e);break;case 30:oa(e);break;default:oa(e)}t=t.sibling}}function Ul(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,i=e,s=i.flags;switch(i.tag){case 0:case 11:case 15:Ul(n,i,l),kn(4,i);break;case 1:if(Ul(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(x){jt(a,a.return,x)}if(a=i,n=a.updateQueue,n!==null){var d=a.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)oo(y[n],d)}catch(x){jt(a,a.return,x)}}l&&s&64&&Cd(i),Zn(i,i.return);break;case 27:Md(i);case 26:case 5:Ul(n,i,l),l&&a===null&&s&4&&zd(i),Zn(i,i.return);break;case 12:Ul(n,i,l);break;case 13:Ul(n,i,l),l&&s&4&&Bd(n,i);break;case 22:i.memoizedState===null&&Ul(n,i,l),Zn(i,i.return);break;case 30:break;default:Ul(n,i,l)}e=e.sibling}}function Br(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&Dn(l))}function Hr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Dn(t))}function Fe(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)qd(t,e,l,a),e=e.sibling}function qd(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Fe(t,e,l,a),n&2048&&kn(9,e);break;case 1:Fe(t,e,l,a);break;case 3:Fe(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Dn(t)));break;case 12:if(n&2048){Fe(t,e,l,a),t=e.stateNode;try{var i=e.memoizedProps,s=i.id,d=i.onPostCommit;typeof d=="function"&&d(s,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){jt(e,e.return,y)}}else Fe(t,e,l,a);break;case 13:Fe(t,e,l,a);break;case 23:break;case 22:i=e.stateNode,s=e.alternate,e.memoizedState!==null?i._visibility&2?Fe(t,e,l,a):Kn(t,e):i._visibility&2?Fe(t,e,l,a):(i._visibility|=2,Qa(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Br(s,e);break;case 24:Fe(t,e,l,a),n&2048&&Hr(e.alternate,e);break;default:Fe(t,e,l,a)}}function Qa(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,s=e,d=l,y=a,x=s.flags;switch(s.tag){case 0:case 11:case 15:Qa(i,s,d,y,n),kn(8,s);break;case 23:break;case 22:var N=s.stateNode;s.memoizedState!==null?N._visibility&2?Qa(i,s,d,y,n):Kn(i,s):(N._visibility|=2,Qa(i,s,d,y,n)),n&&x&2048&&Br(s.alternate,s);break;case 24:Qa(i,s,d,y,n),n&&x&2048&&Hr(s.alternate,s);break;default:Qa(i,s,d,y,n)}e=e.sibling}}function Kn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Kn(l,a),n&2048&&Br(a.alternate,a);break;case 24:Kn(l,a),n&2048&&Hr(a.alternate,a);break;default:Kn(l,a)}e=e.sibling}}var $n=8192;function Xa(t){if(t.subtreeFlags&$n)for(t=t.child;t!==null;)Yd(t),t=t.sibling}function Yd(t){switch(t.tag){case 26:Xa(t),t.flags&$n&&t.memoizedState!==null&&og(ke,t.memoizedState,t.memoizedProps);break;case 5:Xa(t);break;case 3:case 4:var e=ke;ke=Ci(t.stateNode.containerInfo),Xa(t),ke=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=$n,$n=16777216,Xa(t),$n=e):Xa(t));break;default:Xa(t)}}function Gd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Jn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];le=a,Xd(a,t)}Gd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Qd(t),t=t.sibling}function Qd(t){switch(t.tag){case 0:case 11:case 15:Jn(t),t.flags&2048&&Dl(9,t,t.return);break;case 3:Jn(t);break;case 12:Jn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,mi(t)):Jn(t);break;default:Jn(t)}}function mi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];le=a,Xd(a,t)}Gd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Dl(8,e,e.return),mi(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,mi(e));break;default:mi(e)}t=t.sibling}}function Xd(t,e){for(;le!==null;){var l=le;switch(l.tag){case 0:case 11:case 15:Dl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:Dn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,le=a;else t:for(l=t;le!==null;){a=le;var n=a.sibling,i=a.return;if(Ud(a),a===l){le=null;break t}if(n!==null){n.return=i,le=n;break t}le=i}}}var _y={getCacheForType:function(t){var e=fe(Ft),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},zy=typeof WeakMap=="function"?WeakMap:Map,_t=0,wt=null,yt=null,bt=0,zt=0,Ce=null,jl=!1,La=!1,wr=!1,gl=0,Xt=0,Bl=0,da=0,qr=0,Ge=0,Va=0,Wn=null,pe=null,Yr=!1,Gr=0,yi=1/0,gi=null,Hl=null,ue=0,wl=null,ka=null,Za=0,Qr=0,Xr=null,Ld=null,Fn=0,Lr=null;function _e(){if((_t&2)!==0&&bt!==0)return bt&-bt;if(M.T!==null){var t=Ua;return t!==0?t:Wr()}return ns()}function Vd(){Ge===0&&(Ge=(bt&536870912)===0||Tt?ts():536870912);var t=Ye.current;return t!==null&&(t.flags|=32),Ge}function ze(t,e,l){(t===wt&&(zt===2||zt===9)||t.cancelPendingCommit!==null)&&(Ka(t,0),ql(t,bt,Ge,!1)),yn(t,l),((_t&2)===0||t!==wt)&&(t===wt&&((_t&2)===0&&(da|=l),Xt===4&&ql(t,bt,Ge,!1)),Pe(t))}function kd(t,e,l){if((_t&6)!==0)throw Error(r(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||mn(t,e),n=a?Dy(t,e):Zr(t,e,!0),i=a;do{if(n===0){La&&!a&&ql(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!Ry(l)){n=Zr(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var s=0;else s=t.pendingLanes&-536870913,s=s!==0?s:s&536870912?536870912:0;if(s!==0){e=s;t:{var d=t;n=Wn;var y=d.current.memoizedState.isDehydrated;if(y&&(Ka(d,s).flags|=256),s=Zr(d,s,!1),s!==2){if(wr&&!y){d.errorRecoveryDisabledLanes|=i,da|=i,n=4;break t}i=pe,pe=n,i!==null&&(pe===null?pe=i:pe.push.apply(pe,i))}n=s}if(i=!1,n!==2)continue}}if(n===1){Ka(t,0),ql(t,e,0,!0);break}t:{switch(a=t,i=n,i){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:ql(a,e,Ge,!jl);break t;case 2:pe=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(n=Gr+300-Ke(),10<n)){if(ql(a,e,Ge,!jl),_u(a,0,!0)!==0)break t;a.timeoutHandle=S0(Zd.bind(null,a,l,pe,gi,Yr,e,Ge,da,Va,jl,i,2,-0,0),n);break t}Zd(a,l,pe,gi,Yr,e,Ge,da,Va,jl,i,0,-0,0)}}break}while(!0);Pe(t)}function Zd(t,e,l,a,n,i,s,d,y,x,N,B,C,_){if(t.timeoutHandle=-1,B=e.subtreeFlags,(B&8192||(B&16785408)===16785408)&&(nu={stylesheets:null,count:0,unsuspend:sg},Yd(e),B=dg(),B!==null)){t.cancelPendingCommit=B(Id.bind(null,t,e,i,l,a,n,s,d,y,N,1,C,_)),ql(t,i,s,!x);return}Id(t,e,i,l,a,n,s,d,y)}function Ry(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!Ae(i(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function ql(t,e,l,a){e&=~qr,e&=~da,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var i=31-Te(n),s=1<<i;a[i]=-1,n&=~s}l!==0&&ls(t,l,e)}function vi(){return(_t&6)===0?(Pn(0),!1):!0}function Vr(){if(yt!==null){if(zt===0)var t=yt.return;else t=yt,rl=ia=null,cr(t),Ya=null,Xn=0,t=yt;for(;t!==null;)Od(t.alternate,t),t=t.return;yt=null}}function Ka(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Ky(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Vr(),wt=t,yt=l=ul(t.current,null),bt=e,zt=0,Ce=null,jl=!1,La=mn(t,e),wr=!1,Va=Ge=qr=da=Bl=Xt=0,pe=Wn=null,Yr=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-Te(a),i=1<<n;e|=t[n],a&=~i}return gl=e,Yu(),l}function Kd(t,e){dt=null,M.H=ni,e===Un||e===$u?(e=fo(),zt=3):e===io?(e=fo(),zt=4):zt=e===od?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ce=e,yt===null&&(Xt=1,fi(t,Be(e,t.current)))}function $d(){var t=M.H;return M.H=ni,t===null?ni:t}function Jd(){var t=M.A;return M.A=_y,t}function kr(){Xt=4,jl||(bt&4194048)!==bt&&Ye.current!==null||(La=!0),(Bl&134217727)===0&&(da&134217727)===0||wt===null||ql(wt,bt,Ge,!1)}function Zr(t,e,l){var a=_t;_t|=2;var n=$d(),i=Jd();(wt!==t||bt!==e)&&(gi=null,Ka(t,e)),e=!1;var s=Xt;t:do try{if(zt!==0&&yt!==null){var d=yt,y=Ce;switch(zt){case 8:Vr(),s=6;break t;case 3:case 2:case 9:case 6:Ye.current===null&&(e=!0);var x=zt;if(zt=0,Ce=null,$a(t,d,y,x),l&&La){s=0;break t}break;default:x=zt,zt=0,Ce=null,$a(t,d,y,x)}}My(),s=Xt;break}catch(N){Kd(t,N)}while(!0);return e&&t.shellSuspendCounter++,rl=ia=null,_t=a,M.H=n,M.A=i,yt===null&&(wt=null,bt=0,Yu()),s}function My(){for(;yt!==null;)Wd(yt)}function Dy(t,e){var l=_t;_t|=2;var a=$d(),n=Jd();wt!==t||bt!==e?(gi=null,yi=Ke()+500,Ka(t,e)):La=mn(t,e);t:do try{if(zt!==0&&yt!==null){e=yt;var i=Ce;e:switch(zt){case 1:zt=0,Ce=null,$a(t,e,i,1);break;case 2:case 9:if(co(i)){zt=0,Ce=null,Fd(e);break}e=function(){zt!==2&&zt!==9||wt!==t||(zt=7),Pe(t)},i.then(e,e);break t;case 3:zt=7;break t;case 4:zt=5;break t;case 7:co(i)?(zt=0,Ce=null,Fd(e)):(zt=0,Ce=null,$a(t,e,i,7));break;case 5:var s=null;switch(yt.tag){case 26:s=yt.memoizedState;case 5:case 27:var d=yt;if(!s||N0(s)){zt=0,Ce=null;var y=d.sibling;if(y!==null)yt=y;else{var x=d.return;x!==null?(yt=x,pi(x)):yt=null}break e}}zt=0,Ce=null,$a(t,e,i,5);break;case 6:zt=0,Ce=null,$a(t,e,i,6);break;case 8:Vr(),Xt=6;break t;default:throw Error(r(462))}}Ny();break}catch(N){Kd(t,N)}while(!0);return rl=ia=null,M.H=a,M.A=n,_t=l,yt!==null?0:(wt=null,bt=0,Yu(),Xt)}function Ny(){for(;yt!==null&&!Jl();)Wd(yt)}function Wd(t){var e=Ed(t.alternate,t,gl);t.memoizedProps=t.pendingProps,e===null?pi(t):yt=e}function Fd(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=vd(l,e,e.pendingProps,e.type,void 0,bt);break;case 11:e=vd(l,e,e.pendingProps,e.type.render,e.ref,bt);break;case 5:cr(e);default:Od(l,e),e=yt=Fs(e,gl),e=Ed(l,e,gl)}t.memoizedProps=t.pendingProps,e===null?pi(t):yt=e}function $a(t,e,l,a){rl=ia=null,cr(e),Ya=null,Xn=0;var n=e.return;try{if(Ty(t,n,e,l,bt)){Xt=1,fi(t,Be(l,t.current)),yt=null;return}}catch(i){if(n!==null)throw yt=n,i;Xt=1,fi(t,Be(l,t.current)),yt=null;return}e.flags&32768?(Tt||a===1?t=!0:La||(bt&536870912)!==0?t=!1:(jl=t=!0,(a===2||a===9||a===3||a===6)&&(a=Ye.current,a!==null&&a.tag===13&&(a.flags|=16384))),Pd(e,t)):pi(e)}function pi(t){var e=t;do{if((e.flags&32768)!==0){Pd(e,jl);return}t=e.return;var l=Ey(e.alternate,e,gl);if(l!==null){yt=l;return}if(e=e.sibling,e!==null){yt=e;return}yt=e=t}while(e!==null);Xt===0&&(Xt=5)}function Pd(t,e){do{var l=xy(t.alternate,t);if(l!==null){l.flags&=32767,yt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){yt=t;return}yt=t=l}while(t!==null);Xt=6,yt=null}function Id(t,e,l,a,n,i,s,d,y){t.cancelPendingCommit=null;do bi();while(ue!==0);if((_t&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(i=e.lanes|e.childLanes,i|=Hc,sm(t,l,i,s,d,y),t===wt&&(yt=wt=null,bt=0),ka=e,wl=t,Za=l,Qr=i,Xr=n,Ld=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Hy(xu,function(){return n0(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=M.T,M.T=null,n=G.p,G.p=2,s=_t,_t|=4;try{Oy(t,e,l)}finally{_t=s,G.p=n,M.T=a}}ue=1,t0(),e0(),l0()}}function t0(){if(ue===1){ue=0;var t=wl,e=ka,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=M.T,M.T=null;var a=G.p;G.p=2;var n=_t;_t|=4;try{Hd(e,t);var i=nf,s=Qs(t.containerInfo),d=i.focusedElem,y=i.selectionRange;if(s!==d&&d&&d.ownerDocument&&Gs(d.ownerDocument.documentElement,d)){if(y!==null&&Dc(d)){var x=y.start,N=y.end;if(N===void 0&&(N=x),"selectionStart"in d)d.selectionStart=x,d.selectionEnd=Math.min(N,d.value.length);else{var B=d.ownerDocument||document,C=B&&B.defaultView||window;if(C.getSelection){var _=C.getSelection(),ct=d.textContent.length,at=Math.min(y.start,ct),Nt=y.end===void 0?at:Math.min(y.end,ct);!_.extend&&at>Nt&&(s=Nt,Nt=at,at=s);var T=Ys(d,at),b=Ys(d,Nt);if(T&&b&&(_.rangeCount!==1||_.anchorNode!==T.node||_.anchorOffset!==T.offset||_.focusNode!==b.node||_.focusOffset!==b.offset)){var E=B.createRange();E.setStart(T.node,T.offset),_.removeAllRanges(),at>Nt?(_.addRange(E),_.extend(b.node,b.offset)):(E.setEnd(b.node,b.offset),_.addRange(E))}}}}for(B=[],_=d;_=_.parentNode;)_.nodeType===1&&B.push({element:_,left:_.scrollLeft,top:_.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<B.length;d++){var j=B[d];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}Di=!!af,nf=af=null}finally{_t=n,G.p=a,M.T=l}}t.current=e,ue=2}}function e0(){if(ue===2){ue=0;var t=wl,e=ka,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=M.T,M.T=null;var a=G.p;G.p=2;var n=_t;_t|=4;try{Nd(t,e.alternate,e)}finally{_t=n,G.p=a,M.T=l}}ue=3}}function l0(){if(ue===4||ue===3){ue=0,em();var t=wl,e=ka,l=Za,a=Ld;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ue=5:(ue=0,ka=wl=null,a0(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Hl=null),sc(l),e=e.stateNode,Se&&typeof Se.onCommitFiberRoot=="function")try{Se.onCommitFiberRoot(hn,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=M.T,n=G.p,G.p=2,M.T=null;try{for(var i=t.onRecoverableError,s=0;s<a.length;s++){var d=a[s];i(d.value,{componentStack:d.stack})}}finally{M.T=e,G.p=n}}(Za&3)!==0&&bi(),Pe(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Lr?Fn++:(Fn=0,Lr=t):Fn=0,Pn(0)}}function a0(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Dn(e)))}function bi(t){return t0(),e0(),l0(),n0()}function n0(){if(ue!==5)return!1;var t=wl,e=Qr;Qr=0;var l=sc(Za),a=M.T,n=G.p;try{G.p=32>l?32:l,M.T=null,l=Xr,Xr=null;var i=wl,s=Za;if(ue=0,ka=wl=null,Za=0,(_t&6)!==0)throw Error(r(331));var d=_t;if(_t|=4,Qd(i.current),qd(i,i.current,s,l),_t=d,Pn(0,!1),Se&&typeof Se.onPostCommitFiberRoot=="function")try{Se.onPostCommitFiberRoot(hn,i)}catch{}return!0}finally{G.p=n,M.T=a,a0(t,e)}}function u0(t,e,l){e=Be(l,e),e=Tr(t.stateNode,e,2),t=_l(t,e,2),t!==null&&(yn(t,2),Pe(t))}function jt(t,e,l){if(t.tag===3)u0(t,t,l);else for(;e!==null;){if(e.tag===3){u0(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Hl===null||!Hl.has(a))){t=Be(l,t),l=fd(2),a=_l(e,l,2),a!==null&&(sd(l,a,e,t),yn(a,2),Pe(a));break}}e=e.return}}function Kr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new zy;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(wr=!0,n.add(l),t=Uy.bind(null,t,e,l),e.then(t,t))}function Uy(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,wt===t&&(bt&l)===l&&(Xt===4||Xt===3&&(bt&62914560)===bt&&300>Ke()-Gr?(_t&2)===0&&Ka(t,0):qr|=l,Va===bt&&(Va=0)),Pe(t)}function i0(t,e){e===0&&(e=es()),t=Ra(t,e),t!==null&&(yn(t,e),Pe(t))}function jy(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),i0(t,l)}function By(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(r(314))}a!==null&&a.delete(e),i0(t,l)}function Hy(t,e){return $l(t,e)}var Si=null,Ja=null,$r=!1,Ti=!1,Jr=!1,ha=0;function Pe(t){t!==Ja&&t.next===null&&(Ja===null?Si=Ja=t:Ja=Ja.next=t),Ti=!0,$r||($r=!0,qy())}function Pn(t,e){if(!Jr&&Ti){Jr=!0;do for(var l=!1,a=Si;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var s=a.suspendedLanes,d=a.pingedLanes;i=(1<<31-Te(42|t)+1)-1,i&=n&~(s&~d),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,s0(a,i))}else i=bt,i=_u(a,a===wt?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||mn(a,i)||(l=!0,s0(a,i));a=a.next}while(l);Jr=!1}}function wy(){c0()}function c0(){Ti=$r=!1;var t=0;ha!==0&&(Zy()&&(t=ha),ha=0);for(var e=Ke(),l=null,a=Si;a!==null;){var n=a.next,i=r0(a,e);i===0?(a.next=null,l===null?Si=n:l.next=n,n===null&&(Ja=l)):(l=a,(t!==0||(i&3)!==0)&&(Ti=!0)),a=n}Pn(t)}function r0(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var s=31-Te(i),d=1<<s,y=n[s];y===-1?((d&l)===0||(d&a)!==0)&&(n[s]=fm(d,e)):y<=e&&(t.expiredLanes|=d),i&=~d}if(e=wt,l=bt,l=_u(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(zt===2||zt===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&he(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||mn(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&he(a),sc(l)){case 2:case 8:l=Pf;break;case 32:l=xu;break;case 268435456:l=If;break;default:l=xu}return a=f0.bind(null,t),l=$l(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&he(a),t.callbackPriority=2,t.callbackNode=null,2}function f0(t,e){if(ue!==0&&ue!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(bi()&&t.callbackNode!==l)return null;var a=bt;return a=_u(t,t===wt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(kd(t,a,e),r0(t,Ke()),t.callbackNode!=null&&t.callbackNode===l?f0.bind(null,t):null)}function s0(t,e){if(bi())return null;kd(t,e,!0)}function qy(){$y(function(){(_t&6)!==0?$l(Ff,wy):c0()})}function Wr(){return ha===0&&(ha=ts()),ha}function o0(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Nu(""+t)}function d0(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Yy(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var i=o0((n[me]||null).action),s=a.submitter;s&&(e=(e=s[me]||null)?o0(e.formAction):s.getAttribute("formAction"),e!==null&&(i=e,s=null));var d=new Hu("action","action",null,a,n);t.push({event:d,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ha!==0){var y=s?d0(n,s):new FormData(n);gr(l,{pending:!0,data:y,method:n.method,action:i},null,y)}}else typeof i=="function"&&(d.preventDefault(),y=s?d0(n,s):new FormData(n),gr(l,{pending:!0,data:y,method:n.method,action:i},i,y))},currentTarget:n}]})}}for(var Fr=0;Fr<Bc.length;Fr++){var Pr=Bc[Fr],Gy=Pr.toLowerCase(),Qy=Pr[0].toUpperCase()+Pr.slice(1);Ve(Gy,"on"+Qy)}Ve(Vs,"onAnimationEnd"),Ve(ks,"onAnimationIteration"),Ve(Zs,"onAnimationStart"),Ve("dblclick","onDoubleClick"),Ve("focusin","onFocus"),Ve("focusout","onBlur"),Ve(ny,"onTransitionRun"),Ve(uy,"onTransitionStart"),Ve(iy,"onTransitionCancel"),Ve(Ks,"onTransitionEnd"),ba("onMouseEnter",["mouseout","mouseover"]),ba("onMouseLeave",["mouseout","mouseover"]),ba("onPointerEnter",["pointerout","pointerover"]),ba("onPointerLeave",["pointerout","pointerover"]),Fl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Fl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Fl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Fl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Fl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Fl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var In="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(In));function h0(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var i=void 0;if(e)for(var s=a.length-1;0<=s;s--){var d=a[s],y=d.instance,x=d.currentTarget;if(d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=x;try{i(n)}catch(N){ri(N)}n.currentTarget=null,i=y}else for(s=0;s<a.length;s++){if(d=a[s],y=d.instance,x=d.currentTarget,d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=x;try{i(n)}catch(N){ri(N)}n.currentTarget=null,i=y}}}}function gt(t,e){var l=e[oc];l===void 0&&(l=e[oc]=new Set);var a=t+"__bubble";l.has(a)||(m0(e,t,2,!1),l.add(a))}function Ir(t,e,l){var a=0;e&&(a|=4),m0(l,t,a,e)}var Ai="_reactListening"+Math.random().toString(36).slice(2);function tf(t){if(!t[Ai]){t[Ai]=!0,is.forEach(function(l){l!=="selectionchange"&&(Xy.has(l)||Ir(l,!1,t),Ir(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Ai]||(e[Ai]=!0,Ir("selectionchange",!1,e))}}function m0(t,e,l,a){switch(q0(e)){case 2:var n=yg;break;case 8:n=gg;break;default:n=yf}l=n.bind(null,e,l,t),n=void 0,!Ac||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function ef(t,e,l,a,n){var i=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var s=a.tag;if(s===3||s===4){var d=a.stateNode.containerInfo;if(d===n)break;if(s===4)for(s=a.return;s!==null;){var y=s.tag;if((y===3||y===4)&&s.stateNode.containerInfo===n)return;s=s.return}for(;d!==null;){if(s=ga(d),s===null)return;if(y=s.tag,y===5||y===6||y===26||y===27){a=i=s;continue t}d=d.parentNode}}a=a.return}Ss(function(){var x=i,N=Sc(l),B=[];t:{var C=$s.get(t);if(C!==void 0){var _=Hu,ct=t;switch(t){case"keypress":if(ju(l)===0)break t;case"keydown":case"keyup":_=Hm;break;case"focusin":ct="focus",_=Cc;break;case"focusout":ct="blur",_=Cc;break;case"beforeblur":case"afterblur":_=Cc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":_=Es;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":_=xm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":_=Ym;break;case Vs:case ks:case Zs:_=_m;break;case Ks:_=Qm;break;case"scroll":case"scrollend":_=Am;break;case"wheel":_=Lm;break;case"copy":case"cut":case"paste":_=Rm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":_=Os;break;case"toggle":case"beforetoggle":_=km}var at=(e&4)!==0,Nt=!at&&(t==="scroll"||t==="scrollend"),T=at?C!==null?C+"Capture":null:C;at=[];for(var b=x,E;b!==null;){var j=b;if(E=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||E===null||T===null||(j=pn(b,T),j!=null&&at.push(tu(b,j,E))),Nt)break;b=b.return}0<at.length&&(C=new _(C,ct,null,l,N),B.push({event:C,listeners:at}))}}if((e&7)===0){t:{if(C=t==="mouseover"||t==="pointerover",_=t==="mouseout"||t==="pointerout",C&&l!==bc&&(ct=l.relatedTarget||l.fromElement)&&(ga(ct)||ct[ya]))break t;if((_||C)&&(C=N.window===N?N:(C=N.ownerDocument)?C.defaultView||C.parentWindow:window,_?(ct=l.relatedTarget||l.toElement,_=x,ct=ct?ga(ct):null,ct!==null&&(Nt=h(ct),at=ct.tag,ct!==Nt||at!==5&&at!==27&&at!==6)&&(ct=null)):(_=null,ct=x),_!==ct)){if(at=Es,j="onMouseLeave",T="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(at=Os,j="onPointerLeave",T="onPointerEnter",b="pointer"),Nt=_==null?C:vn(_),E=ct==null?C:vn(ct),C=new at(j,b+"leave",_,l,N),C.target=Nt,C.relatedTarget=E,j=null,ga(N)===x&&(at=new at(T,b+"enter",ct,l,N),at.target=E,at.relatedTarget=Nt,j=at),Nt=j,_&&ct)e:{for(at=_,T=ct,b=0,E=at;E;E=Wa(E))b++;for(E=0,j=T;j;j=Wa(j))E++;for(;0<b-E;)at=Wa(at),b--;for(;0<E-b;)T=Wa(T),E--;for(;b--;){if(at===T||T!==null&&at===T.alternate)break e;at=Wa(at),T=Wa(T)}at=null}else at=null;_!==null&&y0(B,C,_,at,!1),ct!==null&&Nt!==null&&y0(B,Nt,ct,at,!0)}}t:{if(C=x?vn(x):window,_=C.nodeName&&C.nodeName.toLowerCase(),_==="select"||_==="input"&&C.type==="file")var J=Us;else if(Ds(C))if(js)J=ey;else{J=Im;var mt=Pm}else _=C.nodeName,!_||_.toLowerCase()!=="input"||C.type!=="checkbox"&&C.type!=="radio"?x&&pc(x.elementType)&&(J=Us):J=ty;if(J&&(J=J(t,x))){Ns(B,J,l,N);break t}mt&&mt(t,C,x),t==="focusout"&&x&&C.type==="number"&&x.memoizedProps.value!=null&&vc(C,"number",C.value)}switch(mt=x?vn(x):window,t){case"focusin":(Ds(mt)||mt.contentEditable==="true")&&(Ca=mt,Nc=x,Cn=null);break;case"focusout":Cn=Nc=Ca=null;break;case"mousedown":Uc=!0;break;case"contextmenu":case"mouseup":case"dragend":Uc=!1,Xs(B,l,N);break;case"selectionchange":if(ay)break;case"keydown":case"keyup":Xs(B,l,N)}var P;if(zc)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else Oa?Rs(t,l)&&(nt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(nt="onCompositionStart");nt&&(Cs&&l.locale!=="ko"&&(Oa||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&Oa&&(P=Ts()):(El=N,Ec="value"in El?El.value:El.textContent,Oa=!0)),mt=Ei(x,nt),0<mt.length&&(nt=new xs(nt,t,null,l,N),B.push({event:nt,listeners:mt}),P?nt.data=P:(P=Ms(l),P!==null&&(nt.data=P)))),(P=Km?$m(t,l):Jm(t,l))&&(nt=Ei(x,"onBeforeInput"),0<nt.length&&(mt=new xs("onBeforeInput","beforeinput",null,l,N),B.push({event:mt,listeners:nt}),mt.data=P)),Yy(B,t,x,l,N)}h0(B,e)})}function tu(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Ei(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=pn(t,l),n!=null&&a.unshift(tu(t,n,i)),n=pn(t,e),n!=null&&a.push(tu(t,n,i))),t.tag===3)return a;t=t.return}return[]}function Wa(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function y0(t,e,l,a,n){for(var i=e._reactName,s=[];l!==null&&l!==a;){var d=l,y=d.alternate,x=d.stateNode;if(d=d.tag,y!==null&&y===a)break;d!==5&&d!==26&&d!==27||x===null||(y=x,n?(x=pn(l,i),x!=null&&s.unshift(tu(l,x,y))):n||(x=pn(l,i),x!=null&&s.push(tu(l,x,y)))),l=l.return}s.length!==0&&t.push({event:e,listeners:s})}var Ly=/\r\n?/g,Vy=/\u0000|\uFFFD/g;function g0(t){return(typeof t=="string"?t:""+t).replace(Ly,`
`).replace(Vy,"")}function v0(t,e){return e=g0(e),g0(t)===e}function xi(){}function Dt(t,e,l,a,n,i){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||Aa(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&Aa(t,""+a);break;case"className":Ru(t,"class",a);break;case"tabIndex":Ru(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ru(t,l,a);break;case"style":ps(t,a,i);break;case"data":if(e!=="object"){Ru(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Nu(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&Dt(t,e,"name",n.name,n,null),Dt(t,e,"formEncType",n.formEncType,n,null),Dt(t,e,"formMethod",n.formMethod,n,null),Dt(t,e,"formTarget",n.formTarget,n,null)):(Dt(t,e,"encType",n.encType,n,null),Dt(t,e,"method",n.method,n,null),Dt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=Nu(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=xi);break;case"onScroll":a!=null&&gt("scroll",t);break;case"onScrollEnd":a!=null&&gt("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=Nu(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":gt("beforetoggle",t),gt("toggle",t),zu(t,"popover",a);break;case"xlinkActuate":al(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":al(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":al(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":al(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":al(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":al(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":al(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":al(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":al(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":zu(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Sm.get(l)||l,zu(t,l,a))}}function lf(t,e,l,a,n,i){switch(l){case"style":ps(t,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"children":typeof a=="string"?Aa(t,a):(typeof a=="number"||typeof a=="bigint")&&Aa(t,""+a);break;case"onScroll":a!=null&&gt("scroll",t);break;case"onScrollEnd":a!=null&&gt("scrollend",t);break;case"onClick":a!=null&&(t.onclick=xi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!cs.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),i=t[me]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):zu(t,l,a)}}}function ie(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":gt("error",t),gt("load",t);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var s=l[i];if(s!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Dt(t,e,i,s,l,null)}}n&&Dt(t,e,"srcSet",l.srcSet,l,null),a&&Dt(t,e,"src",l.src,l,null);return;case"input":gt("invalid",t);var d=i=s=n=null,y=null,x=null;for(a in l)if(l.hasOwnProperty(a)){var N=l[a];if(N!=null)switch(a){case"name":n=N;break;case"type":s=N;break;case"checked":y=N;break;case"defaultChecked":x=N;break;case"value":i=N;break;case"defaultValue":d=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,e));break;default:Dt(t,e,a,N,l,null)}}ms(t,i,d,y,x,s,n,!1),Mu(t);return;case"select":gt("invalid",t),a=s=i=null;for(n in l)if(l.hasOwnProperty(n)&&(d=l[n],d!=null))switch(n){case"value":i=d;break;case"defaultValue":s=d;break;case"multiple":a=d;default:Dt(t,e,n,d,l,null)}e=i,l=s,t.multiple=!!a,e!=null?Ta(t,!!a,e,!1):l!=null&&Ta(t,!!a,l,!0);return;case"textarea":gt("invalid",t),i=n=a=null;for(s in l)if(l.hasOwnProperty(s)&&(d=l[s],d!=null))switch(s){case"value":a=d;break;case"defaultValue":n=d;break;case"children":i=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(r(91));break;default:Dt(t,e,s,d,l,null)}gs(t,a,n,i),Mu(t);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:Dt(t,e,y,a,l,null)}return;case"dialog":gt("beforetoggle",t),gt("toggle",t),gt("cancel",t),gt("close",t);break;case"iframe":case"object":gt("load",t);break;case"video":case"audio":for(a=0;a<In.length;a++)gt(In[a],t);break;case"image":gt("error",t),gt("load",t);break;case"details":gt("toggle",t);break;case"embed":case"source":case"link":gt("error",t),gt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(x in l)if(l.hasOwnProperty(x)&&(a=l[x],a!=null))switch(x){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:Dt(t,e,x,a,l,null)}return;default:if(pc(e)){for(N in l)l.hasOwnProperty(N)&&(a=l[N],a!==void 0&&lf(t,e,N,a,l,void 0));return}}for(d in l)l.hasOwnProperty(d)&&(a=l[d],a!=null&&Dt(t,e,d,a,l,null))}function ky(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,s=null,d=null,y=null,x=null,N=null;for(_ in l){var B=l[_];if(l.hasOwnProperty(_)&&B!=null)switch(_){case"checked":break;case"value":break;case"defaultValue":y=B;default:a.hasOwnProperty(_)||Dt(t,e,_,null,a,B)}}for(var C in a){var _=a[C];if(B=l[C],a.hasOwnProperty(C)&&(_!=null||B!=null))switch(C){case"type":i=_;break;case"name":n=_;break;case"checked":x=_;break;case"defaultChecked":N=_;break;case"value":s=_;break;case"defaultValue":d=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,e));break;default:_!==B&&Dt(t,e,C,_,a,B)}}gc(t,s,d,y,x,N,i,n);return;case"select":_=s=d=C=null;for(i in l)if(y=l[i],l.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":_=y;default:a.hasOwnProperty(i)||Dt(t,e,i,null,a,y)}for(n in a)if(i=a[n],y=l[n],a.hasOwnProperty(n)&&(i!=null||y!=null))switch(n){case"value":C=i;break;case"defaultValue":d=i;break;case"multiple":s=i;default:i!==y&&Dt(t,e,n,i,a,y)}e=d,l=s,a=_,C!=null?Ta(t,!!l,C,!1):!!a!=!!l&&(e!=null?Ta(t,!!l,e,!0):Ta(t,!!l,l?[]:"",!1));return;case"textarea":_=C=null;for(d in l)if(n=l[d],l.hasOwnProperty(d)&&n!=null&&!a.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Dt(t,e,d,null,a,n)}for(s in a)if(n=a[s],i=l[s],a.hasOwnProperty(s)&&(n!=null||i!=null))switch(s){case"value":C=n;break;case"defaultValue":_=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==i&&Dt(t,e,s,n,a,i)}ys(t,C,_);return;case"option":for(var ct in l)if(C=l[ct],l.hasOwnProperty(ct)&&C!=null&&!a.hasOwnProperty(ct))switch(ct){case"selected":t.selected=!1;break;default:Dt(t,e,ct,null,a,C)}for(y in a)if(C=a[y],_=l[y],a.hasOwnProperty(y)&&C!==_&&(C!=null||_!=null))switch(y){case"selected":t.selected=C&&typeof C!="function"&&typeof C!="symbol";break;default:Dt(t,e,y,C,a,_)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var at in l)C=l[at],l.hasOwnProperty(at)&&C!=null&&!a.hasOwnProperty(at)&&Dt(t,e,at,null,a,C);for(x in a)if(C=a[x],_=l[x],a.hasOwnProperty(x)&&C!==_&&(C!=null||_!=null))switch(x){case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(r(137,e));break;default:Dt(t,e,x,C,a,_)}return;default:if(pc(e)){for(var Nt in l)C=l[Nt],l.hasOwnProperty(Nt)&&C!==void 0&&!a.hasOwnProperty(Nt)&&lf(t,e,Nt,void 0,a,C);for(N in a)C=a[N],_=l[N],!a.hasOwnProperty(N)||C===_||C===void 0&&_===void 0||lf(t,e,N,C,a,_);return}}for(var T in l)C=l[T],l.hasOwnProperty(T)&&C!=null&&!a.hasOwnProperty(T)&&Dt(t,e,T,null,a,C);for(B in a)C=a[B],_=l[B],!a.hasOwnProperty(B)||C===_||C==null&&_==null||Dt(t,e,B,C,a,_)}var af=null,nf=null;function Oi(t){return t.nodeType===9?t:t.ownerDocument}function p0(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function b0(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function uf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var cf=null;function Zy(){var t=window.event;return t&&t.type==="popstate"?t===cf?!1:(cf=t,!0):(cf=null,!1)}var S0=typeof setTimeout=="function"?setTimeout:void 0,Ky=typeof clearTimeout=="function"?clearTimeout:void 0,T0=typeof Promise=="function"?Promise:void 0,$y=typeof queueMicrotask=="function"?queueMicrotask:typeof T0<"u"?function(t){return T0.resolve(null).then(t).catch(Jy)}:S0;function Jy(t){setTimeout(function(){throw t})}function Yl(t){return t==="head"}function A0(t,e){var l=e,a=0,n=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var s=t.ownerDocument;if(l&1&&eu(s.documentElement),l&2&&eu(s.body),l&4)for(l=s.head,eu(l),s=l.firstChild;s;){var d=s.nextSibling,y=s.nodeName;s[gn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&s.rel.toLowerCase()==="stylesheet"||l.removeChild(s),s=d}}if(n===0){t.removeChild(i),fu(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);fu(e)}function rf(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":rf(l),dc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Wy(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[gn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Ze(t.nextSibling),t===null)break}return null}function Fy(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Ze(t.nextSibling),t===null))return null;return t}function ff(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Py(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Ze(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var sf=null;function E0(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function x0(t,e,l){switch(e=Oi(l),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function eu(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);dc(t)}var Qe=new Map,O0=new Set;function Ci(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var vl=G.d;G.d={f:Iy,r:tg,D:eg,C:lg,L:ag,m:ng,X:ig,S:ug,M:cg};function Iy(){var t=vl.f(),e=vi();return t||e}function tg(t){var e=va(t);e!==null&&e.tag===5&&e.type==="form"?ko(e):vl.r(t)}var Fa=typeof document>"u"?null:document;function C0(t,e,l){var a=Fa;if(a&&typeof e=="string"&&e){var n=je(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),O0.has(n)||(O0.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),ie(e,"link",t),te(e),a.head.appendChild(e)))}}function eg(t){vl.D(t),C0("dns-prefetch",t,null)}function lg(t,e){vl.C(t,e),C0("preconnect",t,e)}function ag(t,e,l){vl.L(t,e,l);var a=Fa;if(a&&t&&e){var n='link[rel="preload"][as="'+je(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+je(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+je(l.imageSizes)+'"]')):n+='[href="'+je(t)+'"]';var i=n;switch(e){case"style":i=Pa(t);break;case"script":i=Ia(t)}Qe.has(i)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Qe.set(i,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(lu(i))||e==="script"&&a.querySelector(au(i))||(e=a.createElement("link"),ie(e,"link",t),te(e),a.head.appendChild(e)))}}function ng(t,e){vl.m(t,e);var l=Fa;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+je(a)+'"][href="'+je(t)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ia(t)}if(!Qe.has(i)&&(t=z({rel:"modulepreload",href:t},e),Qe.set(i,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(au(i)))return}a=l.createElement("link"),ie(a,"link",t),te(a),l.head.appendChild(a)}}}function ug(t,e,l){vl.S(t,e,l);var a=Fa;if(a&&t){var n=pa(a).hoistableStyles,i=Pa(t);e=e||"default";var s=n.get(i);if(!s){var d={loading:0,preload:null};if(s=a.querySelector(lu(i)))d.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Qe.get(i))&&of(t,l);var y=s=a.createElement("link");te(y),ie(y,"link",t),y._p=new Promise(function(x,N){y.onload=x,y.onerror=N}),y.addEventListener("load",function(){d.loading|=1}),y.addEventListener("error",function(){d.loading|=2}),d.loading|=4,_i(s,e,a)}s={type:"stylesheet",instance:s,count:1,state:d},n.set(i,s)}}}function ig(t,e){vl.X(t,e);var l=Fa;if(l&&t){var a=pa(l).hoistableScripts,n=Ia(t),i=a.get(n);i||(i=l.querySelector(au(n)),i||(t=z({src:t,async:!0},e),(e=Qe.get(n))&&df(t,e),i=l.createElement("script"),te(i),ie(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function cg(t,e){vl.M(t,e);var l=Fa;if(l&&t){var a=pa(l).hoistableScripts,n=Ia(t),i=a.get(n);i||(i=l.querySelector(au(n)),i||(t=z({src:t,async:!0,type:"module"},e),(e=Qe.get(n))&&df(t,e),i=l.createElement("script"),te(i),ie(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function _0(t,e,l,a){var n=(n=rt.current)?Ci(n):null;if(!n)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Pa(l.href),l=pa(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Pa(l.href);var i=pa(n).hoistableStyles,s=i.get(t);if(s||(n=n.ownerDocument||n,s={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,s),(i=n.querySelector(lu(t)))&&!i._p&&(s.instance=i,s.state.loading=5),Qe.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Qe.set(t,l),i||rg(n,t,l,s.state))),e&&a===null)throw Error(r(528,""));return s}if(e&&a!==null)throw Error(r(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ia(l),l=pa(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Pa(t){return'href="'+je(t)+'"'}function lu(t){return'link[rel="stylesheet"]['+t+"]"}function z0(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function rg(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),ie(e,"link",l),te(e),t.head.appendChild(e))}function Ia(t){return'[src="'+je(t)+'"]'}function au(t){return"script[async]"+t}function R0(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+je(l.href)+'"]');if(a)return e.instance=a,te(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),te(a),ie(a,"style",n),_i(a,l.precedence,t),e.instance=a;case"stylesheet":n=Pa(l.href);var i=t.querySelector(lu(n));if(i)return e.state.loading|=4,e.instance=i,te(i),i;a=z0(l),(n=Qe.get(n))&&of(a,n),i=(t.ownerDocument||t).createElement("link"),te(i);var s=i;return s._p=new Promise(function(d,y){s.onload=d,s.onerror=y}),ie(i,"link",a),e.state.loading|=4,_i(i,l.precedence,t),e.instance=i;case"script":return i=Ia(l.src),(n=t.querySelector(au(i)))?(e.instance=n,te(n),n):(a=l,(n=Qe.get(i))&&(a=z({},l),df(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),te(n),ie(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,_i(a,l.precedence,t));return e.instance}function _i(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,s=0;s<a.length;s++){var d=a[s];if(d.dataset.precedence===e)i=d;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function of(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function df(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var zi=null;function M0(t,e,l){if(zi===null){var a=new Map,n=zi=new Map;n.set(l,a)}else n=zi,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var i=l[n];if(!(i[gn]||i[re]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var s=i.getAttribute(e)||"";s=t+s;var d=a.get(s);d?d.push(i):a.set(s,[i])}}return a}function D0(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function fg(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function N0(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var nu=null;function sg(){}function og(t,e,l){if(nu===null)throw Error(r(475));var a=nu;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Pa(l.href),i=t.querySelector(lu(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Ri.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=i,te(i);return}i=t.ownerDocument||t,l=z0(l),(n=Qe.get(n))&&of(l,n),i=i.createElement("link"),te(i);var s=i;s._p=new Promise(function(d,y){s.onload=d,s.onerror=y}),ie(i,"link",l),e.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Ri.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function dg(){if(nu===null)throw Error(r(475));var t=nu;return t.stylesheets&&t.count===0&&hf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&hf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Ri(){if(this.count--,this.count===0){if(this.stylesheets)hf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Mi=null;function hf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Mi=new Map,e.forEach(hg,t),Mi=null,Ri.call(t))}function hg(t,e){if(!(e.state.loading&4)){var l=Mi.get(t);if(l)var a=l.get(null);else{l=new Map,Mi.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var s=n[i];(s.nodeName==="LINK"||s.getAttribute("media")!=="not all")&&(l.set(s.dataset.precedence,s),a=s)}a&&l.set(null,a)}n=e.instance,s=n.getAttribute("data-precedence"),i=l.get(s)||a,i===a&&l.set(null,n),l.set(s,n),this.count++,a=Ri.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var uu={$$typeof:k,Provider:null,Consumer:null,_currentValue:tt,_currentValue2:tt,_threadCount:0};function mg(t,e,l,a,n,i,s,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=rc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=rc(0),this.hiddenUpdates=rc(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function U0(t,e,l,a,n,i,s,d,y,x,N,B){return t=new mg(t,e,l,s,d,y,x,B),e=1,i===!0&&(e|=24),i=Ee(3,null,null,e),t.current=i,i.stateNode=t,e=Kc(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:e},Fc(i),t}function j0(t){return t?(t=Ma,t):Ma}function B0(t,e,l,a,n,i){n=j0(n),a.context===null?a.context=n:a.pendingContext=n,a=Cl(e),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=_l(t,a,e),l!==null&&(ze(l,t,e),Bn(l,t,e))}function H0(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function mf(t,e){H0(t,e),(t=t.alternate)&&H0(t,e)}function w0(t){if(t.tag===13){var e=Ra(t,67108864);e!==null&&ze(e,t,67108864),mf(t,67108864)}}var Di=!0;function yg(t,e,l,a){var n=M.T;M.T=null;var i=G.p;try{G.p=2,yf(t,e,l,a)}finally{G.p=i,M.T=n}}function gg(t,e,l,a){var n=M.T;M.T=null;var i=G.p;try{G.p=8,yf(t,e,l,a)}finally{G.p=i,M.T=n}}function yf(t,e,l,a){if(Di){var n=gf(a);if(n===null)ef(t,e,a,Ni,l),Y0(t,a);else if(pg(n,t,e,l,a))a.stopPropagation();else if(Y0(t,a),e&4&&-1<vg.indexOf(t)){for(;n!==null;){var i=va(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var s=Wl(i.pendingLanes);if(s!==0){var d=i;for(d.pendingLanes|=2,d.entangledLanes|=2;s;){var y=1<<31-Te(s);d.entanglements[1]|=y,s&=~y}Pe(i),(_t&6)===0&&(yi=Ke()+500,Pn(0))}}break;case 13:d=Ra(i,2),d!==null&&ze(d,i,2),vi(),mf(i,2)}if(i=gf(a),i===null&&ef(t,e,a,Ni,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else ef(t,e,a,null,l)}}function gf(t){return t=Sc(t),vf(t)}var Ni=null;function vf(t){if(Ni=null,t=ga(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=v(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Ni=t,null}function q0(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(lm()){case Ff:return 2;case Pf:return 8;case xu:case am:return 32;case If:return 268435456;default:return 32}default:return 32}}var pf=!1,Gl=null,Ql=null,Xl=null,iu=new Map,cu=new Map,Ll=[],vg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Y0(t,e){switch(t){case"focusin":case"focusout":Gl=null;break;case"dragenter":case"dragleave":Ql=null;break;case"mouseover":case"mouseout":Xl=null;break;case"pointerover":case"pointerout":iu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":cu.delete(e.pointerId)}}function ru(t,e,l,a,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},e!==null&&(e=va(e),e!==null&&w0(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function pg(t,e,l,a,n){switch(e){case"focusin":return Gl=ru(Gl,t,e,l,a,n),!0;case"dragenter":return Ql=ru(Ql,t,e,l,a,n),!0;case"mouseover":return Xl=ru(Xl,t,e,l,a,n),!0;case"pointerover":var i=n.pointerId;return iu.set(i,ru(iu.get(i)||null,t,e,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,cu.set(i,ru(cu.get(i)||null,t,e,l,a,n)),!0}return!1}function G0(t){var e=ga(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=v(l),e!==null){t.blockedOn=e,om(t.priority,function(){if(l.tag===13){var a=_e();a=fc(a);var n=Ra(l,a);n!==null&&ze(n,l,a),mf(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function Ui(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=gf(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);bc=a,l.target.dispatchEvent(a),bc=null}else return e=va(l),e!==null&&w0(e),t.blockedOn=l,!1;e.shift()}return!0}function Q0(t,e,l){Ui(t)&&l.delete(e)}function bg(){pf=!1,Gl!==null&&Ui(Gl)&&(Gl=null),Ql!==null&&Ui(Ql)&&(Ql=null),Xl!==null&&Ui(Xl)&&(Xl=null),iu.forEach(Q0),cu.forEach(Q0)}function ji(t,e){t.blockedOn===e&&(t.blockedOn=null,pf||(pf=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,bg)))}var Bi=null;function X0(t){Bi!==t&&(Bi=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){Bi===t&&(Bi=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(vf(a||l)===null)continue;break}var i=va(l);i!==null&&(t.splice(e,3),e-=3,gr(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function fu(t){function e(y){return ji(y,t)}Gl!==null&&ji(Gl,t),Ql!==null&&ji(Ql,t),Xl!==null&&ji(Xl,t),iu.forEach(e),cu.forEach(e);for(var l=0;l<Ll.length;l++){var a=Ll[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Ll.length&&(l=Ll[0],l.blockedOn===null);)G0(l),l.blockedOn===null&&Ll.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],s=n[me]||null;if(typeof i=="function")s||X0(l);else if(s){var d=null;if(i&&i.hasAttribute("formAction")){if(n=i,s=i[me]||null)d=s.formAction;else if(vf(n)!==null)continue}else d=s.action;typeof d=="function"?l[a+1]=d:(l.splice(a,3),a-=3),X0(l)}}}function bf(t){this._internalRoot=t}Hi.prototype.render=bf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var l=e.current,a=_e();B0(l,a,t,e,null,null)},Hi.prototype.unmount=bf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;B0(t.current,2,null,t,null,null),vi(),e[ya]=null}};function Hi(t){this._internalRoot=t}Hi.prototype.unstable_scheduleHydration=function(t){if(t){var e=ns();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Ll.length&&e!==0&&e<Ll[l].priority;l++);Ll.splice(l,0,t),l===0&&G0(t)}};var L0=c.version;if(L0!=="19.1.0")throw Error(r(527,L0,"19.1.0"));G.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=A(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var Sg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wi.isDisabled&&wi.supportsFiber)try{hn=wi.inject(Sg),Se=wi}catch{}}return ou.createRoot=function(t,e){if(!o(t))throw Error(r(299));var l=!1,a="",n=ud,i=id,s=cd,d=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(s=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=U0(t,1,!1,null,null,l,a,n,i,s,d,null),t[ya]=e.current,tf(t),new bf(e)},ou.hydrateRoot=function(t,e,l){if(!o(t))throw Error(r(299));var a=!1,n="",i=ud,s=id,d=cd,y=null,x=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(s=l.onCaughtError),l.onRecoverableError!==void 0&&(d=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(x=l.formState)),e=U0(t,1,!0,e,l??null,a,n,i,s,d,y,x),e.context=j0(null),l=e.current,a=_e(),a=fc(a),n=Cl(a),n.callback=null,_l(l,n,a),l=a,e.current.lanes=l,yn(e,l),Pe(e),t[ya]=e.current,tf(t),new Hi(e)},ou.version="19.1.0",ou}var th;function Mg(){if(th)return Af.exports;th=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),Af.exports=Rg(),Af.exports}var Dg=Mg();const Ng=xh(Dg),_h=lt.createContext(),Ug=({children:u})=>{const[c,f]=lt.useState(()=>{const o=localStorage.getItem("darkMode");return o?JSON.parse(o):!1});lt.useEffect(()=>{localStorage.setItem("darkMode",JSON.stringify(c)),document.body.className=c?"dark-mode":"light-mode"},[c]);const r=()=>{f(!c)};return q.jsx(_h.Provider,{value:{darkMode:c,toggleTheme:r},children:u})},eh=u=>u,jg=()=>{let u=eh;return{configure(c){u=c},generate(c){return u(c)},reset(){u=eh}}},Bg=jg();function ma(u,...c){const f=new URL(`https://mui.com/production-error/?code=${u}`);return c.forEach(r=>f.searchParams.append("args[]",r)),`Minified MUI error #${u}; visit ${f} for the full message.`}function rn(u){if(typeof u!="string")throw new Error(ma(7));return u.charAt(0).toUpperCase()+u.slice(1)}function zh(u){var c,f,r="";if(typeof u=="string"||typeof u=="number")r+=u;else if(typeof u=="object")if(Array.isArray(u)){var o=u.length;for(c=0;c<o;c++)u[c]&&(f=zh(u[c]))&&(r&&(r+=" "),r+=f)}else for(f in u)u[f]&&(r&&(r+=" "),r+=f);return r}function Hg(){for(var u,c,f=0,r="",o=arguments.length;f<o;f++)(u=arguments[f])&&(c=zh(u))&&(r&&(r+=" "),r+=c);return r}function wg(u,c,f=void 0){const r={};for(const o in u){const h=u[o];let v="",S=!0;for(let A=0;A<h.length;A+=1){const g=h[A];g&&(v+=(S===!0?"":" ")+c(g),S=!1,f&&f[g]&&(v+=" "+f[g]))}r[o]=v}return r}var Cf={exports:{}},Ut={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lh;function qg(){if(lh)return Ut;lh=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),g=Symbol.for("react.suspense_list"),z=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),U=Symbol.for("react.view_transition"),Y=Symbol.for("react.client.reference");function w(O){if(typeof O=="object"&&O!==null){var L=O.$$typeof;switch(L){case u:switch(O=O.type,O){case f:case o:case r:case A:case g:case U:return O;default:switch(O=O&&O.$$typeof,O){case v:case S:case R:case z:return O;case h:return O;default:return L}}case c:return L}}}return Ut.ContextConsumer=h,Ut.ContextProvider=v,Ut.Element=u,Ut.ForwardRef=S,Ut.Fragment=f,Ut.Lazy=R,Ut.Memo=z,Ut.Portal=c,Ut.Profiler=o,Ut.StrictMode=r,Ut.Suspense=A,Ut.SuspenseList=g,Ut.isContextConsumer=function(O){return w(O)===h},Ut.isContextProvider=function(O){return w(O)===v},Ut.isElement=function(O){return typeof O=="object"&&O!==null&&O.$$typeof===u},Ut.isForwardRef=function(O){return w(O)===S},Ut.isFragment=function(O){return w(O)===f},Ut.isLazy=function(O){return w(O)===R},Ut.isMemo=function(O){return w(O)===z},Ut.isPortal=function(O){return w(O)===c},Ut.isProfiler=function(O){return w(O)===o},Ut.isStrictMode=function(O){return w(O)===r},Ut.isSuspense=function(O){return w(O)===A},Ut.isSuspenseList=function(O){return w(O)===g},Ut.isValidElementType=function(O){return typeof O=="string"||typeof O=="function"||O===f||O===o||O===r||O===A||O===g||typeof O=="object"&&O!==null&&(O.$$typeof===R||O.$$typeof===z||O.$$typeof===v||O.$$typeof===h||O.$$typeof===S||O.$$typeof===Y||O.getModuleId!==void 0)},Ut.typeOf=w,Ut}var ah;function Yg(){return ah||(ah=1,Cf.exports=qg()),Cf.exports}var Rh=Yg();function bl(u){if(typeof u!="object"||u===null)return!1;const c=Object.getPrototypeOf(u);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(Symbol.toStringTag in u)&&!(Symbol.iterator in u)}function Mh(u){if(lt.isValidElement(u)||Rh.isValidElementType(u)||!bl(u))return u;const c={};return Object.keys(u).forEach(f=>{c[f]=Mh(u[f])}),c}function De(u,c,f={clone:!0}){const r=f.clone?{...u}:u;return bl(u)&&bl(c)&&Object.keys(c).forEach(o=>{lt.isValidElement(c[o])||Rh.isValidElementType(c[o])?r[o]=c[o]:bl(c[o])&&Object.prototype.hasOwnProperty.call(u,o)&&bl(u[o])?r[o]=De(u[o],c[o],f):f.clone?r[o]=bl(c[o])?Mh(c[o]):c[o]:r[o]=c[o]}),r}function gu(u,c){return c?De(u,c,{clone:!1}):u}function Gg(u,c){if(!u.containerQueries)return c;const f=Object.keys(c).filter(r=>r.startsWith("@container")).sort((r,o)=>{var v,S;const h=/min-width:\s*([0-9.]+)/;return+(((v=r.match(h))==null?void 0:v[1])||0)-+(((S=o.match(h))==null?void 0:S[1])||0)});return f.length?f.reduce((r,o)=>{const h=c[o];return delete r[o],r[o]=h,r},{...c}):c}function Qg(u,c){return c==="@"||c.startsWith("@")&&(u.some(f=>c.startsWith(`@${f}`))||!!c.match(/^@\d/))}function Xg(u,c){const f=c.match(/^@([^/]+)?\/?(.+)?$/);if(!f)return null;const[,r,o]=f,h=Number.isNaN(+r)?r||0:+r;return u.containerQueries(o).up(h)}function Lg(u){const c=(h,v)=>h.replace("@media",v?`@container ${v}`:"@container");function f(h,v){h.up=(...S)=>c(u.breakpoints.up(...S),v),h.down=(...S)=>c(u.breakpoints.down(...S),v),h.between=(...S)=>c(u.breakpoints.between(...S),v),h.only=(...S)=>c(u.breakpoints.only(...S),v),h.not=(...S)=>{const A=c(u.breakpoints.not(...S),v);return A.includes("not all and")?A.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):A}}const r={},o=h=>(f(r,h),r);return f(o),{...u,containerQueries:o}}const $i={xs:0,sm:600,md:900,lg:1200,xl:1536},nh={keys:["xs","sm","md","lg","xl"],up:u=>`@media (min-width:${$i[u]}px)`},Vg={containerQueries:u=>({up:c=>{let f=typeof c=="number"?c:$i[c]||c;return typeof f=="number"&&(f=`${f}px`),u?`@container ${u} (min-width:${f})`:`@container (min-width:${f})`}})};function Sl(u,c,f){const r=u.theme||{};if(Array.isArray(c)){const h=r.breakpoints||nh;return c.reduce((v,S,A)=>(v[h.up(h.keys[A])]=f(c[A]),v),{})}if(typeof c=="object"){const h=r.breakpoints||nh;return Object.keys(c).reduce((v,S)=>{if(Qg(h.keys,S)){const A=Xg(r.containerQueries?r:Vg,S);A&&(v[A]=f(c[S],S))}else if(Object.keys(h.values||$i).includes(S)){const A=h.up(S);v[A]=f(c[S],S)}else{const A=S;v[A]=c[A]}return v},{})}return f(c)}function kg(u={}){var f;return((f=u.keys)==null?void 0:f.reduce((r,o)=>{const h=u.up(o);return r[h]={},r},{}))||{}}function Zg(u,c){return u.reduce((f,r)=>{const o=f[r];return(!o||Object.keys(o).length===0)&&delete f[r],f},c)}function Ji(u,c,f=!0){if(!c||typeof c!="string")return null;if(u&&u.vars&&f){const r=`vars.${c}`.split(".").reduce((o,h)=>o&&o[h]?o[h]:null,u);if(r!=null)return r}return c.split(".").reduce((r,o)=>r&&r[o]!=null?r[o]:null,u)}function Zi(u,c,f,r=f){let o;return typeof u=="function"?o=u(f):Array.isArray(u)?o=u[f]||r:o=Ji(u,f)||r,c&&(o=c(o,r,u)),o}function Kt(u){const{prop:c,cssProperty:f=u.prop,themeKey:r,transform:o}=u,h=v=>{if(v[c]==null)return null;const S=v[c],A=v.theme,g=Ji(A,r)||{};return Sl(v,S,R=>{let U=Zi(g,o,R);return R===U&&typeof R=="string"&&(U=Zi(g,o,`${c}${R==="default"?"":rn(R)}`,R)),f===!1?U:{[f]:U}})};return h.propTypes={},h.filterProps=[c],h}function Kg(u){const c={};return f=>(c[f]===void 0&&(c[f]=u(f)),c[f])}const $g={m:"margin",p:"padding"},Jg={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},uh={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Wg=Kg(u=>{if(u.length>2)if(uh[u])u=uh[u];else return[u];const[c,f]=u.split(""),r=$g[c],o=Jg[f]||"";return Array.isArray(o)?o.map(h=>r+h):[r+o]}),Gf=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Qf=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Gf,...Qf];function Tu(u,c,f,r){const o=Ji(u,c,!0)??f;return typeof o=="number"||typeof o=="string"?h=>typeof h=="string"?h:typeof o=="string"?o.startsWith("var(")&&h===0?0:o.startsWith("var(")&&h===1?o:`calc(${h} * ${o})`:o*h:Array.isArray(o)?h=>{if(typeof h=="string")return h;const v=Math.abs(h),S=o[v];return h>=0?S:typeof S=="number"?-S:typeof S=="string"&&S.startsWith("var(")?`calc(-1 * ${S})`:`-${S}`}:typeof o=="function"?o:()=>{}}function Xf(u){return Tu(u,"spacing",8)}function Au(u,c){return typeof c=="string"||c==null?c:u(c)}function Fg(u,c){return f=>u.reduce((r,o)=>(r[o]=Au(c,f),r),{})}function Pg(u,c,f,r){if(!c.includes(f))return null;const o=Wg(f),h=Fg(o,r),v=u[f];return Sl(u,v,h)}function Dh(u,c){const f=Xf(u.theme);return Object.keys(u).map(r=>Pg(u,c,r,f)).reduce(gu,{})}function Lt(u){return Dh(u,Gf)}Lt.propTypes={};Lt.filterProps=Gf;function Vt(u){return Dh(u,Qf)}Vt.propTypes={};Vt.filterProps=Qf;function Wi(...u){const c=u.reduce((r,o)=>(o.filterProps.forEach(h=>{r[h]=o}),r),{}),f=r=>Object.keys(r).reduce((o,h)=>c[h]?gu(o,c[h](r)):o,{});return f.propTypes={},f.filterProps=u.reduce((r,o)=>r.concat(o.filterProps),[]),f}function Xe(u){return typeof u!="number"?u:`${u}px solid`}function Le(u,c){return Kt({prop:u,themeKey:"borders",transform:c})}const Ig=Le("border",Xe),t1=Le("borderTop",Xe),e1=Le("borderRight",Xe),l1=Le("borderBottom",Xe),a1=Le("borderLeft",Xe),n1=Le("borderColor"),u1=Le("borderTopColor"),i1=Le("borderRightColor"),c1=Le("borderBottomColor"),r1=Le("borderLeftColor"),f1=Le("outline",Xe),s1=Le("outlineColor"),Fi=u=>{if(u.borderRadius!==void 0&&u.borderRadius!==null){const c=Tu(u.theme,"shape.borderRadius",4),f=r=>({borderRadius:Au(c,r)});return Sl(u,u.borderRadius,f)}return null};Fi.propTypes={};Fi.filterProps=["borderRadius"];Wi(Ig,t1,e1,l1,a1,n1,u1,i1,c1,r1,Fi,f1,s1);const Pi=u=>{if(u.gap!==void 0&&u.gap!==null){const c=Tu(u.theme,"spacing",8),f=r=>({gap:Au(c,r)});return Sl(u,u.gap,f)}return null};Pi.propTypes={};Pi.filterProps=["gap"];const Ii=u=>{if(u.columnGap!==void 0&&u.columnGap!==null){const c=Tu(u.theme,"spacing",8),f=r=>({columnGap:Au(c,r)});return Sl(u,u.columnGap,f)}return null};Ii.propTypes={};Ii.filterProps=["columnGap"];const tc=u=>{if(u.rowGap!==void 0&&u.rowGap!==null){const c=Tu(u.theme,"spacing",8),f=r=>({rowGap:Au(c,r)});return Sl(u,u.rowGap,f)}return null};tc.propTypes={};tc.filterProps=["rowGap"];const o1=Kt({prop:"gridColumn"}),d1=Kt({prop:"gridRow"}),h1=Kt({prop:"gridAutoFlow"}),m1=Kt({prop:"gridAutoColumns"}),y1=Kt({prop:"gridAutoRows"}),g1=Kt({prop:"gridTemplateColumns"}),v1=Kt({prop:"gridTemplateRows"}),p1=Kt({prop:"gridTemplateAreas"}),b1=Kt({prop:"gridArea"});Wi(Pi,Ii,tc,o1,d1,h1,m1,y1,g1,v1,p1,b1);function un(u,c){return c==="grey"?c:u}const S1=Kt({prop:"color",themeKey:"palette",transform:un}),T1=Kt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:un}),A1=Kt({prop:"backgroundColor",themeKey:"palette",transform:un});Wi(S1,T1,A1);function Me(u){return u<=1&&u!==0?`${u*100}%`:u}const E1=Kt({prop:"width",transform:Me}),Lf=u=>{if(u.maxWidth!==void 0&&u.maxWidth!==null){const c=f=>{var o,h,v,S,A;const r=((v=(h=(o=u.theme)==null?void 0:o.breakpoints)==null?void 0:h.values)==null?void 0:v[f])||$i[f];return r?((A=(S=u.theme)==null?void 0:S.breakpoints)==null?void 0:A.unit)!=="px"?{maxWidth:`${r}${u.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:Me(f)}};return Sl(u,u.maxWidth,c)}return null};Lf.filterProps=["maxWidth"];const x1=Kt({prop:"minWidth",transform:Me}),O1=Kt({prop:"height",transform:Me}),C1=Kt({prop:"maxHeight",transform:Me}),_1=Kt({prop:"minHeight",transform:Me});Kt({prop:"size",cssProperty:"width",transform:Me});Kt({prop:"size",cssProperty:"height",transform:Me});const z1=Kt({prop:"boxSizing"});Wi(E1,Lf,x1,O1,C1,_1,z1);const ec={border:{themeKey:"borders",transform:Xe},borderTop:{themeKey:"borders",transform:Xe},borderRight:{themeKey:"borders",transform:Xe},borderBottom:{themeKey:"borders",transform:Xe},borderLeft:{themeKey:"borders",transform:Xe},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:Xe},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Fi},color:{themeKey:"palette",transform:un},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:un},backgroundColor:{themeKey:"palette",transform:un},p:{style:Vt},pt:{style:Vt},pr:{style:Vt},pb:{style:Vt},pl:{style:Vt},px:{style:Vt},py:{style:Vt},padding:{style:Vt},paddingTop:{style:Vt},paddingRight:{style:Vt},paddingBottom:{style:Vt},paddingLeft:{style:Vt},paddingX:{style:Vt},paddingY:{style:Vt},paddingInline:{style:Vt},paddingInlineStart:{style:Vt},paddingInlineEnd:{style:Vt},paddingBlock:{style:Vt},paddingBlockStart:{style:Vt},paddingBlockEnd:{style:Vt},m:{style:Lt},mt:{style:Lt},mr:{style:Lt},mb:{style:Lt},ml:{style:Lt},mx:{style:Lt},my:{style:Lt},margin:{style:Lt},marginTop:{style:Lt},marginRight:{style:Lt},marginBottom:{style:Lt},marginLeft:{style:Lt},marginX:{style:Lt},marginY:{style:Lt},marginInline:{style:Lt},marginInlineStart:{style:Lt},marginInlineEnd:{style:Lt},marginBlock:{style:Lt},marginBlockStart:{style:Lt},marginBlockEnd:{style:Lt},displayPrint:{cssProperty:!1,transform:u=>({"@media print":{display:u}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Pi},rowGap:{style:tc},columnGap:{style:Ii},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Me},maxWidth:{style:Lf},minWidth:{transform:Me},height:{transform:Me},maxHeight:{transform:Me},minHeight:{transform:Me},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function R1(...u){const c=u.reduce((r,o)=>r.concat(Object.keys(o)),[]),f=new Set(c);return u.every(r=>f.size===Object.keys(r).length)}function M1(u,c){return typeof u=="function"?u(c):u}function D1(){function u(f,r,o,h){const v={[f]:r,theme:o},S=h[f];if(!S)return{[f]:r};const{cssProperty:A=f,themeKey:g,transform:z,style:R}=S;if(r==null)return null;if(g==="typography"&&r==="inherit")return{[f]:r};const U=Ji(o,g)||{};return R?R(v):Sl(v,r,w=>{let O=Zi(U,z,w);return w===O&&typeof w=="string"&&(O=Zi(U,z,`${f}${w==="default"?"":rn(w)}`,w)),A===!1?O:{[A]:O}})}function c(f){const{sx:r,theme:o={}}=f||{};if(!r)return null;const h=o.unstable_sxConfig??ec;function v(S){let A=S;if(typeof S=="function")A=S(o);else if(typeof S!="object")return S;if(!A)return null;const g=kg(o.breakpoints),z=Object.keys(g);let R=g;return Object.keys(A).forEach(U=>{const Y=M1(A[U],o);if(Y!=null)if(typeof Y=="object")if(h[U])R=gu(R,u(U,Y,o,h));else{const w=Sl({theme:o},Y,O=>({[U]:O}));R1(w,Y)?R[U]=c({sx:Y,theme:o}):R=gu(R,w)}else R=gu(R,u(U,Y,o,h))}),Gg(o,Zg(z,R))}return Array.isArray(r)?r.map(v):v(r)}return c}const fn=D1();fn.filterProps=["sx"];function Df(){return Df=Object.assign?Object.assign.bind():function(u){for(var c=1;c<arguments.length;c++){var f=arguments[c];for(var r in f)({}).hasOwnProperty.call(f,r)&&(u[r]=f[r])}return u},Df.apply(null,arguments)}function N1(u){if(u.sheet)return u.sheet;for(var c=0;c<document.styleSheets.length;c++)if(document.styleSheets[c].ownerNode===u)return document.styleSheets[c]}function U1(u){var c=document.createElement("style");return c.setAttribute("data-emotion",u.key),u.nonce!==void 0&&c.setAttribute("nonce",u.nonce),c.appendChild(document.createTextNode("")),c.setAttribute("data-s",""),c}var j1=function(){function u(f){var r=this;this._insertTag=function(o){var h;r.tags.length===0?r.insertionPoint?h=r.insertionPoint.nextSibling:r.prepend?h=r.container.firstChild:h=r.before:h=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(o,h),r.tags.push(o)},this.isSpeedy=f.speedy===void 0?!0:f.speedy,this.tags=[],this.ctr=0,this.nonce=f.nonce,this.key=f.key,this.container=f.container,this.prepend=f.prepend,this.insertionPoint=f.insertionPoint,this.before=null}var c=u.prototype;return c.hydrate=function(r){r.forEach(this._insertTag)},c.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(U1(this));var o=this.tags[this.tags.length-1];if(this.isSpeedy){var h=N1(o);try{h.insertRule(r,h.cssRules.length)}catch{}}else o.appendChild(document.createTextNode(r));this.ctr++},c.flush=function(){this.tags.forEach(function(r){var o;return(o=r.parentNode)==null?void 0:o.removeChild(r)}),this.tags=[],this.ctr=0},u}(),oe="-ms-",Ki="-moz-",At="-webkit-",Nh="comm",Vf="rule",kf="decl",B1="@import",Uh="@keyframes",H1="@layer",w1=Math.abs,lc=String.fromCharCode,q1=Object.assign;function Y1(u,c){return ce(u,0)^45?(((c<<2^ce(u,0))<<2^ce(u,1))<<2^ce(u,2))<<2^ce(u,3):0}function jh(u){return u.trim()}function G1(u,c){return(u=c.exec(u))?u[0]:u}function Et(u,c,f){return u.replace(c,f)}function Nf(u,c){return u.indexOf(c)}function ce(u,c){return u.charCodeAt(c)|0}function vu(u,c,f){return u.slice(c,f)}function tl(u){return u.length}function Zf(u){return u.length}function qi(u,c){return c.push(u),u}function Q1(u,c){return u.map(c).join("")}var ac=1,sn=1,Bh=0,be=0,Wt=0,on="";function nc(u,c,f,r,o,h,v){return{value:u,root:c,parent:f,type:r,props:o,children:h,line:ac,column:sn,length:v,return:""}}function du(u,c){return q1(nc("",null,null,"",null,null,0),u,{length:-u.length},c)}function X1(){return Wt}function L1(){return Wt=be>0?ce(on,--be):0,sn--,Wt===10&&(sn=1,ac--),Wt}function Ne(){return Wt=be<Bh?ce(on,be++):0,sn++,Wt===10&&(sn=1,ac++),Wt}function ll(){return ce(on,be)}function Xi(){return be}function Eu(u,c){return vu(on,u,c)}function pu(u){switch(u){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Hh(u){return ac=sn=1,Bh=tl(on=u),be=0,[]}function wh(u){return on="",u}function Li(u){return jh(Eu(be-1,Uf(u===91?u+2:u===40?u+1:u)))}function V1(u){for(;(Wt=ll())&&Wt<33;)Ne();return pu(u)>2||pu(Wt)>3?"":" "}function k1(u,c){for(;--c&&Ne()&&!(Wt<48||Wt>102||Wt>57&&Wt<65||Wt>70&&Wt<97););return Eu(u,Xi()+(c<6&&ll()==32&&Ne()==32))}function Uf(u){for(;Ne();)switch(Wt){case u:return be;case 34:case 39:u!==34&&u!==39&&Uf(Wt);break;case 40:u===41&&Uf(u);break;case 92:Ne();break}return be}function Z1(u,c){for(;Ne()&&u+Wt!==57;)if(u+Wt===84&&ll()===47)break;return"/*"+Eu(c,be-1)+"*"+lc(u===47?u:Ne())}function K1(u){for(;!pu(ll());)Ne();return Eu(u,be)}function $1(u){return wh(Vi("",null,null,null,[""],u=Hh(u),0,[0],u))}function Vi(u,c,f,r,o,h,v,S,A){for(var g=0,z=0,R=v,U=0,Y=0,w=0,O=1,L=1,$=1,ut=0,k="",K=o,Q=h,F=r,W=k;L;)switch(w=ut,ut=Ne()){case 40:if(w!=108&&ce(W,R-1)==58){Nf(W+=Et(Li(ut),"&","&\f"),"&\f")!=-1&&($=-1);break}case 34:case 39:case 91:W+=Li(ut);break;case 9:case 10:case 13:case 32:W+=V1(w);break;case 92:W+=k1(Xi()-1,7);continue;case 47:switch(ll()){case 42:case 47:qi(J1(Z1(Ne(),Xi()),c,f),A);break;default:W+="/"}break;case 123*O:S[g++]=tl(W)*$;case 125*O:case 59:case 0:switch(ut){case 0:case 125:L=0;case 59+z:$==-1&&(W=Et(W,/\f/g,"")),Y>0&&tl(W)-R&&qi(Y>32?ch(W+";",r,f,R-1):ch(Et(W," ","")+";",r,f,R-2),A);break;case 59:W+=";";default:if(qi(F=ih(W,c,f,g,z,o,S,k,K=[],Q=[],R),h),ut===123)if(z===0)Vi(W,c,F,F,K,h,R,S,Q);else switch(U===99&&ce(W,3)===110?100:U){case 100:case 108:case 109:case 115:Vi(u,F,F,r&&qi(ih(u,F,F,0,0,o,S,k,o,K=[],R),Q),o,Q,R,S,r?K:Q);break;default:Vi(W,F,F,F,[""],Q,0,S,Q)}}g=z=Y=0,O=$=1,k=W="",R=v;break;case 58:R=1+tl(W),Y=w;default:if(O<1){if(ut==123)--O;else if(ut==125&&O++==0&&L1()==125)continue}switch(W+=lc(ut),ut*O){case 38:$=z>0?1:(W+="\f",-1);break;case 44:S[g++]=(tl(W)-1)*$,$=1;break;case 64:ll()===45&&(W+=Li(Ne())),U=ll(),z=R=tl(k=W+=K1(Xi())),ut++;break;case 45:w===45&&tl(W)==2&&(O=0)}}return h}function ih(u,c,f,r,o,h,v,S,A,g,z){for(var R=o-1,U=o===0?h:[""],Y=Zf(U),w=0,O=0,L=0;w<r;++w)for(var $=0,ut=vu(u,R+1,R=w1(O=v[w])),k=u;$<Y;++$)(k=jh(O>0?U[$]+" "+ut:Et(ut,/&\f/g,U[$])))&&(A[L++]=k);return nc(u,c,f,o===0?Vf:S,A,g,z)}function J1(u,c,f){return nc(u,c,f,Nh,lc(X1()),vu(u,2,-2),0)}function ch(u,c,f,r){return nc(u,c,f,kf,vu(u,0,r),vu(u,r+1,-1),r)}function cn(u,c){for(var f="",r=Zf(u),o=0;o<r;o++)f+=c(u[o],o,u,c)||"";return f}function W1(u,c,f,r){switch(u.type){case H1:if(u.children.length)break;case B1:case kf:return u.return=u.return||u.value;case Nh:return"";case Uh:return u.return=u.value+"{"+cn(u.children,r)+"}";case Vf:u.value=u.props.join(",")}return tl(f=cn(u.children,r))?u.return=u.value+"{"+f+"}":""}function F1(u){var c=Zf(u);return function(f,r,o,h){for(var v="",S=0;S<c;S++)v+=u[S](f,r,o,h)||"";return v}}function P1(u){return function(c){c.root||(c=c.return)&&u(c)}}function qh(u){var c=Object.create(null);return function(f){return c[f]===void 0&&(c[f]=u(f)),c[f]}}var I1=function(c,f,r){for(var o=0,h=0;o=h,h=ll(),o===38&&h===12&&(f[r]=1),!pu(h);)Ne();return Eu(c,be)},tv=function(c,f){var r=-1,o=44;do switch(pu(o)){case 0:o===38&&ll()===12&&(f[r]=1),c[r]+=I1(be-1,f,r);break;case 2:c[r]+=Li(o);break;case 4:if(o===44){c[++r]=ll()===58?"&\f":"",f[r]=c[r].length;break}default:c[r]+=lc(o)}while(o=Ne());return c},ev=function(c,f){return wh(tv(Hh(c),f))},rh=new WeakMap,lv=function(c){if(!(c.type!=="rule"||!c.parent||c.length<1)){for(var f=c.value,r=c.parent,o=c.column===r.column&&c.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(c.props.length===1&&f.charCodeAt(0)!==58&&!rh.get(r))&&!o){rh.set(c,!0);for(var h=[],v=ev(f,h),S=r.props,A=0,g=0;A<v.length;A++)for(var z=0;z<S.length;z++,g++)c.props[g]=h[A]?v[A].replace(/&\f/g,S[z]):S[z]+" "+v[A]}}},av=function(c){if(c.type==="decl"){var f=c.value;f.charCodeAt(0)===108&&f.charCodeAt(2)===98&&(c.return="",c.value="")}};function Yh(u,c){switch(Y1(u,c)){case 5103:return At+"print-"+u+u;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return At+u+u;case 5349:case 4246:case 4810:case 6968:case 2756:return At+u+Ki+u+oe+u+u;case 6828:case 4268:return At+u+oe+u+u;case 6165:return At+u+oe+"flex-"+u+u;case 5187:return At+u+Et(u,/(\w+).+(:[^]+)/,At+"box-$1$2"+oe+"flex-$1$2")+u;case 5443:return At+u+oe+"flex-item-"+Et(u,/flex-|-self/,"")+u;case 4675:return At+u+oe+"flex-line-pack"+Et(u,/align-content|flex-|-self/,"")+u;case 5548:return At+u+oe+Et(u,"shrink","negative")+u;case 5292:return At+u+oe+Et(u,"basis","preferred-size")+u;case 6060:return At+"box-"+Et(u,"-grow","")+At+u+oe+Et(u,"grow","positive")+u;case 4554:return At+Et(u,/([^-])(transform)/g,"$1"+At+"$2")+u;case 6187:return Et(Et(Et(u,/(zoom-|grab)/,At+"$1"),/(image-set)/,At+"$1"),u,"")+u;case 5495:case 3959:return Et(u,/(image-set\([^]*)/,At+"$1$`$1");case 4968:return Et(Et(u,/(.+:)(flex-)?(.*)/,At+"box-pack:$3"+oe+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+At+u+u;case 4095:case 3583:case 4068:case 2532:return Et(u,/(.+)-inline(.+)/,At+"$1$2")+u;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(tl(u)-1-c>6)switch(ce(u,c+1)){case 109:if(ce(u,c+4)!==45)break;case 102:return Et(u,/(.+:)(.+)-([^]+)/,"$1"+At+"$2-$3$1"+Ki+(ce(u,c+3)==108?"$3":"$2-$3"))+u;case 115:return~Nf(u,"stretch")?Yh(Et(u,"stretch","fill-available"),c)+u:u}break;case 4949:if(ce(u,c+1)!==115)break;case 6444:switch(ce(u,tl(u)-3-(~Nf(u,"!important")&&10))){case 107:return Et(u,":",":"+At)+u;case 101:return Et(u,/(.+:)([^;!]+)(;|!.+)?/,"$1"+At+(ce(u,14)===45?"inline-":"")+"box$3$1"+At+"$2$3$1"+oe+"$2box$3")+u}break;case 5936:switch(ce(u,c+11)){case 114:return At+u+oe+Et(u,/[svh]\w+-[tblr]{2}/,"tb")+u;case 108:return At+u+oe+Et(u,/[svh]\w+-[tblr]{2}/,"tb-rl")+u;case 45:return At+u+oe+Et(u,/[svh]\w+-[tblr]{2}/,"lr")+u}return At+u+oe+u+u}return u}var nv=function(c,f,r,o){if(c.length>-1&&!c.return)switch(c.type){case kf:c.return=Yh(c.value,c.length);break;case Uh:return cn([du(c,{value:Et(c.value,"@","@"+At)})],o);case Vf:if(c.length)return Q1(c.props,function(h){switch(G1(h,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return cn([du(c,{props:[Et(h,/:(read-\w+)/,":"+Ki+"$1")]})],o);case"::placeholder":return cn([du(c,{props:[Et(h,/:(plac\w+)/,":"+At+"input-$1")]}),du(c,{props:[Et(h,/:(plac\w+)/,":"+Ki+"$1")]}),du(c,{props:[Et(h,/:(plac\w+)/,oe+"input-$1")]})],o)}return""})}},uv=[nv],iv=function(c){var f=c.key;if(f==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(O){var L=O.getAttribute("data-emotion");L.indexOf(" ")!==-1&&(document.head.appendChild(O),O.setAttribute("data-s",""))})}var o=c.stylisPlugins||uv,h={},v,S=[];v=c.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+f+' "]'),function(O){for(var L=O.getAttribute("data-emotion").split(" "),$=1;$<L.length;$++)h[L[$]]=!0;S.push(O)});var A,g=[lv,av];{var z,R=[W1,P1(function(O){z.insert(O)})],U=F1(g.concat(o,R)),Y=function(L){return cn($1(L),U)};A=function(L,$,ut,k){z=ut,Y(L?L+"{"+$.styles+"}":$.styles),k&&(w.inserted[$.name]=!0)}}var w={key:f,sheet:new j1({key:f,container:v,nonce:c.nonce,speedy:c.speedy,prepend:c.prepend,insertionPoint:c.insertionPoint}),nonce:c.nonce,inserted:h,registered:{},insert:A};return w.sheet.hydrate(S),w},cv=!0;function rv(u,c,f){var r="";return f.split(" ").forEach(function(o){u[o]!==void 0?c.push(u[o]+";"):o&&(r+=o+" ")}),r}var Gh=function(c,f,r){var o=c.key+"-"+f.name;(r===!1||cv===!1)&&c.registered[o]===void 0&&(c.registered[o]=f.styles)},fv=function(c,f,r){Gh(c,f,r);var o=c.key+"-"+f.name;if(c.inserted[f.name]===void 0){var h=f;do c.insert(f===h?"."+o:"",h,c.sheet,!0),h=h.next;while(h!==void 0)}};function sv(u){for(var c=0,f,r=0,o=u.length;o>=4;++r,o-=4)f=u.charCodeAt(r)&255|(u.charCodeAt(++r)&255)<<8|(u.charCodeAt(++r)&255)<<16|(u.charCodeAt(++r)&255)<<24,f=(f&65535)*1540483477+((f>>>16)*59797<<16),f^=f>>>24,c=(f&65535)*1540483477+((f>>>16)*59797<<16)^(c&65535)*1540483477+((c>>>16)*59797<<16);switch(o){case 3:c^=(u.charCodeAt(r+2)&255)<<16;case 2:c^=(u.charCodeAt(r+1)&255)<<8;case 1:c^=u.charCodeAt(r)&255,c=(c&65535)*1540483477+((c>>>16)*59797<<16)}return c^=c>>>13,c=(c&65535)*1540483477+((c>>>16)*59797<<16),((c^c>>>15)>>>0).toString(36)}var ov={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},dv=/[A-Z]|^ms/g,hv=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Qh=function(c){return c.charCodeAt(1)===45},fh=function(c){return c!=null&&typeof c!="boolean"},_f=qh(function(u){return Qh(u)?u:u.replace(dv,"-$&").toLowerCase()}),sh=function(c,f){switch(c){case"animation":case"animationName":if(typeof f=="string")return f.replace(hv,function(r,o,h){return el={name:o,styles:h,next:el},o})}return ov[c]!==1&&!Qh(c)&&typeof f=="number"&&f!==0?f+"px":f};function bu(u,c,f){if(f==null)return"";var r=f;if(r.__emotion_styles!==void 0)return r;switch(typeof f){case"boolean":return"";case"object":{var o=f;if(o.anim===1)return el={name:o.name,styles:o.styles,next:el},o.name;var h=f;if(h.styles!==void 0){var v=h.next;if(v!==void 0)for(;v!==void 0;)el={name:v.name,styles:v.styles,next:el},v=v.next;var S=h.styles+";";return S}return mv(u,c,f)}case"function":{if(u!==void 0){var A=el,g=f(u);return el=A,bu(u,c,g)}break}}var z=f;if(c==null)return z;var R=c[z];return R!==void 0?R:z}function mv(u,c,f){var r="";if(Array.isArray(f))for(var o=0;o<f.length;o++)r+=bu(u,c,f[o])+";";else for(var h in f){var v=f[h];if(typeof v!="object"){var S=v;c!=null&&c[S]!==void 0?r+=h+"{"+c[S]+"}":fh(S)&&(r+=_f(h)+":"+sh(h,S)+";")}else if(Array.isArray(v)&&typeof v[0]=="string"&&(c==null||c[v[0]]===void 0))for(var A=0;A<v.length;A++)fh(v[A])&&(r+=_f(h)+":"+sh(h,v[A])+";");else{var g=bu(u,c,v);switch(h){case"animation":case"animationName":{r+=_f(h)+":"+g+";";break}default:r+=h+"{"+g+"}"}}}return r}var oh=/label:\s*([^\s;{]+)\s*(;|$)/g,el;function Xh(u,c,f){if(u.length===1&&typeof u[0]=="object"&&u[0]!==null&&u[0].styles!==void 0)return u[0];var r=!0,o="";el=void 0;var h=u[0];if(h==null||h.raw===void 0)r=!1,o+=bu(f,c,h);else{var v=h;o+=v[0]}for(var S=1;S<u.length;S++)if(o+=bu(f,c,u[S]),r){var A=h;o+=A[S]}oh.lastIndex=0;for(var g="",z;(z=oh.exec(o))!==null;)g+="-"+z[1];var R=sv(o)+g;return{name:R,styles:o,next:el}}var yv=function(c){return c()},gv=$0.useInsertionEffect?$0.useInsertionEffect:!1,vv=gv||yv,Lh=lt.createContext(typeof HTMLElement<"u"?iv({key:"css"}):null);Lh.Provider;var pv=function(c){return lt.forwardRef(function(f,r){var o=lt.useContext(Lh);return c(f,o,r)})},bv=lt.createContext({}),Sv=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Tv=qh(function(u){return Sv.test(u)||u.charCodeAt(0)===111&&u.charCodeAt(1)===110&&u.charCodeAt(2)<91}),Av=Tv,Ev=function(c){return c!=="theme"},dh=function(c){return typeof c=="string"&&c.charCodeAt(0)>96?Av:Ev},hh=function(c,f,r){var o;if(f){var h=f.shouldForwardProp;o=c.__emotion_forwardProp&&h?function(v){return c.__emotion_forwardProp(v)&&h(v)}:h}return typeof o!="function"&&r&&(o=c.__emotion_forwardProp),o},xv=function(c){var f=c.cache,r=c.serialized,o=c.isStringTag;return Gh(f,r,o),vv(function(){return fv(f,r,o)}),null},Ov=function u(c,f){var r=c.__emotion_real===c,o=r&&c.__emotion_base||c,h,v;f!==void 0&&(h=f.label,v=f.target);var S=hh(c,f,r),A=S||dh(o),g=!A("as");return function(){var z=arguments,R=r&&c.__emotion_styles!==void 0?c.__emotion_styles.slice(0):[];if(h!==void 0&&R.push("label:"+h+";"),z[0]==null||z[0].raw===void 0)R.push.apply(R,z);else{var U=z[0];R.push(U[0]);for(var Y=z.length,w=1;w<Y;w++)R.push(z[w],U[w])}var O=pv(function(L,$,ut){var k=g&&L.as||o,K="",Q=[],F=L;if(L.theme==null){F={};for(var W in L)F[W]=L[W];F.theme=lt.useContext(bv)}typeof L.className=="string"?K=rv($.registered,Q,L.className):L.className!=null&&(K=L.className+" ");var ot=Xh(R.concat(Q),$.registered,F);K+=$.key+"-"+ot.name,v!==void 0&&(K+=" "+v);var xt=g&&S===void 0?dh(k):A,m={};for(var X in L)g&&X==="as"||xt(X)&&(m[X]=L[X]);return m.className=K,ut&&(m.ref=ut),lt.createElement(lt.Fragment,null,lt.createElement(xv,{cache:$,serialized:ot,isStringTag:typeof k=="string"}),lt.createElement(k,m))});return O.displayName=h!==void 0?h:"Styled("+(typeof o=="string"?o:o.displayName||o.name||"Component")+")",O.defaultProps=c.defaultProps,O.__emotion_real=O,O.__emotion_base=o,O.__emotion_styles=R,O.__emotion_forwardProp=S,Object.defineProperty(O,"toString",{value:function(){return"."+v}}),O.withComponent=function(L,$){var ut=u(L,Df({},f,$,{shouldForwardProp:hh(O,$,!0)}));return ut.apply(void 0,R)},O}},Cv=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],jf=Ov.bind(null);Cv.forEach(function(u){jf[u]=jf(u)});function _v(u,c){return jf(u,c)}function zv(u,c){Array.isArray(u.__emotion_styles)&&(u.__emotion_styles=c(u.__emotion_styles))}const mh=[];function yh(u){return mh[0]=u,Xh(mh)}const Rv=u=>{const c=Object.keys(u).map(f=>({key:f,val:u[f]}))||[];return c.sort((f,r)=>f.val-r.val),c.reduce((f,r)=>({...f,[r.key]:r.val}),{})};function Mv(u){const{values:c={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:f="px",step:r=5,...o}=u,h=Rv(c),v=Object.keys(h);function S(U){return`@media (min-width:${typeof c[U]=="number"?c[U]:U}${f})`}function A(U){return`@media (max-width:${(typeof c[U]=="number"?c[U]:U)-r/100}${f})`}function g(U,Y){const w=v.indexOf(Y);return`@media (min-width:${typeof c[U]=="number"?c[U]:U}${f}) and (max-width:${(w!==-1&&typeof c[v[w]]=="number"?c[v[w]]:Y)-r/100}${f})`}function z(U){return v.indexOf(U)+1<v.length?g(U,v[v.indexOf(U)+1]):S(U)}function R(U){const Y=v.indexOf(U);return Y===0?S(v[1]):Y===v.length-1?A(v[Y]):g(U,v[v.indexOf(U)+1]).replace("@media","@media not all and")}return{keys:v,values:h,up:S,down:A,between:g,only:z,not:R,unit:f,...o}}const Dv={borderRadius:4};function Vh(u=8,c=Xf({spacing:u})){if(u.mui)return u;const f=(...r)=>(r.length===0?[1]:r).map(h=>{const v=c(h);return typeof v=="number"?`${v}px`:v}).join(" ");return f.mui=!0,f}function Nv(u,c){var r;const f=this;if(f.vars){if(!((r=f.colorSchemes)!=null&&r[u])||typeof f.getColorSchemeSelector!="function")return{};let o=f.getColorSchemeSelector(u);return o==="&"?c:((o.includes("data-")||o.includes("."))&&(o=`*:where(${o.replace(/\s*&$/,"")}) &`),{[o]:c})}return f.palette.mode===u?c:{}}function kh(u={},...c){const{breakpoints:f={},palette:r={},spacing:o,shape:h={},...v}=u,S=Mv(f),A=Vh(o);let g=De({breakpoints:S,direction:"ltr",components:{},palette:{mode:"light",...r},spacing:A,shape:{...Dv,...h}},v);return g=Lg(g),g.applyStyles=Nv,g=c.reduce((z,R)=>De(z,R),g),g.unstable_sxConfig={...ec,...v==null?void 0:v.unstable_sxConfig},g.unstable_sx=function(R){return fn({sx:R,theme:this})},g}const Uv={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Zh(u,c,f="Mui"){const r=Uv[c];return r?`${f}-${r}`:`${Bg.generate(u)}-${c}`}function jv(u,c,f="Mui"){const r={};return c.forEach(o=>{r[o]=Zh(u,o,f)}),r}function Kh(u){const{variants:c,...f}=u,r={variants:c,style:yh(f),isProcessed:!0};return r.style===f||c&&c.forEach(o=>{typeof o.style!="function"&&(o.style=yh(o.style))}),r}const Bv=kh();function zf(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}function Hv(u){return u?(c,f)=>f[u]:null}function wv(u,c,f){u.theme=Gv(u.theme)?f:u.theme[c]||u.theme}function ki(u,c){const f=typeof c=="function"?c(u):c;if(Array.isArray(f))return f.flatMap(r=>ki(u,r));if(Array.isArray(f==null?void 0:f.variants)){let r;if(f.isProcessed)r=f.style;else{const{variants:o,...h}=f;r=h}return $h(u,f.variants,[r])}return f!=null&&f.isProcessed?f.style:f}function $h(u,c,f=[]){var o;let r;t:for(let h=0;h<c.length;h+=1){const v=c[h];if(typeof v.props=="function"){if(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),!v.props(r))continue}else for(const S in v.props)if(u[S]!==v.props[S]&&((o=u.ownerState)==null?void 0:o[S])!==v.props[S])continue t;typeof v.style=="function"?(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),f.push(v.style(r))):f.push(v.style)}return f}function qv(u={}){const{themeId:c,defaultTheme:f=Bv,rootShouldForwardProp:r=zf,slotShouldForwardProp:o=zf}=u;function h(S){wv(S,c,f)}return(S,A={})=>{zv(S,Q=>Q.filter(F=>F!==fn));const{name:g,slot:z,skipVariantsResolver:R,skipSx:U,overridesResolver:Y=Hv(Xv(z)),...w}=A,O=R!==void 0?R:z&&z!=="Root"&&z!=="root"||!1,L=U||!1;let $=zf;z==="Root"||z==="root"?$=r:z?$=o:Qv(S)&&($=void 0);const ut=_v(S,{shouldForwardProp:$,label:Yv(),...w}),k=Q=>{if(Q.__emotion_real===Q)return Q;if(typeof Q=="function")return function(W){return ki(W,Q)};if(bl(Q)){const F=Kh(Q);return F.variants?function(ot){return ki(ot,F)}:F.style}return Q},K=(...Q)=>{const F=[],W=Q.map(k),ot=[];if(F.push(h),g&&Y&&ot.push(function(I){var M,G;const Rt=(G=(M=I.theme.components)==null?void 0:M[g])==null?void 0:G.styleOverrides;if(!Rt)return null;const Ot={};for(const tt in Rt)Ot[tt]=ki(I,Rt[tt]);return Y(I,Ot)}),g&&!O&&ot.push(function(I){var Ot,M;const ft=I.theme,Rt=(M=(Ot=ft==null?void 0:ft.components)==null?void 0:Ot[g])==null?void 0:M.variants;return Rt?$h(I,Rt):null}),L||ot.push(fn),Array.isArray(W[0])){const X=W.shift(),I=new Array(F.length).fill(""),ft=new Array(ot.length).fill("");let Rt;Rt=[...I,...X,...ft],Rt.raw=[...I,...X.raw,...ft],F.unshift(Rt)}const xt=[...F,...W,...ot],m=ut(...xt);return S.muiName&&(m.muiName=S.muiName),m};return ut.withConfig&&(K.withConfig=ut.withConfig),K}}function Yv(u,c){return void 0}function Gv(u){for(const c in u)return!1;return!0}function Qv(u){return typeof u=="string"&&u.charCodeAt(0)>96}function Xv(u){return u&&u.charAt(0).toLowerCase()+u.slice(1)}function Bf(u,c){const f={...c};for(const r in u)if(Object.prototype.hasOwnProperty.call(u,r)){const o=r;if(o==="components"||o==="slots")f[o]={...u[o],...f[o]};else if(o==="componentsProps"||o==="slotProps"){const h=u[o],v=c[o];if(!v)f[o]=h||{};else if(!h)f[o]=v;else{f[o]={...v};for(const S in h)if(Object.prototype.hasOwnProperty.call(h,S)){const A=S;f[o][A]=Bf(h[A],v[A])}}}else f[o]===void 0&&(f[o]=u[o])}return f}function Lv(u,c=Number.MIN_SAFE_INTEGER,f=Number.MAX_SAFE_INTEGER){return Math.max(c,Math.min(u,f))}function Kf(u,c=0,f=1){return Lv(u,c,f)}function Vv(u){u=u.slice(1);const c=new RegExp(`.{1,${u.length>=6?2:1}}`,"g");let f=u.match(c);return f&&f[0].length===1&&(f=f.map(r=>r+r)),f?`rgb${f.length===4?"a":""}(${f.map((r,o)=>o<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function kl(u){if(u.type)return u;if(u.charAt(0)==="#")return kl(Vv(u));const c=u.indexOf("("),f=u.substring(0,c);if(!["rgb","rgba","hsl","hsla","color"].includes(f))throw new Error(ma(9,u));let r=u.substring(c+1,u.length-1),o;if(f==="color"){if(r=r.split(" "),o=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(o))throw new Error(ma(10,o))}else r=r.split(",");return r=r.map(h=>parseFloat(h)),{type:f,values:r,colorSpace:o}}const kv=u=>{const c=kl(u);return c.values.slice(0,3).map((f,r)=>c.type.includes("hsl")&&r!==0?`${f}%`:f).join(" ")},mu=(u,c)=>{try{return kv(u)}catch{return u}};function uc(u){const{type:c,colorSpace:f}=u;let{values:r}=u;return c.includes("rgb")?r=r.map((o,h)=>h<3?parseInt(o,10):o):c.includes("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),c.includes("color")?r=`${f} ${r.join(" ")}`:r=`${r.join(", ")}`,`${c}(${r})`}function Jh(u){u=kl(u);const{values:c}=u,f=c[0],r=c[1]/100,o=c[2]/100,h=r*Math.min(o,1-o),v=(g,z=(g+f/30)%12)=>o-h*Math.max(Math.min(z-3,9-z,1),-1);let S="rgb";const A=[Math.round(v(0)*255),Math.round(v(8)*255),Math.round(v(4)*255)];return u.type==="hsla"&&(S+="a",A.push(c[3])),uc({type:S,values:A})}function Hf(u){u=kl(u);let c=u.type==="hsl"||u.type==="hsla"?kl(Jh(u)).values:u.values;return c=c.map(f=>(u.type!=="color"&&(f/=255),f<=.03928?f/12.92:((f+.055)/1.055)**2.4)),Number((.2126*c[0]+.7152*c[1]+.0722*c[2]).toFixed(3))}function Zv(u,c){const f=Hf(u),r=Hf(c);return(Math.max(f,r)+.05)/(Math.min(f,r)+.05)}function Kv(u,c){return u=kl(u),c=Kf(c),(u.type==="rgb"||u.type==="hsl")&&(u.type+="a"),u.type==="color"?u.values[3]=`/${c}`:u.values[3]=c,uc(u)}function Yi(u,c,f){try{return Kv(u,c)}catch{return u}}function $f(u,c){if(u=kl(u),c=Kf(c),u.type.includes("hsl"))u.values[2]*=1-c;else if(u.type.includes("rgb")||u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]*=1-c;return uc(u)}function Bt(u,c,f){try{return $f(u,c)}catch{return u}}function Jf(u,c){if(u=kl(u),c=Kf(c),u.type.includes("hsl"))u.values[2]+=(100-u.values[2])*c;else if(u.type.includes("rgb"))for(let f=0;f<3;f+=1)u.values[f]+=(255-u.values[f])*c;else if(u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]+=(1-u.values[f])*c;return uc(u)}function Ht(u,c,f){try{return Jf(u,c)}catch{return u}}function $v(u,c=.15){return Hf(u)>.5?$f(u,c):Jf(u,c)}function Gi(u,c,f){try{return $v(u,c)}catch{return u}}const Jv=lt.createContext(void 0);function Wv(u){const{theme:c,name:f,props:r}=u;if(!c||!c.components||!c.components[f])return r;const o=c.components[f];return o.defaultProps?Bf(o.defaultProps,r):!o.styleOverrides&&!o.variants?Bf(o,r):r}function Fv({props:u,name:c}){const f=lt.useContext(Jv);return Wv({props:u,name:c,theme:{components:f}})}const gh={theme:void 0};function Pv(u){let c,f;return function(o){let h=c;return(h===void 0||o.theme!==f)&&(gh.theme=o.theme,h=Kh(u(gh)),c=h,f=o.theme),h}}function Iv(u=""){function c(...r){if(!r.length)return"";const o=r[0];return typeof o=="string"&&!o.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${u?`${u}-`:""}${o}${c(...r.slice(1))})`:`, ${o}`}return(r,...o)=>`var(--${u?`${u}-`:""}${r}${c(...o)})`}const vh=(u,c,f,r=[])=>{let o=u;c.forEach((h,v)=>{v===c.length-1?Array.isArray(o)?o[Number(h)]=f:o&&typeof o=="object"&&(o[h]=f):o&&typeof o=="object"&&(o[h]||(o[h]=r.includes(h)?[]:{}),o=o[h])})},tp=(u,c,f)=>{function r(o,h=[],v=[]){Object.entries(o).forEach(([S,A])=>{(!f||f&&!f([...h,S]))&&A!=null&&(typeof A=="object"&&Object.keys(A).length>0?r(A,[...h,S],Array.isArray(A)?[...v,S]:v):c([...h,S],A,v))})}r(u)},ep=(u,c)=>typeof c=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(r=>u.includes(r))||u[u.length-1].toLowerCase().includes("opacity")?c:`${c}px`:c;function Rf(u,c){const{prefix:f,shouldSkipGeneratingVar:r}=c||{},o={},h={},v={};return tp(u,(S,A,g)=>{if((typeof A=="string"||typeof A=="number")&&(!r||!r(S,A))){const z=`--${f?`${f}-`:""}${S.join("-")}`,R=ep(S,A);Object.assign(o,{[z]:R}),vh(h,S,`var(${z})`,g),vh(v,S,`var(${z}, ${R})`,g)}},S=>S[0]==="vars"),{css:o,vars:h,varsWithDefaults:v}}function lp(u,c={}){const{getSelector:f=L,disableCssColorScheme:r,colorSchemeSelector:o}=c,{colorSchemes:h={},components:v,defaultColorScheme:S="light",...A}=u,{vars:g,css:z,varsWithDefaults:R}=Rf(A,c);let U=R;const Y={},{[S]:w,...O}=h;if(Object.entries(O||{}).forEach(([k,K])=>{const{vars:Q,css:F,varsWithDefaults:W}=Rf(K,c);U=De(U,W),Y[k]={css:F,vars:Q}}),w){const{css:k,vars:K,varsWithDefaults:Q}=Rf(w,c);U=De(U,Q),Y[S]={css:k,vars:K}}function L(k,K){var F,W;let Q=o;if(o==="class"&&(Q=".%s"),o==="data"&&(Q="[data-%s]"),o!=null&&o.startsWith("data-")&&!o.includes("%s")&&(Q=`[${o}="%s"]`),k){if(Q==="media")return u.defaultColorScheme===k?":root":{[`@media (prefers-color-scheme: ${((W=(F=h[k])==null?void 0:F.palette)==null?void 0:W.mode)||k})`]:{":root":K}};if(Q)return u.defaultColorScheme===k?`:root, ${Q.replace("%s",String(k))}`:Q.replace("%s",String(k))}return":root"}return{vars:U,generateThemeVars:()=>{let k={...g};return Object.entries(Y).forEach(([,{vars:K}])=>{k=De(k,K)}),k},generateStyleSheets:()=>{var ot,xt;const k=[],K=u.defaultColorScheme||"light";function Q(m,X){Object.keys(X).length&&k.push(typeof m=="string"?{[m]:{...X}}:m)}Q(f(void 0,{...z}),z);const{[K]:F,...W}=Y;if(F){const{css:m}=F,X=(xt=(ot=h[K])==null?void 0:ot.palette)==null?void 0:xt.mode,I=!r&&X?{colorScheme:X,...m}:{...m};Q(f(K,{...I}),I)}return Object.entries(W).forEach(([m,{css:X}])=>{var Rt,Ot;const I=(Ot=(Rt=h[m])==null?void 0:Rt.palette)==null?void 0:Ot.mode,ft=!r&&I?{colorScheme:I,...X}:{...X};Q(f(m,{...ft}),ft)}),k}}}function ap(u){return function(f){return u==="media"?`@media (prefers-color-scheme: ${f})`:u?u.startsWith("data-")&&!u.includes("%s")?`[${u}="${f}"] &`:u==="class"?`.${f} &`:u==="data"?`[data-${f}] &`:`${u.replace("%s",f)} &`:"&"}}const Su={black:"#000",white:"#fff"},np={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},tn={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},en={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},hu={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},ln={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},an={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},nn={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function Wh(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Su.white,default:Su.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const up=Wh();function Fh(){return{text:{primary:Su.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Su.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const ph=Fh();function bh(u,c,f,r){const o=r.light||r,h=r.dark||r*1.5;u[c]||(u.hasOwnProperty(f)?u[c]=u[f]:c==="light"?u.light=Jf(u.main,o):c==="dark"&&(u.dark=$f(u.main,h)))}function ip(u="light"){return u==="dark"?{main:ln[200],light:ln[50],dark:ln[400]}:{main:ln[700],light:ln[400],dark:ln[800]}}function cp(u="light"){return u==="dark"?{main:tn[200],light:tn[50],dark:tn[400]}:{main:tn[500],light:tn[300],dark:tn[700]}}function rp(u="light"){return u==="dark"?{main:en[500],light:en[300],dark:en[700]}:{main:en[700],light:en[400],dark:en[800]}}function fp(u="light"){return u==="dark"?{main:an[400],light:an[300],dark:an[700]}:{main:an[700],light:an[500],dark:an[900]}}function sp(u="light"){return u==="dark"?{main:nn[400],light:nn[300],dark:nn[700]}:{main:nn[800],light:nn[500],dark:nn[900]}}function op(u="light"){return u==="dark"?{main:hu[400],light:hu[300],dark:hu[700]}:{main:"#ed6c02",light:hu[500],dark:hu[900]}}function Wf(u){const{mode:c="light",contrastThreshold:f=3,tonalOffset:r=.2,...o}=u,h=u.primary||ip(c),v=u.secondary||cp(c),S=u.error||rp(c),A=u.info||fp(c),g=u.success||sp(c),z=u.warning||op(c);function R(O){return Zv(O,ph.text.primary)>=f?ph.text.primary:up.text.primary}const U=({color:O,name:L,mainShade:$=500,lightShade:ut=300,darkShade:k=700})=>{if(O={...O},!O.main&&O[$]&&(O.main=O[$]),!O.hasOwnProperty("main"))throw new Error(ma(11,L?` (${L})`:"",$));if(typeof O.main!="string")throw new Error(ma(12,L?` (${L})`:"",JSON.stringify(O.main)));return bh(O,"light",ut,r),bh(O,"dark",k,r),O.contrastText||(O.contrastText=R(O.main)),O};let Y;return c==="light"?Y=Wh():c==="dark"&&(Y=Fh()),De({common:{...Su},mode:c,primary:U({color:h,name:"primary"}),secondary:U({color:v,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:U({color:S,name:"error"}),warning:U({color:z,name:"warning"}),info:U({color:A,name:"info"}),success:U({color:g,name:"success"}),grey:np,contrastThreshold:f,getContrastText:R,augmentColor:U,tonalOffset:r,...Y},o)}function dp(u){const c={};return Object.entries(u).forEach(r=>{const[o,h]=r;typeof h=="object"&&(c[o]=`${h.fontStyle?`${h.fontStyle} `:""}${h.fontVariant?`${h.fontVariant} `:""}${h.fontWeight?`${h.fontWeight} `:""}${h.fontStretch?`${h.fontStretch} `:""}${h.fontSize||""}${h.lineHeight?`/${h.lineHeight} `:""}${h.fontFamily||""}`)}),c}function hp(u,c){return{toolbar:{minHeight:56,[u.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[u.up("sm")]:{minHeight:64}},...c}}function mp(u){return Math.round(u*1e5)/1e5}const Sh={textTransform:"uppercase"},Th='"Roboto", "Helvetica", "Arial", sans-serif';function yp(u,c){const{fontFamily:f=Th,fontSize:r=14,fontWeightLight:o=300,fontWeightRegular:h=400,fontWeightMedium:v=500,fontWeightBold:S=700,htmlFontSize:A=16,allVariants:g,pxToRem:z,...R}=typeof c=="function"?c(u):c,U=r/14,Y=z||(L=>`${L/A*U}rem`),w=(L,$,ut,k,K)=>({fontFamily:f,fontWeight:L,fontSize:Y($),lineHeight:ut,...f===Th?{letterSpacing:`${mp(k/$)}em`}:{},...K,...g}),O={h1:w(o,96,1.167,-1.5),h2:w(o,60,1.2,-.5),h3:w(h,48,1.167,0),h4:w(h,34,1.235,.25),h5:w(h,24,1.334,0),h6:w(v,20,1.6,.15),subtitle1:w(h,16,1.75,.15),subtitle2:w(v,14,1.57,.1),body1:w(h,16,1.5,.15),body2:w(h,14,1.43,.15),button:w(v,14,1.75,.4,Sh),caption:w(h,12,1.66,.4),overline:w(h,12,2.66,1,Sh),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return De({htmlFontSize:A,pxToRem:Y,fontFamily:f,fontSize:r,fontWeightLight:o,fontWeightRegular:h,fontWeightMedium:v,fontWeightBold:S,...O},R,{clone:!1})}const gp=.2,vp=.14,pp=.12;function Yt(...u){return[`${u[0]}px ${u[1]}px ${u[2]}px ${u[3]}px rgba(0,0,0,${gp})`,`${u[4]}px ${u[5]}px ${u[6]}px ${u[7]}px rgba(0,0,0,${vp})`,`${u[8]}px ${u[9]}px ${u[10]}px ${u[11]}px rgba(0,0,0,${pp})`].join(",")}const bp=["none",Yt(0,2,1,-1,0,1,1,0,0,1,3,0),Yt(0,3,1,-2,0,2,2,0,0,1,5,0),Yt(0,3,3,-2,0,3,4,0,0,1,8,0),Yt(0,2,4,-1,0,4,5,0,0,1,10,0),Yt(0,3,5,-1,0,5,8,0,0,1,14,0),Yt(0,3,5,-1,0,6,10,0,0,1,18,0),Yt(0,4,5,-2,0,7,10,1,0,2,16,1),Yt(0,5,5,-3,0,8,10,1,0,3,14,2),Yt(0,5,6,-3,0,9,12,1,0,3,16,2),Yt(0,6,6,-3,0,10,14,1,0,4,18,3),Yt(0,6,7,-4,0,11,15,1,0,4,20,3),Yt(0,7,8,-4,0,12,17,2,0,5,22,4),Yt(0,7,8,-4,0,13,19,2,0,5,24,4),Yt(0,7,9,-4,0,14,21,2,0,5,26,4),Yt(0,8,9,-5,0,15,22,2,0,6,28,5),Yt(0,8,10,-5,0,16,24,2,0,6,30,5),Yt(0,8,11,-5,0,17,26,2,0,6,32,5),Yt(0,9,11,-5,0,18,28,2,0,7,34,6),Yt(0,9,12,-6,0,19,29,2,0,7,36,6),Yt(0,10,13,-6,0,20,31,3,0,8,38,7),Yt(0,10,13,-6,0,21,33,3,0,8,40,7),Yt(0,10,14,-6,0,22,35,3,0,8,42,7),Yt(0,11,14,-7,0,23,36,3,0,9,44,8),Yt(0,11,15,-7,0,24,38,3,0,9,46,8)],Sp={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Tp={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Ah(u){return`${Math.round(u)}ms`}function Ap(u){if(!u)return 0;const c=u/36;return Math.min(Math.round((4+15*c**.25+c/5)*10),3e3)}function Ep(u){const c={...Sp,...u.easing},f={...Tp,...u.duration};return{getAutoHeightDuration:Ap,create:(o=["all"],h={})=>{const{duration:v=f.standard,easing:S=c.easeInOut,delay:A=0,...g}=h;return(Array.isArray(o)?o:[o]).map(z=>`${z} ${typeof v=="string"?v:Ah(v)} ${S} ${typeof A=="string"?A:Ah(A)}`).join(",")},...u,easing:c,duration:f}}const xp={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Op(u){return bl(u)||typeof u>"u"||typeof u=="string"||typeof u=="boolean"||typeof u=="number"||Array.isArray(u)}function Ph(u={}){const c={...u};function f(r){const o=Object.entries(r);for(let h=0;h<o.length;h++){const[v,S]=o[h];!Op(S)||v.startsWith("unstable_")?delete r[v]:bl(S)&&(r[v]={...S},f(r[v]))}}return f(c),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(c,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function wf(u={},...c){const{breakpoints:f,mixins:r={},spacing:o,palette:h={},transitions:v={},typography:S={},shape:A,...g}=u;if(u.vars&&u.generateThemeVars===void 0)throw new Error(ma(20));const z=Wf(h),R=kh(u);let U=De(R,{mixins:hp(R.breakpoints,r),palette:z,shadows:bp.slice(),typography:yp(z,S),transitions:Ep(v),zIndex:{...xp}});return U=De(U,g),U=c.reduce((Y,w)=>De(Y,w),U),U.unstable_sxConfig={...ec,...g==null?void 0:g.unstable_sxConfig},U.unstable_sx=function(w){return fn({sx:w,theme:this})},U.toRuntimeSource=Ph,U}function Cp(u){let c;return u<1?c=5.11916*u**2:c=4.5*Math.log(u+1)+2,Math.round(c*10)/1e3}const _p=[...Array(25)].map((u,c)=>{if(c===0)return"none";const f=Cp(c);return`linear-gradient(rgba(255 255 255 / ${f}), rgba(255 255 255 / ${f}))`});function Ih(u){return{inputPlaceholder:u==="dark"?.5:.42,inputUnderline:u==="dark"?.7:.42,switchTrackDisabled:u==="dark"?.2:.12,switchTrack:u==="dark"?.3:.38}}function tm(u){return u==="dark"?_p:[]}function zp(u){const{palette:c={mode:"light"},opacity:f,overlays:r,...o}=u,h=Wf(c);return{palette:h,opacity:{...Ih(h.mode),...f},overlays:r||tm(h.mode),...o}}function Rp(u){var c;return!!u[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!u[0].match(/sxConfig$/)||u[0]==="palette"&&!!((c=u[1])!=null&&c.match(/(mode|contrastThreshold|tonalOffset)/))}const Mp=u=>[...[...Array(25)].map((c,f)=>`--${u?`${u}-`:""}overlays-${f}`),`--${u?`${u}-`:""}palette-AppBar-darkBg`,`--${u?`${u}-`:""}palette-AppBar-darkColor`],Dp=u=>(c,f)=>{const r=u.rootSelector||":root",o=u.colorSchemeSelector;let h=o;if(o==="class"&&(h=".%s"),o==="data"&&(h="[data-%s]"),o!=null&&o.startsWith("data-")&&!o.includes("%s")&&(h=`[${o}="%s"]`),u.defaultColorScheme===c){if(c==="dark"){const v={};return Mp(u.cssVarPrefix).forEach(S=>{v[S]=f[S],delete f[S]}),h==="media"?{[r]:f,"@media (prefers-color-scheme: dark)":{[r]:v}}:h?{[h.replace("%s",c)]:v,[`${r}, ${h.replace("%s",c)}`]:f}:{[r]:{...f,...v}}}if(h&&h!=="media")return`${r}, ${h.replace("%s",String(c))}`}else if(c){if(h==="media")return{[`@media (prefers-color-scheme: ${String(c)})`]:{[r]:f}};if(h)return h.replace("%s",String(c))}return r};function Np(u,c){c.forEach(f=>{u[f]||(u[f]={})})}function D(u,c,f){!u[c]&&f&&(u[c]=f)}function yu(u){return typeof u!="string"||!u.startsWith("hsl")?u:Jh(u)}function pl(u,c){`${c}Channel`in u||(u[`${c}Channel`]=mu(yu(u[c])))}function Up(u){return typeof u=="number"?`${u}px`:typeof u=="string"||typeof u=="function"||Array.isArray(u)?u:"8px"}const Ie=u=>{try{return u()}catch{}},jp=(u="mui")=>Iv(u);function Mf(u,c,f,r){if(!c)return;c=c===!0?{}:c;const o=r==="dark"?"dark":"light";if(!f){u[r]=zp({...c,palette:{mode:o,...c==null?void 0:c.palette}});return}const{palette:h,...v}=wf({...f,palette:{mode:o,...c==null?void 0:c.palette}});return u[r]={...c,palette:h,opacity:{...Ih(o),...c==null?void 0:c.opacity},overlays:(c==null?void 0:c.overlays)||tm(o)},v}function Bp(u={},...c){const{colorSchemes:f={light:!0},defaultColorScheme:r,disableCssColorScheme:o=!1,cssVarPrefix:h="mui",shouldSkipGeneratingVar:v=Rp,colorSchemeSelector:S=f.light&&f.dark?"media":void 0,rootSelector:A=":root",...g}=u,z=Object.keys(f)[0],R=r||(f.light&&z!=="light"?"light":z),U=jp(h),{[R]:Y,light:w,dark:O,...L}=f,$={...L};let ut=Y;if((R==="dark"&&!("dark"in f)||R==="light"&&!("light"in f))&&(ut=!0),!ut)throw new Error(ma(21,R));const k=Mf($,ut,g,R);w&&!$.light&&Mf($,w,void 0,"light"),O&&!$.dark&&Mf($,O,void 0,"dark");let K={defaultColorScheme:R,...k,cssVarPrefix:h,colorSchemeSelector:S,rootSelector:A,getCssVar:U,colorSchemes:$,font:{...dp(k.typography),...k.font},spacing:Up(g.spacing)};Object.keys(K.colorSchemes).forEach(xt=>{const m=K.colorSchemes[xt].palette,X=I=>{const ft=I.split("-"),Rt=ft[1],Ot=ft[2];return U(I,m[Rt][Ot])};if(m.mode==="light"&&(D(m.common,"background","#fff"),D(m.common,"onBackground","#000")),m.mode==="dark"&&(D(m.common,"background","#000"),D(m.common,"onBackground","#fff")),Np(m,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),m.mode==="light"){D(m.Alert,"errorColor",Bt(m.error.light,.6)),D(m.Alert,"infoColor",Bt(m.info.light,.6)),D(m.Alert,"successColor",Bt(m.success.light,.6)),D(m.Alert,"warningColor",Bt(m.warning.light,.6)),D(m.Alert,"errorFilledBg",X("palette-error-main")),D(m.Alert,"infoFilledBg",X("palette-info-main")),D(m.Alert,"successFilledBg",X("palette-success-main")),D(m.Alert,"warningFilledBg",X("palette-warning-main")),D(m.Alert,"errorFilledColor",Ie(()=>m.getContrastText(m.error.main))),D(m.Alert,"infoFilledColor",Ie(()=>m.getContrastText(m.info.main))),D(m.Alert,"successFilledColor",Ie(()=>m.getContrastText(m.success.main))),D(m.Alert,"warningFilledColor",Ie(()=>m.getContrastText(m.warning.main))),D(m.Alert,"errorStandardBg",Ht(m.error.light,.9)),D(m.Alert,"infoStandardBg",Ht(m.info.light,.9)),D(m.Alert,"successStandardBg",Ht(m.success.light,.9)),D(m.Alert,"warningStandardBg",Ht(m.warning.light,.9)),D(m.Alert,"errorIconColor",X("palette-error-main")),D(m.Alert,"infoIconColor",X("palette-info-main")),D(m.Alert,"successIconColor",X("palette-success-main")),D(m.Alert,"warningIconColor",X("palette-warning-main")),D(m.AppBar,"defaultBg",X("palette-grey-100")),D(m.Avatar,"defaultBg",X("palette-grey-400")),D(m.Button,"inheritContainedBg",X("palette-grey-300")),D(m.Button,"inheritContainedHoverBg",X("palette-grey-A100")),D(m.Chip,"defaultBorder",X("palette-grey-400")),D(m.Chip,"defaultAvatarColor",X("palette-grey-700")),D(m.Chip,"defaultIconColor",X("palette-grey-700")),D(m.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),D(m.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),D(m.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),D(m.LinearProgress,"primaryBg",Ht(m.primary.main,.62)),D(m.LinearProgress,"secondaryBg",Ht(m.secondary.main,.62)),D(m.LinearProgress,"errorBg",Ht(m.error.main,.62)),D(m.LinearProgress,"infoBg",Ht(m.info.main,.62)),D(m.LinearProgress,"successBg",Ht(m.success.main,.62)),D(m.LinearProgress,"warningBg",Ht(m.warning.main,.62)),D(m.Skeleton,"bg",`rgba(${X("palette-text-primaryChannel")} / 0.11)`),D(m.Slider,"primaryTrack",Ht(m.primary.main,.62)),D(m.Slider,"secondaryTrack",Ht(m.secondary.main,.62)),D(m.Slider,"errorTrack",Ht(m.error.main,.62)),D(m.Slider,"infoTrack",Ht(m.info.main,.62)),D(m.Slider,"successTrack",Ht(m.success.main,.62)),D(m.Slider,"warningTrack",Ht(m.warning.main,.62));const I=Gi(m.background.default,.8);D(m.SnackbarContent,"bg",I),D(m.SnackbarContent,"color",Ie(()=>m.getContrastText(I))),D(m.SpeedDialAction,"fabHoverBg",Gi(m.background.paper,.15)),D(m.StepConnector,"border",X("palette-grey-400")),D(m.StepContent,"border",X("palette-grey-400")),D(m.Switch,"defaultColor",X("palette-common-white")),D(m.Switch,"defaultDisabledColor",X("palette-grey-100")),D(m.Switch,"primaryDisabledColor",Ht(m.primary.main,.62)),D(m.Switch,"secondaryDisabledColor",Ht(m.secondary.main,.62)),D(m.Switch,"errorDisabledColor",Ht(m.error.main,.62)),D(m.Switch,"infoDisabledColor",Ht(m.info.main,.62)),D(m.Switch,"successDisabledColor",Ht(m.success.main,.62)),D(m.Switch,"warningDisabledColor",Ht(m.warning.main,.62)),D(m.TableCell,"border",Ht(Yi(m.divider,1),.88)),D(m.Tooltip,"bg",Yi(m.grey[700],.92))}if(m.mode==="dark"){D(m.Alert,"errorColor",Ht(m.error.light,.6)),D(m.Alert,"infoColor",Ht(m.info.light,.6)),D(m.Alert,"successColor",Ht(m.success.light,.6)),D(m.Alert,"warningColor",Ht(m.warning.light,.6)),D(m.Alert,"errorFilledBg",X("palette-error-dark")),D(m.Alert,"infoFilledBg",X("palette-info-dark")),D(m.Alert,"successFilledBg",X("palette-success-dark")),D(m.Alert,"warningFilledBg",X("palette-warning-dark")),D(m.Alert,"errorFilledColor",Ie(()=>m.getContrastText(m.error.dark))),D(m.Alert,"infoFilledColor",Ie(()=>m.getContrastText(m.info.dark))),D(m.Alert,"successFilledColor",Ie(()=>m.getContrastText(m.success.dark))),D(m.Alert,"warningFilledColor",Ie(()=>m.getContrastText(m.warning.dark))),D(m.Alert,"errorStandardBg",Bt(m.error.light,.9)),D(m.Alert,"infoStandardBg",Bt(m.info.light,.9)),D(m.Alert,"successStandardBg",Bt(m.success.light,.9)),D(m.Alert,"warningStandardBg",Bt(m.warning.light,.9)),D(m.Alert,"errorIconColor",X("palette-error-main")),D(m.Alert,"infoIconColor",X("palette-info-main")),D(m.Alert,"successIconColor",X("palette-success-main")),D(m.Alert,"warningIconColor",X("palette-warning-main")),D(m.AppBar,"defaultBg",X("palette-grey-900")),D(m.AppBar,"darkBg",X("palette-background-paper")),D(m.AppBar,"darkColor",X("palette-text-primary")),D(m.Avatar,"defaultBg",X("palette-grey-600")),D(m.Button,"inheritContainedBg",X("palette-grey-800")),D(m.Button,"inheritContainedHoverBg",X("palette-grey-700")),D(m.Chip,"defaultBorder",X("palette-grey-700")),D(m.Chip,"defaultAvatarColor",X("palette-grey-300")),D(m.Chip,"defaultIconColor",X("palette-grey-300")),D(m.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),D(m.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),D(m.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),D(m.LinearProgress,"primaryBg",Bt(m.primary.main,.5)),D(m.LinearProgress,"secondaryBg",Bt(m.secondary.main,.5)),D(m.LinearProgress,"errorBg",Bt(m.error.main,.5)),D(m.LinearProgress,"infoBg",Bt(m.info.main,.5)),D(m.LinearProgress,"successBg",Bt(m.success.main,.5)),D(m.LinearProgress,"warningBg",Bt(m.warning.main,.5)),D(m.Skeleton,"bg",`rgba(${X("palette-text-primaryChannel")} / 0.13)`),D(m.Slider,"primaryTrack",Bt(m.primary.main,.5)),D(m.Slider,"secondaryTrack",Bt(m.secondary.main,.5)),D(m.Slider,"errorTrack",Bt(m.error.main,.5)),D(m.Slider,"infoTrack",Bt(m.info.main,.5)),D(m.Slider,"successTrack",Bt(m.success.main,.5)),D(m.Slider,"warningTrack",Bt(m.warning.main,.5));const I=Gi(m.background.default,.98);D(m.SnackbarContent,"bg",I),D(m.SnackbarContent,"color",Ie(()=>m.getContrastText(I))),D(m.SpeedDialAction,"fabHoverBg",Gi(m.background.paper,.15)),D(m.StepConnector,"border",X("palette-grey-600")),D(m.StepContent,"border",X("palette-grey-600")),D(m.Switch,"defaultColor",X("palette-grey-300")),D(m.Switch,"defaultDisabledColor",X("palette-grey-600")),D(m.Switch,"primaryDisabledColor",Bt(m.primary.main,.55)),D(m.Switch,"secondaryDisabledColor",Bt(m.secondary.main,.55)),D(m.Switch,"errorDisabledColor",Bt(m.error.main,.55)),D(m.Switch,"infoDisabledColor",Bt(m.info.main,.55)),D(m.Switch,"successDisabledColor",Bt(m.success.main,.55)),D(m.Switch,"warningDisabledColor",Bt(m.warning.main,.55)),D(m.TableCell,"border",Bt(Yi(m.divider,1),.68)),D(m.Tooltip,"bg",Yi(m.grey[700],.92))}pl(m.background,"default"),pl(m.background,"paper"),pl(m.common,"background"),pl(m.common,"onBackground"),pl(m,"divider"),Object.keys(m).forEach(I=>{const ft=m[I];I!=="tonalOffset"&&ft&&typeof ft=="object"&&(ft.main&&D(m[I],"mainChannel",mu(yu(ft.main))),ft.light&&D(m[I],"lightChannel",mu(yu(ft.light))),ft.dark&&D(m[I],"darkChannel",mu(yu(ft.dark))),ft.contrastText&&D(m[I],"contrastTextChannel",mu(yu(ft.contrastText))),I==="text"&&(pl(m[I],"primary"),pl(m[I],"secondary")),I==="action"&&(ft.active&&pl(m[I],"active"),ft.selected&&pl(m[I],"selected")))})}),K=c.reduce((xt,m)=>De(xt,m),K);const Q={prefix:h,disableCssColorScheme:o,shouldSkipGeneratingVar:v,getSelector:Dp(K)},{vars:F,generateThemeVars:W,generateStyleSheets:ot}=lp(K,Q);return K.vars=F,Object.entries(K.colorSchemes[K.defaultColorScheme]).forEach(([xt,m])=>{K[xt]=m}),K.generateThemeVars=W,K.generateStyleSheets=ot,K.generateSpacing=function(){return Vh(g.spacing,Xf(this))},K.getColorSchemeSelector=ap(S),K.spacing=K.generateSpacing(),K.shouldSkipGeneratingVar=v,K.unstable_sxConfig={...ec,...g==null?void 0:g.unstable_sxConfig},K.unstable_sx=function(m){return fn({sx:m,theme:this})},K.toRuntimeSource=Ph,K}function Eh(u,c,f){u.colorSchemes&&f&&(u.colorSchemes[c]={...f!==!0&&f,palette:Wf({...f===!0?{}:f.palette,mode:c})})}function Hp(u={},...c){const{palette:f,cssVariables:r=!1,colorSchemes:o=f?void 0:{light:!0},defaultColorScheme:h=f==null?void 0:f.mode,...v}=u,S=h||"light",A=o==null?void 0:o[S],g={...o,...f?{[S]:{...typeof A!="boolean"&&A,palette:f}}:void 0};if(r===!1){if(!("colorSchemes"in u))return wf(u,...c);let z=f;"palette"in u||g[S]&&(g[S]!==!0?z=g[S].palette:S==="dark"&&(z={mode:"dark"}));const R=wf({...u,palette:z},...c);return R.defaultColorScheme=S,R.colorSchemes=g,R.palette.mode==="light"&&(R.colorSchemes.light={...g.light!==!0&&g.light,palette:R.palette},Eh(R,"dark",g.dark)),R.palette.mode==="dark"&&(R.colorSchemes.dark={...g.dark!==!0&&g.dark,palette:R.palette},Eh(R,"light",g.light)),R}return!f&&!("light"in g)&&S==="light"&&(g.light=!0),Bp({...v,colorSchemes:g,defaultColorScheme:S,...typeof r!="boolean"&&r},...c)}const wp=Hp(),qp="$$material";function Yp(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}const Gp=u=>Yp(u)&&u!=="classes",Qp=qv({themeId:qp,defaultTheme:wp,rootShouldForwardProp:Gp}),Xp=Pv;function Lp(u){return Fv(u)}function Vp(u){return Zh("MuiSvgIcon",u)}jv("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const kp=u=>{const{color:c,fontSize:f,classes:r}=u,o={root:["root",c!=="inherit"&&`color${rn(c)}`,`fontSize${rn(f)}`]};return wg(o,Vp,r)},Zp=Qp("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(u,c)=>{const{ownerState:f}=u;return[c.root,f.color!=="inherit"&&c[`color${rn(f.color)}`],c[`fontSize${rn(f.fontSize)}`]]}})(Xp(({theme:u})=>{var c,f,r,o,h,v,S,A,g,z,R,U,Y,w;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(o=(c=u.transitions)==null?void 0:c.create)==null?void 0:o.call(c,"fill",{duration:(r=(f=(u.vars??u).transitions)==null?void 0:f.duration)==null?void 0:r.shorter}),variants:[{props:O=>!O.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((v=(h=u.typography)==null?void 0:h.pxToRem)==null?void 0:v.call(h,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((A=(S=u.typography)==null?void 0:S.pxToRem)==null?void 0:A.call(S,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((z=(g=u.typography)==null?void 0:g.pxToRem)==null?void 0:z.call(g,35))||"2.1875rem"}},...Object.entries((u.vars??u).palette).filter(([,O])=>O&&O.main).map(([O])=>{var L,$;return{props:{color:O},style:{color:($=(L=(u.vars??u).palette)==null?void 0:L[O])==null?void 0:$.main}}}),{props:{color:"action"},style:{color:(U=(R=(u.vars??u).palette)==null?void 0:R.action)==null?void 0:U.active}},{props:{color:"disabled"},style:{color:(w=(Y=(u.vars??u).palette)==null?void 0:Y.action)==null?void 0:w.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),qf=lt.forwardRef(function(c,f){const r=Lp({props:c,name:"MuiSvgIcon"}),{children:o,className:h,color:v="inherit",component:S="svg",fontSize:A="medium",htmlColor:g,inheritViewBox:z=!1,titleAccess:R,viewBox:U="0 0 24 24",...Y}=r,w=lt.isValidElement(o)&&o.type==="svg",O={...r,color:v,component:S,fontSize:A,instanceFontSize:c.fontSize,inheritViewBox:z,viewBox:U,hasSvgAsChild:w},L={};z||(L.viewBox=U);const $=kp(O);return q.jsxs(Zp,{as:S,className:Hg($.root,h),focusable:"false",color:g,"aria-hidden":R?void 0:!0,role:R?"img":void 0,ref:f,...L,...Y,...w&&o.props,ownerState:O,children:[w?o.props.children:o,R?q.jsx("title",{children:R}):null]})});qf.muiName="SvgIcon";function Zl(u,c){function f(r,o){return q.jsx(qf,{"data-testid":void 0,ref:o,...r,children:u})}return f.muiName=qf.muiName,lt.memo(lt.forwardRef(f))}const Kp=Zl(q.jsx("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6"})),$p=Zl(q.jsx("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6m0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4"})),ic=Zl(q.jsx("path",{d:"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7zm-6 .67 2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"})),cc=Zl(q.jsx("path",{d:"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92"})),Jp=({text:u,onSave:c})=>{const f=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this joke!",text:u})}catch(r){console.log("Error sharing:",r)}else navigator.clipboard.writeText(u),alert("Joke copied to clipboard!")};return q.jsxs("div",{className:"joke-card card",children:[q.jsx("p",{children:u}),q.jsxs("div",{className:"card-actions",children:[q.jsx("button",{className:"save-button",onClick:c,children:q.jsx(ic,{})}),q.jsx("button",{className:"share-button",onClick:f,children:q.jsx(cc,{})})]})]})},Wp=({text:u,onSave:c})=>{const f=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this fact!",text:u})}catch(r){console.log("Error sharing:",r)}else navigator.clipboard.writeText(u),alert("Fact copied to clipboard!")};return q.jsxs("div",{className:"fact-card card",children:[q.jsx("p",{children:u}),q.jsxs("div",{className:"card-actions",children:[q.jsx("button",{className:"save-button",onClick:c,children:q.jsx(ic,{})}),q.jsx("button",{className:"share-button",onClick:f,children:q.jsx(cc,{})})]})]})},Fp=({question:u,answer:c,onSave:f})=>{const[r,o]=lt.useState(!1);lt.useEffect(()=>{o(!1)},[u]);const h=()=>{o(!r)},v=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this riddle!",text:`Q: ${u}
A: ${c}`})}catch(S){console.log("Error sharing:",S)}else navigator.clipboard.writeText(`Q: ${u}
A: ${c}`),alert("Riddle copied to clipboard!")};return q.jsxs("div",{className:"riddle-card card",children:[q.jsx("div",{className:"riddle-question",children:q.jsx("h3",{children:u})}),q.jsxs("div",{className:"riddle-answer",children:[q.jsx("button",{className:"answer-reveal-button",onClick:h,children:r?"Hide Answer":"Reveal Answer"}),r&&q.jsx("div",{className:"answer-text",children:q.jsx("p",{children:c})})]}),q.jsxs("div",{className:"card-actions",children:[q.jsx("button",{className:"save-button",onClick:f,children:q.jsx(ic,{})}),q.jsx("button",{className:"share-button",onClick:v,children:q.jsx(cc,{})})]})]})},Pp=({question:u,correctAnswer:c,incorrectAnswers:f,onSave:r})=>{const[o,h]=lt.useState(!1),[v,S]=lt.useState(null),[A,g]=lt.useState([]);lt.useEffect(()=>{h(!1),S(null),g([...f,c].sort(()=>Math.random()-.5))},[u,c,f]);const z=Y=>{S(Y),h(!0)},R=Y=>o?Y===c?"correct":Y===v&&Y!==c?"incorrect":"faded":"",U=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this trivia question!",text:`Q: ${u}
A: ${c}`})}catch(Y){console.log("Error sharing:",Y)}else navigator.clipboard.writeText(`Q: ${u}
A: ${c}`),alert("Trivia copied to clipboard!")};return q.jsxs("div",{className:"trivia-card card",children:[q.jsx("div",{className:"trivia-question",children:q.jsx("h3",{dangerouslySetInnerHTML:{__html:u}})}),q.jsx("div",{className:"trivia-answers",children:A.map((Y,w)=>q.jsx("button",{onClick:()=>z(Y),className:`answer-button ${R(Y)}`,disabled:o,dangerouslySetInnerHTML:{__html:Y}},w))}),o&&q.jsx("div",{className:"trivia-result",children:v===c?q.jsx("p",{className:"correct-message",children:"Correct! 🎉"}):q.jsxs("p",{className:"incorrect-message",children:["Sorry! The correct answer is: ",q.jsx("span",{dangerouslySetInnerHTML:{__html:c}})]})}),q.jsxs("div",{className:"card-actions",children:[q.jsx("button",{className:"save-button",onClick:r,children:q.jsx(ic,{})}),q.jsx("button",{className:"share-button",onClick:U,children:q.jsx(cc,{})})]})]})},Ip=Zl(q.jsx("path",{d:"M18 2H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h9c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 14H9V4h9zM3 15v-2h2v2zm0-5.5h2v2H3zM10 20h2v2h-2zm-7-1.5v-2h2v2zM5 22c-1.1 0-2-.9-2-2h2zm3.5 0h-2v-2h2zm5 0v-2h2c0 1.1-.9 2-2 2M5 6v2H3c0-1.1.9-2 2-2"})),Qi=Zl(q.jsx("path",{d:"M5 13h14v-2H5zm-2 4h14v-2H3zM7 7v2h14V7z"})),t2=Zl(q.jsx("path",{d:"M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8"})),e2=({savedJokes:u,savedFacts:c,savedRiddles:f,savedTrivia:r,notepadTab:o,setNotepadTab:h,copyNotepad:v,clearJokes:S,clearFacts:A,clearRiddles:g,clearTrivia:z,removeItem:R,undoRemove:U})=>{const[Y,w]=lt.useState([]),O=()=>{switch(o){case"joke":return u;case"fact":return c;case"riddle":return f;case"trivia":return r;default:return[]}},L=k=>{const K=O()[k];w([...Y,{tab:o,item:K,index:k}]),R(o,k)},$=()=>{if(Y.length>0){const k=Y[Y.length-1];U(k.tab,k.item,k.index),w(Y.slice(0,-1))}},ut=O();return q.jsxs("div",{className:"notepad",children:[q.jsxs("div",{className:"notepad-tabs",children:[q.jsx("button",{className:o==="joke"?"active":"",onClick:()=>h("joke"),children:"Dad Jokes"}),q.jsx("button",{className:o==="fact"?"active":"",onClick:()=>h("fact"),children:"Random Facts"}),q.jsx("button",{className:o==="riddle"?"active":"",onClick:()=>h("riddle"),children:"Riddles"}),q.jsx("button",{className:o==="trivia"?"active":"",onClick:()=>h("trivia"),children:"Trivia"})]}),q.jsxs("div",{className:"notepad-content",children:[ut.length===0?q.jsx("p",{className:"notepad-empty",children:"Nothing saved yet!"}):q.jsx("ul",{children:ut.map((k,K)=>q.jsx("li",{className:"notepad-item",onClick:()=>L(K),children:k},K))}),q.jsxs("div",{className:"notepad-actions",style:{display:"flex",gap:"0.5rem",justifyContent:"flex-end",marginTop:"1rem"},children:[Y.length>0&&q.jsxs("button",{onClick:$,title:`Undo Remove (${Y.length})`,className:"undo-button",children:[q.jsx(t2,{})," ",Y.length>1&&q.jsx("span",{children:Y.length})]}),q.jsx("button",{onClick:v,title:"Copy All",children:q.jsx(Ip,{})}),o==="joke"?q.jsx("button",{onClick:S,title:"Clear All",className:"clear-button",children:q.jsx(Qi,{})}):o==="fact"?q.jsx("button",{onClick:A,title:"Clear All",className:"clear-button",children:q.jsx(Qi,{})}):o==="riddle"?q.jsx("button",{onClick:g,title:"Clear All",className:"clear-button",children:q.jsx(Qi,{})}):q.jsx("button",{onClick:z,title:"Clear All",className:"clear-button",children:q.jsx(Qi,{})})]})]})]})},l2=Zl(q.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}));var Re=Ch();function a2(){const{darkMode:u,toggleTheme:c}=lt.useContext(_h),[f,r]=lt.useState("joke"),[o,h]=lt.useState(""),[v,S]=lt.useState(!1),[A,g]=lt.useState(null),[z,R]=lt.useState(""),[U,Y]=lt.useState(""),[w,O]=lt.useState([]),[L,$]=lt.useState(""),[ut,k]=lt.useState(""),K=()=>{W([])},Q=()=>{xt([])},[F,W]=lt.useState(()=>{const it=localStorage.getItem("savedJokes");return it?JSON.parse(it):[]}),[ot,xt]=lt.useState(()=>{const it=localStorage.getItem("savedFacts");return it?JSON.parse(it):[]}),[m,X]=lt.useState(()=>{const it=localStorage.getItem("savedTrivia");return it?JSON.parse(it):[]}),I=()=>{X([])},[ft,Rt]=lt.useState(()=>{const it=localStorage.getItem("savedRiddles");return it?JSON.parse(it):[]}),Ot=()=>{Rt([])},[M,G]=lt.useState("joke"),[tt,Ct]=lt.useState(!1),p=lt.useRef(null);lt.useEffect(()=>{function it(ht){p.current&&!p.current.contains(ht.target)&&Ct(!1)}return document.addEventListener("mousedown",it),()=>{document.removeEventListener("mousedown",it)}},[]),lt.useEffect(()=>{Re.preconnect("https://icanhazdadjoke.com"),Re.preconnect("https://uselessfacts.jsph.pl"),Re.preconnect("https://the-trivia-api.com"),Re.prefetchDNS("https://api.chucknorris.io"),Re.prefetchDNS("https://opentdb.com")},[]);const H=async(it=0,ht=0)=>{S(!0),g(null);try{let pt="",It={};f==="joke"?(pt="https://icanhazdadjoke.com/",It={Accept:"application/json"}):f==="fact"?pt="https://uselessfacts.jsph.pl/random.json?language=en":f==="riddle"?pt="https://riddles-api.vercel.app/random":f==="trivia"&&(pt="https://the-trivia-api.com/api/questions?limit=1");const Kl=new AbortController,dn=setTimeout(()=>Kl.abort(),5e3),$l=await fetch(pt,{headers:It,signal:Kl.signal});if(clearTimeout(dn),!$l.ok)throw new Error(`API responded with status: ${$l.status}`);const he=await $l.json();if(f==="joke")h(he.joke);else if(f==="fact")h(he.text);else if(f==="riddle"){if(he.riddle.length>300)return console.log("Riddle too long, fetching another one..."),S(!1),H(it,ht);$(he.riddle),k(he.answer),h(`Q: ${he.riddle} A: ${he.answer}`)}else if(f==="trivia")if(he&&he.length>0){const Jl=he[0];R(Jl.question),Y(Jl.correctAnswer),O(Jl.incorrectAnswers),h(`Q: ${Jl.question} A: ${Jl.correctAnswer}`)}else throw new Error("Failed to fetch trivia")}catch(pt){if(console.error("Fetch error:",pt),it<2)return console.log(`Retrying fetch (attempt ${it+1})...`),S(!1),H(it+1);g(pt.message),f==="trivia"&&(R(""),Y(""),O([])),h("")}finally{S(!1)}};lt.useEffect(()=>{h(""),f==="trivia"?(R(""),Y(""),O([]),Re.preconnect("https://the-trivia-api.com")):f==="joke"?Re.preconnect("https://icanhazdadjoke.com"):f==="fact"?Re.preconnect("https://uselessfacts.jsph.pl"):f==="riddle"&&($(""),k(""),Re.preconnect("https://riddles-api.vercel.app")),H()},[f]);const Z=()=>{o&&(f==="joke"&&!F.includes(o)?W([...F,o]):f==="fact"&&!ot.includes(o)?xt([...ot,o]):f==="riddle"&&!ft.includes(o)?Rt([...ft,o]):f==="trivia"&&!m.includes(o)&&X([...m,o]))};lt.useEffect(()=>{localStorage.setItem("savedJokes",JSON.stringify(F))},[F]),lt.useEffect(()=>{localStorage.setItem("savedFacts",JSON.stringify(ot))},[ot]),lt.useEffect(()=>{localStorage.setItem("savedTrivia",JSON.stringify(m))},[m]),lt.useEffect(()=>{localStorage.setItem("savedRiddles",JSON.stringify(ft))},[ft]);const V=()=>{let it="";M==="joke"?it=F.join(`

`):M==="fact"?it=ot.join(`

`):M==="trivia"?it=m.join(`

`):M==="riddle"&&(it=ft.join(`

`)),navigator.clipboard.writeText(it)},et=(it,ht)=>{if(it==="joke"){const pt=[...F];pt.splice(ht,1),W(pt)}else if(it==="fact"){const pt=[...ot];pt.splice(ht,1),xt(pt)}else if(it==="riddle"){const pt=[...ft];pt.splice(ht,1),Rt(pt)}else if(it==="trivia"){const pt=[...m];pt.splice(ht,1),X(pt)}},vt=(it,ht,pt)=>{if(it==="joke"){const It=[...F];It.splice(pt,0,ht),W(It)}else if(it==="fact"){const It=[...ot];It.splice(pt,0,ht),xt(It)}else if(it==="riddle"){const It=[...ft];It.splice(pt,0,ht),Rt(It)}else if(it==="trivia"){const It=[...m];It.splice(pt,0,ht),X(It)}},rt=()=>{f==="joke"?Re.preconnect("https://icanhazdadjoke.com"):f==="fact"?Re.preconnect("https://uselessfacts.jsph.pl"):f==="riddle"?Re.preconnect("https://riddles-api.vercel.app"):f==="trivia"&&Re.preconnect("https://the-trivia-api.com"),H()};return q.jsxs("div",{className:`app ${u?"dark-mode":"light-mode"}`,children:[q.jsx("div",{className:"theme-toggle",children:q.jsx("button",{onClick:c,className:"theme-button",children:u?q.jsx($p,{}):q.jsx(Kp,{})})}),q.jsx("h1",{children:"The Grin Bin"}),q.jsxs("div",{className:"category-buttons",children:[q.jsx("button",{className:f==="joke"?"active":"",onClick:()=>{r("joke"),G("joke")},children:"Dad Jokes"}),q.jsx("button",{className:f==="fact"?"active":"",onClick:()=>{r("fact"),G("fact")},children:"Random Facts"}),q.jsx("button",{className:f==="riddle"?"active":"",onClick:()=>{r("riddle"),G("riddle")},children:"Riddles"}),q.jsx("button",{className:f==="trivia"?"active":"",onClick:()=>{r("trivia"),G("trivia")},children:"Trivia"})]}),q.jsx("div",{className:"content-container",style:{height:f==="trivia"?"400px":f==="riddle"?"300px":"250px"},children:q.jsx("div",{className:"card-wrapper",children:q.jsxs("div",{className:"card-transition",children:[f==="joke"&&o&&q.jsx(Jp,{text:o,onSave:Z}),f==="fact"&&o&&q.jsx(Wp,{text:o,onSave:Z}),f==="riddle"&&L&&q.jsx(Fp,{question:L,answer:ut,onSave:Z}),f==="trivia"&&z&&q.jsx(Pp,{question:z,correctAnswer:U,incorrectAnswers:w,onSave:Z}),!o&&q.jsx("div",{className:"empty-card"})]})})}),q.jsxs("button",{className:"refresh-button",onClick:rt,disabled:v,children:[q.jsx(l2,{style:{marginRight:"0.5rem"}})," Get Another"]}),q.jsx(e2,{savedJokes:F,savedFacts:ot,savedTrivia:m,savedRiddles:ft,notepadTab:M,setNotepadTab:G,copyNotepad:V,clearJokes:K,clearFacts:Q,clearTrivia:I,clearRiddles:Ot,removeItem:et,undoRemove:vt})]})}Ng.createRoot(document.getElementById("root")).render(q.jsx(Oh.StrictMode,{children:q.jsx(Ug,{children:q.jsx(a2,{})})}));
