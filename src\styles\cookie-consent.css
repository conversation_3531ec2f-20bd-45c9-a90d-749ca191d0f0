.cookie-consent-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-top: 1px solid #e9ecef;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 1rem;
  animation: slideUp 0.3s ease-out;
}

.dark-mode .cookie-consent-banner {
  background: rgba(45, 45, 45, 0.98);
  border-top-color: #444;
  color: var(--text-dark);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.cookie-consent-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.cookie-consent-text {
  flex: 1;
}

.cookie-consent-text h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-light);
}

.dark-mode .cookie-consent-text h3 {
  color: var(--text-dark);
}

.cookie-consent-text p {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  line-height: 1.4;
  color: #666;
}

.dark-mode .cookie-consent-text p {
  color: #aaa;
}

.privacy-link {
  background: none;
  border: none;
  color: var(--text-light);
  text-decoration: underline;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  padding: 0;
}

.privacy-link:hover {
  color: #0d47a1;
}

.dark-mode .privacy-link {
  color: var(--text-dark);
}

.dark-mode .privacy-link:hover {
  color: #e3f2fd;
}

.cookie-consent-actions {
  display: flex;
  gap: 0.75rem;
  flex-shrink: 0;
}

.cookie-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-family: inherit;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.cookie-btn-decline {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #dee2e6;
}

.cookie-btn-decline:hover {
  background: #e9ecef;
  color: #495057;
}

.dark-mode .cookie-btn-decline {
  background: #3a3a3a;
  color: #aaa;
  border-color: #555;
}

.dark-mode .cookie-btn-decline:hover {
  background: #4a4a4a;
  color: #ccc;
}

.cookie-btn-customize {
  background: #e3f2fd;
  color: var(--text-light);
  border: 1px solid #bbdefb;
}

.cookie-btn-customize:hover {
  background: #bbdefb;
  color: #0d47a1;
}

.dark-mode .cookie-btn-customize {
  background: #0d47a1;
  color: var(--text-dark);
  border-color: #1565c0;
}

.dark-mode .cookie-btn-customize:hover {
  background: #1565c0;
}

.cookie-btn-accept {
  background: var(--text-light);
  color: white;
  border: 1px solid var(--text-light);
}

.cookie-btn-accept:hover {
  background: #0d47a1;
  border-color: #0d47a1;
}

.dark-mode .cookie-btn-accept {
  background: var(--text-dark);
  color: var(--bg-dark);
  border-color: var(--text-dark);
}

.dark-mode .cookie-btn-accept:hover {
  background: #ffffff;
  border-color: #ffffff;
}

/* Responsive design */
@media (max-width: 768px) {
  .cookie-consent-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .cookie-consent-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .cookie-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .cookie-consent-banner {
    padding: 0.75rem;
  }
  
  .cookie-consent-actions {
    width: 100%;
  }
  
  .cookie-btn {
    flex: 1;
    min-width: 0;
    padding: 0.6rem 0.8rem;
    font-size: 0.8rem;
  }
}
