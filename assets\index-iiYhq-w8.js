function Ag(u,c){for(var f=0;f<c.length;f++){const r=c[f];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in u)){const h=Object.getOwnPropertyDescriptor(r,o);h&&Object.defineProperty(u,o,h.get?h:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const h of o)if(h.type==="childList")for(const p of h.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&r(p)}).observe(document,{childList:!0,subtree:!0});function f(o){const h={};return o.integrity&&(h.integrity=o.integrity),o.referrerPolicy&&(h.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?h.credentials="include":o.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function r(o){if(o.ep)return;o.ep=!0;const h=f(o);fetch(o.href,h)}})();function Eh(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var gf={exports:{}},nu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Q0;function Eg(){if(Q0)return nu;Q0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function f(r,o,h){var p=null;if(h!==void 0&&(p=""+h),o.key!==void 0&&(p=""+o.key),"key"in o){h={};for(var S in o)S!=="key"&&(h[S]=o[S])}else h=o;return o=h.ref,{$$typeof:u,type:r,key:p,ref:o!==void 0?o:null,props:h}}return nu.Fragment=c,nu.jsx=f,nu.jsxs=f,nu}var V0;function Og(){return V0||(V0=1,gf.exports=Eg()),gf.exports}var X=Og(),vf={exports:{}},rt={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Z0;function xg(){if(Z0)return rt;Z0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),p=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),R=Symbol.iterator;function U(g){return g===null||typeof g!="object"?null:(g=R&&g[R]||g["@@iterator"],typeof g=="function"?g:null)}var q={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},w=Object.assign,x={};function V(g,j,Y){this.props=g,this.context=j,this.refs=x,this.updater=Y||q}V.prototype.isReactComponent={},V.prototype.setState=function(g,j){if(typeof g!="object"&&typeof g!="function"&&g!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,g,j,"setState")},V.prototype.forceUpdate=function(g){this.updater.enqueueForceUpdate(this,g,"forceUpdate")};function Z(){}Z.prototype=V.prototype;function P(g,j,Y){this.props=g,this.context=j,this.refs=x,this.updater=Y||q}var $=P.prototype=new Z;$.constructor=P,w($,V.prototype),$.isPureReactComponent=!0;var K=Array.isArray,L={H:null,A:null,T:null,S:null,V:null},F=Object.prototype.hasOwnProperty;function W(g,j,Y,G,lt,mt){return Y=mt.ref,{$$typeof:u,type:g,key:j,ref:Y!==void 0?Y:null,props:mt}}function gt(g,j){return W(g.type,j,void 0,void 0,void 0,g.props)}function At(g){return typeof g=="object"&&g!==null&&g.$$typeof===u}function m(g){var j={"=":"=0",":":"=2"};return"$"+g.replace(/[=:]/g,function(Y){return j[Y]})}var k=/\/+/g;function tt(g,j){return typeof g=="object"&&g!==null&&g.key!=null?m(""+g.key):j.toString(36)}function ot(){}function Dt(g){switch(g.status){case"fulfilled":return g.value;case"rejected":throw g.reason;default:switch(typeof g.status=="string"?g.then(ot,ot):(g.status="pending",g.then(function(j){g.status==="pending"&&(g.status="fulfilled",g.value=j)},function(j){g.status==="pending"&&(g.status="rejected",g.reason=j)})),g.status){case"fulfilled":return g.value;case"rejected":throw g.reason}}throw g}function pt(g,j,Y,G,lt){var mt=typeof g;(mt==="undefined"||mt==="boolean")&&(g=null);var ut=!1;if(g===null)ut=!0;else switch(mt){case"bigint":case"string":case"number":ut=!0;break;case"object":switch(g.$$typeof){case u:case c:ut=!0;break;case z:return ut=g._init,pt(ut(g._payload),j,Y,G,lt)}}if(ut)return lt=lt(g),ut=G===""?"."+tt(g,0):G,K(lt)?(Y="",ut!=null&&(Y=ut.replace(k,"$&/")+"/"),pt(lt,j,Y,"",function(pl){return pl})):lt!=null&&(At(lt)&&(lt=gt(lt,Y+(lt.key==null||g&&g.key===lt.key?"":(""+lt.key).replace(k,"$&/")+"/")+ut)),j.push(lt)),1;ut=0;var Jt=G===""?".":G+":";if(K(g))for(var Et=0;Et<g.length;Et++)G=g[Et],mt=Jt+tt(G,Et),ut+=pt(G,j,Y,mt,lt);else if(Et=U(g),typeof Et=="function")for(g=Et.call(g),Et=0;!(G=g.next()).done;)G=G.value,mt=Jt+tt(G,Et++),ut+=pt(G,j,Y,mt,lt);else if(mt==="object"){if(typeof g.then=="function")return pt(Dt(g),j,Y,G,lt);throw j=String(g),Error("Objects are not valid as a React child (found: "+(j==="[object Object]"?"object with keys {"+Object.keys(g).join(", ")+"}":j)+"). If you meant to render a collection of children, use an array instead.")}return ut}function M(g,j,Y){if(g==null)return g;var G=[],lt=0;return pt(g,G,"","",function(mt){return j.call(Y,mt,lt++)}),G}function Q(g){if(g._status===-1){var j=g._result;j=j(),j.then(function(Y){(g._status===0||g._status===-1)&&(g._status=1,g._result=Y)},function(Y){(g._status===0||g._status===-1)&&(g._status=2,g._result=Y)}),g._status===-1&&(g._status=0,g._result=j)}if(g._status===1)return g._result.default;throw g._result}var et=typeof reportError=="function"?reportError:function(g){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var j=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof g=="object"&&g!==null&&typeof g.message=="string"?String(g.message):String(g),error:g});if(!window.dispatchEvent(j))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",g);return}console.error(g)};function Ot(){}return rt.Children={map:M,forEach:function(g,j,Y){M(g,function(){j.apply(this,arguments)},Y)},count:function(g){var j=0;return M(g,function(){j++}),j},toArray:function(g){return M(g,function(j){return j})||[]},only:function(g){if(!At(g))throw Error("React.Children.only expected to receive a single React element child.");return g}},rt.Component=V,rt.Fragment=f,rt.Profiler=o,rt.PureComponent=P,rt.StrictMode=r,rt.Suspense=A,rt.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=L,rt.__COMPILER_RUNTIME={__proto__:null,c:function(g){return L.H.useMemoCache(g)}},rt.cache=function(g){return function(){return g.apply(null,arguments)}},rt.cloneElement=function(g,j,Y){if(g==null)throw Error("The argument must be a React element, but you passed "+g+".");var G=w({},g.props),lt=g.key,mt=void 0;if(j!=null)for(ut in j.ref!==void 0&&(mt=void 0),j.key!==void 0&&(lt=""+j.key),j)!F.call(j,ut)||ut==="key"||ut==="__self"||ut==="__source"||ut==="ref"&&j.ref===void 0||(G[ut]=j[ut]);var ut=arguments.length-2;if(ut===1)G.children=Y;else if(1<ut){for(var Jt=Array(ut),Et=0;Et<ut;Et++)Jt[Et]=arguments[Et+2];G.children=Jt}return W(g.type,lt,void 0,void 0,mt,G)},rt.createContext=function(g){return g={$$typeof:p,_currentValue:g,_currentValue2:g,_threadCount:0,Provider:null,Consumer:null},g.Provider=g,g.Consumer={$$typeof:h,_context:g},g},rt.createElement=function(g,j,Y){var G,lt={},mt=null;if(j!=null)for(G in j.key!==void 0&&(mt=""+j.key),j)F.call(j,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(lt[G]=j[G]);var ut=arguments.length-2;if(ut===1)lt.children=Y;else if(1<ut){for(var Jt=Array(ut),Et=0;Et<ut;Et++)Jt[Et]=arguments[Et+2];lt.children=Jt}if(g&&g.defaultProps)for(G in ut=g.defaultProps,ut)lt[G]===void 0&&(lt[G]=ut[G]);return W(g,mt,void 0,void 0,null,lt)},rt.createRef=function(){return{current:null}},rt.forwardRef=function(g){return{$$typeof:S,render:g}},rt.isValidElement=At,rt.lazy=function(g){return{$$typeof:z,_payload:{_status:-1,_result:g},_init:Q}},rt.memo=function(g,j){return{$$typeof:v,type:g,compare:j===void 0?null:j}},rt.startTransition=function(g){var j=L.T,Y={};L.T=Y;try{var G=g(),lt=L.S;lt!==null&&lt(Y,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(Ot,et)}catch(mt){et(mt)}finally{L.T=j}},rt.unstable_useCacheRefresh=function(){return L.H.useCacheRefresh()},rt.use=function(g){return L.H.use(g)},rt.useActionState=function(g,j,Y){return L.H.useActionState(g,j,Y)},rt.useCallback=function(g,j){return L.H.useCallback(g,j)},rt.useContext=function(g){return L.H.useContext(g)},rt.useDebugValue=function(){},rt.useDeferredValue=function(g,j){return L.H.useDeferredValue(g,j)},rt.useEffect=function(g,j,Y){var G=L.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(g,j)},rt.useId=function(){return L.H.useId()},rt.useImperativeHandle=function(g,j,Y){return L.H.useImperativeHandle(g,j,Y)},rt.useInsertionEffect=function(g,j){return L.H.useInsertionEffect(g,j)},rt.useLayoutEffect=function(g,j){return L.H.useLayoutEffect(g,j)},rt.useMemo=function(g,j){return L.H.useMemo(g,j)},rt.useOptimistic=function(g,j){return L.H.useOptimistic(g,j)},rt.useReducer=function(g,j,Y){return L.H.useReducer(g,j,Y)},rt.useRef=function(g){return L.H.useRef(g)},rt.useState=function(g){return L.H.useState(g)},rt.useSyncExternalStore=function(g,j,Y){return L.H.useSyncExternalStore(g,j,Y)},rt.useTransition=function(){return L.H.useTransition()},rt.version="19.1.0",rt}var k0;function Hf(){return k0||(k0=1,vf.exports=xg()),vf.exports}var ct=Hf();const Oh=Eh(ct),K0=Ag({__proto__:null,default:Oh},[ct]);var pf={exports:{}},uu={},bf={exports:{}},Sf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $0;function Cg(){return $0||($0=1,function(u){function c(M,Q){var et=M.length;M.push(Q);t:for(;0<et;){var Ot=et-1>>>1,g=M[Ot];if(0<o(g,Q))M[Ot]=Q,M[et]=g,et=Ot;else break t}}function f(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var Q=M[0],et=M.pop();if(et!==Q){M[0]=et;t:for(var Ot=0,g=M.length,j=g>>>1;Ot<j;){var Y=2*(Ot+1)-1,G=M[Y],lt=Y+1,mt=M[lt];if(0>o(G,et))lt<g&&0>o(mt,G)?(M[Ot]=mt,M[lt]=et,Ot=lt):(M[Ot]=G,M[Y]=et,Ot=Y);else if(lt<g&&0>o(mt,et))M[Ot]=mt,M[lt]=et,Ot=lt;else break t}}return Q}function o(M,Q){var et=M.sortIndex-Q.sortIndex;return et!==0?et:M.id-Q.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;u.unstable_now=function(){return h.now()}}else{var p=Date,S=p.now();u.unstable_now=function(){return p.now()-S}}var A=[],v=[],z=1,R=null,U=3,q=!1,w=!1,x=!1,V=!1,Z=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;function K(M){for(var Q=f(v);Q!==null;){if(Q.callback===null)r(v);else if(Q.startTime<=M)r(v),Q.sortIndex=Q.expirationTime,c(A,Q);else break;Q=f(v)}}function L(M){if(x=!1,K(M),!w)if(f(A)!==null)w=!0,F||(F=!0,tt());else{var Q=f(v);Q!==null&&pt(L,Q.startTime-M)}}var F=!1,W=-1,gt=5,At=-1;function m(){return V?!0:!(u.unstable_now()-At<gt)}function k(){if(V=!1,F){var M=u.unstable_now();At=M;var Q=!0;try{t:{w=!1,x&&(x=!1,P(W),W=-1),q=!0;var et=U;try{e:{for(K(M),R=f(A);R!==null&&!(R.expirationTime>M&&m());){var Ot=R.callback;if(typeof Ot=="function"){R.callback=null,U=R.priorityLevel;var g=Ot(R.expirationTime<=M);if(M=u.unstable_now(),typeof g=="function"){R.callback=g,K(M),Q=!0;break e}R===f(A)&&r(A),K(M)}else r(A);R=f(A)}if(R!==null)Q=!0;else{var j=f(v);j!==null&&pt(L,j.startTime-M),Q=!1}}break t}finally{R=null,U=et,q=!1}Q=void 0}}finally{Q?tt():F=!1}}}var tt;if(typeof $=="function")tt=function(){$(k)};else if(typeof MessageChannel<"u"){var ot=new MessageChannel,Dt=ot.port2;ot.port1.onmessage=k,tt=function(){Dt.postMessage(null)}}else tt=function(){Z(k,0)};function pt(M,Q){W=Z(function(){M(u.unstable_now())},Q)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(M){M.callback=null},u.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):gt=0<M?Math.floor(1e3/M):5},u.unstable_getCurrentPriorityLevel=function(){return U},u.unstable_next=function(M){switch(U){case 1:case 2:case 3:var Q=3;break;default:Q=U}var et=U;U=Q;try{return M()}finally{U=et}},u.unstable_requestPaint=function(){V=!0},u.unstable_runWithPriority=function(M,Q){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var et=U;U=M;try{return Q()}finally{U=et}},u.unstable_scheduleCallback=function(M,Q,et){var Ot=u.unstable_now();switch(typeof et=="object"&&et!==null?(et=et.delay,et=typeof et=="number"&&0<et?Ot+et:Ot):et=Ot,M){case 1:var g=-1;break;case 2:g=250;break;case 5:g=1073741823;break;case 4:g=1e4;break;default:g=5e3}return g=et+g,M={id:z++,callback:Q,priorityLevel:M,startTime:et,expirationTime:g,sortIndex:-1},et>Ot?(M.sortIndex=et,c(v,M),f(A)===null&&M===f(v)&&(x?(P(W),W=-1):x=!0,pt(L,et-Ot))):(M.sortIndex=g,c(A,M),w||q||(w=!0,F||(F=!0,tt()))),M},u.unstable_shouldYield=m,u.unstable_wrapCallback=function(M){var Q=U;return function(){var et=U;U=Q;try{return M.apply(this,arguments)}finally{U=et}}}}(Sf)),Sf}var J0;function _g(){return J0||(J0=1,bf.exports=Cg()),bf.exports}var Tf={exports:{}},re={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W0;function zg(){if(W0)return re;W0=1;var u=Hf();function c(A){var v="https://react.dev/errors/"+A;if(1<arguments.length){v+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)v+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+A+"; visit "+v+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var r={d:{f,r:function(){throw Error(c(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},o=Symbol.for("react.portal");function h(A,v,z){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:R==null?null:""+R,children:A,containerInfo:v,implementation:z}}var p=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(A,v){if(A==="font")return"";if(typeof v=="string")return v==="use-credentials"?v:""}return re.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,re.createPortal=function(A,v){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!v||v.nodeType!==1&&v.nodeType!==9&&v.nodeType!==11)throw Error(c(299));return h(A,v,null,z)},re.flushSync=function(A){var v=p.T,z=r.p;try{if(p.T=null,r.p=2,A)return A()}finally{p.T=v,r.p=z,r.d.f()}},re.preconnect=function(A,v){typeof A=="string"&&(v?(v=v.crossOrigin,v=typeof v=="string"?v==="use-credentials"?v:"":void 0):v=null,r.d.C(A,v))},re.prefetchDNS=function(A){typeof A=="string"&&r.d.D(A)},re.preinit=function(A,v){if(typeof A=="string"&&v&&typeof v.as=="string"){var z=v.as,R=S(z,v.crossOrigin),U=typeof v.integrity=="string"?v.integrity:void 0,q=typeof v.fetchPriority=="string"?v.fetchPriority:void 0;z==="style"?r.d.S(A,typeof v.precedence=="string"?v.precedence:void 0,{crossOrigin:R,integrity:U,fetchPriority:q}):z==="script"&&r.d.X(A,{crossOrigin:R,integrity:U,fetchPriority:q,nonce:typeof v.nonce=="string"?v.nonce:void 0})}},re.preinitModule=function(A,v){if(typeof A=="string")if(typeof v=="object"&&v!==null){if(v.as==null||v.as==="script"){var z=S(v.as,v.crossOrigin);r.d.M(A,{crossOrigin:z,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0})}}else v==null&&r.d.M(A)},re.preload=function(A,v){if(typeof A=="string"&&typeof v=="object"&&v!==null&&typeof v.as=="string"){var z=v.as,R=S(z,v.crossOrigin);r.d.L(A,z,{crossOrigin:R,integrity:typeof v.integrity=="string"?v.integrity:void 0,nonce:typeof v.nonce=="string"?v.nonce:void 0,type:typeof v.type=="string"?v.type:void 0,fetchPriority:typeof v.fetchPriority=="string"?v.fetchPriority:void 0,referrerPolicy:typeof v.referrerPolicy=="string"?v.referrerPolicy:void 0,imageSrcSet:typeof v.imageSrcSet=="string"?v.imageSrcSet:void 0,imageSizes:typeof v.imageSizes=="string"?v.imageSizes:void 0,media:typeof v.media=="string"?v.media:void 0})}},re.preloadModule=function(A,v){if(typeof A=="string")if(v){var z=S(v.as,v.crossOrigin);r.d.m(A,{as:typeof v.as=="string"&&v.as!=="script"?v.as:void 0,crossOrigin:z,integrity:typeof v.integrity=="string"?v.integrity:void 0})}else r.d.m(A)},re.requestFormReset=function(A){r.d.r(A)},re.unstable_batchedUpdates=function(A,v){return A(v)},re.useFormState=function(A,v,z){return p.H.useFormState(A,v,z)},re.useFormStatus=function(){return p.H.useHostTransitionStatus()},re.version="19.1.0",re}var F0;function xh(){if(F0)return Tf.exports;F0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),Tf.exports=zg(),Tf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var P0;function Rg(){if(P0)return uu;P0=1;var u=_g(),c=Hf(),f=xh();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function p(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(h(t)!==t)throw Error(r(188))}function A(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(r(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return S(n),t;if(i===a)return S(n),e;i=i.sibling}throw Error(r(188))}if(l.return!==a.return)l=n,a=i;else{for(var s=!1,d=n.child;d;){if(d===l){s=!0,l=n,a=i;break}if(d===a){s=!0,a=n,l=i;break}d=d.sibling}if(!s){for(d=i.child;d;){if(d===l){s=!0,l=i,a=n;break}if(d===a){s=!0,a=i,l=n;break}d=d.sibling}if(!s)throw Error(r(189))}}if(l.alternate!==a)throw Error(r(190))}if(l.tag!==3)throw Error(r(188));return l.stateNode.current===l?t:e}function v(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=v(t),e!==null)return e;t=t.sibling}return null}var z=Object.assign,R=Symbol.for("react.element"),U=Symbol.for("react.transitional.element"),q=Symbol.for("react.portal"),w=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),V=Symbol.for("react.profiler"),Z=Symbol.for("react.provider"),P=Symbol.for("react.consumer"),$=Symbol.for("react.context"),K=Symbol.for("react.forward_ref"),L=Symbol.for("react.suspense"),F=Symbol.for("react.suspense_list"),W=Symbol.for("react.memo"),gt=Symbol.for("react.lazy"),At=Symbol.for("react.activity"),m=Symbol.for("react.memo_cache_sentinel"),k=Symbol.iterator;function tt(t){return t===null||typeof t!="object"?null:(t=k&&t[k]||t["@@iterator"],typeof t=="function"?t:null)}var ot=Symbol.for("react.client.reference");function Dt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ot?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case w:return"Fragment";case V:return"Profiler";case x:return"StrictMode";case L:return"Suspense";case F:return"SuspenseList";case At:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case q:return"Portal";case $:return(t.displayName||"Context")+".Provider";case P:return(t._context.displayName||"Context")+".Consumer";case K:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case W:return e=t.displayName||null,e!==null?e:Dt(t.type)||"Memo";case gt:e=t._payload,t=t._init;try{return Dt(t(e))}catch{}}return null}var pt=Array.isArray,M=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,et={pending:!1,data:null,method:null,action:null},Ot=[],g=-1;function j(t){return{current:t}}function Y(t){0>g||(t.current=Ot[g],Ot[g]=null,g--)}function G(t,e){g++,Ot[g]=t.current,t.current=e}var lt=j(null),mt=j(null),ut=j(null),Jt=j(null);function Et(t,e){switch(G(ut,e),G(mt,t),G(lt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?v0(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=v0(e),t=p0(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(lt),G(lt,t)}function pl(){Y(lt),Y(mt),Y(ut)}function tc(t){t.memoizedState!==null&&G(Jt,t);var e=lt.current,l=p0(e,t.type);e!==l&&(G(mt,t),G(lt,l))}function pu(t){mt.current===t&&(Y(lt),Y(mt)),Jt.current===t&&(Y(Jt),In._currentValue=et)}var ec=Object.prototype.hasOwnProperty,lc=u.unstable_scheduleCallback,ac=u.unstable_cancelCallback,tm=u.unstable_shouldYield,em=u.unstable_requestPaint,Ve=u.unstable_now,lm=u.unstable_getCurrentPriorityLevel,Wf=u.unstable_ImmediatePriority,Ff=u.unstable_UserBlockingPriority,bu=u.unstable_NormalPriority,am=u.unstable_LowPriority,Pf=u.unstable_IdlePriority,nm=u.log,um=u.unstable_setDisableYieldValue,cn=null,ve=null;function bl(t){if(typeof nm=="function"&&um(t),ve&&typeof ve.setStrictMode=="function")try{ve.setStrictMode(cn,t)}catch{}}var pe=Math.clz32?Math.clz32:rm,im=Math.log,cm=Math.LN2;function rm(t){return t>>>=0,t===0?32:31-(im(t)/cm|0)|0}var Su=256,Tu=4194304;function Zl(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Au(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,i=t.suspendedLanes,s=t.pingedLanes;t=t.warmLanes;var d=a&134217727;return d!==0?(a=d&~i,a!==0?n=Zl(a):(s&=d,s!==0?n=Zl(s):l||(l=d&~t,l!==0&&(n=Zl(l))))):(d=a&~i,d!==0?n=Zl(d):s!==0?n=Zl(s):l||(l=a&~t,l!==0&&(n=Zl(l)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:n}function rn(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function fm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function If(){var t=Su;return Su<<=1,(Su&4194048)===0&&(Su=256),t}function ts(){var t=Tu;return Tu<<=1,(Tu&62914560)===0&&(Tu=4194304),t}function nc(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function fn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function sm(t,e,l,a,n,i){var s=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var d=t.entanglements,y=t.expirationTimes,O=t.hiddenUpdates;for(l=s&~l;0<l;){var N=31-pe(l),H=1<<N;d[N]=0,y[N]=-1;var C=O[N];if(C!==null)for(O[N]=null,N=0;N<C.length;N++){var _=C[N];_!==null&&(_.lane&=-536870913)}l&=~H}a!==0&&es(t,a,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(s&~e))}function es(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-pe(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function ls(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-pe(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function uc(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ic(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function as(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:w0(t.type))}function om(t,e){var l=Q.p;try{return Q.p=t,e()}finally{Q.p=l}}var Sl=Math.random().toString(36).slice(2),ie="__reactFiber$"+Sl,oe="__reactProps$"+Sl,sa="__reactContainer$"+Sl,cc="__reactEvents$"+Sl,dm="__reactListeners$"+Sl,hm="__reactHandles$"+Sl,ns="__reactResources$"+Sl,sn="__reactMarker$"+Sl;function rc(t){delete t[ie],delete t[oe],delete t[cc],delete t[dm],delete t[hm]}function oa(t){var e=t[ie];if(e)return e;for(var l=t.parentNode;l;){if(e=l[sa]||l[ie]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=A0(t);t!==null;){if(l=t[ie])return l;t=A0(t)}return e}t=l,l=t.parentNode}return null}function da(t){if(t=t[ie]||t[sa]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function on(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function ha(t){var e=t[ns];return e||(e=t[ns]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Pt(t){t[sn]=!0}var us=new Set,is={};function kl(t,e){ma(t,e),ma(t+"Capture",e)}function ma(t,e){for(is[t]=e,t=0;t<e.length;t++)us.add(e[t])}var mm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),cs={},rs={};function ym(t){return ec.call(rs,t)?!0:ec.call(cs,t)?!1:mm.test(t)?rs[t]=!0:(cs[t]=!0,!1)}function Eu(t,e,l){if(ym(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Ou(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function tl(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var fc,fs;function ya(t){if(fc===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);fc=e&&e[1]||"",fs=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+fc+t+fs}var sc=!1;function oc(t,e){if(!t||sc)return"";sc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(_){var C=_}Reflect.construct(t,[],H)}else{try{H.call()}catch(_){C=_}t.call(H.prototype)}}else{try{throw Error()}catch(_){C=_}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(_){if(_&&C&&typeof _.stack=="string")return[_.stack,C.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),s=i[0],d=i[1];if(s&&d){var y=s.split(`
`),O=d.split(`
`);for(n=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;n<O.length&&!O[n].includes("DetermineComponentFrameRoot");)n++;if(a===y.length||n===O.length)for(a=y.length-1,n=O.length-1;1<=a&&0<=n&&y[a]!==O[n];)n--;for(;1<=a&&0<=n;a--,n--)if(y[a]!==O[n]){if(a!==1||n!==1)do if(a--,n--,0>n||y[a]!==O[n]){var N=`
`+y[a].replace(" at new "," at ");return t.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",t.displayName)),N}while(1<=a&&0<=n);break}}}finally{sc=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ya(l):""}function gm(t){switch(t.tag){case 26:case 27:case 5:return ya(t.type);case 16:return ya("Lazy");case 13:return ya("Suspense");case 19:return ya("SuspenseList");case 0:case 15:return oc(t.type,!1);case 11:return oc(t.type.render,!1);case 1:return oc(t.type,!0);case 31:return ya("Activity");default:return""}}function ss(t){try{var e="";do e+=gm(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Re(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function os(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function vm(t){var e=os(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(s){a=""+s,i.call(this,s)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(s){a=""+s},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function xu(t){t._valueTracker||(t._valueTracker=vm(t))}function ds(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=os(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Cu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var pm=/[\n"\\]/g;function Me(t){return t.replace(pm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function dc(t,e,l,a,n,i,s,d){t.name="",s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"?t.type=s:t.removeAttribute("type"),e!=null?s==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Re(e)):t.value!==""+Re(e)&&(t.value=""+Re(e)):s!=="submit"&&s!=="reset"||t.removeAttribute("value"),e!=null?hc(t,s,Re(e)):l!=null?hc(t,s,Re(l)):a!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+Re(d):t.removeAttribute("name")}function hs(t,e,l,a,n,i,s,d){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+Re(l):"",e=e!=null?""+Re(e):l,d||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=d?t.checked:!!a,t.defaultChecked=!!a,s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(t.name=s)}function hc(t,e,l){e==="number"&&Cu(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function ga(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+Re(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function ms(t,e,l){if(e!=null&&(e=""+Re(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+Re(l):""}function ys(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(r(92));if(pt(a)){if(1<a.length)throw Error(r(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=Re(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function va(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var bm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function gs(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||bm.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function vs(t,e,l){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&gs(t,n,a)}else for(var i in e)e.hasOwnProperty(i)&&gs(t,i,e[i])}function mc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Sm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Tm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _u(t){return Tm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var yc=null;function gc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var pa=null,ba=null;function ps(t){var e=da(t);if(e&&(t=e.stateNode)){var l=t[oe]||null;t:switch(t=e.stateNode,e.type){case"input":if(dc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Me(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[oe]||null;if(!n)throw Error(r(90));dc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&ds(a)}break t;case"textarea":ms(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&ga(t,!!l.multiple,e,!1)}}}var vc=!1;function bs(t,e,l){if(vc)return t(e,l);vc=!0;try{var a=t(e);return a}finally{if(vc=!1,(pa!==null||ba!==null)&&(di(),pa&&(e=pa,t=ba,ba=pa=null,ps(e),t)))for(e=0;e<t.length;e++)ps(t[e])}}function dn(t,e){var l=t.stateNode;if(l===null)return null;var a=l[oe]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(r(231,e,typeof l));return l}var el=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),pc=!1;if(el)try{var hn={};Object.defineProperty(hn,"passive",{get:function(){pc=!0}}),window.addEventListener("test",hn,hn),window.removeEventListener("test",hn,hn)}catch{pc=!1}var Tl=null,bc=null,zu=null;function Ss(){if(zu)return zu;var t,e=bc,l=e.length,a,n="value"in Tl?Tl.value:Tl.textContent,i=n.length;for(t=0;t<l&&e[t]===n[t];t++);var s=l-t;for(a=1;a<=s&&e[l-a]===n[i-a];a++);return zu=n.slice(t,1<a?1-a:void 0)}function Ru(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Mu(){return!0}function Ts(){return!1}function de(t){function e(l,a,n,i,s){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(l=t[d],this[d]=l?l(i):i[d]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Mu:Ts,this.isPropagationStopped=Ts,this}return z(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Mu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Mu)},persist:function(){},isPersistent:Mu}),e}var Kl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Du=de(Kl),mn=z({},Kl,{view:0,detail:0}),Am=de(mn),Sc,Tc,yn,Nu=z({},mn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ec,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==yn&&(yn&&t.type==="mousemove"?(Sc=t.screenX-yn.screenX,Tc=t.screenY-yn.screenY):Tc=Sc=0,yn=t),Sc)},movementY:function(t){return"movementY"in t?t.movementY:Tc}}),As=de(Nu),Em=z({},Nu,{dataTransfer:0}),Om=de(Em),xm=z({},mn,{relatedTarget:0}),Ac=de(xm),Cm=z({},Kl,{animationName:0,elapsedTime:0,pseudoElement:0}),_m=de(Cm),zm=z({},Kl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Rm=de(zm),Mm=z({},Kl,{data:0}),Es=de(Mm),Dm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Nm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Um={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Bm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Um[t])?!!e[t]:!1}function Ec(){return Bm}var jm=z({},mn,{key:function(t){if(t.key){var e=Dm[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ru(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Nm[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ec,charCode:function(t){return t.type==="keypress"?Ru(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ru(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Hm=de(jm),wm=z({},Nu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Os=de(wm),qm=z({},mn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ec}),Ym=de(qm),Gm=z({},Kl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Xm=de(Gm),Lm=z({},Nu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Qm=de(Lm),Vm=z({},Kl,{newState:0,oldState:0}),Zm=de(Vm),km=[9,13,27,32],Oc=el&&"CompositionEvent"in window,gn=null;el&&"documentMode"in document&&(gn=document.documentMode);var Km=el&&"TextEvent"in window&&!gn,xs=el&&(!Oc||gn&&8<gn&&11>=gn),Cs=" ",_s=!1;function zs(t,e){switch(t){case"keyup":return km.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Rs(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Sa=!1;function $m(t,e){switch(t){case"compositionend":return Rs(e);case"keypress":return e.which!==32?null:(_s=!0,Cs);case"textInput":return t=e.data,t===Cs&&_s?null:t;default:return null}}function Jm(t,e){if(Sa)return t==="compositionend"||!Oc&&zs(t,e)?(t=Ss(),zu=bc=Tl=null,Sa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return xs&&e.locale!=="ko"?null:e.data;default:return null}}var Wm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ms(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Wm[t.type]:e==="textarea"}function Ds(t,e,l,a){pa?ba?ba.push(a):ba=[a]:pa=a,e=pi(e,"onChange"),0<e.length&&(l=new Du("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var vn=null,pn=null;function Fm(t){d0(t,0)}function Uu(t){var e=on(t);if(ds(e))return t}function Ns(t,e){if(t==="change")return e}var Us=!1;if(el){var xc;if(el){var Cc="oninput"in document;if(!Cc){var Bs=document.createElement("div");Bs.setAttribute("oninput","return;"),Cc=typeof Bs.oninput=="function"}xc=Cc}else xc=!1;Us=xc&&(!document.documentMode||9<document.documentMode)}function js(){vn&&(vn.detachEvent("onpropertychange",Hs),pn=vn=null)}function Hs(t){if(t.propertyName==="value"&&Uu(pn)){var e=[];Ds(e,pn,t,gc(t)),bs(Fm,e)}}function Pm(t,e,l){t==="focusin"?(js(),vn=e,pn=l,vn.attachEvent("onpropertychange",Hs)):t==="focusout"&&js()}function Im(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Uu(pn)}function ty(t,e){if(t==="click")return Uu(e)}function ey(t,e){if(t==="input"||t==="change")return Uu(e)}function ly(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var be=typeof Object.is=="function"?Object.is:ly;function bn(t,e){if(be(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!ec.call(e,n)||!be(t[n],e[n]))return!1}return!0}function ws(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function qs(t,e){var l=ws(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=ws(l)}}function Ys(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ys(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Gs(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Cu(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Cu(t.document)}return e}function _c(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var ay=el&&"documentMode"in document&&11>=document.documentMode,Ta=null,zc=null,Sn=null,Rc=!1;function Xs(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Rc||Ta==null||Ta!==Cu(a)||(a=Ta,"selectionStart"in a&&_c(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Sn&&bn(Sn,a)||(Sn=a,a=pi(zc,"onSelect"),0<a.length&&(e=new Du("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=Ta)))}function $l(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Aa={animationend:$l("Animation","AnimationEnd"),animationiteration:$l("Animation","AnimationIteration"),animationstart:$l("Animation","AnimationStart"),transitionrun:$l("Transition","TransitionRun"),transitionstart:$l("Transition","TransitionStart"),transitioncancel:$l("Transition","TransitionCancel"),transitionend:$l("Transition","TransitionEnd")},Mc={},Ls={};el&&(Ls=document.createElement("div").style,"AnimationEvent"in window||(delete Aa.animationend.animation,delete Aa.animationiteration.animation,delete Aa.animationstart.animation),"TransitionEvent"in window||delete Aa.transitionend.transition);function Jl(t){if(Mc[t])return Mc[t];if(!Aa[t])return t;var e=Aa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in Ls)return Mc[t]=e[l];return t}var Qs=Jl("animationend"),Vs=Jl("animationiteration"),Zs=Jl("animationstart"),ny=Jl("transitionrun"),uy=Jl("transitionstart"),iy=Jl("transitioncancel"),ks=Jl("transitionend"),Ks=new Map,Dc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Dc.push("scrollEnd");function Ge(t,e){Ks.set(t,e),kl(e,[t])}var $s=new WeakMap;function De(t,e){if(typeof t=="object"&&t!==null){var l=$s.get(t);return l!==void 0?l:(e={value:t,source:e,stack:ss(e)},$s.set(t,e),e)}return{value:t,source:e,stack:ss(e)}}var Ne=[],Ea=0,Nc=0;function Bu(){for(var t=Ea,e=Nc=Ea=0;e<t;){var l=Ne[e];Ne[e++]=null;var a=Ne[e];Ne[e++]=null;var n=Ne[e];Ne[e++]=null;var i=Ne[e];if(Ne[e++]=null,a!==null&&n!==null){var s=a.pending;s===null?n.next=n:(n.next=s.next,s.next=n),a.pending=n}i!==0&&Js(l,n,i)}}function ju(t,e,l,a){Ne[Ea++]=t,Ne[Ea++]=e,Ne[Ea++]=l,Ne[Ea++]=a,Nc|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Uc(t,e,l,a){return ju(t,e,l,a),Hu(t)}function Oa(t,e){return ju(t,null,null,e),Hu(t)}function Js(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=t.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-pe(l),t=i.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),i):null}function Hu(t){if(50<Zn)throw Zn=0,Yr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var xa={};function cy(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Se(t,e,l,a){return new cy(t,e,l,a)}function Bc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function ll(t,e){var l=t.alternate;return l===null?(l=Se(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Ws(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function wu(t,e,l,a,n,i){var s=0;if(a=t,typeof t=="function")Bc(t)&&(s=1);else if(typeof t=="string")s=fg(t,l,lt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case At:return t=Se(31,l,e,n),t.elementType=At,t.lanes=i,t;case w:return Wl(l.children,n,i,e);case x:s=8,n|=24;break;case V:return t=Se(12,l,e,n|2),t.elementType=V,t.lanes=i,t;case L:return t=Se(13,l,e,n),t.elementType=L,t.lanes=i,t;case F:return t=Se(19,l,e,n),t.elementType=F,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Z:case $:s=10;break t;case P:s=9;break t;case K:s=11;break t;case W:s=14;break t;case gt:s=16,a=null;break t}s=29,l=Error(r(130,t===null?"null":typeof t,"")),a=null}return e=Se(s,l,e,n),e.elementType=t,e.type=a,e.lanes=i,e}function Wl(t,e,l,a){return t=Se(7,t,a,e),t.lanes=l,t}function jc(t,e,l){return t=Se(6,t,null,e),t.lanes=l,t}function Hc(t,e,l){return e=Se(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ca=[],_a=0,qu=null,Yu=0,Ue=[],Be=0,Fl=null,al=1,nl="";function Pl(t,e){Ca[_a++]=Yu,Ca[_a++]=qu,qu=t,Yu=e}function Fs(t,e,l){Ue[Be++]=al,Ue[Be++]=nl,Ue[Be++]=Fl,Fl=t;var a=al;t=nl;var n=32-pe(a)-1;a&=~(1<<n),l+=1;var i=32-pe(e)+n;if(30<i){var s=n-n%5;i=(a&(1<<s)-1).toString(32),a>>=s,n-=s,al=1<<32-pe(e)+n|l<<n|a,nl=i+t}else al=1<<i|l<<n|a,nl=t}function wc(t){t.return!==null&&(Pl(t,1),Fs(t,1,0))}function qc(t){for(;t===qu;)qu=Ca[--_a],Ca[_a]=null,Yu=Ca[--_a],Ca[_a]=null;for(;t===Fl;)Fl=Ue[--Be],Ue[Be]=null,nl=Ue[--Be],Ue[Be]=null,al=Ue[--Be],Ue[Be]=null}var se=null,Yt=null,bt=!1,Il=null,Ze=!1,Yc=Error(r(519));function ta(t){var e=Error(r(418,""));throw En(De(e,t)),Yc}function Ps(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[ie]=t,e[oe]=a,l){case"dialog":ht("cancel",e),ht("close",e);break;case"iframe":case"object":case"embed":ht("load",e);break;case"video":case"audio":for(l=0;l<Kn.length;l++)ht(Kn[l],e);break;case"source":ht("error",e);break;case"img":case"image":case"link":ht("error",e),ht("load",e);break;case"details":ht("toggle",e);break;case"input":ht("invalid",e),hs(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),xu(e);break;case"select":ht("invalid",e);break;case"textarea":ht("invalid",e),ys(e,a.value,a.defaultValue,a.children),xu(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||g0(e.textContent,l)?(a.popover!=null&&(ht("beforetoggle",e),ht("toggle",e)),a.onScroll!=null&&ht("scroll",e),a.onScrollEnd!=null&&ht("scrollend",e),a.onClick!=null&&(e.onclick=bi),e=!0):e=!1,e||ta(t)}function Is(t){for(se=t.return;se;)switch(se.tag){case 5:case 13:Ze=!1;return;case 27:case 3:Ze=!0;return;default:se=se.return}}function Tn(t){if(t!==se)return!1;if(!bt)return Is(t),bt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||ef(t.type,t.memoizedProps)),l=!l),l&&Yt&&ta(t),Is(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){Yt=Le(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}Yt=null}}else e===27?(e=Yt,wl(t.type)?(t=uf,uf=null,Yt=t):Yt=e):Yt=se?Le(t.stateNode.nextSibling):null;return!0}function An(){Yt=se=null,bt=!1}function to(){var t=Il;return t!==null&&(ye===null?ye=t:ye.push.apply(ye,t),Il=null),t}function En(t){Il===null?Il=[t]:Il.push(t)}var Gc=j(null),ea=null,ul=null;function Al(t,e,l){G(Gc,e._currentValue),e._currentValue=l}function il(t){t._currentValue=Gc.current,Y(Gc)}function Xc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function Lc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var s=n.child;i=i.firstContext;t:for(;i!==null;){var d=i;i=n;for(var y=0;y<e.length;y++)if(d.context===e[y]){i.lanes|=l,d=i.alternate,d!==null&&(d.lanes|=l),Xc(i.return,l,t),a||(s=null);break t}i=d.next}}else if(n.tag===18){if(s=n.return,s===null)throw Error(r(341));s.lanes|=l,i=s.alternate,i!==null&&(i.lanes|=l),Xc(s,l,t),s=null}else s=n.child;if(s!==null)s.return=n;else for(s=n;s!==null;){if(s===t){s=null;break}if(n=s.sibling,n!==null){n.return=s.return,s=n;break}s=s.return}n=s}}function On(t,e,l,a){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var s=n.alternate;if(s===null)throw Error(r(387));if(s=s.memoizedProps,s!==null){var d=n.type;be(n.pendingProps.value,s.value)||(t!==null?t.push(d):t=[d])}}else if(n===Jt.current){if(s=n.alternate,s===null)throw Error(r(387));s.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(In):t=[In])}n=n.return}t!==null&&Lc(e,t,l,a),e.flags|=262144}function Gu(t){for(t=t.firstContext;t!==null;){if(!be(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function la(t){ea=t,ul=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ce(t){return eo(ea,t)}function Xu(t,e){return ea===null&&la(t),eo(t,e)}function eo(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},ul===null){if(t===null)throw Error(r(308));ul=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else ul=ul.next=e;return l}var ry=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},fy=u.unstable_scheduleCallback,sy=u.unstable_NormalPriority,Wt={$$typeof:$,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Qc(){return{controller:new ry,data:new Map,refCount:0}}function xn(t){t.refCount--,t.refCount===0&&fy(sy,function(){t.controller.abort()})}var Cn=null,Vc=0,za=0,Ra=null;function oy(t,e){if(Cn===null){var l=Cn=[];Vc=0,za=kr(),Ra={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Vc++,e.then(lo,lo),e}function lo(){if(--Vc===0&&Cn!==null){Ra!==null&&(Ra.status="fulfilled");var t=Cn;Cn=null,za=0,Ra=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function dy(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var ao=M.S;M.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&oy(t,e),ao!==null&&ao(t,e)};var aa=j(null);function Zc(){var t=aa.current;return t!==null?t:jt.pooledCache}function Lu(t,e){e===null?G(aa,aa.current):G(aa,e.pool)}function no(){var t=Zc();return t===null?null:{parent:Wt._currentValue,pool:t}}var _n=Error(r(460)),uo=Error(r(474)),Qu=Error(r(542)),kc={then:function(){}};function io(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Vu(){}function co(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(Vu,Vu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,fo(t),t;default:if(typeof e.status=="string")e.then(Vu,Vu);else{if(t=jt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,fo(t),t}throw zn=e,_n}}var zn=null;function ro(){if(zn===null)throw Error(r(459));var t=zn;return zn=null,t}function fo(t){if(t===_n||t===Qu)throw Error(r(483))}var El=!1;function Kc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function $c(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ol(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function xl(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(xt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=Hu(t),Js(t,null,l),e}return ju(t,a,e,l),Hu(t)}function Rn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,ls(t,l)}}function Jc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var s={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=s:i=i.next=s,l=l.next}while(l!==null);i===null?n=i=e:i=i.next=e}else n=i=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var Wc=!1;function Mn(){if(Wc){var t=Ra;if(t!==null)throw t}}function Dn(t,e,l,a){Wc=!1;var n=t.updateQueue;El=!1;var i=n.firstBaseUpdate,s=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var y=d,O=y.next;y.next=null,s===null?i=O:s.next=O,s=y;var N=t.alternate;N!==null&&(N=N.updateQueue,d=N.lastBaseUpdate,d!==s&&(d===null?N.firstBaseUpdate=O:d.next=O,N.lastBaseUpdate=y))}if(i!==null){var H=n.baseState;s=0,N=O=y=null,d=i;do{var C=d.lane&-536870913,_=C!==d.lane;if(_?(yt&C)===C:(a&C)===C){C!==0&&C===za&&(Wc=!0),N!==null&&(N=N.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var it=t,at=d;C=e;var Rt=l;switch(at.tag){case 1:if(it=at.payload,typeof it=="function"){H=it.call(Rt,H,C);break t}H=it;break t;case 3:it.flags=it.flags&-65537|128;case 0:if(it=at.payload,C=typeof it=="function"?it.call(Rt,H,C):it,C==null)break t;H=z({},H,C);break t;case 2:El=!0}}C=d.callback,C!==null&&(t.flags|=64,_&&(t.flags|=8192),_=n.callbacks,_===null?n.callbacks=[C]:_.push(C))}else _={lane:C,tag:d.tag,payload:d.payload,callback:d.callback,next:null},N===null?(O=N=_,y=H):N=N.next=_,s|=C;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;_=d,d=_.next,_.next=null,n.lastBaseUpdate=_,n.shared.pending=null}}while(!0);N===null&&(y=H),n.baseState=y,n.firstBaseUpdate=O,n.lastBaseUpdate=N,i===null&&(n.shared.lanes=0),Ul|=s,t.lanes=s,t.memoizedState=H}}function so(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function oo(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)so(l[t],e)}var Ma=j(null),Zu=j(0);function ho(t,e){t=hl,G(Zu,t),G(Ma,e),hl=t|e.baseLanes}function Fc(){G(Zu,hl),G(Ma,Ma.current)}function Pc(){hl=Zu.current,Y(Ma),Y(Zu)}var Cl=0,ft=null,_t=null,kt=null,ku=!1,Da=!1,na=!1,Ku=0,Nn=0,Na=null,hy=0;function Qt(){throw Error(r(321))}function Ic(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!be(t[l],e[l]))return!1;return!0}function tr(t,e,l,a,n,i){return Cl=i,ft=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,M.H=t===null||t.memoizedState===null?Wo:Fo,na=!1,i=l(a,n),na=!1,Da&&(i=yo(e,l,a,n)),mo(t),i}function mo(t){M.H=Iu;var e=_t!==null&&_t.next!==null;if(Cl=0,kt=_t=ft=null,ku=!1,Nn=0,Na=null,e)throw Error(r(300));t===null||It||(t=t.dependencies,t!==null&&Gu(t)&&(It=!0))}function yo(t,e,l,a){ft=t;var n=0;do{if(Da&&(Na=null),Nn=0,Da=!1,25<=n)throw Error(r(301));if(n+=1,kt=_t=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}M.H=Sy,i=e(l,a)}while(Da);return i}function my(){var t=M.H,e=t.useState()[0];return e=typeof e.then=="function"?Un(e):e,t=t.useState()[0],(_t!==null?_t.memoizedState:null)!==t&&(ft.flags|=1024),e}function er(){var t=Ku!==0;return Ku=0,t}function lr(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function ar(t){if(ku){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ku=!1}Cl=0,kt=_t=ft=null,Da=!1,Nn=Ku=0,Na=null}function he(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return kt===null?ft.memoizedState=kt=t:kt=kt.next=t,kt}function Kt(){if(_t===null){var t=ft.alternate;t=t!==null?t.memoizedState:null}else t=_t.next;var e=kt===null?ft.memoizedState:kt.next;if(e!==null)kt=e,_t=t;else{if(t===null)throw ft.alternate===null?Error(r(467)):Error(r(310));_t=t,t={memoizedState:_t.memoizedState,baseState:_t.baseState,baseQueue:_t.baseQueue,queue:_t.queue,next:null},kt===null?ft.memoizedState=kt=t:kt=kt.next=t}return kt}function nr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Un(t){var e=Nn;return Nn+=1,Na===null&&(Na=[]),t=co(Na,t,e),e=ft,(kt===null?e.memoizedState:kt.next)===null&&(e=e.alternate,M.H=e===null||e.memoizedState===null?Wo:Fo),t}function $u(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Un(t);if(t.$$typeof===$)return ce(t)}throw Error(r(438,String(t)))}function ur(t){var e=null,l=ft.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=ft.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=nr(),ft.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=m;return e.index++,l}function cl(t,e){return typeof e=="function"?e(t):e}function Ju(t){var e=Kt();return ir(e,_t,t)}function ir(t,e,l){var a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=l;var n=t.baseQueue,i=a.pending;if(i!==null){if(n!==null){var s=n.next;n.next=i.next,i.next=s}e.baseQueue=n=i,a.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var d=s=null,y=null,O=e,N=!1;do{var H=O.lane&-536870913;if(H!==O.lane?(yt&H)===H:(Cl&H)===H){var C=O.revertLane;if(C===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),H===za&&(N=!0);else if((Cl&C)===C){O=O.next,C===za&&(N=!0);continue}else H={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=H,s=i):y=y.next=H,ft.lanes|=C,Ul|=C;H=O.action,na&&l(i,H),i=O.hasEagerState?O.eagerState:l(i,H)}else C={lane:H,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=C,s=i):y=y.next=C,ft.lanes|=H,Ul|=H;O=O.next}while(O!==null&&O!==e);if(y===null?s=i:y.next=d,!be(i,t.memoizedState)&&(It=!0,N&&(l=Ra,l!==null)))throw l;t.memoizedState=i,t.baseState=s,t.baseQueue=y,a.lastRenderedState=i}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function cr(t){var e=Kt(),l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,i=e.memoizedState;if(n!==null){l.pending=null;var s=n=n.next;do i=t(i,s.action),s=s.next;while(s!==n);be(i,e.memoizedState)||(It=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,a]}function go(t,e,l){var a=ft,n=Kt(),i=bt;if(i){if(l===void 0)throw Error(r(407));l=l()}else l=e();var s=!be((_t||n).memoizedState,l);s&&(n.memoizedState=l,It=!0),n=n.queue;var d=bo.bind(null,a,n,t);if(Bn(2048,8,d,[t]),n.getSnapshot!==e||s||kt!==null&&kt.memoizedState.tag&1){if(a.flags|=2048,Ua(9,Wu(),po.bind(null,a,n,l,e),null),jt===null)throw Error(r(349));i||(Cl&124)!==0||vo(a,e,l)}return l}function vo(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=ft.updateQueue,e===null?(e=nr(),ft.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function po(t,e,l,a){e.value=l,e.getSnapshot=a,So(e)&&To(t)}function bo(t,e,l){return l(function(){So(e)&&To(t)})}function So(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!be(t,l)}catch{return!0}}function To(t){var e=Oa(t,2);e!==null&&xe(e,t,2)}function rr(t){var e=he();if(typeof t=="function"){var l=t;if(t=l(),na){bl(!0);try{l()}finally{bl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cl,lastRenderedState:t},e}function Ao(t,e,l,a){return t.baseState=l,ir(t,_t,typeof a=="function"?a:cl)}function yy(t,e,l,a,n){if(Pu(t))throw Error(r(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(s){i.listeners.push(s)}};M.T!==null?l(!0):i.isTransition=!1,a(i),l=e.pending,l===null?(i.next=e.pending=i,Eo(e,i)):(i.next=l.next,e.pending=l.next=i)}}function Eo(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var i=M.T,s={};M.T=s;try{var d=l(n,a),y=M.S;y!==null&&y(s,d),Oo(t,e,d)}catch(O){fr(t,e,O)}finally{M.T=i}}else try{i=l(n,a),Oo(t,e,i)}catch(O){fr(t,e,O)}}function Oo(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){xo(t,e,a)},function(a){return fr(t,e,a)}):xo(t,e,l)}function xo(t,e,l){e.status="fulfilled",e.value=l,Co(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,Eo(t,l)))}function fr(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Co(e),e=e.next;while(e!==a)}t.action=null}function Co(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function _o(t,e){return e}function zo(t,e){if(bt){var l=jt.formState;if(l!==null){t:{var a=ft;if(bt){if(Yt){e:{for(var n=Yt,i=Ze;n.nodeType!==8;){if(!i){n=null;break e}if(n=Le(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){Yt=Le(n.nextSibling),a=n.data==="F!";break t}}ta(a)}a=!1}a&&(e=l[0])}}return l=he(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:_o,lastRenderedState:e},l.queue=a,l=Ko.bind(null,ft,a),a.dispatch=l,a=rr(!1),i=mr.bind(null,ft,!1,a.queue),a=he(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=yy.bind(null,ft,n,i,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Ro(t){var e=Kt();return Mo(e,_t,t)}function Mo(t,e,l){if(e=ir(t,e,_o)[0],t=Ju(cl)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Un(e)}catch(s){throw s===_n?Qu:s}else a=e;e=Kt();var n=e.queue,i=n.dispatch;return l!==e.memoizedState&&(ft.flags|=2048,Ua(9,Wu(),gy.bind(null,n,l),null)),[a,i,t]}function gy(t,e){t.action=e}function Do(t){var e=Kt(),l=_t;if(l!==null)return Mo(e,l,t);Kt(),e=e.memoizedState,l=Kt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function Ua(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=ft.updateQueue,e===null&&(e=nr(),ft.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function Wu(){return{destroy:void 0,resource:void 0}}function No(){return Kt().memoizedState}function Fu(t,e,l,a){var n=he();a=a===void 0?null:a,ft.flags|=t,n.memoizedState=Ua(1|e,Wu(),l,a)}function Bn(t,e,l,a){var n=Kt();a=a===void 0?null:a;var i=n.memoizedState.inst;_t!==null&&a!==null&&Ic(a,_t.memoizedState.deps)?n.memoizedState=Ua(e,i,l,a):(ft.flags|=t,n.memoizedState=Ua(1|e,i,l,a))}function Uo(t,e){Fu(8390656,8,t,e)}function Bo(t,e){Bn(2048,8,t,e)}function jo(t,e){return Bn(4,2,t,e)}function Ho(t,e){return Bn(4,4,t,e)}function wo(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function qo(t,e,l){l=l!=null?l.concat([t]):null,Bn(4,4,wo.bind(null,e,t),l)}function sr(){}function Yo(t,e){var l=Kt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&Ic(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Go(t,e){var l=Kt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&Ic(e,a[1]))return a[0];if(a=t(),na){bl(!0);try{t()}finally{bl(!1)}}return l.memoizedState=[a,e],a}function or(t,e,l){return l===void 0||(Cl&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=Qd(),ft.lanes|=t,Ul|=t,l)}function Xo(t,e,l,a){return be(l,e)?l:Ma.current!==null?(t=or(t,l,a),be(t,e)||(It=!0),t):(Cl&42)===0?(It=!0,t.memoizedState=l):(t=Qd(),ft.lanes|=t,Ul|=t,e)}function Lo(t,e,l,a,n){var i=Q.p;Q.p=i!==0&&8>i?i:8;var s=M.T,d={};M.T=d,mr(t,!1,e,l);try{var y=n(),O=M.S;if(O!==null&&O(d,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var N=dy(y,a);jn(t,e,N,Oe(t))}else jn(t,e,a,Oe(t))}catch(H){jn(t,e,{then:function(){},status:"rejected",reason:H},Oe())}finally{Q.p=i,M.T=s}}function vy(){}function dr(t,e,l,a){if(t.tag!==5)throw Error(r(476));var n=Qo(t).queue;Lo(t,n,e,et,l===null?vy:function(){return Vo(t),l(a)})}function Qo(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:et,baseState:et,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cl,lastRenderedState:et},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cl,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Vo(t){var e=Qo(t).next.queue;jn(t,e,{},Oe())}function hr(){return ce(In)}function Zo(){return Kt().memoizedState}function ko(){return Kt().memoizedState}function py(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=Oe();t=Ol(l);var a=xl(e,t,l);a!==null&&(xe(a,e,l),Rn(a,e,l)),e={cache:Qc()},t.payload=e;return}e=e.return}}function by(t,e,l){var a=Oe();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Pu(t)?$o(e,l):(l=Uc(t,e,l,a),l!==null&&(xe(l,t,a),Jo(l,e,a)))}function Ko(t,e,l){var a=Oe();jn(t,e,l,a)}function jn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Pu(t))$o(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var s=e.lastRenderedState,d=i(s,l);if(n.hasEagerState=!0,n.eagerState=d,be(d,s))return ju(t,e,n,0),jt===null&&Bu(),!1}catch{}finally{}if(l=Uc(t,e,n,a),l!==null)return xe(l,t,a),Jo(l,e,a),!0}return!1}function mr(t,e,l,a){if(a={lane:2,revertLane:kr(),action:a,hasEagerState:!1,eagerState:null,next:null},Pu(t)){if(e)throw Error(r(479))}else e=Uc(t,l,a,2),e!==null&&xe(e,t,2)}function Pu(t){var e=t.alternate;return t===ft||e!==null&&e===ft}function $o(t,e){Da=ku=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Jo(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,ls(t,l)}}var Iu={readContext:ce,use:$u,useCallback:Qt,useContext:Qt,useEffect:Qt,useImperativeHandle:Qt,useLayoutEffect:Qt,useInsertionEffect:Qt,useMemo:Qt,useReducer:Qt,useRef:Qt,useState:Qt,useDebugValue:Qt,useDeferredValue:Qt,useTransition:Qt,useSyncExternalStore:Qt,useId:Qt,useHostTransitionStatus:Qt,useFormState:Qt,useActionState:Qt,useOptimistic:Qt,useMemoCache:Qt,useCacheRefresh:Qt},Wo={readContext:ce,use:$u,useCallback:function(t,e){return he().memoizedState=[t,e===void 0?null:e],t},useContext:ce,useEffect:Uo,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Fu(4194308,4,wo.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Fu(4194308,4,t,e)},useInsertionEffect:function(t,e){Fu(4,2,t,e)},useMemo:function(t,e){var l=he();e=e===void 0?null:e;var a=t();if(na){bl(!0);try{t()}finally{bl(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=he();if(l!==void 0){var n=l(e);if(na){bl(!0);try{l(e)}finally{bl(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=by.bind(null,ft,t),[a.memoizedState,t]},useRef:function(t){var e=he();return t={current:t},e.memoizedState=t},useState:function(t){t=rr(t);var e=t.queue,l=Ko.bind(null,ft,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:sr,useDeferredValue:function(t,e){var l=he();return or(l,t,e)},useTransition:function(){var t=rr(!1);return t=Lo.bind(null,ft,t.queue,!0,!1),he().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=ft,n=he();if(bt){if(l===void 0)throw Error(r(407));l=l()}else{if(l=e(),jt===null)throw Error(r(349));(yt&124)!==0||vo(a,e,l)}n.memoizedState=l;var i={value:l,getSnapshot:e};return n.queue=i,Uo(bo.bind(null,a,i,t),[t]),a.flags|=2048,Ua(9,Wu(),po.bind(null,a,i,l,e),null),l},useId:function(){var t=he(),e=jt.identifierPrefix;if(bt){var l=nl,a=al;l=(a&~(1<<32-pe(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=Ku++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=hy++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:hr,useFormState:zo,useActionState:zo,useOptimistic:function(t){var e=he();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=mr.bind(null,ft,!0,l),l.dispatch=e,[t,e]},useMemoCache:ur,useCacheRefresh:function(){return he().memoizedState=py.bind(null,ft)}},Fo={readContext:ce,use:$u,useCallback:Yo,useContext:ce,useEffect:Bo,useImperativeHandle:qo,useInsertionEffect:jo,useLayoutEffect:Ho,useMemo:Go,useReducer:Ju,useRef:No,useState:function(){return Ju(cl)},useDebugValue:sr,useDeferredValue:function(t,e){var l=Kt();return Xo(l,_t.memoizedState,t,e)},useTransition:function(){var t=Ju(cl)[0],e=Kt().memoizedState;return[typeof t=="boolean"?t:Un(t),e]},useSyncExternalStore:go,useId:Zo,useHostTransitionStatus:hr,useFormState:Ro,useActionState:Ro,useOptimistic:function(t,e){var l=Kt();return Ao(l,_t,t,e)},useMemoCache:ur,useCacheRefresh:ko},Sy={readContext:ce,use:$u,useCallback:Yo,useContext:ce,useEffect:Bo,useImperativeHandle:qo,useInsertionEffect:jo,useLayoutEffect:Ho,useMemo:Go,useReducer:cr,useRef:No,useState:function(){return cr(cl)},useDebugValue:sr,useDeferredValue:function(t,e){var l=Kt();return _t===null?or(l,t,e):Xo(l,_t.memoizedState,t,e)},useTransition:function(){var t=cr(cl)[0],e=Kt().memoizedState;return[typeof t=="boolean"?t:Un(t),e]},useSyncExternalStore:go,useId:Zo,useHostTransitionStatus:hr,useFormState:Do,useActionState:Do,useOptimistic:function(t,e){var l=Kt();return _t!==null?Ao(l,_t,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:ur,useCacheRefresh:ko},Ba=null,Hn=0;function ti(t){var e=Hn;return Hn+=1,Ba===null&&(Ba=[]),co(Ba,t,e)}function wn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ei(t,e){throw e.$$typeof===R?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Po(t){var e=t._init;return e(t._payload)}function Io(t){function e(T,b){if(t){var E=T.deletions;E===null?(T.deletions=[b],T.flags|=16):E.push(b)}}function l(T,b){if(!t)return null;for(;b!==null;)e(T,b),b=b.sibling;return null}function a(T){for(var b=new Map;T!==null;)T.key!==null?b.set(T.key,T):b.set(T.index,T),T=T.sibling;return b}function n(T,b){return T=ll(T,b),T.index=0,T.sibling=null,T}function i(T,b,E){return T.index=E,t?(E=T.alternate,E!==null?(E=E.index,E<b?(T.flags|=67108866,b):E):(T.flags|=67108866,b)):(T.flags|=1048576,b)}function s(T){return t&&T.alternate===null&&(T.flags|=67108866),T}function d(T,b,E,B){return b===null||b.tag!==6?(b=jc(E,T.mode,B),b.return=T,b):(b=n(b,E),b.return=T,b)}function y(T,b,E,B){var J=E.type;return J===w?N(T,b,E.props.children,B,E.key):b!==null&&(b.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===gt&&Po(J)===b.type)?(b=n(b,E.props),wn(b,E),b.return=T,b):(b=wu(E.type,E.key,E.props,null,T.mode,B),wn(b,E),b.return=T,b)}function O(T,b,E,B){return b===null||b.tag!==4||b.stateNode.containerInfo!==E.containerInfo||b.stateNode.implementation!==E.implementation?(b=Hc(E,T.mode,B),b.return=T,b):(b=n(b,E.children||[]),b.return=T,b)}function N(T,b,E,B,J){return b===null||b.tag!==7?(b=Wl(E,T.mode,B,J),b.return=T,b):(b=n(b,E),b.return=T,b)}function H(T,b,E){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=jc(""+b,T.mode,E),b.return=T,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case U:return E=wu(b.type,b.key,b.props,null,T.mode,E),wn(E,b),E.return=T,E;case q:return b=Hc(b,T.mode,E),b.return=T,b;case gt:var B=b._init;return b=B(b._payload),H(T,b,E)}if(pt(b)||tt(b))return b=Wl(b,T.mode,E,null),b.return=T,b;if(typeof b.then=="function")return H(T,ti(b),E);if(b.$$typeof===$)return H(T,Xu(T,b),E);ei(T,b)}return null}function C(T,b,E,B){var J=b!==null?b.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return J!==null?null:d(T,b,""+E,B);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case U:return E.key===J?y(T,b,E,B):null;case q:return E.key===J?O(T,b,E,B):null;case gt:return J=E._init,E=J(E._payload),C(T,b,E,B)}if(pt(E)||tt(E))return J!==null?null:N(T,b,E,B,null);if(typeof E.then=="function")return C(T,b,ti(E),B);if(E.$$typeof===$)return C(T,b,Xu(T,E),B);ei(T,E)}return null}function _(T,b,E,B,J){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return T=T.get(E)||null,d(b,T,""+B,J);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case U:return T=T.get(B.key===null?E:B.key)||null,y(b,T,B,J);case q:return T=T.get(B.key===null?E:B.key)||null,O(b,T,B,J);case gt:var st=B._init;return B=st(B._payload),_(T,b,E,B,J)}if(pt(B)||tt(B))return T=T.get(E)||null,N(b,T,B,J,null);if(typeof B.then=="function")return _(T,b,E,ti(B),J);if(B.$$typeof===$)return _(T,b,E,Xu(b,B),J);ei(b,B)}return null}function it(T,b,E,B){for(var J=null,st=null,I=b,nt=b=0,ee=null;I!==null&&nt<E.length;nt++){I.index>nt?(ee=I,I=null):ee=I.sibling;var vt=C(T,I,E[nt],B);if(vt===null){I===null&&(I=ee);break}t&&I&&vt.alternate===null&&e(T,I),b=i(vt,b,nt),st===null?J=vt:st.sibling=vt,st=vt,I=ee}if(nt===E.length)return l(T,I),bt&&Pl(T,nt),J;if(I===null){for(;nt<E.length;nt++)I=H(T,E[nt],B),I!==null&&(b=i(I,b,nt),st===null?J=I:st.sibling=I,st=I);return bt&&Pl(T,nt),J}for(I=a(I);nt<E.length;nt++)ee=_(I,T,nt,E[nt],B),ee!==null&&(t&&ee.alternate!==null&&I.delete(ee.key===null?nt:ee.key),b=i(ee,b,nt),st===null?J=ee:st.sibling=ee,st=ee);return t&&I.forEach(function(Ll){return e(T,Ll)}),bt&&Pl(T,nt),J}function at(T,b,E,B){if(E==null)throw Error(r(151));for(var J=null,st=null,I=b,nt=b=0,ee=null,vt=E.next();I!==null&&!vt.done;nt++,vt=E.next()){I.index>nt?(ee=I,I=null):ee=I.sibling;var Ll=C(T,I,vt.value,B);if(Ll===null){I===null&&(I=ee);break}t&&I&&Ll.alternate===null&&e(T,I),b=i(Ll,b,nt),st===null?J=Ll:st.sibling=Ll,st=Ll,I=ee}if(vt.done)return l(T,I),bt&&Pl(T,nt),J;if(I===null){for(;!vt.done;nt++,vt=E.next())vt=H(T,vt.value,B),vt!==null&&(b=i(vt,b,nt),st===null?J=vt:st.sibling=vt,st=vt);return bt&&Pl(T,nt),J}for(I=a(I);!vt.done;nt++,vt=E.next())vt=_(I,T,nt,vt.value,B),vt!==null&&(t&&vt.alternate!==null&&I.delete(vt.key===null?nt:vt.key),b=i(vt,b,nt),st===null?J=vt:st.sibling=vt,st=vt);return t&&I.forEach(function(Tg){return e(T,Tg)}),bt&&Pl(T,nt),J}function Rt(T,b,E,B){if(typeof E=="object"&&E!==null&&E.type===w&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case U:t:{for(var J=E.key;b!==null;){if(b.key===J){if(J=E.type,J===w){if(b.tag===7){l(T,b.sibling),B=n(b,E.props.children),B.return=T,T=B;break t}}else if(b.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===gt&&Po(J)===b.type){l(T,b.sibling),B=n(b,E.props),wn(B,E),B.return=T,T=B;break t}l(T,b);break}else e(T,b);b=b.sibling}E.type===w?(B=Wl(E.props.children,T.mode,B,E.key),B.return=T,T=B):(B=wu(E.type,E.key,E.props,null,T.mode,B),wn(B,E),B.return=T,T=B)}return s(T);case q:t:{for(J=E.key;b!==null;){if(b.key===J)if(b.tag===4&&b.stateNode.containerInfo===E.containerInfo&&b.stateNode.implementation===E.implementation){l(T,b.sibling),B=n(b,E.children||[]),B.return=T,T=B;break t}else{l(T,b);break}else e(T,b);b=b.sibling}B=Hc(E,T.mode,B),B.return=T,T=B}return s(T);case gt:return J=E._init,E=J(E._payload),Rt(T,b,E,B)}if(pt(E))return it(T,b,E,B);if(tt(E)){if(J=tt(E),typeof J!="function")throw Error(r(150));return E=J.call(E),at(T,b,E,B)}if(typeof E.then=="function")return Rt(T,b,ti(E),B);if(E.$$typeof===$)return Rt(T,b,Xu(T,E),B);ei(T,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,b!==null&&b.tag===6?(l(T,b.sibling),B=n(b,E),B.return=T,T=B):(l(T,b),B=jc(E,T.mode,B),B.return=T,T=B),s(T)):l(T,b)}return function(T,b,E,B){try{Hn=0;var J=Rt(T,b,E,B);return Ba=null,J}catch(I){if(I===_n||I===Qu)throw I;var st=Se(29,I,null,T.mode);return st.lanes=B,st.return=T,st}finally{}}}var ja=Io(!0),td=Io(!1),je=j(null),ke=null;function _l(t){var e=t.alternate;G(Ft,Ft.current&1),G(je,t),ke===null&&(e===null||Ma.current!==null||e.memoizedState!==null)&&(ke=t)}function ed(t){if(t.tag===22){if(G(Ft,Ft.current),G(je,t),ke===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(ke=t)}}else zl()}function zl(){G(Ft,Ft.current),G(je,je.current)}function rl(t){Y(je),ke===t&&(ke=null),Y(Ft)}var Ft=j(0);function li(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||nf(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function yr(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:z({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var gr={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=Oe(),n=Ol(a);n.payload=e,l!=null&&(n.callback=l),e=xl(t,n,a),e!==null&&(xe(e,t,a),Rn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=Oe(),n=Ol(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=xl(t,n,a),e!==null&&(xe(e,t,a),Rn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=Oe(),a=Ol(l);a.tag=2,e!=null&&(a.callback=e),e=xl(t,a,l),e!==null&&(xe(e,t,l),Rn(e,t,l))}};function ld(t,e,l,a,n,i,s){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,i,s):e.prototype&&e.prototype.isPureReactComponent?!bn(l,a)||!bn(n,i):!0}function ad(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&gr.enqueueReplaceState(e,e.state,null)}function ua(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=z({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var ai=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function nd(t){ai(t)}function ud(t){console.error(t)}function id(t){ai(t)}function ni(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function cd(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function vr(t,e,l){return l=Ol(l),l.tag=3,l.payload={element:null},l.callback=function(){ni(t,e)},l}function rd(t){return t=Ol(t),t.tag=3,t}function fd(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;t.payload=function(){return n(i)},t.callback=function(){cd(e,l,a)}}var s=l.stateNode;s!==null&&typeof s.componentDidCatch=="function"&&(t.callback=function(){cd(e,l,a),typeof n!="function"&&(Bl===null?Bl=new Set([this]):Bl.add(this));var d=a.stack;this.componentDidCatch(a.value,{componentStack:d!==null?d:""})})}function Ty(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&On(e,l,n,!0),l=je.current,l!==null){switch(l.tag){case 13:return ke===null?Xr():l.alternate===null&&Gt===0&&(Gt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===kc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Qr(t,a,n)),!1;case 22:return l.flags|=65536,a===kc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Qr(t,a,n)),!1}throw Error(r(435,l.tag))}return Qr(t,a,n),Xr(),!1}if(bt)return e=je.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Yc&&(t=Error(r(422),{cause:a}),En(De(t,l)))):(a!==Yc&&(e=Error(r(423),{cause:a}),En(De(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=De(a,l),n=vr(t.stateNode,a,n),Jc(t,n),Gt!==4&&(Gt=2)),!1;var i=Error(r(520),{cause:a});if(i=De(i,l),Vn===null?Vn=[i]:Vn.push(i),Gt!==4&&(Gt=2),e===null)return!0;a=De(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=vr(l.stateNode,a,t),Jc(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Bl===null||!Bl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=rd(n),fd(n,t,l,a),Jc(l,n),!1}l=l.return}while(l!==null);return!1}var sd=Error(r(461)),It=!1;function le(t,e,l,a){e.child=t===null?td(e,null,l,a):ja(e,t.child,l,a)}function od(t,e,l,a,n){l=l.render;var i=e.ref;if("ref"in a){var s={};for(var d in a)d!=="ref"&&(s[d]=a[d])}else s=a;return la(e),a=tr(t,e,l,s,i,n),d=er(),t!==null&&!It?(lr(t,e,n),fl(t,e,n)):(bt&&d&&wc(e),e.flags|=1,le(t,e,a,n),e.child)}function dd(t,e,l,a,n){if(t===null){var i=l.type;return typeof i=="function"&&!Bc(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,hd(t,e,i,a,n)):(t=wu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!xr(t,n)){var s=i.memoizedProps;if(l=l.compare,l=l!==null?l:bn,l(s,a)&&t.ref===e.ref)return fl(t,e,n)}return e.flags|=1,t=ll(i,a),t.ref=e.ref,t.return=e,e.child=t}function hd(t,e,l,a,n){if(t!==null){var i=t.memoizedProps;if(bn(i,a)&&t.ref===e.ref)if(It=!1,e.pendingProps=a=i,xr(t,n))(t.flags&131072)!==0&&(It=!0);else return e.lanes=t.lanes,fl(t,e,n)}return pr(t,e,l,a,n)}function md(t,e,l){var a=e.pendingProps,n=a.children,i=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~a}else e.childLanes=0,e.child=null;return yd(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Lu(e,i!==null?i.cachePool:null),i!==null?ho(e,i):Fc(),ed(e);else return e.lanes=e.childLanes=536870912,yd(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(Lu(e,i.cachePool),ho(e,i),zl(),e.memoizedState=null):(t!==null&&Lu(e,null),Fc(),zl());return le(t,e,n,l),e.child}function yd(t,e,l,a){var n=Zc();return n=n===null?null:{parent:Wt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&Lu(e,null),Fc(),ed(e),t!==null&&On(t,e,a,!0),null}function ui(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(r(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function pr(t,e,l,a,n){return la(e),l=tr(t,e,l,a,void 0,n),a=er(),t!==null&&!It?(lr(t,e,n),fl(t,e,n)):(bt&&a&&wc(e),e.flags|=1,le(t,e,l,n),e.child)}function gd(t,e,l,a,n,i){return la(e),e.updateQueue=null,l=yo(e,a,l,n),mo(t),a=er(),t!==null&&!It?(lr(t,e,i),fl(t,e,i)):(bt&&a&&wc(e),e.flags|=1,le(t,e,l,i),e.child)}function vd(t,e,l,a,n){if(la(e),e.stateNode===null){var i=xa,s=l.contextType;typeof s=="object"&&s!==null&&(i=ce(s)),i=new l(a,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=gr,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=a,i.state=e.memoizedState,i.refs={},Kc(e),s=l.contextType,i.context=typeof s=="object"&&s!==null?ce(s):xa,i.state=e.memoizedState,s=l.getDerivedStateFromProps,typeof s=="function"&&(yr(e,l,s,a),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(s=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),s!==i.state&&gr.enqueueReplaceState(i,i.state,null),Dn(e,a,i,n),Mn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){i=e.stateNode;var d=e.memoizedProps,y=ua(l,d);i.props=y;var O=i.context,N=l.contextType;s=xa,typeof N=="object"&&N!==null&&(s=ce(N));var H=l.getDerivedStateFromProps;N=typeof H=="function"||typeof i.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,N||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(d||O!==s)&&ad(e,i,a,s),El=!1;var C=e.memoizedState;i.state=C,Dn(e,a,i,n),Mn(),O=e.memoizedState,d||C!==O||El?(typeof H=="function"&&(yr(e,l,H,a),O=e.memoizedState),(y=El||ld(e,l,y,a,C,O,s))?(N||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=O),i.props=a,i.state=O,i.context=s,a=y):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{i=e.stateNode,$c(t,e),s=e.memoizedProps,N=ua(l,s),i.props=N,H=e.pendingProps,C=i.context,O=l.contextType,y=xa,typeof O=="object"&&O!==null&&(y=ce(O)),d=l.getDerivedStateFromProps,(O=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(s!==H||C!==y)&&ad(e,i,a,y),El=!1,C=e.memoizedState,i.state=C,Dn(e,a,i,n),Mn();var _=e.memoizedState;s!==H||C!==_||El||t!==null&&t.dependencies!==null&&Gu(t.dependencies)?(typeof d=="function"&&(yr(e,l,d,a),_=e.memoizedState),(N=El||ld(e,l,N,a,C,_,y)||t!==null&&t.dependencies!==null&&Gu(t.dependencies))?(O||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,_,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,_,y)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=_),i.props=a,i.state=_,i.context=y,a=N):(typeof i.componentDidUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||s===t.memoizedProps&&C===t.memoizedState||(e.flags|=1024),a=!1)}return i=a,ui(t,e),a=(e.flags&128)!==0,i||a?(i=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&a?(e.child=ja(e,t.child,null,n),e.child=ja(e,null,l,n)):le(t,e,l,n),e.memoizedState=i.state,t=e.child):t=fl(t,e,n),t}function pd(t,e,l,a){return An(),e.flags|=256,le(t,e,l,a),e.child}var br={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Sr(t){return{baseLanes:t,cachePool:no()}}function Tr(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=He),t}function bd(t,e,l){var a=e.pendingProps,n=!1,i=(e.flags&128)!==0,s;if((s=i)||(s=t!==null&&t.memoizedState===null?!1:(Ft.current&2)!==0),s&&(n=!0,e.flags&=-129),s=(e.flags&32)!==0,e.flags&=-33,t===null){if(bt){if(n?_l(e):zl(),bt){var d=Yt,y;if(y=d){t:{for(y=d,d=Ze;y.nodeType!==8;){if(!d){d=null;break t}if(y=Le(y.nextSibling),y===null){d=null;break t}}d=y}d!==null?(e.memoizedState={dehydrated:d,treeContext:Fl!==null?{id:al,overflow:nl}:null,retryLane:536870912,hydrationErrors:null},y=Se(18,null,null,0),y.stateNode=d,y.return=e,e.child=y,se=e,Yt=null,y=!0):y=!1}y||ta(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return nf(d)?e.lanes=32:e.lanes=536870912,null;rl(e)}return d=a.children,a=a.fallback,n?(zl(),n=e.mode,d=ii({mode:"hidden",children:d},n),a=Wl(a,n,l,null),d.return=e,a.return=e,d.sibling=a,e.child=d,n=e.child,n.memoizedState=Sr(l),n.childLanes=Tr(t,s,l),e.memoizedState=br,a):(_l(e),Ar(e,d))}if(y=t.memoizedState,y!==null&&(d=y.dehydrated,d!==null)){if(i)e.flags&256?(_l(e),e.flags&=-257,e=Er(t,e,l)):e.memoizedState!==null?(zl(),e.child=t.child,e.flags|=128,e=null):(zl(),n=a.fallback,d=e.mode,a=ii({mode:"visible",children:a.children},d),n=Wl(n,d,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,ja(e,t.child,null,l),a=e.child,a.memoizedState=Sr(l),a.childLanes=Tr(t,s,l),e.memoizedState=br,e=n);else if(_l(e),nf(d)){if(s=d.nextSibling&&d.nextSibling.dataset,s)var O=s.dgst;s=O,a=Error(r(419)),a.stack="",a.digest=s,En({value:a,source:null,stack:null}),e=Er(t,e,l)}else if(It||On(t,e,l,!1),s=(l&t.childLanes)!==0,It||s){if(s=jt,s!==null&&(a=l&-l,a=(a&42)!==0?1:uc(a),a=(a&(s.suspendedLanes|l))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,Oa(t,a),xe(s,t,a),sd;d.data==="$?"||Xr(),e=Er(t,e,l)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,Yt=Le(d.nextSibling),se=e,bt=!0,Il=null,Ze=!1,t!==null&&(Ue[Be++]=al,Ue[Be++]=nl,Ue[Be++]=Fl,al=t.id,nl=t.overflow,Fl=e),e=Ar(e,a.children),e.flags|=4096);return e}return n?(zl(),n=a.fallback,d=e.mode,y=t.child,O=y.sibling,a=ll(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,O!==null?n=ll(O,n):(n=Wl(n,d,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,d=t.child.memoizedState,d===null?d=Sr(l):(y=d.cachePool,y!==null?(O=Wt._currentValue,y=y.parent!==O?{parent:O,pool:O}:y):y=no(),d={baseLanes:d.baseLanes|l,cachePool:y}),n.memoizedState=d,n.childLanes=Tr(t,s,l),e.memoizedState=br,a):(_l(e),l=t.child,t=l.sibling,l=ll(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(s=e.deletions,s===null?(e.deletions=[t],e.flags|=16):s.push(t)),e.child=l,e.memoizedState=null,l)}function Ar(t,e){return e=ii({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ii(t,e){return t=Se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Er(t,e,l){return ja(e,t.child,null,l),t=Ar(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Sd(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Xc(t.return,e,l)}function Or(t,e,l,a,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function Td(t,e,l){var a=e.pendingProps,n=a.revealOrder,i=a.tail;if(le(t,e,a.children,l),a=Ft.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Sd(t,l,e);else if(t.tag===19)Sd(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(G(Ft,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&li(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Or(e,!1,n,l,i);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&li(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Or(e,!0,l,null,i);break;case"together":Or(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function fl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),Ul|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(On(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,l=ll(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=ll(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function xr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Gu(t)))}function Ay(t,e,l){switch(e.tag){case 3:Et(e,e.stateNode.containerInfo),Al(e,Wt,t.memoizedState.cache),An();break;case 27:case 5:tc(e);break;case 4:Et(e,e.stateNode.containerInfo);break;case 10:Al(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(_l(e),e.flags|=128,null):(l&e.child.childLanes)!==0?bd(t,e,l):(_l(e),t=fl(t,e,l),t!==null?t.sibling:null);_l(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(On(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return Td(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),G(Ft,Ft.current),a)break;return null;case 22:case 23:return e.lanes=0,md(t,e,l);case 24:Al(e,Wt,t.memoizedState.cache)}return fl(t,e,l)}function Ad(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)It=!0;else{if(!xr(t,l)&&(e.flags&128)===0)return It=!1,Ay(t,e,l);It=(t.flags&131072)!==0}else It=!1,bt&&(e.flags&1048576)!==0&&Fs(e,Yu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Bc(a)?(t=ua(a,t),e.tag=1,e=vd(null,e,a,t,l)):(e.tag=0,e=pr(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===K){e.tag=11,e=od(null,e,a,t,l);break t}else if(n===W){e.tag=14,e=dd(null,e,a,t,l);break t}}throw e=Dt(a)||a,Error(r(306,e,""))}}return e;case 0:return pr(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=ua(a,e.pendingProps),vd(t,e,a,n,l);case 3:t:{if(Et(e,e.stateNode.containerInfo),t===null)throw Error(r(387));a=e.pendingProps;var i=e.memoizedState;n=i.element,$c(t,e),Dn(e,a,null,l);var s=e.memoizedState;if(a=s.cache,Al(e,Wt,a),a!==i.cache&&Lc(e,[Wt],l,!0),Mn(),a=s.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:s.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=pd(t,e,a,l);break t}else if(a!==n){n=De(Error(r(424)),e),En(n),e=pd(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Yt=Le(t.firstChild),se=e,bt=!0,Il=null,Ze=!0,l=td(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(An(),a===n){e=fl(t,e,l);break t}le(t,e,a,l)}e=e.child}return e;case 26:return ui(t,e),t===null?(l=C0(e.type,null,e.pendingProps,null))?e.memoizedState=l:bt||(l=e.type,t=e.pendingProps,a=Si(ut.current).createElement(l),a[ie]=e,a[oe]=t,ne(a,l,t),Pt(a),e.stateNode=a):e.memoizedState=C0(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return tc(e),t===null&&bt&&(a=e.stateNode=E0(e.type,e.pendingProps,ut.current),se=e,Ze=!0,n=Yt,wl(e.type)?(uf=n,Yt=Le(a.firstChild)):Yt=n),le(t,e,e.pendingProps.children,l),ui(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&bt&&((n=a=Yt)&&(a=Wy(a,e.type,e.pendingProps,Ze),a!==null?(e.stateNode=a,se=e,Yt=Le(a.firstChild),Ze=!1,n=!0):n=!1),n||ta(e)),tc(e),n=e.type,i=e.pendingProps,s=t!==null?t.memoizedProps:null,a=i.children,ef(n,i)?a=null:s!==null&&ef(n,s)&&(e.flags|=32),e.memoizedState!==null&&(n=tr(t,e,my,null,null,l),In._currentValue=n),ui(t,e),le(t,e,a,l),e.child;case 6:return t===null&&bt&&((t=l=Yt)&&(l=Fy(l,e.pendingProps,Ze),l!==null?(e.stateNode=l,se=e,Yt=null,t=!0):t=!1),t||ta(e)),null;case 13:return bd(t,e,l);case 4:return Et(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=ja(e,null,a,l):le(t,e,a,l),e.child;case 11:return od(t,e,e.type,e.pendingProps,l);case 7:return le(t,e,e.pendingProps,l),e.child;case 8:return le(t,e,e.pendingProps.children,l),e.child;case 12:return le(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,Al(e,e.type,a.value),le(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,la(e),n=ce(n),a=a(n),e.flags|=1,le(t,e,a,l),e.child;case 14:return dd(t,e,e.type,e.pendingProps,l);case 15:return hd(t,e,e.type,e.pendingProps,l);case 19:return Td(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=ii(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=ll(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return md(t,e,l);case 24:return la(e),a=ce(Wt),t===null?(n=Zc(),n===null&&(n=jt,i=Qc(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),e.memoizedState={parent:a,cache:n},Kc(e),Al(e,Wt,n)):((t.lanes&l)!==0&&($c(t,e),Dn(e,null,null,l),Mn()),n=t.memoizedState,i=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),Al(e,Wt,a)):(a=i.cache,Al(e,Wt,a),a!==n.cache&&Lc(e,[Wt],l,!0))),le(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function sl(t){t.flags|=4}function Ed(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!D0(e)){if(e=je.current,e!==null&&((yt&4194048)===yt?ke!==null:(yt&62914560)!==yt&&(yt&536870912)===0||e!==ke))throw zn=kc,uo;t.flags|=8192}}function ci(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?ts():536870912,t.lanes|=e,Ya|=e)}function qn(t,e){if(!bt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function qt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function Ey(t,e,l){var a=e.pendingProps;switch(qc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qt(e),null;case 1:return qt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),il(Wt),pl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Tn(e)?sl(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,to())),qt(e),null;case 26:return l=e.memoizedState,t===null?(sl(e),l!==null?(qt(e),Ed(e,l)):(qt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(sl(e),qt(e),Ed(e,l)):(qt(e),e.flags&=-16777217):(t.memoizedProps!==a&&sl(e),qt(e),e.flags&=-16777217),null;case 27:pu(e),l=ut.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&sl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return qt(e),null}t=lt.current,Tn(e)?Ps(e):(t=E0(n,a,l),e.stateNode=t,sl(e))}return qt(e),null;case 5:if(pu(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&sl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return qt(e),null}if(t=lt.current,Tn(e))Ps(e);else{switch(n=Si(ut.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[ie]=e,t[oe]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(ne(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&sl(e)}}return qt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&sl(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(r(166));if(t=ut.current,Tn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=se,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[ie]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||g0(t.nodeValue,l)),t||ta(e)}else t=Si(t).createTextNode(a),t[ie]=e,e.stateNode=t}return qt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=Tn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(r(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[ie]=e}else An(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;qt(e),n=!1}else n=to(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(rl(e),e):(rl(e),null)}if(rl(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),ci(e,e.updateQueue),qt(e),null;case 4:return pl(),t===null&&Wr(e.stateNode.containerInfo),qt(e),null;case 10:return il(e.type),qt(e),null;case 19:if(Y(Ft),n=e.memoizedState,n===null)return qt(e),null;if(a=(e.flags&128)!==0,i=n.rendering,i===null)if(a)qn(n,!1);else{if(Gt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=li(t),i!==null){for(e.flags|=128,qn(n,!1),t=i.updateQueue,e.updateQueue=t,ci(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Ws(l,t),l=l.sibling;return G(Ft,Ft.current&1|2),e.child}t=t.sibling}n.tail!==null&&Ve()>si&&(e.flags|=128,a=!0,qn(n,!1),e.lanes=4194304)}else{if(!a)if(t=li(i),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ci(e,t),qn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!bt)return qt(e),null}else 2*Ve()-n.renderingStartTime>si&&l!==536870912&&(e.flags|=128,a=!0,qn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Ve(),e.sibling=null,t=Ft.current,G(Ft,a?t&1|2:t&1),e):(qt(e),null);case 22:case 23:return rl(e),Pc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(qt(e),e.subtreeFlags&6&&(e.flags|=8192)):qt(e),l=e.updateQueue,l!==null&&ci(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&Y(aa),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),il(Wt),qt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function Oy(t,e){switch(qc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return il(Wt),pl(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return pu(e),null;case 13:if(rl(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));An()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(Ft),null;case 4:return pl(),null;case 10:return il(e.type),null;case 22:case 23:return rl(e),Pc(),t!==null&&Y(aa),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return il(Wt),null;case 25:return null;default:return null}}function Od(t,e){switch(qc(e),e.tag){case 3:il(Wt),pl();break;case 26:case 27:case 5:pu(e);break;case 4:pl();break;case 13:rl(e);break;case 19:Y(Ft);break;case 10:il(e.type);break;case 22:case 23:rl(e),Pc(),t!==null&&Y(aa);break;case 24:il(Wt)}}function Yn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var i=l.create,s=l.inst;a=i(),s.destroy=a}l=l.next}while(l!==n)}}catch(d){Nt(e,e.return,d)}}function Rl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){var s=a.inst,d=s.destroy;if(d!==void 0){s.destroy=void 0,n=e;var y=l,O=d;try{O()}catch(N){Nt(n,y,N)}}}a=a.next}while(a!==i)}}catch(N){Nt(e,e.return,N)}}function xd(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{oo(e,l)}catch(a){Nt(t,t.return,a)}}}function Cd(t,e,l){l.props=ua(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Nt(t,e,a)}}function Gn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Nt(t,e,n)}}function Ke(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Nt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Nt(t,e,n)}else l.current=null}function _d(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Nt(t,t.return,n)}}function Cr(t,e,l){try{var a=t.stateNode;Zy(a,t.type,l,e),a[oe]=e}catch(n){Nt(t,t.return,n)}}function zd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&wl(t.type)||t.tag===4}function _r(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||zd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&wl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zr(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=bi));else if(a!==4&&(a===27&&wl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(zr(t,e,l),t=t.sibling;t!==null;)zr(t,e,l),t=t.sibling}function ri(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&wl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(ri(t,e,l),t=t.sibling;t!==null;)ri(t,e,l),t=t.sibling}function Rd(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);ne(e,a,l),e[ie]=t,e[oe]=l}catch(i){Nt(t,t.return,i)}}var ol=!1,Vt=!1,Rr=!1,Md=typeof WeakSet=="function"?WeakSet:Set,te=null;function xy(t,e){if(t=t.containerInfo,Ir=Ci,t=Gs(t),_c(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var s=0,d=-1,y=-1,O=0,N=0,H=t,C=null;e:for(;;){for(var _;H!==l||n!==0&&H.nodeType!==3||(d=s+n),H!==i||a!==0&&H.nodeType!==3||(y=s+a),H.nodeType===3&&(s+=H.nodeValue.length),(_=H.firstChild)!==null;)C=H,H=_;for(;;){if(H===t)break e;if(C===l&&++O===n&&(d=s),C===i&&++N===a&&(y=s),(_=H.nextSibling)!==null)break;H=C,C=H.parentNode}H=_}l=d===-1||y===-1?null:{start:d,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(tf={focusedElem:t,selectionRange:l},Ci=!1,te=e;te!==null;)if(e=te,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,te=t;else for(;te!==null;){switch(e=te,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var it=ua(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(it,i),a.__reactInternalSnapshotBeforeUpdate=t}catch(at){Nt(l,l.return,at)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)af(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":af(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,te=t;break}te=e.return}}function Dd(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Ml(t,l),a&4&&Yn(5,l);break;case 1:if(Ml(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(s){Nt(l,l.return,s)}else{var n=ua(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(s){Nt(l,l.return,s)}}a&64&&xd(l),a&512&&Gn(l,l.return);break;case 3:if(Ml(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{oo(t,e)}catch(s){Nt(l,l.return,s)}}break;case 27:e===null&&a&4&&Rd(l);case 26:case 5:Ml(t,l),e===null&&a&4&&_d(l),a&512&&Gn(l,l.return);break;case 12:Ml(t,l);break;case 13:Ml(t,l),a&4&&Bd(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=By.bind(null,l),Py(t,l))));break;case 22:if(a=l.memoizedState!==null||ol,!a){e=e!==null&&e.memoizedState!==null||Vt,n=ol;var i=Vt;ol=a,(Vt=e)&&!i?Dl(t,l,(l.subtreeFlags&8772)!==0):Ml(t,l),ol=n,Vt=i}break;case 30:break;default:Ml(t,l)}}function Nd(t){var e=t.alternate;e!==null&&(t.alternate=null,Nd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&rc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Ht=null,me=!1;function dl(t,e,l){for(l=l.child;l!==null;)Ud(t,e,l),l=l.sibling}function Ud(t,e,l){if(ve&&typeof ve.onCommitFiberUnmount=="function")try{ve.onCommitFiberUnmount(cn,l)}catch{}switch(l.tag){case 26:Vt||Ke(l,e),dl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Vt||Ke(l,e);var a=Ht,n=me;wl(l.type)&&(Ht=l.stateNode,me=!1),dl(t,e,l),Jn(l.stateNode),Ht=a,me=n;break;case 5:Vt||Ke(l,e);case 6:if(a=Ht,n=me,Ht=null,dl(t,e,l),Ht=a,me=n,Ht!==null)if(me)try{(Ht.nodeType===9?Ht.body:Ht.nodeName==="HTML"?Ht.ownerDocument.body:Ht).removeChild(l.stateNode)}catch(i){Nt(l,e,i)}else try{Ht.removeChild(l.stateNode)}catch(i){Nt(l,e,i)}break;case 18:Ht!==null&&(me?(t=Ht,T0(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),au(t)):T0(Ht,l.stateNode));break;case 4:a=Ht,n=me,Ht=l.stateNode.containerInfo,me=!0,dl(t,e,l),Ht=a,me=n;break;case 0:case 11:case 14:case 15:Vt||Rl(2,l,e),Vt||Rl(4,l,e),dl(t,e,l);break;case 1:Vt||(Ke(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Cd(l,e,a)),dl(t,e,l);break;case 21:dl(t,e,l);break;case 22:Vt=(a=Vt)||l.memoizedState!==null,dl(t,e,l),Vt=a;break;default:dl(t,e,l)}}function Bd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{au(t)}catch(l){Nt(e,e.return,l)}}function Cy(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Md),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Md),e;default:throw Error(r(435,t.tag))}}function Mr(t,e){var l=Cy(t);e.forEach(function(a){var n=jy.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function Te(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=t,s=e,d=s;t:for(;d!==null;){switch(d.tag){case 27:if(wl(d.type)){Ht=d.stateNode,me=!1;break t}break;case 5:Ht=d.stateNode,me=!1;break t;case 3:case 4:Ht=d.stateNode.containerInfo,me=!0;break t}d=d.return}if(Ht===null)throw Error(r(160));Ud(i,s,n),Ht=null,me=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)jd(e,t),e=e.sibling}var Xe=null;function jd(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Te(e,t),Ae(t),a&4&&(Rl(3,t,t.return),Yn(3,t),Rl(5,t,t.return));break;case 1:Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),a&64&&ol&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Xe;if(Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[sn]||i[ie]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),ne(i,a,l),i[ie]=t,Pt(i),a=i;break t;case"link":var s=R0("link","href",n).get(a+(l.href||""));if(s){for(var d=0;d<s.length;d++)if(i=s[d],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){s.splice(d,1);break e}}i=n.createElement(a),ne(i,a,l),n.head.appendChild(i);break;case"meta":if(s=R0("meta","content",n).get(a+(l.content||""))){for(d=0;d<s.length;d++)if(i=s[d],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){s.splice(d,1);break e}}i=n.createElement(a),ne(i,a,l),n.head.appendChild(i);break;default:throw Error(r(468,a))}i[ie]=t,Pt(i),a=i}t.stateNode=a}else M0(n,t.type,t.stateNode);else t.stateNode=z0(n,a,t.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?M0(n,t.type,t.stateNode):z0(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Cr(t,t.memoizedProps,l.memoizedProps)}break;case 27:Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),l!==null&&a&4&&Cr(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),t.flags&32){n=t.stateNode;try{va(n,"")}catch(_){Nt(t,t.return,_)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Cr(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Rr=!0);break;case 6:if(Te(e,t),Ae(t),a&4){if(t.stateNode===null)throw Error(r(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(_){Nt(t,t.return,_)}}break;case 3:if(Ei=null,n=Xe,Xe=Ti(e.containerInfo),Te(e,t),Xe=n,Ae(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{au(e.containerInfo)}catch(_){Nt(t,t.return,_)}Rr&&(Rr=!1,Hd(t));break;case 4:a=Xe,Xe=Ti(t.stateNode.containerInfo),Te(e,t),Ae(t),Xe=a;break;case 12:Te(e,t),Ae(t);break;case 13:Te(e,t),Ae(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Hr=Ve()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Mr(t,a)));break;case 22:n=t.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,O=ol,N=Vt;if(ol=O||n,Vt=N||y,Te(e,t),Vt=N,ol=O,Ae(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||y||ol||Vt||ia(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){y=l=e;try{if(i=y.stateNode,n)s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none";else{d=y.stateNode;var H=y.memoizedProps.style,C=H!=null&&H.hasOwnProperty("display")?H.display:null;d.style.display=C==null||typeof C=="boolean"?"":(""+C).trim()}}catch(_){Nt(y,y.return,_)}}}else if(e.tag===6){if(l===null){y=e;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(_){Nt(y,y.return,_)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,Mr(t,l))));break;case 19:Te(e,t),Ae(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,Mr(t,a)));break;case 30:break;case 21:break;default:Te(e,t),Ae(t)}}function Ae(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(zd(a)){l=a;break}a=a.return}if(l==null)throw Error(r(160));switch(l.tag){case 27:var n=l.stateNode,i=_r(t);ri(t,i,n);break;case 5:var s=l.stateNode;l.flags&32&&(va(s,""),l.flags&=-33);var d=_r(t);ri(t,d,s);break;case 3:case 4:var y=l.stateNode.containerInfo,O=_r(t);zr(t,O,y);break;default:throw Error(r(161))}}catch(N){Nt(t,t.return,N)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Hd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Hd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ml(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Dd(t,e.alternate,e),e=e.sibling}function ia(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Rl(4,e,e.return),ia(e);break;case 1:Ke(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Cd(e,e.return,l),ia(e);break;case 27:Jn(e.stateNode);case 26:case 5:Ke(e,e.return),ia(e);break;case 22:e.memoizedState===null&&ia(e);break;case 30:ia(e);break;default:ia(e)}t=t.sibling}}function Dl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,i=e,s=i.flags;switch(i.tag){case 0:case 11:case 15:Dl(n,i,l),Yn(4,i);break;case 1:if(Dl(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(O){Nt(a,a.return,O)}if(a=i,n=a.updateQueue,n!==null){var d=a.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)so(y[n],d)}catch(O){Nt(a,a.return,O)}}l&&s&64&&xd(i),Gn(i,i.return);break;case 27:Rd(i);case 26:case 5:Dl(n,i,l),l&&a===null&&s&4&&_d(i),Gn(i,i.return);break;case 12:Dl(n,i,l);break;case 13:Dl(n,i,l),l&&s&4&&Bd(n,i);break;case 22:i.memoizedState===null&&Dl(n,i,l),Gn(i,i.return);break;case 30:break;default:Dl(n,i,l)}e=e.sibling}}function Dr(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&xn(l))}function Nr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&xn(t))}function $e(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)wd(t,e,l,a),e=e.sibling}function wd(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:$e(t,e,l,a),n&2048&&Yn(9,e);break;case 1:$e(t,e,l,a);break;case 3:$e(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&xn(t)));break;case 12:if(n&2048){$e(t,e,l,a),t=e.stateNode;try{var i=e.memoizedProps,s=i.id,d=i.onPostCommit;typeof d=="function"&&d(s,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){Nt(e,e.return,y)}}else $e(t,e,l,a);break;case 13:$e(t,e,l,a);break;case 23:break;case 22:i=e.stateNode,s=e.alternate,e.memoizedState!==null?i._visibility&2?$e(t,e,l,a):Xn(t,e):i._visibility&2?$e(t,e,l,a):(i._visibility|=2,Ha(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Dr(s,e);break;case 24:$e(t,e,l,a),n&2048&&Nr(e.alternate,e);break;default:$e(t,e,l,a)}}function Ha(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,s=e,d=l,y=a,O=s.flags;switch(s.tag){case 0:case 11:case 15:Ha(i,s,d,y,n),Yn(8,s);break;case 23:break;case 22:var N=s.stateNode;s.memoizedState!==null?N._visibility&2?Ha(i,s,d,y,n):Xn(i,s):(N._visibility|=2,Ha(i,s,d,y,n)),n&&O&2048&&Dr(s.alternate,s);break;case 24:Ha(i,s,d,y,n),n&&O&2048&&Nr(s.alternate,s);break;default:Ha(i,s,d,y,n)}e=e.sibling}}function Xn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Xn(l,a),n&2048&&Dr(a.alternate,a);break;case 24:Xn(l,a),n&2048&&Nr(a.alternate,a);break;default:Xn(l,a)}e=e.sibling}}var Ln=8192;function wa(t){if(t.subtreeFlags&Ln)for(t=t.child;t!==null;)qd(t),t=t.sibling}function qd(t){switch(t.tag){case 26:wa(t),t.flags&Ln&&t.memoizedState!==null&&og(Xe,t.memoizedState,t.memoizedProps);break;case 5:wa(t);break;case 3:case 4:var e=Xe;Xe=Ti(t.stateNode.containerInfo),wa(t),Xe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ln,Ln=16777216,wa(t),Ln=e):wa(t));break;default:wa(t)}}function Yd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Qn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];te=a,Xd(a,t)}Yd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Gd(t),t=t.sibling}function Gd(t){switch(t.tag){case 0:case 11:case 15:Qn(t),t.flags&2048&&Rl(9,t,t.return);break;case 3:Qn(t);break;case 12:Qn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,fi(t)):Qn(t);break;default:Qn(t)}}function fi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];te=a,Xd(a,t)}Yd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Rl(8,e,e.return),fi(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,fi(e));break;default:fi(e)}t=t.sibling}}function Xd(t,e){for(;te!==null;){var l=te;switch(l.tag){case 0:case 11:case 15:Rl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:xn(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,te=a;else t:for(l=t;te!==null;){a=te;var n=a.sibling,i=a.return;if(Nd(a),a===l){te=null;break t}if(n!==null){n.return=i,te=n;break t}te=i}}}var _y={getCacheForType:function(t){var e=ce(Wt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},zy=typeof WeakMap=="function"?WeakMap:Map,xt=0,jt=null,dt=null,yt=0,Ct=0,Ee=null,Nl=!1,qa=!1,Ur=!1,hl=0,Gt=0,Ul=0,ca=0,Br=0,He=0,Ya=0,Vn=null,ye=null,jr=!1,Hr=0,si=1/0,oi=null,Bl=null,ae=0,jl=null,Ga=null,Xa=0,wr=0,qr=null,Ld=null,Zn=0,Yr=null;function Oe(){if((xt&2)!==0&&yt!==0)return yt&-yt;if(M.T!==null){var t=za;return t!==0?t:kr()}return as()}function Qd(){He===0&&(He=(yt&536870912)===0||bt?If():536870912);var t=je.current;return t!==null&&(t.flags|=32),He}function xe(t,e,l){(t===jt&&(Ct===2||Ct===9)||t.cancelPendingCommit!==null)&&(La(t,0),Hl(t,yt,He,!1)),fn(t,l),((xt&2)===0||t!==jt)&&(t===jt&&((xt&2)===0&&(ca|=l),Gt===4&&Hl(t,yt,He,!1)),Je(t))}function Vd(t,e,l){if((xt&6)!==0)throw Error(r(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||rn(t,e),n=a?Dy(t,e):Lr(t,e,!0),i=a;do{if(n===0){qa&&!a&&Hl(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!Ry(l)){n=Lr(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var s=0;else s=t.pendingLanes&-536870913,s=s!==0?s:s&536870912?536870912:0;if(s!==0){e=s;t:{var d=t;n=Vn;var y=d.current.memoizedState.isDehydrated;if(y&&(La(d,s).flags|=256),s=Lr(d,s,!1),s!==2){if(Ur&&!y){d.errorRecoveryDisabledLanes|=i,ca|=i,n=4;break t}i=ye,ye=n,i!==null&&(ye===null?ye=i:ye.push.apply(ye,i))}n=s}if(i=!1,n!==2)continue}}if(n===1){La(t,0),Hl(t,e,0,!0);break}t:{switch(a=t,i=n,i){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Hl(a,e,He,!Nl);break t;case 2:ye=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(n=Hr+300-Ve(),10<n)){if(Hl(a,e,He,!Nl),Au(a,0,!0)!==0)break t;a.timeoutHandle=b0(Zd.bind(null,a,l,ye,oi,jr,e,He,ca,Ya,Nl,i,2,-0,0),n);break t}Zd(a,l,ye,oi,jr,e,He,ca,Ya,Nl,i,0,-0,0)}}break}while(!0);Je(t)}function Zd(t,e,l,a,n,i,s,d,y,O,N,H,C,_){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Pn={stylesheets:null,count:0,unsuspend:sg},qd(e),H=dg(),H!==null)){t.cancelPendingCommit=H(Pd.bind(null,t,e,i,l,a,n,s,d,y,N,1,C,_)),Hl(t,i,s,!O);return}Pd(t,e,i,l,a,n,s,d,y)}function Ry(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!be(i(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Hl(t,e,l,a){e&=~Br,e&=~ca,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var i=31-pe(n),s=1<<i;a[i]=-1,n&=~s}l!==0&&es(t,l,e)}function di(){return(xt&6)===0?(kn(0),!1):!0}function Gr(){if(dt!==null){if(Ct===0)var t=dt.return;else t=dt,ul=ea=null,ar(t),Ba=null,Hn=0,t=dt;for(;t!==null;)Od(t.alternate,t),t=t.return;dt=null}}function La(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,Ky(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),Gr(),jt=t,dt=l=ll(t.current,null),yt=e,Ct=0,Ee=null,Nl=!1,qa=rn(t,e),Ur=!1,Ya=He=Br=ca=Ul=Gt=0,ye=Vn=null,jr=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-pe(a),i=1<<n;e|=t[n],a&=~i}return hl=e,Bu(),l}function kd(t,e){ft=null,M.H=Iu,e===_n||e===Qu?(e=ro(),Ct=3):e===uo?(e=ro(),Ct=4):Ct=e===sd?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ee=e,dt===null&&(Gt=1,ni(t,De(e,t.current)))}function Kd(){var t=M.H;return M.H=Iu,t===null?Iu:t}function $d(){var t=M.A;return M.A=_y,t}function Xr(){Gt=4,Nl||(yt&4194048)!==yt&&je.current!==null||(qa=!0),(Ul&134217727)===0&&(ca&134217727)===0||jt===null||Hl(jt,yt,He,!1)}function Lr(t,e,l){var a=xt;xt|=2;var n=Kd(),i=$d();(jt!==t||yt!==e)&&(oi=null,La(t,e)),e=!1;var s=Gt;t:do try{if(Ct!==0&&dt!==null){var d=dt,y=Ee;switch(Ct){case 8:Gr(),s=6;break t;case 3:case 2:case 9:case 6:je.current===null&&(e=!0);var O=Ct;if(Ct=0,Ee=null,Qa(t,d,y,O),l&&qa){s=0;break t}break;default:O=Ct,Ct=0,Ee=null,Qa(t,d,y,O)}}My(),s=Gt;break}catch(N){kd(t,N)}while(!0);return e&&t.shellSuspendCounter++,ul=ea=null,xt=a,M.H=n,M.A=i,dt===null&&(jt=null,yt=0,Bu()),s}function My(){for(;dt!==null;)Jd(dt)}function Dy(t,e){var l=xt;xt|=2;var a=Kd(),n=$d();jt!==t||yt!==e?(oi=null,si=Ve()+500,La(t,e)):qa=rn(t,e);t:do try{if(Ct!==0&&dt!==null){e=dt;var i=Ee;e:switch(Ct){case 1:Ct=0,Ee=null,Qa(t,e,i,1);break;case 2:case 9:if(io(i)){Ct=0,Ee=null,Wd(e);break}e=function(){Ct!==2&&Ct!==9||jt!==t||(Ct=7),Je(t)},i.then(e,e);break t;case 3:Ct=7;break t;case 4:Ct=5;break t;case 7:io(i)?(Ct=0,Ee=null,Wd(e)):(Ct=0,Ee=null,Qa(t,e,i,7));break;case 5:var s=null;switch(dt.tag){case 26:s=dt.memoizedState;case 5:case 27:var d=dt;if(!s||D0(s)){Ct=0,Ee=null;var y=d.sibling;if(y!==null)dt=y;else{var O=d.return;O!==null?(dt=O,hi(O)):dt=null}break e}}Ct=0,Ee=null,Qa(t,e,i,5);break;case 6:Ct=0,Ee=null,Qa(t,e,i,6);break;case 8:Gr(),Gt=6;break t;default:throw Error(r(462))}}Ny();break}catch(N){kd(t,N)}while(!0);return ul=ea=null,M.H=a,M.A=n,xt=l,dt!==null?0:(jt=null,yt=0,Bu(),Gt)}function Ny(){for(;dt!==null&&!tm();)Jd(dt)}function Jd(t){var e=Ad(t.alternate,t,hl);t.memoizedProps=t.pendingProps,e===null?hi(t):dt=e}function Wd(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=gd(l,e,e.pendingProps,e.type,void 0,yt);break;case 11:e=gd(l,e,e.pendingProps,e.type.render,e.ref,yt);break;case 5:ar(e);default:Od(l,e),e=dt=Ws(e,hl),e=Ad(l,e,hl)}t.memoizedProps=t.pendingProps,e===null?hi(t):dt=e}function Qa(t,e,l,a){ul=ea=null,ar(e),Ba=null,Hn=0;var n=e.return;try{if(Ty(t,n,e,l,yt)){Gt=1,ni(t,De(l,t.current)),dt=null;return}}catch(i){if(n!==null)throw dt=n,i;Gt=1,ni(t,De(l,t.current)),dt=null;return}e.flags&32768?(bt||a===1?t=!0:qa||(yt&536870912)!==0?t=!1:(Nl=t=!0,(a===2||a===9||a===3||a===6)&&(a=je.current,a!==null&&a.tag===13&&(a.flags|=16384))),Fd(e,t)):hi(e)}function hi(t){var e=t;do{if((e.flags&32768)!==0){Fd(e,Nl);return}t=e.return;var l=Ey(e.alternate,e,hl);if(l!==null){dt=l;return}if(e=e.sibling,e!==null){dt=e;return}dt=e=t}while(e!==null);Gt===0&&(Gt=5)}function Fd(t,e){do{var l=Oy(t.alternate,t);if(l!==null){l.flags&=32767,dt=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){dt=t;return}dt=t=l}while(t!==null);Gt=6,dt=null}function Pd(t,e,l,a,n,i,s,d,y){t.cancelPendingCommit=null;do mi();while(ae!==0);if((xt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(i=e.lanes|e.childLanes,i|=Nc,sm(t,l,i,s,d,y),t===jt&&(dt=jt=null,yt=0),Ga=e,jl=t,Xa=l,wr=i,qr=n,Ld=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Hy(bu,function(){return a0(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=M.T,M.T=null,n=Q.p,Q.p=2,s=xt,xt|=4;try{xy(t,e,l)}finally{xt=s,Q.p=n,M.T=a}}ae=1,Id(),t0(),e0()}}function Id(){if(ae===1){ae=0;var t=jl,e=Ga,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=M.T,M.T=null;var a=Q.p;Q.p=2;var n=xt;xt|=4;try{jd(e,t);var i=tf,s=Gs(t.containerInfo),d=i.focusedElem,y=i.selectionRange;if(s!==d&&d&&d.ownerDocument&&Ys(d.ownerDocument.documentElement,d)){if(y!==null&&_c(d)){var O=y.start,N=y.end;if(N===void 0&&(N=O),"selectionStart"in d)d.selectionStart=O,d.selectionEnd=Math.min(N,d.value.length);else{var H=d.ownerDocument||document,C=H&&H.defaultView||window;if(C.getSelection){var _=C.getSelection(),it=d.textContent.length,at=Math.min(y.start,it),Rt=y.end===void 0?at:Math.min(y.end,it);!_.extend&&at>Rt&&(s=Rt,Rt=at,at=s);var T=qs(d,at),b=qs(d,Rt);if(T&&b&&(_.rangeCount!==1||_.anchorNode!==T.node||_.anchorOffset!==T.offset||_.focusNode!==b.node||_.focusOffset!==b.offset)){var E=H.createRange();E.setStart(T.node,T.offset),_.removeAllRanges(),at>Rt?(_.addRange(E),_.extend(b.node,b.offset)):(E.setEnd(b.node,b.offset),_.addRange(E))}}}}for(H=[],_=d;_=_.parentNode;)_.nodeType===1&&H.push({element:_,left:_.scrollLeft,top:_.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<H.length;d++){var B=H[d];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}Ci=!!Ir,tf=Ir=null}finally{xt=n,Q.p=a,M.T=l}}t.current=e,ae=2}}function t0(){if(ae===2){ae=0;var t=jl,e=Ga,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=M.T,M.T=null;var a=Q.p;Q.p=2;var n=xt;xt|=4;try{Dd(t,e.alternate,e)}finally{xt=n,Q.p=a,M.T=l}}ae=3}}function e0(){if(ae===4||ae===3){ae=0,em();var t=jl,e=Ga,l=Xa,a=Ld;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ae=5:(ae=0,Ga=jl=null,l0(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Bl=null),ic(l),e=e.stateNode,ve&&typeof ve.onCommitFiberRoot=="function")try{ve.onCommitFiberRoot(cn,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=M.T,n=Q.p,Q.p=2,M.T=null;try{for(var i=t.onRecoverableError,s=0;s<a.length;s++){var d=a[s];i(d.value,{componentStack:d.stack})}}finally{M.T=e,Q.p=n}}(Xa&3)!==0&&mi(),Je(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Yr?Zn++:(Zn=0,Yr=t):Zn=0,kn(0)}}function l0(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,xn(e)))}function mi(t){return Id(),t0(),e0(),a0()}function a0(){if(ae!==5)return!1;var t=jl,e=wr;wr=0;var l=ic(Xa),a=M.T,n=Q.p;try{Q.p=32>l?32:l,M.T=null,l=qr,qr=null;var i=jl,s=Xa;if(ae=0,Ga=jl=null,Xa=0,(xt&6)!==0)throw Error(r(331));var d=xt;if(xt|=4,Gd(i.current),wd(i,i.current,s,l),xt=d,kn(0,!1),ve&&typeof ve.onPostCommitFiberRoot=="function")try{ve.onPostCommitFiberRoot(cn,i)}catch{}return!0}finally{Q.p=n,M.T=a,l0(t,e)}}function n0(t,e,l){e=De(l,e),e=vr(t.stateNode,e,2),t=xl(t,e,2),t!==null&&(fn(t,2),Je(t))}function Nt(t,e,l){if(t.tag===3)n0(t,t,l);else for(;e!==null;){if(e.tag===3){n0(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Bl===null||!Bl.has(a))){t=De(l,t),l=rd(2),a=xl(e,l,2),a!==null&&(fd(l,a,e,t),fn(a,2),Je(a));break}}e=e.return}}function Qr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new zy;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Ur=!0,n.add(l),t=Uy.bind(null,t,e,l),e.then(t,t))}function Uy(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,jt===t&&(yt&l)===l&&(Gt===4||Gt===3&&(yt&62914560)===yt&&300>Ve()-Hr?(xt&2)===0&&La(t,0):Br|=l,Ya===yt&&(Ya=0)),Je(t)}function u0(t,e){e===0&&(e=ts()),t=Oa(t,e),t!==null&&(fn(t,e),Je(t))}function By(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),u0(t,l)}function jy(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(r(314))}a!==null&&a.delete(e),u0(t,l)}function Hy(t,e){return lc(t,e)}var yi=null,Va=null,Vr=!1,gi=!1,Zr=!1,ra=0;function Je(t){t!==Va&&t.next===null&&(Va===null?yi=Va=t:Va=Va.next=t),gi=!0,Vr||(Vr=!0,qy())}function kn(t,e){if(!Zr&&gi){Zr=!0;do for(var l=!1,a=yi;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var s=a.suspendedLanes,d=a.pingedLanes;i=(1<<31-pe(42|t)+1)-1,i&=n&~(s&~d),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,f0(a,i))}else i=yt,i=Au(a,a===jt?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||rn(a,i)||(l=!0,f0(a,i));a=a.next}while(l);Zr=!1}}function wy(){i0()}function i0(){gi=Vr=!1;var t=0;ra!==0&&(ky()&&(t=ra),ra=0);for(var e=Ve(),l=null,a=yi;a!==null;){var n=a.next,i=c0(a,e);i===0?(a.next=null,l===null?yi=n:l.next=n,n===null&&(Va=l)):(l=a,(t!==0||(i&3)!==0)&&(gi=!0)),a=n}kn(t)}function c0(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var s=31-pe(i),d=1<<s,y=n[s];y===-1?((d&l)===0||(d&a)!==0)&&(n[s]=fm(d,e)):y<=e&&(t.expiredLanes|=d),i&=~d}if(e=jt,l=yt,l=Au(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(Ct===2||Ct===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&ac(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||rn(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&ac(a),ic(l)){case 2:case 8:l=Ff;break;case 32:l=bu;break;case 268435456:l=Pf;break;default:l=bu}return a=r0.bind(null,t),l=lc(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&ac(a),t.callbackPriority=2,t.callbackNode=null,2}function r0(t,e){if(ae!==0&&ae!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(mi()&&t.callbackNode!==l)return null;var a=yt;return a=Au(t,t===jt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Vd(t,a,e),c0(t,Ve()),t.callbackNode!=null&&t.callbackNode===l?r0.bind(null,t):null)}function f0(t,e){if(mi())return null;Vd(t,e,!0)}function qy(){$y(function(){(xt&6)!==0?lc(Wf,wy):i0()})}function kr(){return ra===0&&(ra=If()),ra}function s0(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_u(""+t)}function o0(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Yy(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var i=s0((n[oe]||null).action),s=a.submitter;s&&(e=(e=s[oe]||null)?s0(e.formAction):s.getAttribute("formAction"),e!==null&&(i=e,s=null));var d=new Du("action","action",null,a,n);t.push({event:d,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ra!==0){var y=s?o0(n,s):new FormData(n);dr(l,{pending:!0,data:y,method:n.method,action:i},null,y)}}else typeof i=="function"&&(d.preventDefault(),y=s?o0(n,s):new FormData(n),dr(l,{pending:!0,data:y,method:n.method,action:i},i,y))},currentTarget:n}]})}}for(var Kr=0;Kr<Dc.length;Kr++){var $r=Dc[Kr],Gy=$r.toLowerCase(),Xy=$r[0].toUpperCase()+$r.slice(1);Ge(Gy,"on"+Xy)}Ge(Qs,"onAnimationEnd"),Ge(Vs,"onAnimationIteration"),Ge(Zs,"onAnimationStart"),Ge("dblclick","onDoubleClick"),Ge("focusin","onFocus"),Ge("focusout","onBlur"),Ge(ny,"onTransitionRun"),Ge(uy,"onTransitionStart"),Ge(iy,"onTransitionCancel"),Ge(ks,"onTransitionEnd"),ma("onMouseEnter",["mouseout","mouseover"]),ma("onMouseLeave",["mouseout","mouseover"]),ma("onPointerEnter",["pointerout","pointerover"]),ma("onPointerLeave",["pointerout","pointerover"]),kl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),kl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),kl("onBeforeInput",["compositionend","keypress","textInput","paste"]),kl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),kl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),kl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ly=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Kn));function d0(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var i=void 0;if(e)for(var s=a.length-1;0<=s;s--){var d=a[s],y=d.instance,O=d.currentTarget;if(d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(N){ai(N)}n.currentTarget=null,i=y}else for(s=0;s<a.length;s++){if(d=a[s],y=d.instance,O=d.currentTarget,d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(N){ai(N)}n.currentTarget=null,i=y}}}}function ht(t,e){var l=e[cc];l===void 0&&(l=e[cc]=new Set);var a=t+"__bubble";l.has(a)||(h0(e,t,2,!1),l.add(a))}function Jr(t,e,l){var a=0;e&&(a|=4),h0(l,t,a,e)}var vi="_reactListening"+Math.random().toString(36).slice(2);function Wr(t){if(!t[vi]){t[vi]=!0,us.forEach(function(l){l!=="selectionchange"&&(Ly.has(l)||Jr(l,!1,t),Jr(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[vi]||(e[vi]=!0,Jr("selectionchange",!1,e))}}function h0(t,e,l,a){switch(w0(e)){case 2:var n=yg;break;case 8:n=gg;break;default:n=of}l=n.bind(null,e,l,t),n=void 0,!pc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function Fr(t,e,l,a,n){var i=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var s=a.tag;if(s===3||s===4){var d=a.stateNode.containerInfo;if(d===n)break;if(s===4)for(s=a.return;s!==null;){var y=s.tag;if((y===3||y===4)&&s.stateNode.containerInfo===n)return;s=s.return}for(;d!==null;){if(s=oa(d),s===null)return;if(y=s.tag,y===5||y===6||y===26||y===27){a=i=s;continue t}d=d.parentNode}}a=a.return}bs(function(){var O=i,N=gc(l),H=[];t:{var C=Ks.get(t);if(C!==void 0){var _=Du,it=t;switch(t){case"keypress":if(Ru(l)===0)break t;case"keydown":case"keyup":_=Hm;break;case"focusin":it="focus",_=Ac;break;case"focusout":it="blur",_=Ac;break;case"beforeblur":case"afterblur":_=Ac;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":_=As;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":_=Om;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":_=Ym;break;case Qs:case Vs:case Zs:_=_m;break;case ks:_=Xm;break;case"scroll":case"scrollend":_=Am;break;case"wheel":_=Qm;break;case"copy":case"cut":case"paste":_=Rm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":_=Os;break;case"toggle":case"beforetoggle":_=Zm}var at=(e&4)!==0,Rt=!at&&(t==="scroll"||t==="scrollend"),T=at?C!==null?C+"Capture":null:C;at=[];for(var b=O,E;b!==null;){var B=b;if(E=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||E===null||T===null||(B=dn(b,T),B!=null&&at.push($n(b,B,E))),Rt)break;b=b.return}0<at.length&&(C=new _(C,it,null,l,N),H.push({event:C,listeners:at}))}}if((e&7)===0){t:{if(C=t==="mouseover"||t==="pointerover",_=t==="mouseout"||t==="pointerout",C&&l!==yc&&(it=l.relatedTarget||l.fromElement)&&(oa(it)||it[sa]))break t;if((_||C)&&(C=N.window===N?N:(C=N.ownerDocument)?C.defaultView||C.parentWindow:window,_?(it=l.relatedTarget||l.toElement,_=O,it=it?oa(it):null,it!==null&&(Rt=h(it),at=it.tag,it!==Rt||at!==5&&at!==27&&at!==6)&&(it=null)):(_=null,it=O),_!==it)){if(at=As,B="onMouseLeave",T="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(at=Os,B="onPointerLeave",T="onPointerEnter",b="pointer"),Rt=_==null?C:on(_),E=it==null?C:on(it),C=new at(B,b+"leave",_,l,N),C.target=Rt,C.relatedTarget=E,B=null,oa(N)===O&&(at=new at(T,b+"enter",it,l,N),at.target=E,at.relatedTarget=Rt,B=at),Rt=B,_&&it)e:{for(at=_,T=it,b=0,E=at;E;E=Za(E))b++;for(E=0,B=T;B;B=Za(B))E++;for(;0<b-E;)at=Za(at),b--;for(;0<E-b;)T=Za(T),E--;for(;b--;){if(at===T||T!==null&&at===T.alternate)break e;at=Za(at),T=Za(T)}at=null}else at=null;_!==null&&m0(H,C,_,at,!1),it!==null&&Rt!==null&&m0(H,Rt,it,at,!0)}}t:{if(C=O?on(O):window,_=C.nodeName&&C.nodeName.toLowerCase(),_==="select"||_==="input"&&C.type==="file")var J=Ns;else if(Ms(C))if(Us)J=ey;else{J=Im;var st=Pm}else _=C.nodeName,!_||_.toLowerCase()!=="input"||C.type!=="checkbox"&&C.type!=="radio"?O&&mc(O.elementType)&&(J=Ns):J=ty;if(J&&(J=J(t,O))){Ds(H,J,l,N);break t}st&&st(t,C,O),t==="focusout"&&O&&C.type==="number"&&O.memoizedProps.value!=null&&hc(C,"number",C.value)}switch(st=O?on(O):window,t){case"focusin":(Ms(st)||st.contentEditable==="true")&&(Ta=st,zc=O,Sn=null);break;case"focusout":Sn=zc=Ta=null;break;case"mousedown":Rc=!0;break;case"contextmenu":case"mouseup":case"dragend":Rc=!1,Xs(H,l,N);break;case"selectionchange":if(ay)break;case"keydown":case"keyup":Xs(H,l,N)}var I;if(Oc)t:{switch(t){case"compositionstart":var nt="onCompositionStart";break t;case"compositionend":nt="onCompositionEnd";break t;case"compositionupdate":nt="onCompositionUpdate";break t}nt=void 0}else Sa?zs(t,l)&&(nt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(nt="onCompositionStart");nt&&(xs&&l.locale!=="ko"&&(Sa||nt!=="onCompositionStart"?nt==="onCompositionEnd"&&Sa&&(I=Ss()):(Tl=N,bc="value"in Tl?Tl.value:Tl.textContent,Sa=!0)),st=pi(O,nt),0<st.length&&(nt=new Es(nt,t,null,l,N),H.push({event:nt,listeners:st}),I?nt.data=I:(I=Rs(l),I!==null&&(nt.data=I)))),(I=Km?$m(t,l):Jm(t,l))&&(nt=pi(O,"onBeforeInput"),0<nt.length&&(st=new Es("onBeforeInput","beforeinput",null,l,N),H.push({event:st,listeners:nt}),st.data=I)),Yy(H,t,O,l,N)}d0(H,e)})}function $n(t,e,l){return{instance:t,listener:e,currentTarget:l}}function pi(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=dn(t,l),n!=null&&a.unshift($n(t,n,i)),n=dn(t,e),n!=null&&a.push($n(t,n,i))),t.tag===3)return a;t=t.return}return[]}function Za(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function m0(t,e,l,a,n){for(var i=e._reactName,s=[];l!==null&&l!==a;){var d=l,y=d.alternate,O=d.stateNode;if(d=d.tag,y!==null&&y===a)break;d!==5&&d!==26&&d!==27||O===null||(y=O,n?(O=dn(l,i),O!=null&&s.unshift($n(l,O,y))):n||(O=dn(l,i),O!=null&&s.push($n(l,O,y)))),l=l.return}s.length!==0&&t.push({event:e,listeners:s})}var Qy=/\r\n?/g,Vy=/\u0000|\uFFFD/g;function y0(t){return(typeof t=="string"?t:""+t).replace(Qy,`
`).replace(Vy,"")}function g0(t,e){return e=y0(e),y0(t)===e}function bi(){}function zt(t,e,l,a,n,i){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||va(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&va(t,""+a);break;case"className":Ou(t,"class",a);break;case"tabIndex":Ou(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Ou(t,l,a);break;case"style":vs(t,a,i);break;case"data":if(e!=="object"){Ou(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&zt(t,e,"name",n.name,n,null),zt(t,e,"formEncType",n.formEncType,n,null),zt(t,e,"formMethod",n.formMethod,n,null),zt(t,e,"formTarget",n.formTarget,n,null)):(zt(t,e,"encType",n.encType,n,null),zt(t,e,"method",n.method,n,null),zt(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=bi);break;case"onScroll":a!=null&&ht("scroll",t);break;case"onScrollEnd":a!=null&&ht("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=_u(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":ht("beforetoggle",t),ht("toggle",t),Eu(t,"popover",a);break;case"xlinkActuate":tl(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":tl(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":tl(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":tl(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":tl(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":tl(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":tl(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":tl(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":tl(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Eu(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Sm.get(l)||l,Eu(t,l,a))}}function Pr(t,e,l,a,n,i){switch(l){case"style":vs(t,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"children":typeof a=="string"?va(t,a):(typeof a=="number"||typeof a=="bigint")&&va(t,""+a);break;case"onScroll":a!=null&&ht("scroll",t);break;case"onScrollEnd":a!=null&&ht("scrollend",t);break;case"onClick":a!=null&&(t.onclick=bi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!is.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),i=t[oe]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Eu(t,l,a)}}}function ne(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ht("error",t),ht("load",t);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var s=l[i];if(s!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:zt(t,e,i,s,l,null)}}n&&zt(t,e,"srcSet",l.srcSet,l,null),a&&zt(t,e,"src",l.src,l,null);return;case"input":ht("invalid",t);var d=i=s=n=null,y=null,O=null;for(a in l)if(l.hasOwnProperty(a)){var N=l[a];if(N!=null)switch(a){case"name":n=N;break;case"type":s=N;break;case"checked":y=N;break;case"defaultChecked":O=N;break;case"value":i=N;break;case"defaultValue":d=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,e));break;default:zt(t,e,a,N,l,null)}}hs(t,i,d,y,O,s,n,!1),xu(t);return;case"select":ht("invalid",t),a=s=i=null;for(n in l)if(l.hasOwnProperty(n)&&(d=l[n],d!=null))switch(n){case"value":i=d;break;case"defaultValue":s=d;break;case"multiple":a=d;default:zt(t,e,n,d,l,null)}e=i,l=s,t.multiple=!!a,e!=null?ga(t,!!a,e,!1):l!=null&&ga(t,!!a,l,!0);return;case"textarea":ht("invalid",t),i=n=a=null;for(s in l)if(l.hasOwnProperty(s)&&(d=l[s],d!=null))switch(s){case"value":a=d;break;case"defaultValue":n=d;break;case"children":i=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(r(91));break;default:zt(t,e,s,d,l,null)}ys(t,a,n,i),xu(t);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:zt(t,e,y,a,l,null)}return;case"dialog":ht("beforetoggle",t),ht("toggle",t),ht("cancel",t),ht("close",t);break;case"iframe":case"object":ht("load",t);break;case"video":case"audio":for(a=0;a<Kn.length;a++)ht(Kn[a],t);break;case"image":ht("error",t),ht("load",t);break;case"details":ht("toggle",t);break;case"embed":case"source":case"link":ht("error",t),ht("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in l)if(l.hasOwnProperty(O)&&(a=l[O],a!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:zt(t,e,O,a,l,null)}return;default:if(mc(e)){for(N in l)l.hasOwnProperty(N)&&(a=l[N],a!==void 0&&Pr(t,e,N,a,l,void 0));return}}for(d in l)l.hasOwnProperty(d)&&(a=l[d],a!=null&&zt(t,e,d,a,l,null))}function Zy(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,s=null,d=null,y=null,O=null,N=null;for(_ in l){var H=l[_];if(l.hasOwnProperty(_)&&H!=null)switch(_){case"checked":break;case"value":break;case"defaultValue":y=H;default:a.hasOwnProperty(_)||zt(t,e,_,null,a,H)}}for(var C in a){var _=a[C];if(H=l[C],a.hasOwnProperty(C)&&(_!=null||H!=null))switch(C){case"type":i=_;break;case"name":n=_;break;case"checked":O=_;break;case"defaultChecked":N=_;break;case"value":s=_;break;case"defaultValue":d=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,e));break;default:_!==H&&zt(t,e,C,_,a,H)}}dc(t,s,d,y,O,N,i,n);return;case"select":_=s=d=C=null;for(i in l)if(y=l[i],l.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":_=y;default:a.hasOwnProperty(i)||zt(t,e,i,null,a,y)}for(n in a)if(i=a[n],y=l[n],a.hasOwnProperty(n)&&(i!=null||y!=null))switch(n){case"value":C=i;break;case"defaultValue":d=i;break;case"multiple":s=i;default:i!==y&&zt(t,e,n,i,a,y)}e=d,l=s,a=_,C!=null?ga(t,!!l,C,!1):!!a!=!!l&&(e!=null?ga(t,!!l,e,!0):ga(t,!!l,l?[]:"",!1));return;case"textarea":_=C=null;for(d in l)if(n=l[d],l.hasOwnProperty(d)&&n!=null&&!a.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:zt(t,e,d,null,a,n)}for(s in a)if(n=a[s],i=l[s],a.hasOwnProperty(s)&&(n!=null||i!=null))switch(s){case"value":C=n;break;case"defaultValue":_=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==i&&zt(t,e,s,n,a,i)}ms(t,C,_);return;case"option":for(var it in l)if(C=l[it],l.hasOwnProperty(it)&&C!=null&&!a.hasOwnProperty(it))switch(it){case"selected":t.selected=!1;break;default:zt(t,e,it,null,a,C)}for(y in a)if(C=a[y],_=l[y],a.hasOwnProperty(y)&&C!==_&&(C!=null||_!=null))switch(y){case"selected":t.selected=C&&typeof C!="function"&&typeof C!="symbol";break;default:zt(t,e,y,C,a,_)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var at in l)C=l[at],l.hasOwnProperty(at)&&C!=null&&!a.hasOwnProperty(at)&&zt(t,e,at,null,a,C);for(O in a)if(C=a[O],_=l[O],a.hasOwnProperty(O)&&C!==_&&(C!=null||_!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(r(137,e));break;default:zt(t,e,O,C,a,_)}return;default:if(mc(e)){for(var Rt in l)C=l[Rt],l.hasOwnProperty(Rt)&&C!==void 0&&!a.hasOwnProperty(Rt)&&Pr(t,e,Rt,void 0,a,C);for(N in a)C=a[N],_=l[N],!a.hasOwnProperty(N)||C===_||C===void 0&&_===void 0||Pr(t,e,N,C,a,_);return}}for(var T in l)C=l[T],l.hasOwnProperty(T)&&C!=null&&!a.hasOwnProperty(T)&&zt(t,e,T,null,a,C);for(H in a)C=a[H],_=l[H],!a.hasOwnProperty(H)||C===_||C==null&&_==null||zt(t,e,H,C,a,_)}var Ir=null,tf=null;function Si(t){return t.nodeType===9?t:t.ownerDocument}function v0(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function p0(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function ef(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var lf=null;function ky(){var t=window.event;return t&&t.type==="popstate"?t===lf?!1:(lf=t,!0):(lf=null,!1)}var b0=typeof setTimeout=="function"?setTimeout:void 0,Ky=typeof clearTimeout=="function"?clearTimeout:void 0,S0=typeof Promise=="function"?Promise:void 0,$y=typeof queueMicrotask=="function"?queueMicrotask:typeof S0<"u"?function(t){return S0.resolve(null).then(t).catch(Jy)}:b0;function Jy(t){setTimeout(function(){throw t})}function wl(t){return t==="head"}function T0(t,e){var l=e,a=0,n=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var s=t.ownerDocument;if(l&1&&Jn(s.documentElement),l&2&&Jn(s.body),l&4)for(l=s.head,Jn(l),s=l.firstChild;s;){var d=s.nextSibling,y=s.nodeName;s[sn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&s.rel.toLowerCase()==="stylesheet"||l.removeChild(s),s=d}}if(n===0){t.removeChild(i),au(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);au(e)}function af(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":af(l),rc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Wy(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[sn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Le(t.nextSibling),t===null)break}return null}function Fy(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Le(t.nextSibling),t===null))return null;return t}function nf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Py(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Le(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var uf=null;function A0(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function E0(t,e,l){switch(e=Si(l),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function Jn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);rc(t)}var we=new Map,O0=new Set;function Ti(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var ml=Q.d;Q.d={f:Iy,r:tg,D:eg,C:lg,L:ag,m:ng,X:ig,S:ug,M:cg};function Iy(){var t=ml.f(),e=di();return t||e}function tg(t){var e=da(t);e!==null&&e.tag===5&&e.type==="form"?Vo(e):ml.r(t)}var ka=typeof document>"u"?null:document;function x0(t,e,l){var a=ka;if(a&&typeof e=="string"&&e){var n=Me(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),O0.has(n)||(O0.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),ne(e,"link",t),Pt(e),a.head.appendChild(e)))}}function eg(t){ml.D(t),x0("dns-prefetch",t,null)}function lg(t,e){ml.C(t,e),x0("preconnect",t,e)}function ag(t,e,l){ml.L(t,e,l);var a=ka;if(a&&t&&e){var n='link[rel="preload"][as="'+Me(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+Me(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+Me(l.imageSizes)+'"]')):n+='[href="'+Me(t)+'"]';var i=n;switch(e){case"style":i=Ka(t);break;case"script":i=$a(t)}we.has(i)||(t=z({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),we.set(i,t),a.querySelector(n)!==null||e==="style"&&a.querySelector(Wn(i))||e==="script"&&a.querySelector(Fn(i))||(e=a.createElement("link"),ne(e,"link",t),Pt(e),a.head.appendChild(e)))}}function ng(t,e){ml.m(t,e);var l=ka;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+Me(a)+'"][href="'+Me(t)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=$a(t)}if(!we.has(i)&&(t=z({rel:"modulepreload",href:t},e),we.set(i,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Fn(i)))return}a=l.createElement("link"),ne(a,"link",t),Pt(a),l.head.appendChild(a)}}}function ug(t,e,l){ml.S(t,e,l);var a=ka;if(a&&t){var n=ha(a).hoistableStyles,i=Ka(t);e=e||"default";var s=n.get(i);if(!s){var d={loading:0,preload:null};if(s=a.querySelector(Wn(i)))d.loading=5;else{t=z({rel:"stylesheet",href:t,"data-precedence":e},l),(l=we.get(i))&&cf(t,l);var y=s=a.createElement("link");Pt(y),ne(y,"link",t),y._p=new Promise(function(O,N){y.onload=O,y.onerror=N}),y.addEventListener("load",function(){d.loading|=1}),y.addEventListener("error",function(){d.loading|=2}),d.loading|=4,Ai(s,e,a)}s={type:"stylesheet",instance:s,count:1,state:d},n.set(i,s)}}}function ig(t,e){ml.X(t,e);var l=ka;if(l&&t){var a=ha(l).hoistableScripts,n=$a(t),i=a.get(n);i||(i=l.querySelector(Fn(n)),i||(t=z({src:t,async:!0},e),(e=we.get(n))&&rf(t,e),i=l.createElement("script"),Pt(i),ne(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function cg(t,e){ml.M(t,e);var l=ka;if(l&&t){var a=ha(l).hoistableScripts,n=$a(t),i=a.get(n);i||(i=l.querySelector(Fn(n)),i||(t=z({src:t,async:!0,type:"module"},e),(e=we.get(n))&&rf(t,e),i=l.createElement("script"),Pt(i),ne(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function C0(t,e,l,a){var n=(n=ut.current)?Ti(n):null;if(!n)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Ka(l.href),l=ha(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Ka(l.href);var i=ha(n).hoistableStyles,s=i.get(t);if(s||(n=n.ownerDocument||n,s={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,s),(i=n.querySelector(Wn(t)))&&!i._p&&(s.instance=i,s.state.loading=5),we.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},we.set(t,l),i||rg(n,t,l,s.state))),e&&a===null)throw Error(r(528,""));return s}if(e&&a!==null)throw Error(r(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=$a(l),l=ha(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Ka(t){return'href="'+Me(t)+'"'}function Wn(t){return'link[rel="stylesheet"]['+t+"]"}function _0(t){return z({},t,{"data-precedence":t.precedence,precedence:null})}function rg(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),ne(e,"link",l),Pt(e),t.head.appendChild(e))}function $a(t){return'[src="'+Me(t)+'"]'}function Fn(t){return"script[async]"+t}function z0(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+Me(l.href)+'"]');if(a)return e.instance=a,Pt(a),a;var n=z({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Pt(a),ne(a,"style",n),Ai(a,l.precedence,t),e.instance=a;case"stylesheet":n=Ka(l.href);var i=t.querySelector(Wn(n));if(i)return e.state.loading|=4,e.instance=i,Pt(i),i;a=_0(l),(n=we.get(n))&&cf(a,n),i=(t.ownerDocument||t).createElement("link"),Pt(i);var s=i;return s._p=new Promise(function(d,y){s.onload=d,s.onerror=y}),ne(i,"link",a),e.state.loading|=4,Ai(i,l.precedence,t),e.instance=i;case"script":return i=$a(l.src),(n=t.querySelector(Fn(i)))?(e.instance=n,Pt(n),n):(a=l,(n=we.get(i))&&(a=z({},l),rf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Pt(n),ne(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Ai(a,l.precedence,t));return e.instance}function Ai(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,s=0;s<a.length;s++){var d=a[s];if(d.dataset.precedence===e)i=d;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function cf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function rf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ei=null;function R0(t,e,l){if(Ei===null){var a=new Map,n=Ei=new Map;n.set(l,a)}else n=Ei,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var i=l[n];if(!(i[sn]||i[ie]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var s=i.getAttribute(e)||"";s=t+s;var d=a.get(s);d?d.push(i):a.set(s,[i])}}return a}function M0(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function fg(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function D0(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Pn=null;function sg(){}function og(t,e,l){if(Pn===null)throw Error(r(475));var a=Pn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Ka(l.href),i=t.querySelector(Wn(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Oi.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=i,Pt(i);return}i=t.ownerDocument||t,l=_0(l),(n=we.get(n))&&cf(l,n),i=i.createElement("link"),Pt(i);var s=i;s._p=new Promise(function(d,y){s.onload=d,s.onerror=y}),ne(i,"link",l),e.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Oi.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function dg(){if(Pn===null)throw Error(r(475));var t=Pn;return t.stylesheets&&t.count===0&&ff(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&ff(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Oi(){if(this.count--,this.count===0){if(this.stylesheets)ff(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var xi=null;function ff(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,xi=new Map,e.forEach(hg,t),xi=null,Oi.call(t))}function hg(t,e){if(!(e.state.loading&4)){var l=xi.get(t);if(l)var a=l.get(null);else{l=new Map,xi.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var s=n[i];(s.nodeName==="LINK"||s.getAttribute("media")!=="not all")&&(l.set(s.dataset.precedence,s),a=s)}a&&l.set(null,a)}n=e.instance,s=n.getAttribute("data-precedence"),i=l.get(s)||a,i===a&&l.set(null,n),l.set(s,n),this.count++,a=Oi.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var In={$$typeof:$,Provider:null,Consumer:null,_currentValue:et,_currentValue2:et,_threadCount:0};function mg(t,e,l,a,n,i,s,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=nc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=nc(0),this.hiddenUpdates=nc(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function N0(t,e,l,a,n,i,s,d,y,O,N,H){return t=new mg(t,e,l,s,d,y,O,H),e=1,i===!0&&(e|=24),i=Se(3,null,null,e),t.current=i,i.stateNode=t,e=Qc(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:e},Kc(i),t}function U0(t){return t?(t=xa,t):xa}function B0(t,e,l,a,n,i){n=U0(n),a.context===null?a.context=n:a.pendingContext=n,a=Ol(e),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=xl(t,a,e),l!==null&&(xe(l,t,e),Rn(l,t,e))}function j0(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function sf(t,e){j0(t,e),(t=t.alternate)&&j0(t,e)}function H0(t){if(t.tag===13){var e=Oa(t,67108864);e!==null&&xe(e,t,67108864),sf(t,67108864)}}var Ci=!0;function yg(t,e,l,a){var n=M.T;M.T=null;var i=Q.p;try{Q.p=2,of(t,e,l,a)}finally{Q.p=i,M.T=n}}function gg(t,e,l,a){var n=M.T;M.T=null;var i=Q.p;try{Q.p=8,of(t,e,l,a)}finally{Q.p=i,M.T=n}}function of(t,e,l,a){if(Ci){var n=df(a);if(n===null)Fr(t,e,a,_i,l),q0(t,a);else if(pg(n,t,e,l,a))a.stopPropagation();else if(q0(t,a),e&4&&-1<vg.indexOf(t)){for(;n!==null;){var i=da(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var s=Zl(i.pendingLanes);if(s!==0){var d=i;for(d.pendingLanes|=2,d.entangledLanes|=2;s;){var y=1<<31-pe(s);d.entanglements[1]|=y,s&=~y}Je(i),(xt&6)===0&&(si=Ve()+500,kn(0))}}break;case 13:d=Oa(i,2),d!==null&&xe(d,i,2),di(),sf(i,2)}if(i=df(a),i===null&&Fr(t,e,a,_i,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else Fr(t,e,a,null,l)}}function df(t){return t=gc(t),hf(t)}var _i=null;function hf(t){if(_i=null,t=oa(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=p(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _i=t,null}function w0(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(lm()){case Wf:return 2;case Ff:return 8;case bu:case am:return 32;case Pf:return 268435456;default:return 32}default:return 32}}var mf=!1,ql=null,Yl=null,Gl=null,tu=new Map,eu=new Map,Xl=[],vg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function q0(t,e){switch(t){case"focusin":case"focusout":ql=null;break;case"dragenter":case"dragleave":Yl=null;break;case"mouseover":case"mouseout":Gl=null;break;case"pointerover":case"pointerout":tu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":eu.delete(e.pointerId)}}function lu(t,e,l,a,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},e!==null&&(e=da(e),e!==null&&H0(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function pg(t,e,l,a,n){switch(e){case"focusin":return ql=lu(ql,t,e,l,a,n),!0;case"dragenter":return Yl=lu(Yl,t,e,l,a,n),!0;case"mouseover":return Gl=lu(Gl,t,e,l,a,n),!0;case"pointerover":var i=n.pointerId;return tu.set(i,lu(tu.get(i)||null,t,e,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,eu.set(i,lu(eu.get(i)||null,t,e,l,a,n)),!0}return!1}function Y0(t){var e=oa(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=p(l),e!==null){t.blockedOn=e,om(t.priority,function(){if(l.tag===13){var a=Oe();a=uc(a);var n=Oa(l,a);n!==null&&xe(n,l,a),sf(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zi(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=df(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);yc=a,l.target.dispatchEvent(a),yc=null}else return e=da(l),e!==null&&H0(e),t.blockedOn=l,!1;e.shift()}return!0}function G0(t,e,l){zi(t)&&l.delete(e)}function bg(){mf=!1,ql!==null&&zi(ql)&&(ql=null),Yl!==null&&zi(Yl)&&(Yl=null),Gl!==null&&zi(Gl)&&(Gl=null),tu.forEach(G0),eu.forEach(G0)}function Ri(t,e){t.blockedOn===e&&(t.blockedOn=null,mf||(mf=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,bg)))}var Mi=null;function X0(t){Mi!==t&&(Mi=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){Mi===t&&(Mi=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(hf(a||l)===null)continue;break}var i=da(l);i!==null&&(t.splice(e,3),e-=3,dr(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function au(t){function e(y){return Ri(y,t)}ql!==null&&Ri(ql,t),Yl!==null&&Ri(Yl,t),Gl!==null&&Ri(Gl,t),tu.forEach(e),eu.forEach(e);for(var l=0;l<Xl.length;l++){var a=Xl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<Xl.length&&(l=Xl[0],l.blockedOn===null);)Y0(l),l.blockedOn===null&&Xl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],s=n[oe]||null;if(typeof i=="function")s||X0(l);else if(s){var d=null;if(i&&i.hasAttribute("formAction")){if(n=i,s=i[oe]||null)d=s.formAction;else if(hf(n)!==null)continue}else d=s.action;typeof d=="function"?l[a+1]=d:(l.splice(a,3),a-=3),X0(l)}}}function yf(t){this._internalRoot=t}Di.prototype.render=yf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var l=e.current,a=Oe();B0(l,a,t,e,null,null)},Di.prototype.unmount=yf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;B0(t.current,2,null,t,null,null),di(),e[sa]=null}};function Di(t){this._internalRoot=t}Di.prototype.unstable_scheduleHydration=function(t){if(t){var e=as();t={blockedOn:null,target:t,priority:e};for(var l=0;l<Xl.length&&e!==0&&e<Xl[l].priority;l++);Xl.splice(l,0,t),l===0&&Y0(t)}};var L0=c.version;if(L0!=="19.1.0")throw Error(r(527,L0,"19.1.0"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=A(e),t=t!==null?v(t):null,t=t===null?null:t.stateNode,t};var Sg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ni=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ni.isDisabled&&Ni.supportsFiber)try{cn=Ni.inject(Sg),ve=Ni}catch{}}return uu.createRoot=function(t,e){if(!o(t))throw Error(r(299));var l=!1,a="",n=nd,i=ud,s=id,d=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(s=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=N0(t,1,!1,null,null,l,a,n,i,s,d,null),t[sa]=e.current,Wr(t),new yf(e)},uu.hydrateRoot=function(t,e,l){if(!o(t))throw Error(r(299));var a=!1,n="",i=nd,s=ud,d=id,y=null,O=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(s=l.onCaughtError),l.onRecoverableError!==void 0&&(d=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(O=l.formState)),e=N0(t,1,!0,e,l??null,a,n,i,s,d,y,O),e.context=U0(null),l=e.current,a=Oe(),a=uc(a),n=Ol(a),n.callback=null,xl(l,n,a),l=a,e.current.lanes=l,fn(e,l),Je(e),t[sa]=e.current,Wr(t),new Di(e)},uu.version="19.1.0",uu}var I0;function Mg(){if(I0)return pf.exports;I0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),pf.exports=Rg(),pf.exports}var Dg=Mg();const Ng=Eh(Dg),Ch=ct.createContext(),Ug=({children:u})=>{const[c,f]=ct.useState(()=>{const o=localStorage.getItem("darkMode");return o?JSON.parse(o):!1});ct.useEffect(()=>{localStorage.setItem("darkMode",JSON.stringify(c)),document.body.className=c?"dark-mode":"light-mode"},[c]);const r=()=>{f(!c)};return X.jsx(Ch.Provider,{value:{darkMode:c,toggleTheme:r},children:u})},th=u=>u,Bg=()=>{let u=th;return{configure(c){u=c},generate(c){return u(c)},reset(){u=th}}},jg=Bg();function fa(u,...c){const f=new URL(`https://mui.com/production-error/?code=${u}`);return c.forEach(r=>f.searchParams.append("args[]",r)),`Minified MUI error #${u}; visit ${f} for the full message.`}function ln(u){if(typeof u!="string")throw new Error(fa(7));return u.charAt(0).toUpperCase()+u.slice(1)}function _h(u){var c,f,r="";if(typeof u=="string"||typeof u=="number")r+=u;else if(typeof u=="object")if(Array.isArray(u)){var o=u.length;for(c=0;c<o;c++)u[c]&&(f=_h(u[c]))&&(r&&(r+=" "),r+=f)}else for(f in u)u[f]&&(r&&(r+=" "),r+=f);return r}function Hg(){for(var u,c,f=0,r="",o=arguments.length;f<o;f++)(u=arguments[f])&&(c=_h(u))&&(r&&(r+=" "),r+=c);return r}function wg(u,c,f=void 0){const r={};for(const o in u){const h=u[o];let p="",S=!0;for(let A=0;A<h.length;A+=1){const v=h[A];v&&(p+=(S===!0?"":" ")+c(v),S=!1,f&&f[v]&&(p+=" "+f[v]))}r[o]=p}return r}var Af={exports:{}},Mt={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var eh;function qg(){if(eh)return Mt;eh=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),p=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),v=Symbol.for("react.suspense_list"),z=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),U=Symbol.for("react.view_transition"),q=Symbol.for("react.client.reference");function w(x){if(typeof x=="object"&&x!==null){var V=x.$$typeof;switch(V){case u:switch(x=x.type,x){case f:case o:case r:case A:case v:case U:return x;default:switch(x=x&&x.$$typeof,x){case p:case S:case R:case z:return x;case h:return x;default:return V}}case c:return V}}}return Mt.ContextConsumer=h,Mt.ContextProvider=p,Mt.Element=u,Mt.ForwardRef=S,Mt.Fragment=f,Mt.Lazy=R,Mt.Memo=z,Mt.Portal=c,Mt.Profiler=o,Mt.StrictMode=r,Mt.Suspense=A,Mt.SuspenseList=v,Mt.isContextConsumer=function(x){return w(x)===h},Mt.isContextProvider=function(x){return w(x)===p},Mt.isElement=function(x){return typeof x=="object"&&x!==null&&x.$$typeof===u},Mt.isForwardRef=function(x){return w(x)===S},Mt.isFragment=function(x){return w(x)===f},Mt.isLazy=function(x){return w(x)===R},Mt.isMemo=function(x){return w(x)===z},Mt.isPortal=function(x){return w(x)===c},Mt.isProfiler=function(x){return w(x)===o},Mt.isStrictMode=function(x){return w(x)===r},Mt.isSuspense=function(x){return w(x)===A},Mt.isSuspenseList=function(x){return w(x)===v},Mt.isValidElementType=function(x){return typeof x=="string"||typeof x=="function"||x===f||x===o||x===r||x===A||x===v||typeof x=="object"&&x!==null&&(x.$$typeof===R||x.$$typeof===z||x.$$typeof===p||x.$$typeof===h||x.$$typeof===S||x.$$typeof===q||x.getModuleId!==void 0)},Mt.typeOf=w,Mt}var lh;function Yg(){return lh||(lh=1,Af.exports=qg()),Af.exports}var zh=Yg();function gl(u){if(typeof u!="object"||u===null)return!1;const c=Object.getPrototypeOf(u);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(Symbol.toStringTag in u)&&!(Symbol.iterator in u)}function Rh(u){if(ct.isValidElement(u)||zh.isValidElementType(u)||!gl(u))return u;const c={};return Object.keys(u).forEach(f=>{c[f]=Rh(u[f])}),c}function _e(u,c,f={clone:!0}){const r=f.clone?{...u}:u;return gl(u)&&gl(c)&&Object.keys(c).forEach(o=>{ct.isValidElement(c[o])||zh.isValidElementType(c[o])?r[o]=c[o]:gl(c[o])&&Object.prototype.hasOwnProperty.call(u,o)&&gl(u[o])?r[o]=_e(u[o],c[o],f):f.clone?r[o]=gl(c[o])?Rh(c[o]):c[o]:r[o]=c[o]}),r}function su(u,c){return c?_e(u,c,{clone:!1}):u}function Gg(u,c){if(!u.containerQueries)return c;const f=Object.keys(c).filter(r=>r.startsWith("@container")).sort((r,o)=>{var p,S;const h=/min-width:\s*([0-9.]+)/;return+(((p=r.match(h))==null?void 0:p[1])||0)-+(((S=o.match(h))==null?void 0:S[1])||0)});return f.length?f.reduce((r,o)=>{const h=c[o];return delete r[o],r[o]=h,r},{...c}):c}function Xg(u,c){return c==="@"||c.startsWith("@")&&(u.some(f=>c.startsWith(`@${f}`))||!!c.match(/^@\d/))}function Lg(u,c){const f=c.match(/^@([^/]+)?\/?(.+)?$/);if(!f)return null;const[,r,o]=f,h=Number.isNaN(+r)?r||0:+r;return u.containerQueries(o).up(h)}function Qg(u){const c=(h,p)=>h.replace("@media",p?`@container ${p}`:"@container");function f(h,p){h.up=(...S)=>c(u.breakpoints.up(...S),p),h.down=(...S)=>c(u.breakpoints.down(...S),p),h.between=(...S)=>c(u.breakpoints.between(...S),p),h.only=(...S)=>c(u.breakpoints.only(...S),p),h.not=(...S)=>{const A=c(u.breakpoints.not(...S),p);return A.includes("not all and")?A.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):A}}const r={},o=h=>(f(r,h),r);return f(o),{...u,containerQueries:o}}const Li={xs:0,sm:600,md:900,lg:1200,xl:1536},ah={keys:["xs","sm","md","lg","xl"],up:u=>`@media (min-width:${Li[u]}px)`},Vg={containerQueries:u=>({up:c=>{let f=typeof c=="number"?c:Li[c]||c;return typeof f=="number"&&(f=`${f}px`),u?`@container ${u} (min-width:${f})`:`@container (min-width:${f})`}})};function vl(u,c,f){const r=u.theme||{};if(Array.isArray(c)){const h=r.breakpoints||ah;return c.reduce((p,S,A)=>(p[h.up(h.keys[A])]=f(c[A]),p),{})}if(typeof c=="object"){const h=r.breakpoints||ah;return Object.keys(c).reduce((p,S)=>{if(Xg(h.keys,S)){const A=Lg(r.containerQueries?r:Vg,S);A&&(p[A]=f(c[S],S))}else if(Object.keys(h.values||Li).includes(S)){const A=h.up(S);p[A]=f(c[S],S)}else{const A=S;p[A]=c[A]}return p},{})}return f(c)}function Zg(u={}){var f;return((f=u.keys)==null?void 0:f.reduce((r,o)=>{const h=u.up(o);return r[h]={},r},{}))||{}}function kg(u,c){return u.reduce((f,r)=>{const o=f[r];return(!o||Object.keys(o).length===0)&&delete f[r],f},c)}function Qi(u,c,f=!0){if(!c||typeof c!="string")return null;if(u&&u.vars&&f){const r=`vars.${c}`.split(".").reduce((o,h)=>o&&o[h]?o[h]:null,u);if(r!=null)return r}return c.split(".").reduce((r,o)=>r&&r[o]!=null?r[o]:null,u)}function Gi(u,c,f,r=f){let o;return typeof u=="function"?o=u(f):Array.isArray(u)?o=u[f]||r:o=Qi(u,f)||r,c&&(o=c(o,r,u)),o}function Zt(u){const{prop:c,cssProperty:f=u.prop,themeKey:r,transform:o}=u,h=p=>{if(p[c]==null)return null;const S=p[c],A=p.theme,v=Qi(A,r)||{};return vl(p,S,R=>{let U=Gi(v,o,R);return R===U&&typeof R=="string"&&(U=Gi(v,o,`${c}${R==="default"?"":ln(R)}`,R)),f===!1?U:{[f]:U}})};return h.propTypes={},h.filterProps=[c],h}function Kg(u){const c={};return f=>(c[f]===void 0&&(c[f]=u(f)),c[f])}const $g={m:"margin",p:"padding"},Jg={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},nh={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Wg=Kg(u=>{if(u.length>2)if(nh[u])u=nh[u];else return[u];const[c,f]=u.split(""),r=$g[c],o=Jg[f]||"";return Array.isArray(o)?o.map(h=>r+h):[r+o]}),wf=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],qf=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...wf,...qf];function yu(u,c,f,r){const o=Qi(u,c,!0)??f;return typeof o=="number"||typeof o=="string"?h=>typeof h=="string"?h:typeof o=="string"?o.startsWith("var(")&&h===0?0:o.startsWith("var(")&&h===1?o:`calc(${h} * ${o})`:o*h:Array.isArray(o)?h=>{if(typeof h=="string")return h;const p=Math.abs(h),S=o[p];return h>=0?S:typeof S=="number"?-S:typeof S=="string"&&S.startsWith("var(")?`calc(-1 * ${S})`:`-${S}`}:typeof o=="function"?o:()=>{}}function Yf(u){return yu(u,"spacing",8)}function gu(u,c){return typeof c=="string"||c==null?c:u(c)}function Fg(u,c){return f=>u.reduce((r,o)=>(r[o]=gu(c,f),r),{})}function Pg(u,c,f,r){if(!c.includes(f))return null;const o=Wg(f),h=Fg(o,r),p=u[f];return vl(u,p,h)}function Mh(u,c){const f=Yf(u.theme);return Object.keys(u).map(r=>Pg(u,c,r,f)).reduce(su,{})}function Xt(u){return Mh(u,wf)}Xt.propTypes={};Xt.filterProps=wf;function Lt(u){return Mh(u,qf)}Lt.propTypes={};Lt.filterProps=qf;function Vi(...u){const c=u.reduce((r,o)=>(o.filterProps.forEach(h=>{r[h]=o}),r),{}),f=r=>Object.keys(r).reduce((o,h)=>c[h]?su(o,c[h](r)):o,{});return f.propTypes={},f.filterProps=u.reduce((r,o)=>r.concat(o.filterProps),[]),f}function qe(u){return typeof u!="number"?u:`${u}px solid`}function Ye(u,c){return Zt({prop:u,themeKey:"borders",transform:c})}const Ig=Ye("border",qe),t1=Ye("borderTop",qe),e1=Ye("borderRight",qe),l1=Ye("borderBottom",qe),a1=Ye("borderLeft",qe),n1=Ye("borderColor"),u1=Ye("borderTopColor"),i1=Ye("borderRightColor"),c1=Ye("borderBottomColor"),r1=Ye("borderLeftColor"),f1=Ye("outline",qe),s1=Ye("outlineColor"),Zi=u=>{if(u.borderRadius!==void 0&&u.borderRadius!==null){const c=yu(u.theme,"shape.borderRadius",4),f=r=>({borderRadius:gu(c,r)});return vl(u,u.borderRadius,f)}return null};Zi.propTypes={};Zi.filterProps=["borderRadius"];Vi(Ig,t1,e1,l1,a1,n1,u1,i1,c1,r1,Zi,f1,s1);const ki=u=>{if(u.gap!==void 0&&u.gap!==null){const c=yu(u.theme,"spacing",8),f=r=>({gap:gu(c,r)});return vl(u,u.gap,f)}return null};ki.propTypes={};ki.filterProps=["gap"];const Ki=u=>{if(u.columnGap!==void 0&&u.columnGap!==null){const c=yu(u.theme,"spacing",8),f=r=>({columnGap:gu(c,r)});return vl(u,u.columnGap,f)}return null};Ki.propTypes={};Ki.filterProps=["columnGap"];const $i=u=>{if(u.rowGap!==void 0&&u.rowGap!==null){const c=yu(u.theme,"spacing",8),f=r=>({rowGap:gu(c,r)});return vl(u,u.rowGap,f)}return null};$i.propTypes={};$i.filterProps=["rowGap"];const o1=Zt({prop:"gridColumn"}),d1=Zt({prop:"gridRow"}),h1=Zt({prop:"gridAutoFlow"}),m1=Zt({prop:"gridAutoColumns"}),y1=Zt({prop:"gridAutoRows"}),g1=Zt({prop:"gridTemplateColumns"}),v1=Zt({prop:"gridTemplateRows"}),p1=Zt({prop:"gridTemplateAreas"}),b1=Zt({prop:"gridArea"});Vi(ki,Ki,$i,o1,d1,h1,m1,y1,g1,v1,p1,b1);function tn(u,c){return c==="grey"?c:u}const S1=Zt({prop:"color",themeKey:"palette",transform:tn}),T1=Zt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:tn}),A1=Zt({prop:"backgroundColor",themeKey:"palette",transform:tn});Vi(S1,T1,A1);function Ce(u){return u<=1&&u!==0?`${u*100}%`:u}const E1=Zt({prop:"width",transform:Ce}),Gf=u=>{if(u.maxWidth!==void 0&&u.maxWidth!==null){const c=f=>{var o,h,p,S,A;const r=((p=(h=(o=u.theme)==null?void 0:o.breakpoints)==null?void 0:h.values)==null?void 0:p[f])||Li[f];return r?((A=(S=u.theme)==null?void 0:S.breakpoints)==null?void 0:A.unit)!=="px"?{maxWidth:`${r}${u.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:Ce(f)}};return vl(u,u.maxWidth,c)}return null};Gf.filterProps=["maxWidth"];const O1=Zt({prop:"minWidth",transform:Ce}),x1=Zt({prop:"height",transform:Ce}),C1=Zt({prop:"maxHeight",transform:Ce}),_1=Zt({prop:"minHeight",transform:Ce});Zt({prop:"size",cssProperty:"width",transform:Ce});Zt({prop:"size",cssProperty:"height",transform:Ce});const z1=Zt({prop:"boxSizing"});Vi(E1,Gf,O1,x1,C1,_1,z1);const Ji={border:{themeKey:"borders",transform:qe},borderTop:{themeKey:"borders",transform:qe},borderRight:{themeKey:"borders",transform:qe},borderBottom:{themeKey:"borders",transform:qe},borderLeft:{themeKey:"borders",transform:qe},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:qe},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Zi},color:{themeKey:"palette",transform:tn},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:tn},backgroundColor:{themeKey:"palette",transform:tn},p:{style:Lt},pt:{style:Lt},pr:{style:Lt},pb:{style:Lt},pl:{style:Lt},px:{style:Lt},py:{style:Lt},padding:{style:Lt},paddingTop:{style:Lt},paddingRight:{style:Lt},paddingBottom:{style:Lt},paddingLeft:{style:Lt},paddingX:{style:Lt},paddingY:{style:Lt},paddingInline:{style:Lt},paddingInlineStart:{style:Lt},paddingInlineEnd:{style:Lt},paddingBlock:{style:Lt},paddingBlockStart:{style:Lt},paddingBlockEnd:{style:Lt},m:{style:Xt},mt:{style:Xt},mr:{style:Xt},mb:{style:Xt},ml:{style:Xt},mx:{style:Xt},my:{style:Xt},margin:{style:Xt},marginTop:{style:Xt},marginRight:{style:Xt},marginBottom:{style:Xt},marginLeft:{style:Xt},marginX:{style:Xt},marginY:{style:Xt},marginInline:{style:Xt},marginInlineStart:{style:Xt},marginInlineEnd:{style:Xt},marginBlock:{style:Xt},marginBlockStart:{style:Xt},marginBlockEnd:{style:Xt},displayPrint:{cssProperty:!1,transform:u=>({"@media print":{display:u}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:ki},rowGap:{style:$i},columnGap:{style:Ki},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ce},maxWidth:{style:Gf},minWidth:{transform:Ce},height:{transform:Ce},maxHeight:{transform:Ce},minHeight:{transform:Ce},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function R1(...u){const c=u.reduce((r,o)=>r.concat(Object.keys(o)),[]),f=new Set(c);return u.every(r=>f.size===Object.keys(r).length)}function M1(u,c){return typeof u=="function"?u(c):u}function D1(){function u(f,r,o,h){const p={[f]:r,theme:o},S=h[f];if(!S)return{[f]:r};const{cssProperty:A=f,themeKey:v,transform:z,style:R}=S;if(r==null)return null;if(v==="typography"&&r==="inherit")return{[f]:r};const U=Qi(o,v)||{};return R?R(p):vl(p,r,w=>{let x=Gi(U,z,w);return w===x&&typeof w=="string"&&(x=Gi(U,z,`${f}${w==="default"?"":ln(w)}`,w)),A===!1?x:{[A]:x}})}function c(f){const{sx:r,theme:o={}}=f||{};if(!r)return null;const h=o.unstable_sxConfig??Ji;function p(S){let A=S;if(typeof S=="function")A=S(o);else if(typeof S!="object")return S;if(!A)return null;const v=Zg(o.breakpoints),z=Object.keys(v);let R=v;return Object.keys(A).forEach(U=>{const q=M1(A[U],o);if(q!=null)if(typeof q=="object")if(h[U])R=su(R,u(U,q,o,h));else{const w=vl({theme:o},q,x=>({[U]:x}));R1(w,q)?R[U]=c({sx:q,theme:o}):R=su(R,w)}else R=su(R,u(U,q,o,h))}),Gg(o,kg(z,R))}return Array.isArray(r)?r.map(p):p(r)}return c}const an=D1();an.filterProps=["sx"];function zf(){return zf=Object.assign?Object.assign.bind():function(u){for(var c=1;c<arguments.length;c++){var f=arguments[c];for(var r in f)({}).hasOwnProperty.call(f,r)&&(u[r]=f[r])}return u},zf.apply(null,arguments)}function N1(u){if(u.sheet)return u.sheet;for(var c=0;c<document.styleSheets.length;c++)if(document.styleSheets[c].ownerNode===u)return document.styleSheets[c]}function U1(u){var c=document.createElement("style");return c.setAttribute("data-emotion",u.key),u.nonce!==void 0&&c.setAttribute("nonce",u.nonce),c.appendChild(document.createTextNode("")),c.setAttribute("data-s",""),c}var B1=function(){function u(f){var r=this;this._insertTag=function(o){var h;r.tags.length===0?r.insertionPoint?h=r.insertionPoint.nextSibling:r.prepend?h=r.container.firstChild:h=r.before:h=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(o,h),r.tags.push(o)},this.isSpeedy=f.speedy===void 0?!0:f.speedy,this.tags=[],this.ctr=0,this.nonce=f.nonce,this.key=f.key,this.container=f.container,this.prepend=f.prepend,this.insertionPoint=f.insertionPoint,this.before=null}var c=u.prototype;return c.hydrate=function(r){r.forEach(this._insertTag)},c.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(U1(this));var o=this.tags[this.tags.length-1];if(this.isSpeedy){var h=N1(o);try{h.insertRule(r,h.cssRules.length)}catch{}}else o.appendChild(document.createTextNode(r));this.ctr++},c.flush=function(){this.tags.forEach(function(r){var o;return(o=r.parentNode)==null?void 0:o.removeChild(r)}),this.tags=[],this.ctr=0},u}(),fe="-ms-",Xi="-moz-",St="-webkit-",Dh="comm",Xf="rule",Lf="decl",j1="@import",Nh="@keyframes",H1="@layer",w1=Math.abs,Wi=String.fromCharCode,q1=Object.assign;function Y1(u,c){return ue(u,0)^45?(((c<<2^ue(u,0))<<2^ue(u,1))<<2^ue(u,2))<<2^ue(u,3):0}function Uh(u){return u.trim()}function G1(u,c){return(u=c.exec(u))?u[0]:u}function Tt(u,c,f){return u.replace(c,f)}function Rf(u,c){return u.indexOf(c)}function ue(u,c){return u.charCodeAt(c)|0}function ou(u,c,f){return u.slice(c,f)}function Fe(u){return u.length}function Qf(u){return u.length}function Ui(u,c){return c.push(u),u}function X1(u,c){return u.map(c).join("")}var Fi=1,nn=1,Bh=0,ge=0,$t=0,un="";function Pi(u,c,f,r,o,h,p){return{value:u,root:c,parent:f,type:r,props:o,children:h,line:Fi,column:nn,length:p,return:""}}function iu(u,c){return q1(Pi("",null,null,"",null,null,0),u,{length:-u.length},c)}function L1(){return $t}function Q1(){return $t=ge>0?ue(un,--ge):0,nn--,$t===10&&(nn=1,Fi--),$t}function ze(){return $t=ge<Bh?ue(un,ge++):0,nn++,$t===10&&(nn=1,Fi++),$t}function Ie(){return ue(un,ge)}function Hi(){return ge}function vu(u,c){return ou(un,u,c)}function du(u){switch(u){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function jh(u){return Fi=nn=1,Bh=Fe(un=u),ge=0,[]}function Hh(u){return un="",u}function wi(u){return Uh(vu(ge-1,Mf(u===91?u+2:u===40?u+1:u)))}function V1(u){for(;($t=Ie())&&$t<33;)ze();return du(u)>2||du($t)>3?"":" "}function Z1(u,c){for(;--c&&ze()&&!($t<48||$t>102||$t>57&&$t<65||$t>70&&$t<97););return vu(u,Hi()+(c<6&&Ie()==32&&ze()==32))}function Mf(u){for(;ze();)switch($t){case u:return ge;case 34:case 39:u!==34&&u!==39&&Mf($t);break;case 40:u===41&&Mf(u);break;case 92:ze();break}return ge}function k1(u,c){for(;ze()&&u+$t!==57;)if(u+$t===84&&Ie()===47)break;return"/*"+vu(c,ge-1)+"*"+Wi(u===47?u:ze())}function K1(u){for(;!du(Ie());)ze();return vu(u,ge)}function $1(u){return Hh(qi("",null,null,null,[""],u=jh(u),0,[0],u))}function qi(u,c,f,r,o,h,p,S,A){for(var v=0,z=0,R=p,U=0,q=0,w=0,x=1,V=1,Z=1,P=0,$="",K=o,L=h,F=r,W=$;V;)switch(w=P,P=ze()){case 40:if(w!=108&&ue(W,R-1)==58){Rf(W+=Tt(wi(P),"&","&\f"),"&\f")!=-1&&(Z=-1);break}case 34:case 39:case 91:W+=wi(P);break;case 9:case 10:case 13:case 32:W+=V1(w);break;case 92:W+=Z1(Hi()-1,7);continue;case 47:switch(Ie()){case 42:case 47:Ui(J1(k1(ze(),Hi()),c,f),A);break;default:W+="/"}break;case 123*x:S[v++]=Fe(W)*Z;case 125*x:case 59:case 0:switch(P){case 0:case 125:V=0;case 59+z:Z==-1&&(W=Tt(W,/\f/g,"")),q>0&&Fe(W)-R&&Ui(q>32?ih(W+";",r,f,R-1):ih(Tt(W," ","")+";",r,f,R-2),A);break;case 59:W+=";";default:if(Ui(F=uh(W,c,f,v,z,o,S,$,K=[],L=[],R),h),P===123)if(z===0)qi(W,c,F,F,K,h,R,S,L);else switch(U===99&&ue(W,3)===110?100:U){case 100:case 108:case 109:case 115:qi(u,F,F,r&&Ui(uh(u,F,F,0,0,o,S,$,o,K=[],R),L),o,L,R,S,r?K:L);break;default:qi(W,F,F,F,[""],L,0,S,L)}}v=z=q=0,x=Z=1,$=W="",R=p;break;case 58:R=1+Fe(W),q=w;default:if(x<1){if(P==123)--x;else if(P==125&&x++==0&&Q1()==125)continue}switch(W+=Wi(P),P*x){case 38:Z=z>0?1:(W+="\f",-1);break;case 44:S[v++]=(Fe(W)-1)*Z,Z=1;break;case 64:Ie()===45&&(W+=wi(ze())),U=Ie(),z=R=Fe($=W+=K1(Hi())),P++;break;case 45:w===45&&Fe(W)==2&&(x=0)}}return h}function uh(u,c,f,r,o,h,p,S,A,v,z){for(var R=o-1,U=o===0?h:[""],q=Qf(U),w=0,x=0,V=0;w<r;++w)for(var Z=0,P=ou(u,R+1,R=w1(x=p[w])),$=u;Z<q;++Z)($=Uh(x>0?U[Z]+" "+P:Tt(P,/&\f/g,U[Z])))&&(A[V++]=$);return Pi(u,c,f,o===0?Xf:S,A,v,z)}function J1(u,c,f){return Pi(u,c,f,Dh,Wi(L1()),ou(u,2,-2),0)}function ih(u,c,f,r){return Pi(u,c,f,Lf,ou(u,0,r),ou(u,r+1,-1),r)}function en(u,c){for(var f="",r=Qf(u),o=0;o<r;o++)f+=c(u[o],o,u,c)||"";return f}function W1(u,c,f,r){switch(u.type){case H1:if(u.children.length)break;case j1:case Lf:return u.return=u.return||u.value;case Dh:return"";case Nh:return u.return=u.value+"{"+en(u.children,r)+"}";case Xf:u.value=u.props.join(",")}return Fe(f=en(u.children,r))?u.return=u.value+"{"+f+"}":""}function F1(u){var c=Qf(u);return function(f,r,o,h){for(var p="",S=0;S<c;S++)p+=u[S](f,r,o,h)||"";return p}}function P1(u){return function(c){c.root||(c=c.return)&&u(c)}}function wh(u){var c=Object.create(null);return function(f){return c[f]===void 0&&(c[f]=u(f)),c[f]}}var I1=function(c,f,r){for(var o=0,h=0;o=h,h=Ie(),o===38&&h===12&&(f[r]=1),!du(h);)ze();return vu(c,ge)},tv=function(c,f){var r=-1,o=44;do switch(du(o)){case 0:o===38&&Ie()===12&&(f[r]=1),c[r]+=I1(ge-1,f,r);break;case 2:c[r]+=wi(o);break;case 4:if(o===44){c[++r]=Ie()===58?"&\f":"",f[r]=c[r].length;break}default:c[r]+=Wi(o)}while(o=ze());return c},ev=function(c,f){return Hh(tv(jh(c),f))},ch=new WeakMap,lv=function(c){if(!(c.type!=="rule"||!c.parent||c.length<1)){for(var f=c.value,r=c.parent,o=c.column===r.column&&c.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(c.props.length===1&&f.charCodeAt(0)!==58&&!ch.get(r))&&!o){ch.set(c,!0);for(var h=[],p=ev(f,h),S=r.props,A=0,v=0;A<p.length;A++)for(var z=0;z<S.length;z++,v++)c.props[v]=h[A]?p[A].replace(/&\f/g,S[z]):S[z]+" "+p[A]}}},av=function(c){if(c.type==="decl"){var f=c.value;f.charCodeAt(0)===108&&f.charCodeAt(2)===98&&(c.return="",c.value="")}};function qh(u,c){switch(Y1(u,c)){case 5103:return St+"print-"+u+u;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return St+u+u;case 5349:case 4246:case 4810:case 6968:case 2756:return St+u+Xi+u+fe+u+u;case 6828:case 4268:return St+u+fe+u+u;case 6165:return St+u+fe+"flex-"+u+u;case 5187:return St+u+Tt(u,/(\w+).+(:[^]+)/,St+"box-$1$2"+fe+"flex-$1$2")+u;case 5443:return St+u+fe+"flex-item-"+Tt(u,/flex-|-self/,"")+u;case 4675:return St+u+fe+"flex-line-pack"+Tt(u,/align-content|flex-|-self/,"")+u;case 5548:return St+u+fe+Tt(u,"shrink","negative")+u;case 5292:return St+u+fe+Tt(u,"basis","preferred-size")+u;case 6060:return St+"box-"+Tt(u,"-grow","")+St+u+fe+Tt(u,"grow","positive")+u;case 4554:return St+Tt(u,/([^-])(transform)/g,"$1"+St+"$2")+u;case 6187:return Tt(Tt(Tt(u,/(zoom-|grab)/,St+"$1"),/(image-set)/,St+"$1"),u,"")+u;case 5495:case 3959:return Tt(u,/(image-set\([^]*)/,St+"$1$`$1");case 4968:return Tt(Tt(u,/(.+:)(flex-)?(.*)/,St+"box-pack:$3"+fe+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+St+u+u;case 4095:case 3583:case 4068:case 2532:return Tt(u,/(.+)-inline(.+)/,St+"$1$2")+u;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Fe(u)-1-c>6)switch(ue(u,c+1)){case 109:if(ue(u,c+4)!==45)break;case 102:return Tt(u,/(.+:)(.+)-([^]+)/,"$1"+St+"$2-$3$1"+Xi+(ue(u,c+3)==108?"$3":"$2-$3"))+u;case 115:return~Rf(u,"stretch")?qh(Tt(u,"stretch","fill-available"),c)+u:u}break;case 4949:if(ue(u,c+1)!==115)break;case 6444:switch(ue(u,Fe(u)-3-(~Rf(u,"!important")&&10))){case 107:return Tt(u,":",":"+St)+u;case 101:return Tt(u,/(.+:)([^;!]+)(;|!.+)?/,"$1"+St+(ue(u,14)===45?"inline-":"")+"box$3$1"+St+"$2$3$1"+fe+"$2box$3")+u}break;case 5936:switch(ue(u,c+11)){case 114:return St+u+fe+Tt(u,/[svh]\w+-[tblr]{2}/,"tb")+u;case 108:return St+u+fe+Tt(u,/[svh]\w+-[tblr]{2}/,"tb-rl")+u;case 45:return St+u+fe+Tt(u,/[svh]\w+-[tblr]{2}/,"lr")+u}return St+u+fe+u+u}return u}var nv=function(c,f,r,o){if(c.length>-1&&!c.return)switch(c.type){case Lf:c.return=qh(c.value,c.length);break;case Nh:return en([iu(c,{value:Tt(c.value,"@","@"+St)})],o);case Xf:if(c.length)return X1(c.props,function(h){switch(G1(h,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return en([iu(c,{props:[Tt(h,/:(read-\w+)/,":"+Xi+"$1")]})],o);case"::placeholder":return en([iu(c,{props:[Tt(h,/:(plac\w+)/,":"+St+"input-$1")]}),iu(c,{props:[Tt(h,/:(plac\w+)/,":"+Xi+"$1")]}),iu(c,{props:[Tt(h,/:(plac\w+)/,fe+"input-$1")]})],o)}return""})}},uv=[nv],iv=function(c){var f=c.key;if(f==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(x){var V=x.getAttribute("data-emotion");V.indexOf(" ")!==-1&&(document.head.appendChild(x),x.setAttribute("data-s",""))})}var o=c.stylisPlugins||uv,h={},p,S=[];p=c.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+f+' "]'),function(x){for(var V=x.getAttribute("data-emotion").split(" "),Z=1;Z<V.length;Z++)h[V[Z]]=!0;S.push(x)});var A,v=[lv,av];{var z,R=[W1,P1(function(x){z.insert(x)})],U=F1(v.concat(o,R)),q=function(V){return en($1(V),U)};A=function(V,Z,P,$){z=P,q(V?V+"{"+Z.styles+"}":Z.styles),$&&(w.inserted[Z.name]=!0)}}var w={key:f,sheet:new B1({key:f,container:p,nonce:c.nonce,speedy:c.speedy,prepend:c.prepend,insertionPoint:c.insertionPoint}),nonce:c.nonce,inserted:h,registered:{},insert:A};return w.sheet.hydrate(S),w},cv=!0;function rv(u,c,f){var r="";return f.split(" ").forEach(function(o){u[o]!==void 0?c.push(u[o]+";"):o&&(r+=o+" ")}),r}var Yh=function(c,f,r){var o=c.key+"-"+f.name;(r===!1||cv===!1)&&c.registered[o]===void 0&&(c.registered[o]=f.styles)},fv=function(c,f,r){Yh(c,f,r);var o=c.key+"-"+f.name;if(c.inserted[f.name]===void 0){var h=f;do c.insert(f===h?"."+o:"",h,c.sheet,!0),h=h.next;while(h!==void 0)}};function sv(u){for(var c=0,f,r=0,o=u.length;o>=4;++r,o-=4)f=u.charCodeAt(r)&255|(u.charCodeAt(++r)&255)<<8|(u.charCodeAt(++r)&255)<<16|(u.charCodeAt(++r)&255)<<24,f=(f&65535)*1540483477+((f>>>16)*59797<<16),f^=f>>>24,c=(f&65535)*1540483477+((f>>>16)*59797<<16)^(c&65535)*1540483477+((c>>>16)*59797<<16);switch(o){case 3:c^=(u.charCodeAt(r+2)&255)<<16;case 2:c^=(u.charCodeAt(r+1)&255)<<8;case 1:c^=u.charCodeAt(r)&255,c=(c&65535)*1540483477+((c>>>16)*59797<<16)}return c^=c>>>13,c=(c&65535)*1540483477+((c>>>16)*59797<<16),((c^c>>>15)>>>0).toString(36)}var ov={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},dv=/[A-Z]|^ms/g,hv=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Gh=function(c){return c.charCodeAt(1)===45},rh=function(c){return c!=null&&typeof c!="boolean"},Ef=wh(function(u){return Gh(u)?u:u.replace(dv,"-$&").toLowerCase()}),fh=function(c,f){switch(c){case"animation":case"animationName":if(typeof f=="string")return f.replace(hv,function(r,o,h){return Pe={name:o,styles:h,next:Pe},o})}return ov[c]!==1&&!Gh(c)&&typeof f=="number"&&f!==0?f+"px":f};function hu(u,c,f){if(f==null)return"";var r=f;if(r.__emotion_styles!==void 0)return r;switch(typeof f){case"boolean":return"";case"object":{var o=f;if(o.anim===1)return Pe={name:o.name,styles:o.styles,next:Pe},o.name;var h=f;if(h.styles!==void 0){var p=h.next;if(p!==void 0)for(;p!==void 0;)Pe={name:p.name,styles:p.styles,next:Pe},p=p.next;var S=h.styles+";";return S}return mv(u,c,f)}case"function":{if(u!==void 0){var A=Pe,v=f(u);return Pe=A,hu(u,c,v)}break}}var z=f;if(c==null)return z;var R=c[z];return R!==void 0?R:z}function mv(u,c,f){var r="";if(Array.isArray(f))for(var o=0;o<f.length;o++)r+=hu(u,c,f[o])+";";else for(var h in f){var p=f[h];if(typeof p!="object"){var S=p;c!=null&&c[S]!==void 0?r+=h+"{"+c[S]+"}":rh(S)&&(r+=Ef(h)+":"+fh(h,S)+";")}else if(Array.isArray(p)&&typeof p[0]=="string"&&(c==null||c[p[0]]===void 0))for(var A=0;A<p.length;A++)rh(p[A])&&(r+=Ef(h)+":"+fh(h,p[A])+";");else{var v=hu(u,c,p);switch(h){case"animation":case"animationName":{r+=Ef(h)+":"+v+";";break}default:r+=h+"{"+v+"}"}}}return r}var sh=/label:\s*([^\s;{]+)\s*(;|$)/g,Pe;function Xh(u,c,f){if(u.length===1&&typeof u[0]=="object"&&u[0]!==null&&u[0].styles!==void 0)return u[0];var r=!0,o="";Pe=void 0;var h=u[0];if(h==null||h.raw===void 0)r=!1,o+=hu(f,c,h);else{var p=h;o+=p[0]}for(var S=1;S<u.length;S++)if(o+=hu(f,c,u[S]),r){var A=h;o+=A[S]}sh.lastIndex=0;for(var v="",z;(z=sh.exec(o))!==null;)v+="-"+z[1];var R=sv(o)+v;return{name:R,styles:o,next:Pe}}var yv=function(c){return c()},gv=K0.useInsertionEffect?K0.useInsertionEffect:!1,vv=gv||yv,Lh=ct.createContext(typeof HTMLElement<"u"?iv({key:"css"}):null);Lh.Provider;var pv=function(c){return ct.forwardRef(function(f,r){var o=ct.useContext(Lh);return c(f,o,r)})},bv=ct.createContext({}),Sv=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Tv=wh(function(u){return Sv.test(u)||u.charCodeAt(0)===111&&u.charCodeAt(1)===110&&u.charCodeAt(2)<91}),Av=Tv,Ev=function(c){return c!=="theme"},oh=function(c){return typeof c=="string"&&c.charCodeAt(0)>96?Av:Ev},dh=function(c,f,r){var o;if(f){var h=f.shouldForwardProp;o=c.__emotion_forwardProp&&h?function(p){return c.__emotion_forwardProp(p)&&h(p)}:h}return typeof o!="function"&&r&&(o=c.__emotion_forwardProp),o},Ov=function(c){var f=c.cache,r=c.serialized,o=c.isStringTag;return Yh(f,r,o),vv(function(){return fv(f,r,o)}),null},xv=function u(c,f){var r=c.__emotion_real===c,o=r&&c.__emotion_base||c,h,p;f!==void 0&&(h=f.label,p=f.target);var S=dh(c,f,r),A=S||oh(o),v=!A("as");return function(){var z=arguments,R=r&&c.__emotion_styles!==void 0?c.__emotion_styles.slice(0):[];if(h!==void 0&&R.push("label:"+h+";"),z[0]==null||z[0].raw===void 0)R.push.apply(R,z);else{var U=z[0];R.push(U[0]);for(var q=z.length,w=1;w<q;w++)R.push(z[w],U[w])}var x=pv(function(V,Z,P){var $=v&&V.as||o,K="",L=[],F=V;if(V.theme==null){F={};for(var W in V)F[W]=V[W];F.theme=ct.useContext(bv)}typeof V.className=="string"?K=rv(Z.registered,L,V.className):V.className!=null&&(K=V.className+" ");var gt=Xh(R.concat(L),Z.registered,F);K+=Z.key+"-"+gt.name,p!==void 0&&(K+=" "+p);var At=v&&S===void 0?oh($):A,m={};for(var k in V)v&&k==="as"||At(k)&&(m[k]=V[k]);return m.className=K,P&&(m.ref=P),ct.createElement(ct.Fragment,null,ct.createElement(Ov,{cache:Z,serialized:gt,isStringTag:typeof $=="string"}),ct.createElement($,m))});return x.displayName=h!==void 0?h:"Styled("+(typeof o=="string"?o:o.displayName||o.name||"Component")+")",x.defaultProps=c.defaultProps,x.__emotion_real=x,x.__emotion_base=o,x.__emotion_styles=R,x.__emotion_forwardProp=S,Object.defineProperty(x,"toString",{value:function(){return"."+p}}),x.withComponent=function(V,Z){var P=u(V,zf({},f,Z,{shouldForwardProp:dh(x,Z,!0)}));return P.apply(void 0,R)},x}},Cv=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],Df=xv.bind(null);Cv.forEach(function(u){Df[u]=Df(u)});function _v(u,c){return Df(u,c)}function zv(u,c){Array.isArray(u.__emotion_styles)&&(u.__emotion_styles=c(u.__emotion_styles))}const hh=[];function mh(u){return hh[0]=u,Xh(hh)}const Rv=u=>{const c=Object.keys(u).map(f=>({key:f,val:u[f]}))||[];return c.sort((f,r)=>f.val-r.val),c.reduce((f,r)=>({...f,[r.key]:r.val}),{})};function Mv(u){const{values:c={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:f="px",step:r=5,...o}=u,h=Rv(c),p=Object.keys(h);function S(U){return`@media (min-width:${typeof c[U]=="number"?c[U]:U}${f})`}function A(U){return`@media (max-width:${(typeof c[U]=="number"?c[U]:U)-r/100}${f})`}function v(U,q){const w=p.indexOf(q);return`@media (min-width:${typeof c[U]=="number"?c[U]:U}${f}) and (max-width:${(w!==-1&&typeof c[p[w]]=="number"?c[p[w]]:q)-r/100}${f})`}function z(U){return p.indexOf(U)+1<p.length?v(U,p[p.indexOf(U)+1]):S(U)}function R(U){const q=p.indexOf(U);return q===0?S(p[1]):q===p.length-1?A(p[q]):v(U,p[p.indexOf(U)+1]).replace("@media","@media not all and")}return{keys:p,values:h,up:S,down:A,between:v,only:z,not:R,unit:f,...o}}const Dv={borderRadius:4};function Qh(u=8,c=Yf({spacing:u})){if(u.mui)return u;const f=(...r)=>(r.length===0?[1]:r).map(h=>{const p=c(h);return typeof p=="number"?`${p}px`:p}).join(" ");return f.mui=!0,f}function Nv(u,c){var r;const f=this;if(f.vars){if(!((r=f.colorSchemes)!=null&&r[u])||typeof f.getColorSchemeSelector!="function")return{};let o=f.getColorSchemeSelector(u);return o==="&"?c:((o.includes("data-")||o.includes("."))&&(o=`*:where(${o.replace(/\s*&$/,"")}) &`),{[o]:c})}return f.palette.mode===u?c:{}}function Vh(u={},...c){const{breakpoints:f={},palette:r={},spacing:o,shape:h={},...p}=u,S=Mv(f),A=Qh(o);let v=_e({breakpoints:S,direction:"ltr",components:{},palette:{mode:"light",...r},spacing:A,shape:{...Dv,...h}},p);return v=Qg(v),v.applyStyles=Nv,v=c.reduce((z,R)=>_e(z,R),v),v.unstable_sxConfig={...Ji,...p==null?void 0:p.unstable_sxConfig},v.unstable_sx=function(R){return an({sx:R,theme:this})},v}const Uv={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function Zh(u,c,f="Mui"){const r=Uv[c];return r?`${f}-${r}`:`${jg.generate(u)}-${c}`}function Bv(u,c,f="Mui"){const r={};return c.forEach(o=>{r[o]=Zh(u,o,f)}),r}function kh(u){const{variants:c,...f}=u,r={variants:c,style:mh(f),isProcessed:!0};return r.style===f||c&&c.forEach(o=>{typeof o.style!="function"&&(o.style=mh(o.style))}),r}const jv=Vh();function Of(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}function Hv(u){return u?(c,f)=>f[u]:null}function wv(u,c,f){u.theme=Gv(u.theme)?f:u.theme[c]||u.theme}function Yi(u,c){const f=typeof c=="function"?c(u):c;if(Array.isArray(f))return f.flatMap(r=>Yi(u,r));if(Array.isArray(f==null?void 0:f.variants)){let r;if(f.isProcessed)r=f.style;else{const{variants:o,...h}=f;r=h}return Kh(u,f.variants,[r])}return f!=null&&f.isProcessed?f.style:f}function Kh(u,c,f=[]){var o;let r;t:for(let h=0;h<c.length;h+=1){const p=c[h];if(typeof p.props=="function"){if(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),!p.props(r))continue}else for(const S in p.props)if(u[S]!==p.props[S]&&((o=u.ownerState)==null?void 0:o[S])!==p.props[S])continue t;typeof p.style=="function"?(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),f.push(p.style(r))):f.push(p.style)}return f}function qv(u={}){const{themeId:c,defaultTheme:f=jv,rootShouldForwardProp:r=Of,slotShouldForwardProp:o=Of}=u;function h(S){wv(S,c,f)}return(S,A={})=>{zv(S,L=>L.filter(F=>F!==an));const{name:v,slot:z,skipVariantsResolver:R,skipSx:U,overridesResolver:q=Hv(Lv(z)),...w}=A,x=R!==void 0?R:z&&z!=="Root"&&z!=="root"||!1,V=U||!1;let Z=Of;z==="Root"||z==="root"?Z=r:z?Z=o:Xv(S)&&(Z=void 0);const P=_v(S,{shouldForwardProp:Z,label:Yv(),...w}),$=L=>{if(L.__emotion_real===L)return L;if(typeof L=="function")return function(W){return Yi(W,L)};if(gl(L)){const F=kh(L);return F.variants?function(gt){return Yi(gt,F)}:F.style}return L},K=(...L)=>{const F=[],W=L.map($),gt=[];if(F.push(h),v&&q&&gt.push(function(tt){var M,Q;const Dt=(Q=(M=tt.theme.components)==null?void 0:M[v])==null?void 0:Q.styleOverrides;if(!Dt)return null;const pt={};for(const et in Dt)pt[et]=Yi(tt,Dt[et]);return q(tt,pt)}),v&&!x&&gt.push(function(tt){var pt,M;const ot=tt.theme,Dt=(M=(pt=ot==null?void 0:ot.components)==null?void 0:pt[v])==null?void 0:M.variants;return Dt?Kh(tt,Dt):null}),V||gt.push(an),Array.isArray(W[0])){const k=W.shift(),tt=new Array(F.length).fill(""),ot=new Array(gt.length).fill("");let Dt;Dt=[...tt,...k,...ot],Dt.raw=[...tt,...k.raw,...ot],F.unshift(Dt)}const At=[...F,...W,...gt],m=P(...At);return S.muiName&&(m.muiName=S.muiName),m};return P.withConfig&&(K.withConfig=P.withConfig),K}}function Yv(u,c){return void 0}function Gv(u){for(const c in u)return!1;return!0}function Xv(u){return typeof u=="string"&&u.charCodeAt(0)>96}function Lv(u){return u&&u.charAt(0).toLowerCase()+u.slice(1)}function Nf(u,c){const f={...c};for(const r in u)if(Object.prototype.hasOwnProperty.call(u,r)){const o=r;if(o==="components"||o==="slots")f[o]={...u[o],...f[o]};else if(o==="componentsProps"||o==="slotProps"){const h=u[o],p=c[o];if(!p)f[o]=h||{};else if(!h)f[o]=p;else{f[o]={...p};for(const S in h)if(Object.prototype.hasOwnProperty.call(h,S)){const A=S;f[o][A]=Nf(h[A],p[A])}}}else f[o]===void 0&&(f[o]=u[o])}return f}function Qv(u,c=Number.MIN_SAFE_INTEGER,f=Number.MAX_SAFE_INTEGER){return Math.max(c,Math.min(u,f))}function Vf(u,c=0,f=1){return Qv(u,c,f)}function Vv(u){u=u.slice(1);const c=new RegExp(`.{1,${u.length>=6?2:1}}`,"g");let f=u.match(c);return f&&f[0].length===1&&(f=f.map(r=>r+r)),f?`rgb${f.length===4?"a":""}(${f.map((r,o)=>o<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function Ql(u){if(u.type)return u;if(u.charAt(0)==="#")return Ql(Vv(u));const c=u.indexOf("("),f=u.substring(0,c);if(!["rgb","rgba","hsl","hsla","color"].includes(f))throw new Error(fa(9,u));let r=u.substring(c+1,u.length-1),o;if(f==="color"){if(r=r.split(" "),o=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(o))throw new Error(fa(10,o))}else r=r.split(",");return r=r.map(h=>parseFloat(h)),{type:f,values:r,colorSpace:o}}const Zv=u=>{const c=Ql(u);return c.values.slice(0,3).map((f,r)=>c.type.includes("hsl")&&r!==0?`${f}%`:f).join(" ")},ru=(u,c)=>{try{return Zv(u)}catch{return u}};function Ii(u){const{type:c,colorSpace:f}=u;let{values:r}=u;return c.includes("rgb")?r=r.map((o,h)=>h<3?parseInt(o,10):o):c.includes("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),c.includes("color")?r=`${f} ${r.join(" ")}`:r=`${r.join(", ")}`,`${c}(${r})`}function $h(u){u=Ql(u);const{values:c}=u,f=c[0],r=c[1]/100,o=c[2]/100,h=r*Math.min(o,1-o),p=(v,z=(v+f/30)%12)=>o-h*Math.max(Math.min(z-3,9-z,1),-1);let S="rgb";const A=[Math.round(p(0)*255),Math.round(p(8)*255),Math.round(p(4)*255)];return u.type==="hsla"&&(S+="a",A.push(c[3])),Ii({type:S,values:A})}function Uf(u){u=Ql(u);let c=u.type==="hsl"||u.type==="hsla"?Ql($h(u)).values:u.values;return c=c.map(f=>(u.type!=="color"&&(f/=255),f<=.03928?f/12.92:((f+.055)/1.055)**2.4)),Number((.2126*c[0]+.7152*c[1]+.0722*c[2]).toFixed(3))}function kv(u,c){const f=Uf(u),r=Uf(c);return(Math.max(f,r)+.05)/(Math.min(f,r)+.05)}function Kv(u,c){return u=Ql(u),c=Vf(c),(u.type==="rgb"||u.type==="hsl")&&(u.type+="a"),u.type==="color"?u.values[3]=`/${c}`:u.values[3]=c,Ii(u)}function Bi(u,c,f){try{return Kv(u,c)}catch{return u}}function Zf(u,c){if(u=Ql(u),c=Vf(c),u.type.includes("hsl"))u.values[2]*=1-c;else if(u.type.includes("rgb")||u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]*=1-c;return Ii(u)}function Ut(u,c,f){try{return Zf(u,c)}catch{return u}}function kf(u,c){if(u=Ql(u),c=Vf(c),u.type.includes("hsl"))u.values[2]+=(100-u.values[2])*c;else if(u.type.includes("rgb"))for(let f=0;f<3;f+=1)u.values[f]+=(255-u.values[f])*c;else if(u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]+=(1-u.values[f])*c;return Ii(u)}function Bt(u,c,f){try{return kf(u,c)}catch{return u}}function $v(u,c=.15){return Uf(u)>.5?Zf(u,c):kf(u,c)}function ji(u,c,f){try{return $v(u,c)}catch{return u}}const Jv=ct.createContext(void 0);function Wv(u){const{theme:c,name:f,props:r}=u;if(!c||!c.components||!c.components[f])return r;const o=c.components[f];return o.defaultProps?Nf(o.defaultProps,r):!o.styleOverrides&&!o.variants?Nf(o,r):r}function Fv({props:u,name:c}){const f=ct.useContext(Jv);return Wv({props:u,name:c,theme:{components:f}})}const yh={theme:void 0};function Pv(u){let c,f;return function(o){let h=c;return(h===void 0||o.theme!==f)&&(yh.theme=o.theme,h=kh(u(yh)),c=h,f=o.theme),h}}function Iv(u=""){function c(...r){if(!r.length)return"";const o=r[0];return typeof o=="string"&&!o.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${u?`${u}-`:""}${o}${c(...r.slice(1))})`:`, ${o}`}return(r,...o)=>`var(--${u?`${u}-`:""}${r}${c(...o)})`}const gh=(u,c,f,r=[])=>{let o=u;c.forEach((h,p)=>{p===c.length-1?Array.isArray(o)?o[Number(h)]=f:o&&typeof o=="object"&&(o[h]=f):o&&typeof o=="object"&&(o[h]||(o[h]=r.includes(h)?[]:{}),o=o[h])})},tp=(u,c,f)=>{function r(o,h=[],p=[]){Object.entries(o).forEach(([S,A])=>{(!f||f&&!f([...h,S]))&&A!=null&&(typeof A=="object"&&Object.keys(A).length>0?r(A,[...h,S],Array.isArray(A)?[...p,S]:p):c([...h,S],A,p))})}r(u)},ep=(u,c)=>typeof c=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(r=>u.includes(r))||u[u.length-1].toLowerCase().includes("opacity")?c:`${c}px`:c;function xf(u,c){const{prefix:f,shouldSkipGeneratingVar:r}=c||{},o={},h={},p={};return tp(u,(S,A,v)=>{if((typeof A=="string"||typeof A=="number")&&(!r||!r(S,A))){const z=`--${f?`${f}-`:""}${S.join("-")}`,R=ep(S,A);Object.assign(o,{[z]:R}),gh(h,S,`var(${z})`,v),gh(p,S,`var(${z}, ${R})`,v)}},S=>S[0]==="vars"),{css:o,vars:h,varsWithDefaults:p}}function lp(u,c={}){const{getSelector:f=V,disableCssColorScheme:r,colorSchemeSelector:o}=c,{colorSchemes:h={},components:p,defaultColorScheme:S="light",...A}=u,{vars:v,css:z,varsWithDefaults:R}=xf(A,c);let U=R;const q={},{[S]:w,...x}=h;if(Object.entries(x||{}).forEach(([$,K])=>{const{vars:L,css:F,varsWithDefaults:W}=xf(K,c);U=_e(U,W),q[$]={css:F,vars:L}}),w){const{css:$,vars:K,varsWithDefaults:L}=xf(w,c);U=_e(U,L),q[S]={css:$,vars:K}}function V($,K){var F,W;let L=o;if(o==="class"&&(L=".%s"),o==="data"&&(L="[data-%s]"),o!=null&&o.startsWith("data-")&&!o.includes("%s")&&(L=`[${o}="%s"]`),$){if(L==="media")return u.defaultColorScheme===$?":root":{[`@media (prefers-color-scheme: ${((W=(F=h[$])==null?void 0:F.palette)==null?void 0:W.mode)||$})`]:{":root":K}};if(L)return u.defaultColorScheme===$?`:root, ${L.replace("%s",String($))}`:L.replace("%s",String($))}return":root"}return{vars:U,generateThemeVars:()=>{let $={...v};return Object.entries(q).forEach(([,{vars:K}])=>{$=_e($,K)}),$},generateStyleSheets:()=>{var gt,At;const $=[],K=u.defaultColorScheme||"light";function L(m,k){Object.keys(k).length&&$.push(typeof m=="string"?{[m]:{...k}}:m)}L(f(void 0,{...z}),z);const{[K]:F,...W}=q;if(F){const{css:m}=F,k=(At=(gt=h[K])==null?void 0:gt.palette)==null?void 0:At.mode,tt=!r&&k?{colorScheme:k,...m}:{...m};L(f(K,{...tt}),tt)}return Object.entries(W).forEach(([m,{css:k}])=>{var Dt,pt;const tt=(pt=(Dt=h[m])==null?void 0:Dt.palette)==null?void 0:pt.mode,ot=!r&&tt?{colorScheme:tt,...k}:{...k};L(f(m,{...ot}),ot)}),$}}}function ap(u){return function(f){return u==="media"?`@media (prefers-color-scheme: ${f})`:u?u.startsWith("data-")&&!u.includes("%s")?`[${u}="${f}"] &`:u==="class"?`.${f} &`:u==="data"?`[data-${f}] &`:`${u.replace("%s",f)} &`:"&"}}const mu={black:"#000",white:"#fff"},np={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},Ja={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},Wa={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},cu={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Fa={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Pa={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Ia={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function Jh(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:mu.white,default:mu.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const up=Jh();function Wh(){return{text:{primary:mu.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:mu.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const vh=Wh();function ph(u,c,f,r){const o=r.light||r,h=r.dark||r*1.5;u[c]||(u.hasOwnProperty(f)?u[c]=u[f]:c==="light"?u.light=kf(u.main,o):c==="dark"&&(u.dark=Zf(u.main,h)))}function ip(u="light"){return u==="dark"?{main:Fa[200],light:Fa[50],dark:Fa[400]}:{main:Fa[700],light:Fa[400],dark:Fa[800]}}function cp(u="light"){return u==="dark"?{main:Ja[200],light:Ja[50],dark:Ja[400]}:{main:Ja[500],light:Ja[300],dark:Ja[700]}}function rp(u="light"){return u==="dark"?{main:Wa[500],light:Wa[300],dark:Wa[700]}:{main:Wa[700],light:Wa[400],dark:Wa[800]}}function fp(u="light"){return u==="dark"?{main:Pa[400],light:Pa[300],dark:Pa[700]}:{main:Pa[700],light:Pa[500],dark:Pa[900]}}function sp(u="light"){return u==="dark"?{main:Ia[400],light:Ia[300],dark:Ia[700]}:{main:Ia[800],light:Ia[500],dark:Ia[900]}}function op(u="light"){return u==="dark"?{main:cu[400],light:cu[300],dark:cu[700]}:{main:"#ed6c02",light:cu[500],dark:cu[900]}}function Kf(u){const{mode:c="light",contrastThreshold:f=3,tonalOffset:r=.2,...o}=u,h=u.primary||ip(c),p=u.secondary||cp(c),S=u.error||rp(c),A=u.info||fp(c),v=u.success||sp(c),z=u.warning||op(c);function R(x){return kv(x,vh.text.primary)>=f?vh.text.primary:up.text.primary}const U=({color:x,name:V,mainShade:Z=500,lightShade:P=300,darkShade:$=700})=>{if(x={...x},!x.main&&x[Z]&&(x.main=x[Z]),!x.hasOwnProperty("main"))throw new Error(fa(11,V?` (${V})`:"",Z));if(typeof x.main!="string")throw new Error(fa(12,V?` (${V})`:"",JSON.stringify(x.main)));return ph(x,"light",P,r),ph(x,"dark",$,r),x.contrastText||(x.contrastText=R(x.main)),x};let q;return c==="light"?q=Jh():c==="dark"&&(q=Wh()),_e({common:{...mu},mode:c,primary:U({color:h,name:"primary"}),secondary:U({color:p,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:U({color:S,name:"error"}),warning:U({color:z,name:"warning"}),info:U({color:A,name:"info"}),success:U({color:v,name:"success"}),grey:np,contrastThreshold:f,getContrastText:R,augmentColor:U,tonalOffset:r,...q},o)}function dp(u){const c={};return Object.entries(u).forEach(r=>{const[o,h]=r;typeof h=="object"&&(c[o]=`${h.fontStyle?`${h.fontStyle} `:""}${h.fontVariant?`${h.fontVariant} `:""}${h.fontWeight?`${h.fontWeight} `:""}${h.fontStretch?`${h.fontStretch} `:""}${h.fontSize||""}${h.lineHeight?`/${h.lineHeight} `:""}${h.fontFamily||""}`)}),c}function hp(u,c){return{toolbar:{minHeight:56,[u.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[u.up("sm")]:{minHeight:64}},...c}}function mp(u){return Math.round(u*1e5)/1e5}const bh={textTransform:"uppercase"},Sh='"Roboto", "Helvetica", "Arial", sans-serif';function yp(u,c){const{fontFamily:f=Sh,fontSize:r=14,fontWeightLight:o=300,fontWeightRegular:h=400,fontWeightMedium:p=500,fontWeightBold:S=700,htmlFontSize:A=16,allVariants:v,pxToRem:z,...R}=typeof c=="function"?c(u):c,U=r/14,q=z||(V=>`${V/A*U}rem`),w=(V,Z,P,$,K)=>({fontFamily:f,fontWeight:V,fontSize:q(Z),lineHeight:P,...f===Sh?{letterSpacing:`${mp($/Z)}em`}:{},...K,...v}),x={h1:w(o,96,1.167,-1.5),h2:w(o,60,1.2,-.5),h3:w(h,48,1.167,0),h4:w(h,34,1.235,.25),h5:w(h,24,1.334,0),h6:w(p,20,1.6,.15),subtitle1:w(h,16,1.75,.15),subtitle2:w(p,14,1.57,.1),body1:w(h,16,1.5,.15),body2:w(h,14,1.43,.15),button:w(p,14,1.75,.4,bh),caption:w(h,12,1.66,.4),overline:w(h,12,2.66,1,bh),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return _e({htmlFontSize:A,pxToRem:q,fontFamily:f,fontSize:r,fontWeightLight:o,fontWeightRegular:h,fontWeightMedium:p,fontWeightBold:S,...x},R,{clone:!1})}const gp=.2,vp=.14,pp=.12;function wt(...u){return[`${u[0]}px ${u[1]}px ${u[2]}px ${u[3]}px rgba(0,0,0,${gp})`,`${u[4]}px ${u[5]}px ${u[6]}px ${u[7]}px rgba(0,0,0,${vp})`,`${u[8]}px ${u[9]}px ${u[10]}px ${u[11]}px rgba(0,0,0,${pp})`].join(",")}const bp=["none",wt(0,2,1,-1,0,1,1,0,0,1,3,0),wt(0,3,1,-2,0,2,2,0,0,1,5,0),wt(0,3,3,-2,0,3,4,0,0,1,8,0),wt(0,2,4,-1,0,4,5,0,0,1,10,0),wt(0,3,5,-1,0,5,8,0,0,1,14,0),wt(0,3,5,-1,0,6,10,0,0,1,18,0),wt(0,4,5,-2,0,7,10,1,0,2,16,1),wt(0,5,5,-3,0,8,10,1,0,3,14,2),wt(0,5,6,-3,0,9,12,1,0,3,16,2),wt(0,6,6,-3,0,10,14,1,0,4,18,3),wt(0,6,7,-4,0,11,15,1,0,4,20,3),wt(0,7,8,-4,0,12,17,2,0,5,22,4),wt(0,7,8,-4,0,13,19,2,0,5,24,4),wt(0,7,9,-4,0,14,21,2,0,5,26,4),wt(0,8,9,-5,0,15,22,2,0,6,28,5),wt(0,8,10,-5,0,16,24,2,0,6,30,5),wt(0,8,11,-5,0,17,26,2,0,6,32,5),wt(0,9,11,-5,0,18,28,2,0,7,34,6),wt(0,9,12,-6,0,19,29,2,0,7,36,6),wt(0,10,13,-6,0,20,31,3,0,8,38,7),wt(0,10,13,-6,0,21,33,3,0,8,40,7),wt(0,10,14,-6,0,22,35,3,0,8,42,7),wt(0,11,14,-7,0,23,36,3,0,9,44,8),wt(0,11,15,-7,0,24,38,3,0,9,46,8)],Sp={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Tp={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Th(u){return`${Math.round(u)}ms`}function Ap(u){if(!u)return 0;const c=u/36;return Math.min(Math.round((4+15*c**.25+c/5)*10),3e3)}function Ep(u){const c={...Sp,...u.easing},f={...Tp,...u.duration};return{getAutoHeightDuration:Ap,create:(o=["all"],h={})=>{const{duration:p=f.standard,easing:S=c.easeInOut,delay:A=0,...v}=h;return(Array.isArray(o)?o:[o]).map(z=>`${z} ${typeof p=="string"?p:Th(p)} ${S} ${typeof A=="string"?A:Th(A)}`).join(",")},...u,easing:c,duration:f}}const Op={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function xp(u){return gl(u)||typeof u>"u"||typeof u=="string"||typeof u=="boolean"||typeof u=="number"||Array.isArray(u)}function Fh(u={}){const c={...u};function f(r){const o=Object.entries(r);for(let h=0;h<o.length;h++){const[p,S]=o[h];!xp(S)||p.startsWith("unstable_")?delete r[p]:gl(S)&&(r[p]={...S},f(r[p]))}}return f(c),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(c,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Bf(u={},...c){const{breakpoints:f,mixins:r={},spacing:o,palette:h={},transitions:p={},typography:S={},shape:A,...v}=u;if(u.vars&&u.generateThemeVars===void 0)throw new Error(fa(20));const z=Kf(h),R=Vh(u);let U=_e(R,{mixins:hp(R.breakpoints,r),palette:z,shadows:bp.slice(),typography:yp(z,S),transitions:Ep(p),zIndex:{...Op}});return U=_e(U,v),U=c.reduce((q,w)=>_e(q,w),U),U.unstable_sxConfig={...Ji,...v==null?void 0:v.unstable_sxConfig},U.unstable_sx=function(w){return an({sx:w,theme:this})},U.toRuntimeSource=Fh,U}function Cp(u){let c;return u<1?c=5.11916*u**2:c=4.5*Math.log(u+1)+2,Math.round(c*10)/1e3}const _p=[...Array(25)].map((u,c)=>{if(c===0)return"none";const f=Cp(c);return`linear-gradient(rgba(255 255 255 / ${f}), rgba(255 255 255 / ${f}))`});function Ph(u){return{inputPlaceholder:u==="dark"?.5:.42,inputUnderline:u==="dark"?.7:.42,switchTrackDisabled:u==="dark"?.2:.12,switchTrack:u==="dark"?.3:.38}}function Ih(u){return u==="dark"?_p:[]}function zp(u){const{palette:c={mode:"light"},opacity:f,overlays:r,...o}=u,h=Kf(c);return{palette:h,opacity:{...Ph(h.mode),...f},overlays:r||Ih(h.mode),...o}}function Rp(u){var c;return!!u[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!u[0].match(/sxConfig$/)||u[0]==="palette"&&!!((c=u[1])!=null&&c.match(/(mode|contrastThreshold|tonalOffset)/))}const Mp=u=>[...[...Array(25)].map((c,f)=>`--${u?`${u}-`:""}overlays-${f}`),`--${u?`${u}-`:""}palette-AppBar-darkBg`,`--${u?`${u}-`:""}palette-AppBar-darkColor`],Dp=u=>(c,f)=>{const r=u.rootSelector||":root",o=u.colorSchemeSelector;let h=o;if(o==="class"&&(h=".%s"),o==="data"&&(h="[data-%s]"),o!=null&&o.startsWith("data-")&&!o.includes("%s")&&(h=`[${o}="%s"]`),u.defaultColorScheme===c){if(c==="dark"){const p={};return Mp(u.cssVarPrefix).forEach(S=>{p[S]=f[S],delete f[S]}),h==="media"?{[r]:f,"@media (prefers-color-scheme: dark)":{[r]:p}}:h?{[h.replace("%s",c)]:p,[`${r}, ${h.replace("%s",c)}`]:f}:{[r]:{...f,...p}}}if(h&&h!=="media")return`${r}, ${h.replace("%s",String(c))}`}else if(c){if(h==="media")return{[`@media (prefers-color-scheme: ${String(c)})`]:{[r]:f}};if(h)return h.replace("%s",String(c))}return r};function Np(u,c){c.forEach(f=>{u[f]||(u[f]={})})}function D(u,c,f){!u[c]&&f&&(u[c]=f)}function fu(u){return typeof u!="string"||!u.startsWith("hsl")?u:$h(u)}function yl(u,c){`${c}Channel`in u||(u[`${c}Channel`]=ru(fu(u[c])))}function Up(u){return typeof u=="number"?`${u}px`:typeof u=="string"||typeof u=="function"||Array.isArray(u)?u:"8px"}const We=u=>{try{return u()}catch{}},Bp=(u="mui")=>Iv(u);function Cf(u,c,f,r){if(!c)return;c=c===!0?{}:c;const o=r==="dark"?"dark":"light";if(!f){u[r]=zp({...c,palette:{mode:o,...c==null?void 0:c.palette}});return}const{palette:h,...p}=Bf({...f,palette:{mode:o,...c==null?void 0:c.palette}});return u[r]={...c,palette:h,opacity:{...Ph(o),...c==null?void 0:c.opacity},overlays:(c==null?void 0:c.overlays)||Ih(o)},p}function jp(u={},...c){const{colorSchemes:f={light:!0},defaultColorScheme:r,disableCssColorScheme:o=!1,cssVarPrefix:h="mui",shouldSkipGeneratingVar:p=Rp,colorSchemeSelector:S=f.light&&f.dark?"media":void 0,rootSelector:A=":root",...v}=u,z=Object.keys(f)[0],R=r||(f.light&&z!=="light"?"light":z),U=Bp(h),{[R]:q,light:w,dark:x,...V}=f,Z={...V};let P=q;if((R==="dark"&&!("dark"in f)||R==="light"&&!("light"in f))&&(P=!0),!P)throw new Error(fa(21,R));const $=Cf(Z,P,v,R);w&&!Z.light&&Cf(Z,w,void 0,"light"),x&&!Z.dark&&Cf(Z,x,void 0,"dark");let K={defaultColorScheme:R,...$,cssVarPrefix:h,colorSchemeSelector:S,rootSelector:A,getCssVar:U,colorSchemes:Z,font:{...dp($.typography),...$.font},spacing:Up(v.spacing)};Object.keys(K.colorSchemes).forEach(At=>{const m=K.colorSchemes[At].palette,k=tt=>{const ot=tt.split("-"),Dt=ot[1],pt=ot[2];return U(tt,m[Dt][pt])};if(m.mode==="light"&&(D(m.common,"background","#fff"),D(m.common,"onBackground","#000")),m.mode==="dark"&&(D(m.common,"background","#000"),D(m.common,"onBackground","#fff")),Np(m,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),m.mode==="light"){D(m.Alert,"errorColor",Ut(m.error.light,.6)),D(m.Alert,"infoColor",Ut(m.info.light,.6)),D(m.Alert,"successColor",Ut(m.success.light,.6)),D(m.Alert,"warningColor",Ut(m.warning.light,.6)),D(m.Alert,"errorFilledBg",k("palette-error-main")),D(m.Alert,"infoFilledBg",k("palette-info-main")),D(m.Alert,"successFilledBg",k("palette-success-main")),D(m.Alert,"warningFilledBg",k("palette-warning-main")),D(m.Alert,"errorFilledColor",We(()=>m.getContrastText(m.error.main))),D(m.Alert,"infoFilledColor",We(()=>m.getContrastText(m.info.main))),D(m.Alert,"successFilledColor",We(()=>m.getContrastText(m.success.main))),D(m.Alert,"warningFilledColor",We(()=>m.getContrastText(m.warning.main))),D(m.Alert,"errorStandardBg",Bt(m.error.light,.9)),D(m.Alert,"infoStandardBg",Bt(m.info.light,.9)),D(m.Alert,"successStandardBg",Bt(m.success.light,.9)),D(m.Alert,"warningStandardBg",Bt(m.warning.light,.9)),D(m.Alert,"errorIconColor",k("palette-error-main")),D(m.Alert,"infoIconColor",k("palette-info-main")),D(m.Alert,"successIconColor",k("palette-success-main")),D(m.Alert,"warningIconColor",k("palette-warning-main")),D(m.AppBar,"defaultBg",k("palette-grey-100")),D(m.Avatar,"defaultBg",k("palette-grey-400")),D(m.Button,"inheritContainedBg",k("palette-grey-300")),D(m.Button,"inheritContainedHoverBg",k("palette-grey-A100")),D(m.Chip,"defaultBorder",k("palette-grey-400")),D(m.Chip,"defaultAvatarColor",k("palette-grey-700")),D(m.Chip,"defaultIconColor",k("palette-grey-700")),D(m.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),D(m.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),D(m.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),D(m.LinearProgress,"primaryBg",Bt(m.primary.main,.62)),D(m.LinearProgress,"secondaryBg",Bt(m.secondary.main,.62)),D(m.LinearProgress,"errorBg",Bt(m.error.main,.62)),D(m.LinearProgress,"infoBg",Bt(m.info.main,.62)),D(m.LinearProgress,"successBg",Bt(m.success.main,.62)),D(m.LinearProgress,"warningBg",Bt(m.warning.main,.62)),D(m.Skeleton,"bg",`rgba(${k("palette-text-primaryChannel")} / 0.11)`),D(m.Slider,"primaryTrack",Bt(m.primary.main,.62)),D(m.Slider,"secondaryTrack",Bt(m.secondary.main,.62)),D(m.Slider,"errorTrack",Bt(m.error.main,.62)),D(m.Slider,"infoTrack",Bt(m.info.main,.62)),D(m.Slider,"successTrack",Bt(m.success.main,.62)),D(m.Slider,"warningTrack",Bt(m.warning.main,.62));const tt=ji(m.background.default,.8);D(m.SnackbarContent,"bg",tt),D(m.SnackbarContent,"color",We(()=>m.getContrastText(tt))),D(m.SpeedDialAction,"fabHoverBg",ji(m.background.paper,.15)),D(m.StepConnector,"border",k("palette-grey-400")),D(m.StepContent,"border",k("palette-grey-400")),D(m.Switch,"defaultColor",k("palette-common-white")),D(m.Switch,"defaultDisabledColor",k("palette-grey-100")),D(m.Switch,"primaryDisabledColor",Bt(m.primary.main,.62)),D(m.Switch,"secondaryDisabledColor",Bt(m.secondary.main,.62)),D(m.Switch,"errorDisabledColor",Bt(m.error.main,.62)),D(m.Switch,"infoDisabledColor",Bt(m.info.main,.62)),D(m.Switch,"successDisabledColor",Bt(m.success.main,.62)),D(m.Switch,"warningDisabledColor",Bt(m.warning.main,.62)),D(m.TableCell,"border",Bt(Bi(m.divider,1),.88)),D(m.Tooltip,"bg",Bi(m.grey[700],.92))}if(m.mode==="dark"){D(m.Alert,"errorColor",Bt(m.error.light,.6)),D(m.Alert,"infoColor",Bt(m.info.light,.6)),D(m.Alert,"successColor",Bt(m.success.light,.6)),D(m.Alert,"warningColor",Bt(m.warning.light,.6)),D(m.Alert,"errorFilledBg",k("palette-error-dark")),D(m.Alert,"infoFilledBg",k("palette-info-dark")),D(m.Alert,"successFilledBg",k("palette-success-dark")),D(m.Alert,"warningFilledBg",k("palette-warning-dark")),D(m.Alert,"errorFilledColor",We(()=>m.getContrastText(m.error.dark))),D(m.Alert,"infoFilledColor",We(()=>m.getContrastText(m.info.dark))),D(m.Alert,"successFilledColor",We(()=>m.getContrastText(m.success.dark))),D(m.Alert,"warningFilledColor",We(()=>m.getContrastText(m.warning.dark))),D(m.Alert,"errorStandardBg",Ut(m.error.light,.9)),D(m.Alert,"infoStandardBg",Ut(m.info.light,.9)),D(m.Alert,"successStandardBg",Ut(m.success.light,.9)),D(m.Alert,"warningStandardBg",Ut(m.warning.light,.9)),D(m.Alert,"errorIconColor",k("palette-error-main")),D(m.Alert,"infoIconColor",k("palette-info-main")),D(m.Alert,"successIconColor",k("palette-success-main")),D(m.Alert,"warningIconColor",k("palette-warning-main")),D(m.AppBar,"defaultBg",k("palette-grey-900")),D(m.AppBar,"darkBg",k("palette-background-paper")),D(m.AppBar,"darkColor",k("palette-text-primary")),D(m.Avatar,"defaultBg",k("palette-grey-600")),D(m.Button,"inheritContainedBg",k("palette-grey-800")),D(m.Button,"inheritContainedHoverBg",k("palette-grey-700")),D(m.Chip,"defaultBorder",k("palette-grey-700")),D(m.Chip,"defaultAvatarColor",k("palette-grey-300")),D(m.Chip,"defaultIconColor",k("palette-grey-300")),D(m.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),D(m.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),D(m.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),D(m.LinearProgress,"primaryBg",Ut(m.primary.main,.5)),D(m.LinearProgress,"secondaryBg",Ut(m.secondary.main,.5)),D(m.LinearProgress,"errorBg",Ut(m.error.main,.5)),D(m.LinearProgress,"infoBg",Ut(m.info.main,.5)),D(m.LinearProgress,"successBg",Ut(m.success.main,.5)),D(m.LinearProgress,"warningBg",Ut(m.warning.main,.5)),D(m.Skeleton,"bg",`rgba(${k("palette-text-primaryChannel")} / 0.13)`),D(m.Slider,"primaryTrack",Ut(m.primary.main,.5)),D(m.Slider,"secondaryTrack",Ut(m.secondary.main,.5)),D(m.Slider,"errorTrack",Ut(m.error.main,.5)),D(m.Slider,"infoTrack",Ut(m.info.main,.5)),D(m.Slider,"successTrack",Ut(m.success.main,.5)),D(m.Slider,"warningTrack",Ut(m.warning.main,.5));const tt=ji(m.background.default,.98);D(m.SnackbarContent,"bg",tt),D(m.SnackbarContent,"color",We(()=>m.getContrastText(tt))),D(m.SpeedDialAction,"fabHoverBg",ji(m.background.paper,.15)),D(m.StepConnector,"border",k("palette-grey-600")),D(m.StepContent,"border",k("palette-grey-600")),D(m.Switch,"defaultColor",k("palette-grey-300")),D(m.Switch,"defaultDisabledColor",k("palette-grey-600")),D(m.Switch,"primaryDisabledColor",Ut(m.primary.main,.55)),D(m.Switch,"secondaryDisabledColor",Ut(m.secondary.main,.55)),D(m.Switch,"errorDisabledColor",Ut(m.error.main,.55)),D(m.Switch,"infoDisabledColor",Ut(m.info.main,.55)),D(m.Switch,"successDisabledColor",Ut(m.success.main,.55)),D(m.Switch,"warningDisabledColor",Ut(m.warning.main,.55)),D(m.TableCell,"border",Ut(Bi(m.divider,1),.68)),D(m.Tooltip,"bg",Bi(m.grey[700],.92))}yl(m.background,"default"),yl(m.background,"paper"),yl(m.common,"background"),yl(m.common,"onBackground"),yl(m,"divider"),Object.keys(m).forEach(tt=>{const ot=m[tt];tt!=="tonalOffset"&&ot&&typeof ot=="object"&&(ot.main&&D(m[tt],"mainChannel",ru(fu(ot.main))),ot.light&&D(m[tt],"lightChannel",ru(fu(ot.light))),ot.dark&&D(m[tt],"darkChannel",ru(fu(ot.dark))),ot.contrastText&&D(m[tt],"contrastTextChannel",ru(fu(ot.contrastText))),tt==="text"&&(yl(m[tt],"primary"),yl(m[tt],"secondary")),tt==="action"&&(ot.active&&yl(m[tt],"active"),ot.selected&&yl(m[tt],"selected")))})}),K=c.reduce((At,m)=>_e(At,m),K);const L={prefix:h,disableCssColorScheme:o,shouldSkipGeneratingVar:p,getSelector:Dp(K)},{vars:F,generateThemeVars:W,generateStyleSheets:gt}=lp(K,L);return K.vars=F,Object.entries(K.colorSchemes[K.defaultColorScheme]).forEach(([At,m])=>{K[At]=m}),K.generateThemeVars=W,K.generateStyleSheets=gt,K.generateSpacing=function(){return Qh(v.spacing,Yf(this))},K.getColorSchemeSelector=ap(S),K.spacing=K.generateSpacing(),K.shouldSkipGeneratingVar=p,K.unstable_sxConfig={...Ji,...v==null?void 0:v.unstable_sxConfig},K.unstable_sx=function(m){return an({sx:m,theme:this})},K.toRuntimeSource=Fh,K}function Ah(u,c,f){u.colorSchemes&&f&&(u.colorSchemes[c]={...f!==!0&&f,palette:Kf({...f===!0?{}:f.palette,mode:c})})}function Hp(u={},...c){const{palette:f,cssVariables:r=!1,colorSchemes:o=f?void 0:{light:!0},defaultColorScheme:h=f==null?void 0:f.mode,...p}=u,S=h||"light",A=o==null?void 0:o[S],v={...o,...f?{[S]:{...typeof A!="boolean"&&A,palette:f}}:void 0};if(r===!1){if(!("colorSchemes"in u))return Bf(u,...c);let z=f;"palette"in u||v[S]&&(v[S]!==!0?z=v[S].palette:S==="dark"&&(z={mode:"dark"}));const R=Bf({...u,palette:z},...c);return R.defaultColorScheme=S,R.colorSchemes=v,R.palette.mode==="light"&&(R.colorSchemes.light={...v.light!==!0&&v.light,palette:R.palette},Ah(R,"dark",v.dark)),R.palette.mode==="dark"&&(R.colorSchemes.dark={...v.dark!==!0&&v.dark,palette:R.palette},Ah(R,"light",v.light)),R}return!f&&!("light"in v)&&S==="light"&&(v.light=!0),jp({...p,colorSchemes:v,defaultColorScheme:S,...typeof r!="boolean"&&r},...c)}const wp=Hp(),qp="$$material";function Yp(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}const Gp=u=>Yp(u)&&u!=="classes",Xp=qv({themeId:qp,defaultTheme:wp,rootShouldForwardProp:Gp}),Lp=Pv;function Qp(u){return Fv(u)}function Vp(u){return Zh("MuiSvgIcon",u)}Bv("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const Zp=u=>{const{color:c,fontSize:f,classes:r}=u,o={root:["root",c!=="inherit"&&`color${ln(c)}`,`fontSize${ln(f)}`]};return wg(o,Vp,r)},kp=Xp("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(u,c)=>{const{ownerState:f}=u;return[c.root,f.color!=="inherit"&&c[`color${ln(f.color)}`],c[`fontSize${ln(f.fontSize)}`]]}})(Lp(({theme:u})=>{var c,f,r,o,h,p,S,A,v,z,R,U,q,w;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(o=(c=u.transitions)==null?void 0:c.create)==null?void 0:o.call(c,"fill",{duration:(r=(f=(u.vars??u).transitions)==null?void 0:f.duration)==null?void 0:r.shorter}),variants:[{props:x=>!x.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((p=(h=u.typography)==null?void 0:h.pxToRem)==null?void 0:p.call(h,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((A=(S=u.typography)==null?void 0:S.pxToRem)==null?void 0:A.call(S,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((z=(v=u.typography)==null?void 0:v.pxToRem)==null?void 0:z.call(v,35))||"2.1875rem"}},...Object.entries((u.vars??u).palette).filter(([,x])=>x&&x.main).map(([x])=>{var V,Z;return{props:{color:x},style:{color:(Z=(V=(u.vars??u).palette)==null?void 0:V[x])==null?void 0:Z.main}}}),{props:{color:"action"},style:{color:(U=(R=(u.vars??u).palette)==null?void 0:R.action)==null?void 0:U.active}},{props:{color:"disabled"},style:{color:(w=(q=(u.vars??u).palette)==null?void 0:q.action)==null?void 0:w.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),jf=ct.forwardRef(function(c,f){const r=Qp({props:c,name:"MuiSvgIcon"}),{children:o,className:h,color:p="inherit",component:S="svg",fontSize:A="medium",htmlColor:v,inheritViewBox:z=!1,titleAccess:R,viewBox:U="0 0 24 24",...q}=r,w=ct.isValidElement(o)&&o.type==="svg",x={...r,color:p,component:S,fontSize:A,instanceFontSize:c.fontSize,inheritViewBox:z,viewBox:U,hasSvgAsChild:w},V={};z||(V.viewBox=U);const Z=Zp(x);return X.jsxs(kp,{as:S,className:Hg(Z.root,h),focusable:"false",color:v,"aria-hidden":R?void 0:!0,role:R?"img":void 0,ref:f,...V,...q,...w&&o.props,ownerState:x,children:[w?o.props.children:o,R?X.jsx("title",{children:R}):null]})});jf.muiName="SvgIcon";function Vl(u,c){function f(r,o){return X.jsx(jf,{"data-testid":void 0,ref:o,...r,children:u})}return f.muiName=jf.muiName,ct.memo(ct.forwardRef(f))}const Kp=Vl(X.jsx("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6c3.31 0 6 2.69 6 6s-2.69 6-6 6"})),$p=Vl(X.jsx("path",{d:"M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12zM12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6m0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4"})),$f=Vl(X.jsx("path",{d:"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7zm-6 .67 2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"})),Jf=Vl(X.jsx("path",{d:"M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92"})),Jp=({text:u,onSave:c})=>{const f=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this joke!",text:u})}catch(r){console.log("Error sharing:",r)}else navigator.clipboard.writeText(u),alert("Joke copied to clipboard!")};return X.jsxs("div",{className:"joke-card card",children:[X.jsx("p",{children:u}),X.jsxs("div",{className:"card-actions",children:[X.jsx("button",{className:"save-button",onClick:c,children:X.jsx($f,{})}),X.jsx("button",{className:"share-button",onClick:f,children:X.jsx(Jf,{})})]})]})},Wp=({text:u,onSave:c})=>{const f=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this fact!",text:u})}catch(r){console.log("Error sharing:",r)}else navigator.clipboard.writeText(u),alert("Fact copied to clipboard!")};return X.jsxs("div",{className:"fact-card card",children:[X.jsx("p",{children:u}),X.jsxs("div",{className:"card-actions",children:[X.jsx("button",{className:"save-button",onClick:c,children:X.jsx($f,{})}),X.jsx("button",{className:"share-button",onClick:f,children:X.jsx(Jf,{})})]})]})},Fp=({question:u,correctAnswer:c,incorrectAnswers:f,onSave:r})=>{const[o,h]=ct.useState(!1),[p,S]=ct.useState(null),[A,v]=ct.useState([]);ct.useEffect(()=>{h(!1),S(null),v([...f,c].sort(()=>Math.random()-.5))},[u,c,f]);const z=q=>{S(q),h(!0)},R=q=>o?q===c?"correct":q===p&&q!==c?"incorrect":"faded":"",U=async()=>{if(navigator.share)try{await navigator.share({title:"Check out this trivia question!",text:`Q: ${u}
A: ${c}`})}catch(q){console.log("Error sharing:",q)}else navigator.clipboard.writeText(`Q: ${u}
A: ${c}`),alert("Trivia copied to clipboard!")};return X.jsxs("div",{className:"trivia-card card",children:[X.jsx("div",{className:"trivia-question",children:X.jsx("h3",{dangerouslySetInnerHTML:{__html:u}})}),X.jsx("div",{className:"trivia-answers",children:A.map((q,w)=>X.jsx("button",{onClick:()=>z(q),className:`answer-button ${R(q)}`,disabled:o,dangerouslySetInnerHTML:{__html:q}},w))}),o&&X.jsx("div",{className:"trivia-result",children:p===c?X.jsx("p",{className:"correct-message",children:"Correct! 🎉"}):X.jsxs("p",{className:"incorrect-message",children:["Sorry! The correct answer is: ",X.jsx("span",{dangerouslySetInnerHTML:{__html:c}})]})}),X.jsxs("div",{className:"card-actions",children:[X.jsx("button",{className:"save-button",onClick:r,children:X.jsx($f,{})}),X.jsx("button",{className:"share-button",onClick:U,children:X.jsx(Jf,{})})]})]})},Pp=Vl(X.jsx("path",{d:"M18 2H9c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h9c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 14H9V4h9zM3 15v-2h2v2zm0-5.5h2v2H3zM10 20h2v2h-2zm-7-1.5v-2h2v2zM5 22c-1.1 0-2-.9-2-2h2zm3.5 0h-2v-2h2zm5 0v-2h2c0 1.1-.9 2-2 2M5 6v2H3c0-1.1.9-2 2-2"})),_f=Vl(X.jsx("path",{d:"M5 13h14v-2H5zm-2 4h14v-2H3zM7 7v2h14V7z"})),Ip=Vl(X.jsx("path",{d:"M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8"})),t2=({savedJokes:u,savedFacts:c,savedTrivia:f,notepadTab:r,setNotepadTab:o,copyNotepad:h,clearJokes:p,clearFacts:S,clearTrivia:A,removeItem:v,undoRemove:z})=>{const[R,U]=ct.useState([]),q=()=>{switch(r){case"joke":return u;case"fact":return c;case"trivia":return f;default:return[]}},w=Z=>{const P=q()[Z];U([...R,{tab:r,item:P,index:Z}]),v(r,Z)},x=()=>{if(R.length>0){const Z=R[R.length-1];z(Z.tab,Z.item,Z.index),U(R.slice(0,-1))}},V=q();return X.jsxs("div",{className:"notepad",children:[X.jsxs("div",{className:"notepad-tabs",children:[X.jsx("button",{className:r==="joke"?"active":"",onClick:()=>o("joke"),children:"Dad Jokes"}),X.jsx("button",{className:r==="fact"?"active":"",onClick:()=>o("fact"),children:"Random Facts"}),X.jsx("button",{className:r==="trivia"?"active":"",onClick:()=>o("trivia"),children:"Trivia"})]}),X.jsxs("div",{className:"notepad-content",children:[V.length===0?X.jsx("p",{className:"notepad-empty",children:"Nothing saved yet!"}):X.jsx("ul",{children:V.map((Z,P)=>X.jsx("li",{className:"notepad-item",onClick:()=>w(P),children:Z},P))}),X.jsxs("div",{className:"notepad-actions",style:{display:"flex",gap:"0.5rem",justifyContent:"flex-end",marginTop:"1rem"},children:[R.length>0&&X.jsxs("button",{onClick:x,title:`Undo Remove (${R.length})`,className:"undo-button",children:[X.jsx(Ip,{})," ",R.length>1&&X.jsx("span",{children:R.length})]}),X.jsx("button",{onClick:h,title:"Copy All",children:X.jsx(Pp,{})}),r==="joke"?X.jsx("button",{onClick:p,title:"Clear All",className:"clear-button",children:X.jsx(_f,{})}):r==="fact"?X.jsx("button",{onClick:S,title:"Clear All",className:"clear-button",children:X.jsx(_f,{})}):X.jsx("button",{onClick:A,title:"Clear All",className:"clear-button",children:X.jsx(_f,{})})]})]})]})},e2=Vl(X.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}));var Qe=xh();function l2(){const{darkMode:u,toggleTheme:c}=ct.useContext(Ch),[f,r]=ct.useState("joke"),[o,h]=ct.useState(""),[p,S]=ct.useState(!1),[A,v]=ct.useState(null),[z,R]=ct.useState(""),[U,q]=ct.useState(""),[w,x]=ct.useState([]),V=()=>{$([])},Z=()=>{L([])},[P,$]=ct.useState(()=>{const g=localStorage.getItem("savedJokes");return g?JSON.parse(g):[]}),[K,L]=ct.useState(()=>{const g=localStorage.getItem("savedFacts");return g?JSON.parse(g):[]}),[F,W]=ct.useState(()=>{const g=localStorage.getItem("savedTrivia");return g?JSON.parse(g):[]}),gt=()=>{W([])},[At,m]=ct.useState("joke"),[k,tt]=ct.useState(!1),ot=ct.useRef(null);ct.useEffect(()=>{function g(j){ot.current&&!ot.current.contains(j.target)&&tt(!1)}return document.addEventListener("mousedown",g),()=>{document.removeEventListener("mousedown",g)}},[]),ct.useEffect(()=>{Qe.preconnect("https://icanhazdadjoke.com"),Qe.preconnect("https://uselessfacts.jsph.pl"),Qe.preconnect("https://the-trivia-api.com"),Qe.prefetchDNS("https://api.chucknorris.io"),Qe.prefetchDNS("https://opentdb.com")},[]);const Dt=async(g=0,j=0)=>{S(!0),v(null);try{let Y="",G={};f==="joke"?(Y="https://icanhazdadjoke.com/",G={Accept:"application/json"}):f==="fact"?Y="https://uselessfacts.jsph.pl/random.json?language=en":f==="trivia"&&(Y="https://the-trivia-api.com/api/questions?limit=1");const lt=new AbortController,mt=setTimeout(()=>lt.abort(),5e3),ut=await fetch(Y,{headers:G,signal:lt.signal});if(clearTimeout(mt),!ut.ok)throw new Error(`API responded with status: ${ut.status}`);const Jt=await ut.json();if(f==="joke")h(Jt.joke);else if(f==="fact")h(Jt.text);else if(f==="trivia")if(Jt&&Jt.length>0){const Et=Jt[0];R(Et.question),q(Et.correctAnswer),x(Et.incorrectAnswers),h(`Q: ${Et.question} A: ${Et.correctAnswer}`)}else throw new Error("Failed to fetch trivia")}catch(Y){if(console.error("Fetch error:",Y),g<2)return console.log(`Retrying fetch (attempt ${g+1})...`),S(!1),Dt(g+1);v(Y.message),f==="trivia"&&(R(""),q(""),x([])),h("")}finally{S(!1)}};ct.useEffect(()=>{h(""),f==="trivia"?(R(""),q(""),x([]),Qe.preconnect("https://the-trivia-api.com")):f==="joke"?Qe.preconnect("https://icanhazdadjoke.com"):f==="fact"&&Qe.preconnect("https://uselessfacts.jsph.pl"),Dt()},[f]);const pt=()=>{o&&(f==="joke"&&!P.includes(o)?$([...P,o]):f==="fact"&&!K.includes(o)?L([...K,o]):f==="trivia"&&!F.includes(o)&&W([...F,o]))};ct.useEffect(()=>{localStorage.setItem("savedJokes",JSON.stringify(P))},[P]),ct.useEffect(()=>{localStorage.setItem("savedFacts",JSON.stringify(K))},[K]),ct.useEffect(()=>{localStorage.setItem("savedTrivia",JSON.stringify(F))},[F]);const M=()=>{let g="";At==="joke"?g=P.join(`

`):At==="fact"?g=K.join(`

`):At==="trivia"&&(g=F.join(`

`)),navigator.clipboard.writeText(g)},Q=(g,j)=>{if(g==="joke"){const Y=[...P];Y.splice(j,1),$(Y)}else if(g==="fact"){const Y=[...K];Y.splice(j,1),L(Y)}else if(g==="trivia"){const Y=[...F];Y.splice(j,1),W(Y)}},et=(g,j,Y)=>{if(g==="joke"){const G=[...P];G.splice(Y,0,j),$(G)}else if(g==="fact"){const G=[...K];G.splice(Y,0,j),L(G)}else if(g==="trivia"){const G=[...F];G.splice(Y,0,j),W(G)}},Ot=()=>{f==="joke"?Qe.preconnect("https://icanhazdadjoke.com"):f==="fact"?Qe.preconnect("https://uselessfacts.jsph.pl"):f==="trivia"&&Qe.preconnect("https://the-trivia-api.com"),Dt()};return X.jsxs("div",{className:`app ${u?"dark-mode":"light-mode"}`,children:[X.jsx("div",{className:"theme-toggle",children:X.jsx("button",{onClick:c,className:"theme-button",children:u?X.jsx($p,{}):X.jsx(Kp,{})})}),X.jsx("h1",{children:"The Grin Bin"}),X.jsxs("div",{className:"category-buttons",children:[X.jsx("button",{className:f==="joke"?"active":"",onClick:()=>{r("joke"),m("joke")},children:"Dad Jokes"}),X.jsx("button",{className:f==="fact"?"active":"",onClick:()=>{r("fact"),m("fact")},children:"Random Fact"}),X.jsx("button",{className:f==="trivia"?"active":"",onClick:()=>{r("trivia"),m("trivia")},children:"Trivia"})]}),X.jsx("div",{className:"content-container",style:{height:f==="trivia"?"400px":"250px"},children:X.jsx("div",{className:"card-wrapper",children:X.jsxs("div",{className:"card-transition",children:[f==="joke"&&o&&X.jsx(Jp,{text:o,onSave:pt}),f==="fact"&&o&&X.jsx(Wp,{text:o,onSave:pt}),f==="trivia"&&z&&X.jsx(Fp,{question:z,correctAnswer:U,incorrectAnswers:w,onSave:pt}),!o&&X.jsx("div",{className:"empty-card"})]})})}),X.jsxs("button",{className:"refresh-button",onClick:Ot,disabled:p,children:[X.jsx(e2,{style:{marginRight:"0.5rem"}})," Get Another"]}),X.jsx(t2,{savedJokes:P,savedFacts:K,savedTrivia:F,notepadTab:At,setNotepadTab:m,copyNotepad:M,clearJokes:V,clearFacts:Z,clearTrivia:gt,removeItem:Q,undoRemove:et})]})}Ng.createRoot(document.getElementById("root")).render(X.jsx(Oh.StrictMode,{children:X.jsx(Ug,{children:X.jsx(l2,{})})}));
