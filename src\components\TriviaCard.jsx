import React, { useState, useEffect } from 'react';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import ShareIcon from '@mui/icons-material/Share';

const TriviaCard = ({ question, correctAnswer, incorrectAnswers, onSave }) => {
  const [showAnswer, setShowAnswer] = useState(false);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [shuffledAnswers, setShuffledAnswers] = useState([]);

  // Reset state and shuffle answers when question changes
  useEffect(() => {
    setShowAnswer(false);
    setSelectedAnswer(null);
    // Shuffle answers only when question changes
    setShuffledAnswers([...incorrectAnswers, correctAnswer].sort(() => Math.random() - 0.5));
  }, [question, correctAnswer, incorrectAnswers]);

  const handleAnswerSelect = (answer) => {
    setSelectedAnswer(answer);
    setShowAnswer(true);
  };

  const getAnswerClass = (answer) => {
    if (!showAnswer) return '';
    if (answer === correctAnswer) return 'correct';
    if (answer === selectedAnswer && answer !== correctAnswer) return 'incorrect';
    return 'faded';
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Check out this trivia question!',
          text: `Q: ${question}\nA: ${correctAnswer}`
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(`Q: ${question}\nA: ${correctAnswer}`);
      alert('Trivia copied to clipboard!');
    }
  };

  return (
    <div className="trivia-card card">
      <div className="trivia-question">
        <h3 dangerouslySetInnerHTML={{ __html: question }}></h3>
      </div>

      <div className="trivia-answers">
        {shuffledAnswers.map((answer, index) => (
          <button
            key={index}
            onClick={() => handleAnswerSelect(answer)}
            className={`answer-button ${getAnswerClass(answer)}`}
            disabled={showAnswer}
            dangerouslySetInnerHTML={{ __html: answer }}
          ></button>
        ))}
      </div>

      {showAnswer && (
        <div className="trivia-result">
          {selectedAnswer === correctAnswer ? (
            <p className="correct-message">Correct! 🎉</p>
          ) : (
            <p className="incorrect-message">
              Sorry! The correct answer is: <span dangerouslySetInnerHTML={{ __html: correctAnswer }}></span>
            </p>
          )}
        </div>
      )}

      <div className="card-actions">
        <button className="save-button" onClick={onSave}>
          <SaveAltIcon />
        </button>
        <button className="share-button" onClick={handleShare}>
          <ShareIcon />
        </button>
      </div>
    </div>
  );
};

export default TriviaCard;




