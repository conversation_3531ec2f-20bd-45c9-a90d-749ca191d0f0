import React, { useState, useEffect } from 'react';
import { getFirestore, collection, getDocs, doc, updateDoc, query, where, orderBy } from 'firebase/firestore';
import './AdminPanel.css';

const AdminPanel = () => {
  const [pendingSubmissions, setPendingSubmissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState('all'); // all, joke, fact, riddle, trivia

  const db = getFirestore();

  const fetchPendingSubmissions = async () => {
    setLoading(true);
    try {
      let q;
      if (filter === 'all') {
        q = query(
          collection(db, 'userSubmissions'),
          where('status', '==', 'pending'),
          orderBy('createdAt', 'desc')
        );
      } else {
        q = query(
          collection(db, 'userSubmissions'),
          where('status', '==', 'pending'),
          where('type', '==', filter),
          orderBy('createdAt', 'desc')
        );
      }

      const snapshot = await getDocs(q);
      const submissions = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));

      setPendingSubmissions(submissions);
    } catch (err) {
      console.error('Error fetching submissions:', err);
      setError('Failed to load submissions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPendingSubmissions();
  }, [filter]);

  const handleApprove = async (id) => {
    try {
      await updateDoc(doc(db, 'userSubmissions', id), {
        status: 'approved',
        approvedAt: new Date()
      });

      // Update local state
      setPendingSubmissions(prev =>
        prev.filter(submission => submission.id !== id)
      );
    } catch (err) {
      console.error('Error approving submission:', err);
      setError('Failed to approve submission');
    }
  };

  const handleReject = async (id) => {
    try {
      await updateDoc(doc(db, 'userSubmissions', id), {
        status: 'rejected'
      });

      // Update local state
      setPendingSubmissions(prev =>
        prev.filter(submission => submission.id !== id)
      );
    } catch (err) {
      console.error('Error rejecting submission:', err);
      setError('Failed to reject submission');
    }
  };

  const renderContent = (submission) => {
    if (submission.type === 'joke' || submission.type === 'fact') {
      return <p>{submission.content}</p>;
    } else if (submission.type === 'riddle') {
      return (
        <>
          <p><strong>Question:</strong> {submission.content.question}</p>
          <p><strong>Answer:</strong> {submission.content.answer}</p>
        </>
      );
    } else if (submission.type === 'trivia') {
      return (
        <>
          <p><strong>Question:</strong> {submission.content.question}</p>
          <p><strong>Correct Answer:</strong> {submission.content.correctAnswer}</p>
          <p><strong>Incorrect Answers:</strong></p>
          <ul>
            {submission.content.incorrectAnswers.map((answer, index) => (
              <li key={index}>{answer}</li>
            ))}
          </ul>
        </>
      );
    }
  };

  return (
    <div className="admin-panel">
      <h1>Admin Panel - Content Moderation</h1>

      {error && <div className="error-message">{error}</div>}

      <div className="filter-controls">
        <label>Filter by type:</label>
        <select value={filter} onChange={(e) => setFilter(e.target.value)}>
          <option value="all">All Types</option>
          <option value="joke">Jokes</option>
          <option value="fact">Facts</option>
          <option value="riddle">Riddles</option>
          <option value="trivia">Trivia</option>
        </select>
      </div>

      {loading ? (
        <div className="loading">Loading submissions...</div>
      ) : pendingSubmissions.length === 0 ? (
        <div className="no-submissions">No pending submissions to review.</div>
      ) : (
        <div className="submissions-list">
          {pendingSubmissions.map(submission => (
            <div key={submission.id} className="submission-item">
              <div className="submission-header">
                <span className="submission-type">{submission.type}</span>
                <span className="submission-date">
                  {submission.createdAt.toLocaleDateString()} at {submission.createdAt.toLocaleTimeString()}
                </span>
              </div>

              <div className="submission-content">
                {renderContent(submission)}
              </div>

              <div className="submission-author">
                Submitted by: {submission.authorName || 'Anonymous'}
              </div>

              <div className="submission-actions">
                <button
                  className="approve-button"
                  onClick={() => handleApprove(submission.id)}
                >
                  Approve
                </button>
                <button
                  className="reject-button"
                  onClick={() => handleReject(submission.id)}
                >
                  Reject
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdminPanel;