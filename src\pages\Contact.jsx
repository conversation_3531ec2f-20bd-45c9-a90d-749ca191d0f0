import React from 'react';
import '../styles/legal.css';

const Contact = () => {

  return (
    <div className="legal-page">
      <div className="legal-container">
        <h1>Contact Us</h1>

        <section>
          <h2>Get in Touch</h2>
          <p>
            We'd love to hear from you! Whether you have questions, feedback, suggestions,
            or just want to say hello, feel free to reach out using any of the methods below.
          </p>
        </section>

        <section>
          <h2>Contact Information</h2>
          <div className="single-contact-card">
            <h3>📧 Get in Touch</h3>
            <p className="main-email">
              <a href="mailto:<EMAIL>" className="contact-link">
                <EMAIL>
              </a>
            </p>
            <p className="contact-description">
              We handle all inquiries through this single email address, including:
            </p>
            <ul className="contact-types">
              <li>General questions, feedback, and suggestions</li>
              <li>Technical support and bug reports</li>
              <li>Business partnerships and advertising opportunities</li>
              <li>Legal matters and privacy concerns</li>
              <li>Content moderation and community violations</li>
              <li>DMCA notices and copyright claims</li>
            </ul>
          </div>
        </section>



        <section>
          <h2>Response Time</h2>
          <p>
            We strive to respond to all inquiries within 24-48 hours during business days.
            For urgent technical issues, we aim to respond even faster.
          </p>
        </section>

        <section>
          <h2>Frequently Asked Questions</h2>
          <div className="faq-item">
            <h3>How do I submit content?</h3>
            <p>
              Click the "Submit Your Own" button on the main page to share your jokes,
              facts, riddles, or trivia with the community.
            </p>
          </div>

          <div className="faq-item">
            <h3>Why was my content not approved?</h3>
            <p>
              Content may not be approved if it violates our community guidelines.
              Please review our guidelines and ensure your content is family-friendly
              and appropriate.
            </p>
          </div>

          <div className="faq-item">
            <h3>How do I report inappropriate content?</h3>
            <p>
              Email <NAME_EMAIL> with details about the content
              you'd like to report.
            </p>
          </div>

          <div className="faq-item">
            <h3>Can I suggest new features?</h3>
            <p>
              Absolutely! We love hearing from our community. Send your suggestions
              to <EMAIL>.
            </p>
          </div>
        </section>
      </div>
    </div>
  );
};

export default Contact;
