import React from 'react';
import '../styles/legal.css';

const About = () => {
  return (
    <div className="legal-page">
      <div className="legal-container">
        <h1>About The Grin Bin</h1>

        <section>
          <h2>Welcome to The Grin Bin!</h2>
          <p>
            The Grin Bin is your go-to destination for entertainment and learning. We're a community-driven
            platform that brings together word play and classic games all in one place. Explore our Word Play
            section for jokes, fascinating facts, challenging riddles, and engaging trivia questions, or check
            out our Classic Games section for timeless entertainment.
          </p>
        </section>

        <section>
          <h2>What We Offer</h2>
          <div className="feature-grid">
            <div className="feature">
              <h3>🎭 Word Play</h3>
              <p>Enjoy dad jokes, discover fascinating facts, solve challenging riddles, and test your knowledge with trivia questions.</p>
            </div>
            <div className="feature">
              <h3>🎮 Classic Games</h3>
              <p>Experience timeless games including dice games, card games, puzzles, and arcade-style entertainment.</p>
            </div>
            <div className="feature">
              <h3>👥 Community Content</h3>
              <p>Submit your own content and vote on community submissions to help curate the best entertainment.</p>
            </div>
            <div className="feature">
              <h3>📝 Personal Collections</h3>
              <p>Save your favorite content to your personal notepad and organize it by category.</p>
            </div>
          </div>
        </section>

        <section>
          <h2>How It Works</h2>
          <ol>
            <li><strong>Choose Your Adventure:</strong> Select between Word Play and Classic Games from our homepage</li>
            <li><strong>Explore:</strong> Browse through different categories of content and games</li>
            <li><strong>Save:</strong> Build your personal collection of favorites in your notepad</li>
            <li><strong>Share:</strong> Spread the joy by sharing content with friends</li>
            <li><strong>Contribute:</strong> Submit your own content to the Word Play section</li>
            <li><strong>Vote:</strong> Help the community by voting on submitted content</li>
          </ol>
        </section>

        <section>
          <h2>Community Features</h2>
          <ul>
            <li><strong>User Submissions:</strong> Share your own content with the community</li>
            <li><strong>Voting System:</strong> Upvote and downvote content to help surface the best material</li>
            <li><strong>Content Moderation:</strong> We review submissions to maintain quality and appropriateness</li>
            <li><strong>Personal Collections:</strong> Save your favorite content to revisit anytime</li>
          </ul>
        </section>

        <section>
          <h2>Our Mission</h2>
          <p>
            We believe that laughter and learning go hand in hand. Our mission is to create a positive,
            family-friendly space where people can discover entertaining content, learn something new,
            and share moments of joy with others.
          </p>
        </section>

        <section>
          <h2>Content Sources</h2>
          <p>
            Our content comes from a combination of:
          </p>
          <ul>
            <li>Curated external APIs for fresh, daily content</li>
            <li>Community submissions from users like you</li>
            <li>Moderated content to ensure quality and appropriateness</li>
          </ul>
        </section>

        <section>
          <h2>Privacy & Safety</h2>
          <p>
            We take your privacy seriously and are committed to providing a safe environment for all users.
            We collect minimal personal information and use it only to improve your experience. All content
            is moderated to ensure it meets our community guidelines.
          </p>
        </section>

        <section>
          <h2>Get in Touch</h2>
          <p>
            We'd love to hear from you! Whether you have feedback, suggestions, questions, or just want
            to say hello, feel free to reach out:
          </p>
          <div className="contact-info">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Support:</strong> <EMAIL></p>
            <p><strong>Business Inquiries:</strong> <EMAIL></p>
            <p><strong>Website:</strong> thegrinbin.com</p>
          </div>
        </section>

        <section>
          <h2>Join Our Community</h2>
          <p>
            Ready to start exploring? Jump in and discover what The Grin Bin has to offer. Whether you're
            here for a quick laugh, to learn something new, or to challenge your brain, we've got something
            for everyone!
          </p>
        </section>
      </div>
    </div>
  );
};

export default About;
