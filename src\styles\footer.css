.app-footer {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  margin-top: auto;
  padding: 2rem 0 1rem;
  color: var(--text-light);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.dark-mode .app-footer {
  background: #2d2d2d;
  border-top-color: #444;
  color: var(--text-dark);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.footer-sections-mobile {
  display: contents;
}

.footer-section h3 {
  color: var(--text-light);
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 700;
}

.dark-mode .footer-section h3 {
  color: var(--text-dark);
}

.footer-section h4 {
  color: var(--text-light);
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
}

.dark-mode .footer-section h4 {
  color: var(--text-dark);
}

.footer-section p {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  color: #666;
}

.dark-mode .footer-section p {
  color: #aaa;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-link {
  color: var(--text-light);
  text-decoration: none;
  background: none;
  border: none;
  padding: 0;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #0d47a1;
  text-decoration: underline;
}

.dark-mode .footer-link {
  color: var(--text-dark);
}

.dark-mode .footer-link:hover {
  color: #e3f2fd;
}

.footer-bottom {
  max-width: 1200px;
  margin: 2rem auto 0;
  padding: 1rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
}

.dark-mode .footer-bottom {
  border-top-color: #444;
}

.footer-bottom p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
  color: #666;
}

.dark-mode .footer-bottom p {
  color: #aaa;
}

.footer-disclaimer {
  font-size: 0.8rem !important;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-footer {
    padding: 1rem 0 0.5rem;
  }

  .footer-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 0 1rem;
  }

  /* Hide the main logo section on mobile */
  .footer-content>.footer-section:first-child {
    display: none;
  }

  /* Create horizontal layout for remaining sections */
  .footer-sections-mobile {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }

  .footer-section h4 {
    font-size: 0.85rem;
    margin-bottom: 0.3rem;
    font-weight: 600;
  }

  .footer-section ul {
    margin: 0;
  }

  .footer-section li {
    margin-bottom: 0.15rem;
  }

  .footer-link {
    font-size: 0.75rem;
  }

  .footer-bottom {
    margin-top: 0.75rem;
    padding: 0.5rem 1rem;
  }

  .footer-bottom p {
    font-size: 0.7rem;
    margin: 0.1rem 0;
  }

  .footer-disclaimer {
    font-size: 0.65rem !important;
  }
}

@media (max-width: 480px) {
  .app-footer {
    padding: 0.75rem 0 0.4rem;
  }

  .footer-content {
    gap: 0.5rem;
    padding: 0 0.75rem;
  }

  .footer-sections-mobile {
    gap: 0.5rem;
  }

  .footer-section h4 {
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
  }

  .footer-link {
    font-size: 0.7rem;
  }

  .footer-section li {
    margin-bottom: 0.1rem;
  }

  .footer-bottom {
    margin-top: 0.5rem;
    padding: 0.4rem 0.75rem;
  }

  .footer-bottom p {
    font-size: 0.65rem;
    margin: 0.05rem 0;
  }

  .footer-disclaimer {
    font-size: 0.6rem !important;
  }
}