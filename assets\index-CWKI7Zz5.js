function yg(u,c){for(var f=0;f<c.length;f++){const r=c[f];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in u)){const h=Object.getOwnPropertyDescriptor(r,s);h&&Object.defineProperty(u,s,h.get?h:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(u,Symbol.toStringTag,{value:"Module"}))}(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const h of s)if(h.type==="childList")for(const v of h.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&r(v)}).observe(document,{childList:!0,subtree:!0});function f(s){const h={};return s.integrity&&(h.integrity=s.integrity),s.referrerPolicy&&(h.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?h.credentials="include":s.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function r(s){if(s.ep)return;s.ep=!0;const h=f(s);fetch(s.href,h)}})();function ph(u){return u&&u.__esModule&&Object.prototype.hasOwnProperty.call(u,"default")?u.default:u}var mf={exports:{}},lu={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var j0;function gg(){if(j0)return lu;j0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function f(r,s,h){var v=null;if(h!==void 0&&(v=""+h),s.key!==void 0&&(v=""+s.key),"key"in s){h={};for(var S in s)S!=="key"&&(h[S]=s[S])}else h=s;return s=h.ref,{$$typeof:u,type:r,key:v,ref:s!==void 0?s:null,props:h}}return lu.Fragment=c,lu.jsx=f,lu.jsxs=f,lu}var G0;function vg(){return G0||(G0=1,mf.exports=gg()),mf.exports}var Qt=vg(),yf={exports:{}},it={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var w0;function pg(){if(w0)return it;w0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),R=Symbol.iterator;function N(p){return p===null||typeof p!="object"?null:(p=R&&p[R]||p["@@iterator"],typeof p=="function"?p:null)}var w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Y=Object.assign,z={};function L(p,q,V){this.props=p,this.context=q,this.refs=z,this.updater=V||w}L.prototype.isReactComponent={},L.prototype.setState=function(p,q){if(typeof p!="object"&&typeof p!="function"&&p!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,p,q,"setState")},L.prototype.forceUpdate=function(p){this.updater.enqueueForceUpdate(this,p,"forceUpdate")};function K(){}K.prototype=L.prototype;function nt(p,q,V){this.props=p,this.context=q,this.refs=z,this.updater=V||w}var $=nt.prototype=new K;$.constructor=nt,Y($,L.prototype),$.isPureReactComponent=!0;var k=Array.isArray,j={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function J(p,q,V,X,tt,ht){return V=ht.ref,{$$typeof:u,type:p,key:q,ref:V!==void 0?V:null,props:ht}}function pt(p,q){return J(p.type,q,void 0,void 0,void 0,p.props)}function Dt(p){return typeof p=="object"&&p!==null&&p.$$typeof===u}function m(p){var q={"=":"=0",":":"=2"};return"$"+p.replace(/[=:]/g,function(V){return q[V]})}var Q=/\/+/g;function F(p,q){return typeof p=="object"&&p!==null&&p.key!=null?m(""+p.key):q.toString(36)}function st(){}function qt(p){switch(p.status){case"fulfilled":return p.value;case"rejected":throw p.reason;default:switch(typeof p.status=="string"?p.then(st,st):(p.status="pending",p.then(function(q){p.status==="pending"&&(p.status="fulfilled",p.value=q)},function(q){p.status==="pending"&&(p.status="rejected",p.reason=q)})),p.status){case"fulfilled":return p.value;case"rejected":throw p.reason}}throw p}function _t(p,q,V,X,tt){var ht=typeof p;(ht==="undefined"||ht==="boolean")&&(p=null);var ut=!1;if(p===null)ut=!0;else switch(ht){case"bigint":case"string":case"number":ut=!0;break;case"object":switch(p.$$typeof){case u:case c:ut=!0;break;case x:return ut=p._init,_t(ut(p._payload),q,V,X,tt)}}if(ut)return tt=tt(p),ut=X===""?"."+F(p,0):X,k(tt)?(V="",ut!=null&&(V=ut.replace(Q,"$&/")+"/"),_t(tt,q,V,"",function(vl){return vl})):tt!=null&&(Dt(tt)&&(tt=pt(tt,V+(tt.key==null||p&&p.key===tt.key?"":(""+tt.key).replace(Q,"$&/")+"/")+ut)),q.push(tt)),1;ut=0;var ge=X===""?".":X+":";if(k(p))for(var Ut=0;Ut<p.length;Ut++)X=p[Ut],ht=ge+F(X,Ut),ut+=_t(X,q,V,ht,tt);else if(Ut=N(p),typeof Ut=="function")for(p=Ut.call(p),Ut=0;!(X=p.next()).done;)X=X.value,ht=ge+F(X,Ut++),ut+=_t(X,q,V,ht,tt);else if(ht==="object"){if(typeof p.then=="function")return _t(qt(p),q,V,X,tt);throw q=String(p),Error("Objects are not valid as a React child (found: "+(q==="[object Object]"?"object with keys {"+Object.keys(p).join(", ")+"}":q)+"). If you meant to render a collection of children, use an array instead.")}return ut}function M(p,q,V){if(p==null)return p;var X=[],tt=0;return _t(p,X,"","",function(ht){return q.call(V,ht,tt++)}),X}function G(p){if(p._status===-1){var q=p._result;q=q(),q.then(function(V){(p._status===0||p._status===-1)&&(p._status=1,p._result=V)},function(V){(p._status===0||p._status===-1)&&(p._status=2,p._result=V)}),p._status===-1&&(p._status=0,p._result=q)}if(p._status===1)return p._result.default;throw p._result}var P=typeof reportError=="function"?reportError:function(p){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var q=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof p=="object"&&p!==null&&typeof p.message=="string"?String(p.message):String(p),error:p});if(!window.dispatchEvent(q))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",p);return}console.error(p)};function zt(){}return it.Children={map:M,forEach:function(p,q,V){M(p,function(){q.apply(this,arguments)},V)},count:function(p){var q=0;return M(p,function(){q++}),q},toArray:function(p){return M(p,function(q){return q})||[]},only:function(p){if(!Dt(p))throw Error("React.Children.only expected to receive a single React element child.");return p}},it.Component=L,it.Fragment=f,it.Profiler=s,it.PureComponent=nt,it.StrictMode=r,it.Suspense=A,it.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=j,it.__COMPILER_RUNTIME={__proto__:null,c:function(p){return j.H.useMemoCache(p)}},it.cache=function(p){return function(){return p.apply(null,arguments)}},it.cloneElement=function(p,q,V){if(p==null)throw Error("The argument must be a React element, but you passed "+p+".");var X=Y({},p.props),tt=p.key,ht=void 0;if(q!=null)for(ut in q.ref!==void 0&&(ht=void 0),q.key!==void 0&&(tt=""+q.key),q)!I.call(q,ut)||ut==="key"||ut==="__self"||ut==="__source"||ut==="ref"&&q.ref===void 0||(X[ut]=q[ut]);var ut=arguments.length-2;if(ut===1)X.children=V;else if(1<ut){for(var ge=Array(ut),Ut=0;Ut<ut;Ut++)ge[Ut]=arguments[Ut+2];X.children=ge}return J(p.type,tt,void 0,void 0,ht,X)},it.createContext=function(p){return p={$$typeof:v,_currentValue:p,_currentValue2:p,_threadCount:0,Provider:null,Consumer:null},p.Provider=p,p.Consumer={$$typeof:h,_context:p},p},it.createElement=function(p,q,V){var X,tt={},ht=null;if(q!=null)for(X in q.key!==void 0&&(ht=""+q.key),q)I.call(q,X)&&X!=="key"&&X!=="__self"&&X!=="__source"&&(tt[X]=q[X]);var ut=arguments.length-2;if(ut===1)tt.children=V;else if(1<ut){for(var ge=Array(ut),Ut=0;Ut<ut;Ut++)ge[Ut]=arguments[Ut+2];tt.children=ge}if(p&&p.defaultProps)for(X in ut=p.defaultProps,ut)tt[X]===void 0&&(tt[X]=ut[X]);return J(p,ht,void 0,void 0,null,tt)},it.createRef=function(){return{current:null}},it.forwardRef=function(p){return{$$typeof:S,render:p}},it.isValidElement=Dt,it.lazy=function(p){return{$$typeof:x,_payload:{_status:-1,_result:p},_init:G}},it.memo=function(p,q){return{$$typeof:g,type:p,compare:q===void 0?null:q}},it.startTransition=function(p){var q=j.T,V={};j.T=V;try{var X=p(),tt=j.S;tt!==null&&tt(V,X),typeof X=="object"&&X!==null&&typeof X.then=="function"&&X.then(zt,P)}catch(ht){P(ht)}finally{j.T=q}},it.unstable_useCacheRefresh=function(){return j.H.useCacheRefresh()},it.use=function(p){return j.H.use(p)},it.useActionState=function(p,q,V){return j.H.useActionState(p,q,V)},it.useCallback=function(p,q){return j.H.useCallback(p,q)},it.useContext=function(p){return j.H.useContext(p)},it.useDebugValue=function(){},it.useDeferredValue=function(p,q){return j.H.useDeferredValue(p,q)},it.useEffect=function(p,q,V){var X=j.H;if(typeof V=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return X.useEffect(p,q)},it.useId=function(){return j.H.useId()},it.useImperativeHandle=function(p,q,V){return j.H.useImperativeHandle(p,q,V)},it.useInsertionEffect=function(p,q){return j.H.useInsertionEffect(p,q)},it.useLayoutEffect=function(p,q){return j.H.useLayoutEffect(p,q)},it.useMemo=function(p,q){return j.H.useMemo(p,q)},it.useOptimistic=function(p,q){return j.H.useOptimistic(p,q)},it.useReducer=function(p,q,V){return j.H.useReducer(p,q,V)},it.useRef=function(p){return j.H.useRef(p)},it.useState=function(p){return j.H.useState(p)},it.useSyncExternalStore=function(p,q,V){return j.H.useSyncExternalStore(p,q,V)},it.useTransition=function(){return j.H.useTransition()},it.version="19.1.0",it}var X0;function Bf(){return X0||(X0=1,yf.exports=pg()),yf.exports}var Ht=Bf();const bh=ph(Ht),Q0=yg({__proto__:null,default:bh},[Ht]);var gf={exports:{}},au={},vf={exports:{}},pf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var L0;function bg(){return L0||(L0=1,function(u){function c(M,G){var P=M.length;M.push(G);t:for(;0<P;){var zt=P-1>>>1,p=M[zt];if(0<s(p,G))M[zt]=G,M[P]=p,P=zt;else break t}}function f(M){return M.length===0?null:M[0]}function r(M){if(M.length===0)return null;var G=M[0],P=M.pop();if(P!==G){M[0]=P;t:for(var zt=0,p=M.length,q=p>>>1;zt<q;){var V=2*(zt+1)-1,X=M[V],tt=V+1,ht=M[tt];if(0>s(X,P))tt<p&&0>s(ht,X)?(M[zt]=ht,M[tt]=P,zt=tt):(M[zt]=X,M[V]=P,zt=V);else if(tt<p&&0>s(ht,P))M[zt]=ht,M[tt]=P,zt=tt;else break t}}return G}function s(M,G){var P=M.sortIndex-G.sortIndex;return P!==0?P:M.id-G.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;u.unstable_now=function(){return h.now()}}else{var v=Date,S=v.now();u.unstable_now=function(){return v.now()-S}}var A=[],g=[],x=1,R=null,N=3,w=!1,Y=!1,z=!1,L=!1,K=typeof setTimeout=="function"?setTimeout:null,nt=typeof clearTimeout=="function"?clearTimeout:null,$=typeof setImmediate<"u"?setImmediate:null;function k(M){for(var G=f(g);G!==null;){if(G.callback===null)r(g);else if(G.startTime<=M)r(g),G.sortIndex=G.expirationTime,c(A,G);else break;G=f(g)}}function j(M){if(z=!1,k(M),!Y)if(f(A)!==null)Y=!0,I||(I=!0,F());else{var G=f(g);G!==null&&_t(j,G.startTime-M)}}var I=!1,J=-1,pt=5,Dt=-1;function m(){return L?!0:!(u.unstable_now()-Dt<pt)}function Q(){if(L=!1,I){var M=u.unstable_now();Dt=M;var G=!0;try{t:{Y=!1,z&&(z=!1,nt(J),J=-1),w=!0;var P=N;try{e:{for(k(M),R=f(A);R!==null&&!(R.expirationTime>M&&m());){var zt=R.callback;if(typeof zt=="function"){R.callback=null,N=R.priorityLevel;var p=zt(R.expirationTime<=M);if(M=u.unstable_now(),typeof p=="function"){R.callback=p,k(M),G=!0;break e}R===f(A)&&r(A),k(M)}else r(A);R=f(A)}if(R!==null)G=!0;else{var q=f(g);q!==null&&_t(j,q.startTime-M),G=!1}}break t}finally{R=null,N=P,w=!1}G=void 0}}finally{G?F():I=!1}}}var F;if(typeof $=="function")F=function(){$(Q)};else if(typeof MessageChannel<"u"){var st=new MessageChannel,qt=st.port2;st.port1.onmessage=Q,F=function(){qt.postMessage(null)}}else F=function(){K(Q,0)};function _t(M,G){J=K(function(){M(u.unstable_now())},G)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(M){M.callback=null},u.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):pt=0<M?Math.floor(1e3/M):5},u.unstable_getCurrentPriorityLevel=function(){return N},u.unstable_next=function(M){switch(N){case 1:case 2:case 3:var G=3;break;default:G=N}var P=N;N=G;try{return M()}finally{N=P}},u.unstable_requestPaint=function(){L=!0},u.unstable_runWithPriority=function(M,G){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var P=N;N=M;try{return G()}finally{N=P}},u.unstable_scheduleCallback=function(M,G,P){var zt=u.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?zt+P:zt):P=zt,M){case 1:var p=-1;break;case 2:p=250;break;case 5:p=1073741823;break;case 4:p=1e4;break;default:p=5e3}return p=P+p,M={id:x++,callback:G,priorityLevel:M,startTime:P,expirationTime:p,sortIndex:-1},P>zt?(M.sortIndex=P,c(g,M),f(A)===null&&M===f(g)&&(z?(nt(J),J=-1):z=!0,_t(j,P-zt))):(M.sortIndex=p,c(A,M),Y||w||(Y=!0,I||(I=!0,F()))),M},u.unstable_shouldYield=m,u.unstable_wrapCallback=function(M){var G=N;return function(){var P=N;N=G;try{return M.apply(this,arguments)}finally{N=P}}}}(pf)),pf}var V0;function Sg(){return V0||(V0=1,vf.exports=bg()),vf.exports}var bf={exports:{}},ce={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Z0;function Tg(){if(Z0)return ce;Z0=1;var u=Bf();function c(A){var g="https://react.dev/errors/"+A;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)g+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+A+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function f(){}var r={d:{f,r:function(){throw Error(c(522))},D:f,C:f,L:f,m:f,X:f,S:f,M:f},p:0,findDOMNode:null},s=Symbol.for("react.portal");function h(A,g,x){var R=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:R==null?null:""+R,children:A,containerInfo:g,implementation:x}}var v=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function S(A,g){if(A==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return ce.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,ce.createPortal=function(A,g){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(c(299));return h(A,g,null,x)},ce.flushSync=function(A){var g=v.T,x=r.p;try{if(v.T=null,r.p=2,A)return A()}finally{v.T=g,r.p=x,r.d.f()}},ce.preconnect=function(A,g){typeof A=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,r.d.C(A,g))},ce.prefetchDNS=function(A){typeof A=="string"&&r.d.D(A)},ce.preinit=function(A,g){if(typeof A=="string"&&g&&typeof g.as=="string"){var x=g.as,R=S(x,g.crossOrigin),N=typeof g.integrity=="string"?g.integrity:void 0,w=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;x==="style"?r.d.S(A,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:R,integrity:N,fetchPriority:w}):x==="script"&&r.d.X(A,{crossOrigin:R,integrity:N,fetchPriority:w,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},ce.preinitModule=function(A,g){if(typeof A=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var x=S(g.as,g.crossOrigin);r.d.M(A,{crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&r.d.M(A)},ce.preload=function(A,g){if(typeof A=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var x=g.as,R=S(x,g.crossOrigin);r.d.L(A,x,{crossOrigin:R,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},ce.preloadModule=function(A,g){if(typeof A=="string")if(g){var x=S(g.as,g.crossOrigin);r.d.m(A,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else r.d.m(A)},ce.requestFormReset=function(A){r.d.r(A)},ce.unstable_batchedUpdates=function(A,g){return A(g)},ce.useFormState=function(A,g,x){return v.H.useFormState(A,g,x)},ce.useFormStatus=function(){return v.H.useHostTransitionStatus()},ce.version="19.1.0",ce}var K0;function Ag(){if(K0)return bf.exports;K0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),bf.exports=Tg(),bf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var k0;function Eg(){if(k0)return au;k0=1;var u=Sg(),c=Bf(),f=Ag();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function v(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function S(t){if(h(t)!==t)throw Error(r(188))}function A(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(r(188));return e!==t?null:t}for(var l=t,a=e;;){var n=l.return;if(n===null)break;var i=n.alternate;if(i===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===i.child){for(i=n.child;i;){if(i===l)return S(n),t;if(i===a)return S(n),e;i=i.sibling}throw Error(r(188))}if(l.return!==a.return)l=n,a=i;else{for(var o=!1,d=n.child;d;){if(d===l){o=!0,l=n,a=i;break}if(d===a){o=!0,a=n,l=i;break}d=d.sibling}if(!o){for(d=i.child;d;){if(d===l){o=!0,l=i,a=n;break}if(d===a){o=!0,a=i,l=n;break}d=d.sibling}if(!o)throw Error(r(189))}}if(l.alternate!==a)throw Error(r(190))}if(l.tag!==3)throw Error(r(188));return l.stateNode.current===l?t:e}function g(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=g(t),e!==null)return e;t=t.sibling}return null}var x=Object.assign,R=Symbol.for("react.element"),N=Symbol.for("react.transitional.element"),w=Symbol.for("react.portal"),Y=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),L=Symbol.for("react.profiler"),K=Symbol.for("react.provider"),nt=Symbol.for("react.consumer"),$=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),J=Symbol.for("react.memo"),pt=Symbol.for("react.lazy"),Dt=Symbol.for("react.activity"),m=Symbol.for("react.memo_cache_sentinel"),Q=Symbol.iterator;function F(t){return t===null||typeof t!="object"?null:(t=Q&&t[Q]||t["@@iterator"],typeof t=="function"?t:null)}var st=Symbol.for("react.client.reference");function qt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===st?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case Y:return"Fragment";case L:return"Profiler";case z:return"StrictMode";case j:return"Suspense";case I:return"SuspenseList";case Dt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case w:return"Portal";case $:return(t.displayName||"Context")+".Provider";case nt:return(t._context.displayName||"Context")+".Consumer";case k:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case J:return e=t.displayName||null,e!==null?e:qt(t.type)||"Memo";case pt:e=t._payload,t=t._init;try{return qt(t(e))}catch{}}return null}var _t=Array.isArray,M=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G=f.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},zt=[],p=-1;function q(t){return{current:t}}function V(t){0>p||(t.current=zt[p],zt[p]=null,p--)}function X(t,e){p++,zt[p]=t.current,t.current=e}var tt=q(null),ht=q(null),ut=q(null),ge=q(null);function Ut(t,e){switch(X(ut,e),X(ht,t),X(tt,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?d0(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=d0(e),t=h0(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}V(tt),X(tt,t)}function vl(){V(tt),V(ht),V(ut)}function Pi(t){t.memoizedState!==null&&X(ge,t);var e=tt.current,l=h0(e,t.type);e!==l&&(X(ht,t),X(tt,l))}function gu(t){ht.current===t&&(V(tt),V(ht)),ge.current===t&&(V(ge),Fn._currentValue=P)}var Ii=Object.prototype.hasOwnProperty,tc=u.unstable_scheduleCallback,ec=u.unstable_cancelCallback,kh=u.unstable_shouldYield,$h=u.unstable_requestPaint,Le=u.unstable_now,Jh=u.unstable_getCurrentPriorityLevel,Zf=u.unstable_ImmediatePriority,Kf=u.unstable_UserBlockingPriority,vu=u.unstable_NormalPriority,Wh=u.unstable_LowPriority,kf=u.unstable_IdlePriority,Fh=u.log,Ph=u.unstable_setDisableYieldValue,nn=null,ve=null;function pl(t){if(typeof Fh=="function"&&Ph(t),ve&&typeof ve.setStrictMode=="function")try{ve.setStrictMode(nn,t)}catch{}}var pe=Math.clz32?Math.clz32:em,Ih=Math.log,tm=Math.LN2;function em(t){return t>>>=0,t===0?32:31-(Ih(t)/tm|0)|0}var pu=256,bu=4194304;function Ll(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Su(t,e,l){var a=t.pendingLanes;if(a===0)return 0;var n=0,i=t.suspendedLanes,o=t.pingedLanes;t=t.warmLanes;var d=a&134217727;return d!==0?(a=d&~i,a!==0?n=Ll(a):(o&=d,o!==0?n=Ll(o):l||(l=d&~t,l!==0&&(n=Ll(l))))):(d=a&~i,d!==0?n=Ll(d):o!==0?n=Ll(o):l||(l=a&~t,l!==0&&(n=Ll(l)))),n===0?0:e!==0&&e!==n&&(e&i)===0&&(i=n&-n,l=e&-e,i>=l||i===32&&(l&4194048)!==0)?e:n}function un(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function lm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $f(){var t=pu;return pu<<=1,(pu&4194048)===0&&(pu=256),t}function Jf(){var t=bu;return bu<<=1,(bu&62914560)===0&&(bu=4194304),t}function lc(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function cn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function am(t,e,l,a,n,i){var o=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var d=t.entanglements,y=t.expirationTimes,O=t.hiddenUpdates;for(l=o&~l;0<l;){var U=31-pe(l),H=1<<U;d[U]=0,y[U]=-1;var _=O[U];if(_!==null)for(O[U]=null,U=0;U<_.length;U++){var C=_[U];C!==null&&(C.lane&=-536870913)}l&=~H}a!==0&&Wf(t,a,0),i!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=i&~(o&~e))}function Wf(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var a=31-pe(e);t.entangledLanes|=e,t.entanglements[a]=t.entanglements[a]|1073741824|l&4194090}function Ff(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var a=31-pe(l),n=1<<a;n&e|t[a]&e&&(t[a]|=e),l&=~n}}function ac(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function nc(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Pf(){var t=G.p;return t!==0?t:(t=window.event,t===void 0?32:U0(t.type))}function nm(t,e){var l=G.p;try{return G.p=t,e()}finally{G.p=l}}var bl=Math.random().toString(36).slice(2),ue="__reactFiber$"+bl,oe="__reactProps$"+bl,ra="__reactContainer$"+bl,uc="__reactEvents$"+bl,um="__reactListeners$"+bl,im="__reactHandles$"+bl,If="__reactResources$"+bl,rn="__reactMarker$"+bl;function ic(t){delete t[ue],delete t[oe],delete t[uc],delete t[um],delete t[im]}function fa(t){var e=t[ue];if(e)return e;for(var l=t.parentNode;l;){if(e=l[ra]||l[ue]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=v0(t);t!==null;){if(l=t[ue])return l;t=v0(t)}return e}t=l,l=t.parentNode}return null}function oa(t){if(t=t[ue]||t[ra]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fn(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function sa(t){var e=t[If];return e||(e=t[If]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ft(t){t[rn]=!0}var to=new Set,eo={};function Vl(t,e){da(t,e),da(t+"Capture",e)}function da(t,e){for(eo[t]=e,t=0;t<e.length;t++)to.add(e[t])}var cm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),lo={},ao={};function rm(t){return Ii.call(ao,t)?!0:Ii.call(lo,t)?!1:cm.test(t)?ao[t]=!0:(lo[t]=!0,!1)}function Tu(t,e,l){if(rm(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var a=e.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Au(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function Ie(t,e,l,a){if(a===null)t.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+a)}}var cc,no;function ha(t){if(cc===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);cc=e&&e[1]||"",no=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+cc+t+no}var rc=!1;function fc(t,e){if(!t||rc)return"";rc=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(e){var H=function(){throw Error()};if(Object.defineProperty(H.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(H,[])}catch(C){var _=C}Reflect.construct(t,[],H)}else{try{H.call()}catch(C){_=C}t.call(H.prototype)}}else{try{throw Error()}catch(C){_=C}(H=t())&&typeof H.catch=="function"&&H.catch(function(){})}}catch(C){if(C&&_&&typeof C.stack=="string")return[C.stack,_.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=a.DetermineComponentFrameRoot(),o=i[0],d=i[1];if(o&&d){var y=o.split(`
`),O=d.split(`
`);for(n=a=0;a<y.length&&!y[a].includes("DetermineComponentFrameRoot");)a++;for(;n<O.length&&!O[n].includes("DetermineComponentFrameRoot");)n++;if(a===y.length||n===O.length)for(a=y.length-1,n=O.length-1;1<=a&&0<=n&&y[a]!==O[n];)n--;for(;1<=a&&0<=n;a--,n--)if(y[a]!==O[n]){if(a!==1||n!==1)do if(a--,n--,0>n||y[a]!==O[n]){var U=`
`+y[a].replace(" at new "," at ");return t.displayName&&U.includes("<anonymous>")&&(U=U.replace("<anonymous>",t.displayName)),U}while(1<=a&&0<=n);break}}}finally{rc=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?ha(l):""}function fm(t){switch(t.tag){case 26:case 27:case 5:return ha(t.type);case 16:return ha("Lazy");case 13:return ha("Suspense");case 19:return ha("SuspenseList");case 0:case 15:return fc(t.type,!1);case 11:return fc(t.type.render,!1);case 1:return fc(t.type,!0);case 31:return ha("Activity");default:return""}}function uo(t){try{var e="";do e+=fm(t),t=t.return;while(t);return e}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function Re(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function io(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function om(t){var e=io(t)?"checked":"value",l=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),a=""+t[e];if(!t.hasOwnProperty(e)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,i=l.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(o){a=""+o,i.call(this,o)}}),Object.defineProperty(t,e,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(o){a=""+o},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Eu(t){t._valueTracker||(t._valueTracker=om(t))}function co(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),a="";return t&&(a=io(t)?t.checked?"true":"false":t.value),t=a,t!==l?(e.setValue(t),!0):!1}function Ou(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var sm=/[\n"\\]/g;function Me(t){return t.replace(sm,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function oc(t,e,l,a,n,i,o,d){t.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.type=o:t.removeAttribute("type"),e!=null?o==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Re(e)):t.value!==""+Re(e)&&(t.value=""+Re(e)):o!=="submit"&&o!=="reset"||t.removeAttribute("value"),e!=null?sc(t,o,Re(e)):l!=null?sc(t,o,Re(l)):a!=null&&t.removeAttribute("value"),n==null&&i!=null&&(t.defaultChecked=!!i),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+Re(d):t.removeAttribute("name")}function ro(t,e,l,a,n,i,o,d){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(t.type=i),e!=null||l!=null){if(!(i!=="submit"&&i!=="reset"||e!=null))return;l=l!=null?""+Re(l):"",e=e!=null?""+Re(e):l,d||e===t.value||(t.value=e),t.defaultValue=e}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,t.checked=d?t.checked:!!a,t.defaultChecked=!!a,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.name=o)}function sc(t,e,l){e==="number"&&Ou(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function ma(t,e,l,a){if(t=t.options,e){e={};for(var n=0;n<l.length;n++)e["$"+l[n]]=!0;for(l=0;l<t.length;l++)n=e.hasOwnProperty("$"+t[l].value),t[l].selected!==n&&(t[l].selected=n),n&&a&&(t[l].defaultSelected=!0)}else{for(l=""+Re(l),e=null,n=0;n<t.length;n++){if(t[n].value===l){t[n].selected=!0,a&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function fo(t,e,l){if(e!=null&&(e=""+Re(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+Re(l):""}function oo(t,e,l,a){if(e==null){if(a!=null){if(l!=null)throw Error(r(92));if(_t(a)){if(1<a.length)throw Error(r(93));a=a[0]}l=a}l==null&&(l=""),e=l}l=Re(e),t.defaultValue=l,a=t.textContent,a===l&&a!==""&&a!==null&&(t.value=a)}function ya(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var dm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function so(t,e,l){var a=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":a?t.setProperty(e,l):typeof l!="number"||l===0||dm.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function ho(t,e,l){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||e!=null&&e.hasOwnProperty(a)||(a.indexOf("--")===0?t.setProperty(a,""):a==="float"?t.cssFloat="":t[a]="");for(var n in e)a=e[n],e.hasOwnProperty(n)&&l[n]!==a&&so(t,n,a)}else for(var i in e)e.hasOwnProperty(i)&&so(t,i,e[i])}function dc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var hm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),mm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _u(t){return mm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var hc=null;function mc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ga=null,va=null;function mo(t){var e=oa(t);if(e&&(t=e.stateNode)){var l=t[oe]||null;t:switch(t=e.stateNode,e.type){case"input":if(oc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Me(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var a=l[e];if(a!==t&&a.form===t.form){var n=a[oe]||null;if(!n)throw Error(r(90));oc(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<l.length;e++)a=l[e],a.form===t.form&&co(a)}break t;case"textarea":fo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&ma(t,!!l.multiple,e,!1)}}}var yc=!1;function yo(t,e,l){if(yc)return t(e,l);yc=!0;try{var a=t(e);return a}finally{if(yc=!1,(ga!==null||va!==null)&&(oi(),ga&&(e=ga,t=va,va=ga=null,mo(e),t)))for(e=0;e<t.length;e++)mo(t[e])}}function on(t,e){var l=t.stateNode;if(l===null)return null;var a=l[oe]||null;if(a===null)return null;l=a[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(t=t.type,a=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!a;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(r(231,e,typeof l));return l}var tl=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),gc=!1;if(tl)try{var sn={};Object.defineProperty(sn,"passive",{get:function(){gc=!0}}),window.addEventListener("test",sn,sn),window.removeEventListener("test",sn,sn)}catch{gc=!1}var Sl=null,vc=null,zu=null;function go(){if(zu)return zu;var t,e=vc,l=e.length,a,n="value"in Sl?Sl.value:Sl.textContent,i=n.length;for(t=0;t<l&&e[t]===n[t];t++);var o=l-t;for(a=1;a<=o&&e[l-a]===n[i-a];a++);return zu=n.slice(t,1<a?1-a:void 0)}function Cu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function xu(){return!0}function vo(){return!1}function se(t){function e(l,a,n,i,o){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(l=t[d],this[d]=l?l(i):i[d]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?xu:vo,this.isPropagationStopped=vo,this}return x(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=xu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=xu)},persist:function(){},isPersistent:xu}),e}var Zl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ru=se(Zl),dn=x({},Zl,{view:0,detail:0}),ym=se(dn),pc,bc,hn,Mu=x({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Tc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==hn&&(hn&&t.type==="mousemove"?(pc=t.screenX-hn.screenX,bc=t.screenY-hn.screenY):bc=pc=0,hn=t),pc)},movementY:function(t){return"movementY"in t?t.movementY:bc}}),po=se(Mu),gm=x({},Mu,{dataTransfer:0}),vm=se(gm),pm=x({},dn,{relatedTarget:0}),Sc=se(pm),bm=x({},Zl,{animationName:0,elapsedTime:0,pseudoElement:0}),Sm=se(bm),Tm=x({},Zl,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Am=se(Tm),Em=x({},Zl,{data:0}),bo=se(Em),Om={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},_m={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},zm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cm(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=zm[t])?!!e[t]:!1}function Tc(){return Cm}var xm=x({},dn,{key:function(t){if(t.key){var e=Om[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Cu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?_m[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Tc,charCode:function(t){return t.type==="keypress"?Cu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Cu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Rm=se(xm),Mm=x({},Mu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),So=se(Mm),Dm=x({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Tc}),Um=se(Dm),Bm=x({},Zl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Nm=se(Bm),Hm=x({},Mu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),qm=se(Hm),Ym=x({},Zl,{newState:0,oldState:0}),jm=se(Ym),Gm=[9,13,27,32],Ac=tl&&"CompositionEvent"in window,mn=null;tl&&"documentMode"in document&&(mn=document.documentMode);var wm=tl&&"TextEvent"in window&&!mn,To=tl&&(!Ac||mn&&8<mn&&11>=mn),Ao=" ",Eo=!1;function Oo(t,e){switch(t){case"keyup":return Gm.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function _o(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var pa=!1;function Xm(t,e){switch(t){case"compositionend":return _o(e);case"keypress":return e.which!==32?null:(Eo=!0,Ao);case"textInput":return t=e.data,t===Ao&&Eo?null:t;default:return null}}function Qm(t,e){if(pa)return t==="compositionend"||!Ac&&Oo(t,e)?(t=go(),zu=vc=Sl=null,pa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return To&&e.locale!=="ko"?null:e.data;default:return null}}var Lm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function zo(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Lm[t.type]:e==="textarea"}function Co(t,e,l,a){ga?va?va.push(a):va=[a]:ga=a,e=gi(e,"onChange"),0<e.length&&(l=new Ru("onChange","change",null,l,a),t.push({event:l,listeners:e}))}var yn=null,gn=null;function Vm(t){c0(t,0)}function Du(t){var e=fn(t);if(co(e))return t}function xo(t,e){if(t==="change")return e}var Ro=!1;if(tl){var Ec;if(tl){var Oc="oninput"in document;if(!Oc){var Mo=document.createElement("div");Mo.setAttribute("oninput","return;"),Oc=typeof Mo.oninput=="function"}Ec=Oc}else Ec=!1;Ro=Ec&&(!document.documentMode||9<document.documentMode)}function Do(){yn&&(yn.detachEvent("onpropertychange",Uo),gn=yn=null)}function Uo(t){if(t.propertyName==="value"&&Du(gn)){var e=[];Co(e,gn,t,mc(t)),yo(Vm,e)}}function Zm(t,e,l){t==="focusin"?(Do(),yn=e,gn=l,yn.attachEvent("onpropertychange",Uo)):t==="focusout"&&Do()}function Km(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Du(gn)}function km(t,e){if(t==="click")return Du(e)}function $m(t,e){if(t==="input"||t==="change")return Du(e)}function Jm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var be=typeof Object.is=="function"?Object.is:Jm;function vn(t,e){if(be(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),a=Object.keys(e);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!Ii.call(e,n)||!be(t[n],e[n]))return!1}return!0}function Bo(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function No(t,e){var l=Bo(t);t=0;for(var a;l;){if(l.nodeType===3){if(a=t+l.textContent.length,t<=e&&a>=e)return{node:l,offset:e-t};t=a}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Bo(l)}}function Ho(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Ho(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function qo(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ou(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=Ou(t.document)}return e}function _c(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var Wm=tl&&"documentMode"in document&&11>=document.documentMode,ba=null,zc=null,pn=null,Cc=!1;function Yo(t,e,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Cc||ba==null||ba!==Ou(a)||(a=ba,"selectionStart"in a&&_c(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),pn&&vn(pn,a)||(pn=a,a=gi(zc,"onSelect"),0<a.length&&(e=new Ru("onSelect","select",null,e,l),t.push({event:e,listeners:a}),e.target=ba)))}function Kl(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Sa={animationend:Kl("Animation","AnimationEnd"),animationiteration:Kl("Animation","AnimationIteration"),animationstart:Kl("Animation","AnimationStart"),transitionrun:Kl("Transition","TransitionRun"),transitionstart:Kl("Transition","TransitionStart"),transitioncancel:Kl("Transition","TransitionCancel"),transitionend:Kl("Transition","TransitionEnd")},xc={},jo={};tl&&(jo=document.createElement("div").style,"AnimationEvent"in window||(delete Sa.animationend.animation,delete Sa.animationiteration.animation,delete Sa.animationstart.animation),"TransitionEvent"in window||delete Sa.transitionend.transition);function kl(t){if(xc[t])return xc[t];if(!Sa[t])return t;var e=Sa[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in jo)return xc[t]=e[l];return t}var Go=kl("animationend"),wo=kl("animationiteration"),Xo=kl("animationstart"),Fm=kl("transitionrun"),Pm=kl("transitionstart"),Im=kl("transitioncancel"),Qo=kl("transitionend"),Lo=new Map,Rc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Rc.push("scrollEnd");function we(t,e){Lo.set(t,e),Vl(e,[t])}var Vo=new WeakMap;function De(t,e){if(typeof t=="object"&&t!==null){var l=Vo.get(t);return l!==void 0?l:(e={value:t,source:e,stack:uo(e)},Vo.set(t,e),e)}return{value:t,source:e,stack:uo(e)}}var Ue=[],Ta=0,Mc=0;function Uu(){for(var t=Ta,e=Mc=Ta=0;e<t;){var l=Ue[e];Ue[e++]=null;var a=Ue[e];Ue[e++]=null;var n=Ue[e];Ue[e++]=null;var i=Ue[e];if(Ue[e++]=null,a!==null&&n!==null){var o=a.pending;o===null?n.next=n:(n.next=o.next,o.next=n),a.pending=n}i!==0&&Zo(l,n,i)}}function Bu(t,e,l,a){Ue[Ta++]=t,Ue[Ta++]=e,Ue[Ta++]=l,Ue[Ta++]=a,Mc|=a,t.lanes|=a,t=t.alternate,t!==null&&(t.lanes|=a)}function Dc(t,e,l,a){return Bu(t,e,l,a),Nu(t)}function Aa(t,e){return Bu(t,null,null,e),Nu(t)}function Zo(t,e,l){t.lanes|=l;var a=t.alternate;a!==null&&(a.lanes|=l);for(var n=!1,i=t.return;i!==null;)i.childLanes|=l,a=i.alternate,a!==null&&(a.childLanes|=l),i.tag===22&&(t=i.stateNode,t===null||t._visibility&1||(n=!0)),t=i,i=i.return;return t.tag===3?(i=t.stateNode,n&&e!==null&&(n=31-pe(l),t=i.hiddenUpdates,a=t[n],a===null?t[n]=[e]:a.push(e),e.lane=l|536870912),i):null}function Nu(t){if(50<Ln)throw Ln=0,Yr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ea={};function ty(t,e,l,a){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Se(t,e,l,a){return new ty(t,e,l,a)}function Uc(t){return t=t.prototype,!(!t||!t.isReactComponent)}function el(t,e){var l=t.alternate;return l===null?(l=Se(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Ko(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Hu(t,e,l,a,n,i){var o=0;if(a=t,typeof t=="function")Uc(t)&&(o=1);else if(typeof t=="string")o=lg(t,l,tt.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Dt:return t=Se(31,l,e,n),t.elementType=Dt,t.lanes=i,t;case Y:return $l(l.children,n,i,e);case z:o=8,n|=24;break;case L:return t=Se(12,l,e,n|2),t.elementType=L,t.lanes=i,t;case j:return t=Se(13,l,e,n),t.elementType=j,t.lanes=i,t;case I:return t=Se(19,l,e,n),t.elementType=I,t.lanes=i,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case K:case $:o=10;break t;case nt:o=9;break t;case k:o=11;break t;case J:o=14;break t;case pt:o=16,a=null;break t}o=29,l=Error(r(130,t===null?"null":typeof t,"")),a=null}return e=Se(o,l,e,n),e.elementType=t,e.type=a,e.lanes=i,e}function $l(t,e,l,a){return t=Se(7,t,a,e),t.lanes=l,t}function Bc(t,e,l){return t=Se(6,t,null,e),t.lanes=l,t}function Nc(t,e,l){return e=Se(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Oa=[],_a=0,qu=null,Yu=0,Be=[],Ne=0,Jl=null,ll=1,al="";function Wl(t,e){Oa[_a++]=Yu,Oa[_a++]=qu,qu=t,Yu=e}function ko(t,e,l){Be[Ne++]=ll,Be[Ne++]=al,Be[Ne++]=Jl,Jl=t;var a=ll;t=al;var n=32-pe(a)-1;a&=~(1<<n),l+=1;var i=32-pe(e)+n;if(30<i){var o=n-n%5;i=(a&(1<<o)-1).toString(32),a>>=o,n-=o,ll=1<<32-pe(e)+n|l<<n|a,al=i+t}else ll=1<<i|l<<n|a,al=t}function Hc(t){t.return!==null&&(Wl(t,1),ko(t,1,0))}function qc(t){for(;t===qu;)qu=Oa[--_a],Oa[_a]=null,Yu=Oa[--_a],Oa[_a]=null;for(;t===Jl;)Jl=Be[--Ne],Be[Ne]=null,al=Be[--Ne],Be[Ne]=null,ll=Be[--Ne],Be[Ne]=null}var fe=null,jt=null,yt=!1,Fl=null,Ve=!1,Yc=Error(r(519));function Pl(t){var e=Error(r(418,""));throw Tn(De(e,t)),Yc}function $o(t){var e=t.stateNode,l=t.type,a=t.memoizedProps;switch(e[ue]=t,e[oe]=a,l){case"dialog":ot("cancel",e),ot("close",e);break;case"iframe":case"object":case"embed":ot("load",e);break;case"video":case"audio":for(l=0;l<Zn.length;l++)ot(Zn[l],e);break;case"source":ot("error",e);break;case"img":case"image":case"link":ot("error",e),ot("load",e);break;case"details":ot("toggle",e);break;case"input":ot("invalid",e),ro(e,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),Eu(e);break;case"select":ot("invalid",e);break;case"textarea":ot("invalid",e),oo(e,a.value,a.defaultValue,a.children),Eu(e)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||a.suppressHydrationWarning===!0||s0(e.textContent,l)?(a.popover!=null&&(ot("beforetoggle",e),ot("toggle",e)),a.onScroll!=null&&ot("scroll",e),a.onScrollEnd!=null&&ot("scrollend",e),a.onClick!=null&&(e.onclick=vi),e=!0):e=!1,e||Pl(t)}function Jo(t){for(fe=t.return;fe;)switch(fe.tag){case 5:case 13:Ve=!1;return;case 27:case 3:Ve=!0;return;default:fe=fe.return}}function bn(t){if(t!==fe)return!1;if(!yt)return Jo(t),yt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||Ir(t.type,t.memoizedProps)),l=!l),l&&jt&&Pl(t),Jo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(l=t.data,l==="/$"){if(e===0){jt=Qe(t.nextSibling);break t}e--}else l!=="$"&&l!=="$!"&&l!=="$?"||e++;t=t.nextSibling}jt=null}}else e===27?(e=jt,ql(t.type)?(t=af,af=null,jt=t):jt=e):jt=fe?Qe(t.stateNode.nextSibling):null;return!0}function Sn(){jt=fe=null,yt=!1}function Wo(){var t=Fl;return t!==null&&(me===null?me=t:me.push.apply(me,t),Fl=null),t}function Tn(t){Fl===null?Fl=[t]:Fl.push(t)}var jc=q(null),Il=null,nl=null;function Tl(t,e,l){X(jc,e._currentValue),e._currentValue=l}function ul(t){t._currentValue=jc.current,V(jc)}function Gc(t,e,l){for(;t!==null;){var a=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,a!==null&&(a.childLanes|=e)):a!==null&&(a.childLanes&e)!==e&&(a.childLanes|=e),t===l)break;t=t.return}}function wc(t,e,l,a){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var i=n.dependencies;if(i!==null){var o=n.child;i=i.firstContext;t:for(;i!==null;){var d=i;i=n;for(var y=0;y<e.length;y++)if(d.context===e[y]){i.lanes|=l,d=i.alternate,d!==null&&(d.lanes|=l),Gc(i.return,l,t),a||(o=null);break t}i=d.next}}else if(n.tag===18){if(o=n.return,o===null)throw Error(r(341));o.lanes|=l,i=o.alternate,i!==null&&(i.lanes|=l),Gc(o,l,t),o=null}else o=n.child;if(o!==null)o.return=n;else for(o=n;o!==null;){if(o===t){o=null;break}if(n=o.sibling,n!==null){n.return=o.return,o=n;break}o=o.return}n=o}}function An(t,e,l,a){t=null;for(var n=e,i=!1;n!==null;){if(!i){if((n.flags&524288)!==0)i=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var o=n.alternate;if(o===null)throw Error(r(387));if(o=o.memoizedProps,o!==null){var d=n.type;be(n.pendingProps.value,o.value)||(t!==null?t.push(d):t=[d])}}else if(n===ge.current){if(o=n.alternate,o===null)throw Error(r(387));o.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Fn):t=[Fn])}n=n.return}t!==null&&wc(e,t,l,a),e.flags|=262144}function ju(t){for(t=t.firstContext;t!==null;){if(!be(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function ta(t){Il=t,nl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ie(t){return Fo(Il,t)}function Gu(t,e){return Il===null&&ta(t),Fo(t,e)}function Fo(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},nl===null){if(t===null)throw Error(r(308));nl=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else nl=nl.next=e;return l}var ey=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,a){t.push(a)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},ly=u.unstable_scheduleCallback,ay=u.unstable_NormalPriority,Jt={$$typeof:$,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Xc(){return{controller:new ey,data:new Map,refCount:0}}function En(t){t.refCount--,t.refCount===0&&ly(ay,function(){t.controller.abort()})}var On=null,Qc=0,za=0,Ca=null;function ny(t,e){if(On===null){var l=On=[];Qc=0,za=Vr(),Ca={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Qc++,e.then(Po,Po),e}function Po(){if(--Qc===0&&On!==null){Ca!==null&&(Ca.status="fulfilled");var t=On;On=null,za=0,Ca=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function uy(t,e){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return t.then(function(){a.status="fulfilled",a.value=e;for(var n=0;n<l.length;n++)(0,l[n])(e)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Io=M.S;M.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&ny(t,e),Io!==null&&Io(t,e)};var ea=q(null);function Lc(){var t=ea.current;return t!==null?t:Mt.pooledCache}function wu(t,e){e===null?X(ea,ea.current):X(ea,e.pool)}function ts(){var t=Lc();return t===null?null:{parent:Jt._currentValue,pool:t}}var _n=Error(r(460)),es=Error(r(474)),Xu=Error(r(542)),Vc={then:function(){}};function ls(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Qu(){}function as(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(Qu,Qu),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,us(t),t;default:if(typeof e.status=="string")e.then(Qu,Qu);else{if(t=Mt,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(a){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=a}},function(a){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=a}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,us(t),t}throw zn=e,_n}}var zn=null;function ns(){if(zn===null)throw Error(r(459));var t=zn;return zn=null,t}function us(t){if(t===_n||t===Xu)throw Error(r(483))}var Al=!1;function Zc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Kc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function El(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Ol(t,e,l){var a=t.updateQueue;if(a===null)return null;if(a=a.shared,(bt&2)!==0){var n=a.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),a.pending=e,e=Nu(t),Zo(t,null,l),e}return Bu(t,a,e,l),Nu(t)}function Cn(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Ff(t,l)}}function kc(t,e){var l=t.updateQueue,a=t.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,i=null;if(l=l.firstBaseUpdate,l!==null){do{var o={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};i===null?n=i=o:i=i.next=o,l=l.next}while(l!==null);i===null?n=i=e:i=i.next=e}else n=i=e;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:i,shared:a.shared,callbacks:a.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var $c=!1;function xn(){if($c){var t=Ca;if(t!==null)throw t}}function Rn(t,e,l,a){$c=!1;var n=t.updateQueue;Al=!1;var i=n.firstBaseUpdate,o=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var y=d,O=y.next;y.next=null,o===null?i=O:o.next=O,o=y;var U=t.alternate;U!==null&&(U=U.updateQueue,d=U.lastBaseUpdate,d!==o&&(d===null?U.firstBaseUpdate=O:d.next=O,U.lastBaseUpdate=y))}if(i!==null){var H=n.baseState;o=0,U=O=y=null,d=i;do{var _=d.lane&-536870913,C=_!==d.lane;if(C?(dt&_)===_:(a&_)===_){_!==0&&_===za&&($c=!0),U!==null&&(U=U.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var at=t,et=d;_=e;var Et=l;switch(et.tag){case 1:if(at=et.payload,typeof at=="function"){H=at.call(Et,H,_);break t}H=at;break t;case 3:at.flags=at.flags&-65537|128;case 0:if(at=et.payload,_=typeof at=="function"?at.call(Et,H,_):at,_==null)break t;H=x({},H,_);break t;case 2:Al=!0}}_=d.callback,_!==null&&(t.flags|=64,C&&(t.flags|=8192),C=n.callbacks,C===null?n.callbacks=[_]:C.push(_))}else C={lane:_,tag:d.tag,payload:d.payload,callback:d.callback,next:null},U===null?(O=U=C,y=H):U=U.next=C,o|=_;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;C=d,d=C.next,C.next=null,n.lastBaseUpdate=C,n.shared.pending=null}}while(!0);U===null&&(y=H),n.baseState=y,n.firstBaseUpdate=O,n.lastBaseUpdate=U,i===null&&(n.shared.lanes=0),Ul|=o,t.lanes=o,t.memoizedState=H}}function is(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function cs(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)is(l[t],e)}var xa=q(null),Lu=q(0);function rs(t,e){t=dl,X(Lu,t),X(xa,e),dl=t|e.baseLanes}function Jc(){X(Lu,dl),X(xa,xa.current)}function Wc(){dl=Lu.current,V(xa),V(Lu)}var _l=0,ct=null,Tt=null,Kt=null,Vu=!1,Ra=!1,la=!1,Zu=0,Mn=0,Ma=null,iy=0;function Lt(){throw Error(r(321))}function Fc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!be(t[l],e[l]))return!1;return!0}function Pc(t,e,l,a,n,i){return _l=i,ct=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,M.H=t===null||t.memoizedState===null?Zs:Ks,la=!1,i=l(a,n),la=!1,Ra&&(i=os(e,l,a,n)),fs(t),i}function fs(t){M.H=Fu;var e=Tt!==null&&Tt.next!==null;if(_l=0,Kt=Tt=ct=null,Vu=!1,Mn=0,Ma=null,e)throw Error(r(300));t===null||Pt||(t=t.dependencies,t!==null&&ju(t)&&(Pt=!0))}function os(t,e,l,a){ct=t;var n=0;do{if(Ra&&(Ma=null),Mn=0,Ra=!1,25<=n)throw Error(r(301));if(n+=1,Kt=Tt=null,t.updateQueue!=null){var i=t.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}M.H=hy,i=e(l,a)}while(Ra);return i}function cy(){var t=M.H,e=t.useState()[0];return e=typeof e.then=="function"?Dn(e):e,t=t.useState()[0],(Tt!==null?Tt.memoizedState:null)!==t&&(ct.flags|=1024),e}function Ic(){var t=Zu!==0;return Zu=0,t}function tr(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function er(t){if(Vu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Vu=!1}_l=0,Kt=Tt=ct=null,Ra=!1,Mn=Zu=0,Ma=null}function de(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Kt===null?ct.memoizedState=Kt=t:Kt=Kt.next=t,Kt}function kt(){if(Tt===null){var t=ct.alternate;t=t!==null?t.memoizedState:null}else t=Tt.next;var e=Kt===null?ct.memoizedState:Kt.next;if(e!==null)Kt=e,Tt=t;else{if(t===null)throw ct.alternate===null?Error(r(467)):Error(r(310));Tt=t,t={memoizedState:Tt.memoizedState,baseState:Tt.baseState,baseQueue:Tt.baseQueue,queue:Tt.queue,next:null},Kt===null?ct.memoizedState=Kt=t:Kt=Kt.next=t}return Kt}function lr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Dn(t){var e=Mn;return Mn+=1,Ma===null&&(Ma=[]),t=as(Ma,t,e),e=ct,(Kt===null?e.memoizedState:Kt.next)===null&&(e=e.alternate,M.H=e===null||e.memoizedState===null?Zs:Ks),t}function Ku(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Dn(t);if(t.$$typeof===$)return ie(t)}throw Error(r(438,String(t)))}function ar(t){var e=null,l=ct.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var a=ct.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(e={data:a.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=lr(),ct.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),a=0;a<t;a++)l[a]=m;return e.index++,l}function il(t,e){return typeof e=="function"?e(t):e}function ku(t){var e=kt();return nr(e,Tt,t)}function nr(t,e,l){var a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=l;var n=t.baseQueue,i=a.pending;if(i!==null){if(n!==null){var o=n.next;n.next=i.next,i.next=o}e.baseQueue=n=i,a.pending=null}if(i=t.baseState,n===null)t.memoizedState=i;else{e=n.next;var d=o=null,y=null,O=e,U=!1;do{var H=O.lane&-536870913;if(H!==O.lane?(dt&H)===H:(_l&H)===H){var _=O.revertLane;if(_===0)y!==null&&(y=y.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),H===za&&(U=!0);else if((_l&_)===_){O=O.next,_===za&&(U=!0);continue}else H={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=H,o=i):y=y.next=H,ct.lanes|=_,Ul|=_;H=O.action,la&&l(i,H),i=O.hasEagerState?O.eagerState:l(i,H)}else _={lane:H,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},y===null?(d=y=_,o=i):y=y.next=_,ct.lanes|=H,Ul|=H;O=O.next}while(O!==null&&O!==e);if(y===null?o=i:y.next=d,!be(i,t.memoizedState)&&(Pt=!0,U&&(l=Ca,l!==null)))throw l;t.memoizedState=i,t.baseState=o,t.baseQueue=y,a.lastRenderedState=i}return n===null&&(a.lanes=0),[t.memoizedState,a.dispatch]}function ur(t){var e=kt(),l=e.queue;if(l===null)throw Error(r(311));l.lastRenderedReducer=t;var a=l.dispatch,n=l.pending,i=e.memoizedState;if(n!==null){l.pending=null;var o=n=n.next;do i=t(i,o.action),o=o.next;while(o!==n);be(i,e.memoizedState)||(Pt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),l.lastRenderedState=i}return[i,a]}function ss(t,e,l){var a=ct,n=kt(),i=yt;if(i){if(l===void 0)throw Error(r(407));l=l()}else l=e();var o=!be((Tt||n).memoizedState,l);o&&(n.memoizedState=l,Pt=!0),n=n.queue;var d=ms.bind(null,a,n,t);if(Un(2048,8,d,[t]),n.getSnapshot!==e||o||Kt!==null&&Kt.memoizedState.tag&1){if(a.flags|=2048,Da(9,$u(),hs.bind(null,a,n,l,e),null),Mt===null)throw Error(r(349));i||(_l&124)!==0||ds(a,e,l)}return l}function ds(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=ct.updateQueue,e===null?(e=lr(),ct.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function hs(t,e,l,a){e.value=l,e.getSnapshot=a,ys(e)&&gs(t)}function ms(t,e,l){return l(function(){ys(e)&&gs(t)})}function ys(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!be(t,l)}catch{return!0}}function gs(t){var e=Aa(t,2);e!==null&&_e(e,t,2)}function ir(t){var e=de();if(typeof t=="function"){var l=t;if(t=l(),la){pl(!0);try{l()}finally{pl(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:t},e}function vs(t,e,l,a){return t.baseState=l,nr(t,Tt,typeof a=="function"?a:il)}function ry(t,e,l,a,n){if(Wu(t))throw Error(r(485));if(t=e.action,t!==null){var i={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){i.listeners.push(o)}};M.T!==null?l(!0):i.isTransition=!1,a(i),l=e.pending,l===null?(i.next=e.pending=i,ps(e,i)):(i.next=l.next,e.pending=l.next=i)}}function ps(t,e){var l=e.action,a=e.payload,n=t.state;if(e.isTransition){var i=M.T,o={};M.T=o;try{var d=l(n,a),y=M.S;y!==null&&y(o,d),bs(t,e,d)}catch(O){cr(t,e,O)}finally{M.T=i}}else try{i=l(n,a),bs(t,e,i)}catch(O){cr(t,e,O)}}function bs(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){Ss(t,e,a)},function(a){return cr(t,e,a)}):Ss(t,e,l)}function Ss(t,e,l){e.status="fulfilled",e.value=l,Ts(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,ps(t,l)))}function cr(t,e,l){var a=t.pending;if(t.pending=null,a!==null){a=a.next;do e.status="rejected",e.reason=l,Ts(e),e=e.next;while(e!==a)}t.action=null}function Ts(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function As(t,e){return e}function Es(t,e){if(yt){var l=Mt.formState;if(l!==null){t:{var a=ct;if(yt){if(jt){e:{for(var n=jt,i=Ve;n.nodeType!==8;){if(!i){n=null;break e}if(n=Qe(n.nextSibling),n===null){n=null;break e}}i=n.data,n=i==="F!"||i==="F"?n:null}if(n){jt=Qe(n.nextSibling),a=n.data==="F!";break t}}Pl(a)}a=!1}a&&(e=l[0])}}return l=de(),l.memoizedState=l.baseState=e,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:As,lastRenderedState:e},l.queue=a,l=Qs.bind(null,ct,a),a.dispatch=l,a=ir(!1),i=dr.bind(null,ct,!1,a.queue),a=de(),n={state:e,dispatch:null,action:t,pending:null},a.queue=n,l=ry.bind(null,ct,n,i,l),n.dispatch=l,a.memoizedState=t,[e,l,!1]}function Os(t){var e=kt();return _s(e,Tt,t)}function _s(t,e,l){if(e=nr(t,e,As)[0],t=ku(il)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var a=Dn(e)}catch(o){throw o===_n?Xu:o}else a=e;e=kt();var n=e.queue,i=n.dispatch;return l!==e.memoizedState&&(ct.flags|=2048,Da(9,$u(),fy.bind(null,n,l),null)),[a,i,t]}function fy(t,e){t.action=e}function zs(t){var e=kt(),l=Tt;if(l!==null)return _s(e,l,t);kt(),e=e.memoizedState,l=kt();var a=l.queue.dispatch;return l.memoizedState=t,[e,a,!1]}function Da(t,e,l,a){return t={tag:t,create:l,deps:a,inst:e,next:null},e=ct.updateQueue,e===null&&(e=lr(),ct.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(a=l.next,l.next=t,t.next=a,e.lastEffect=t),t}function $u(){return{destroy:void 0,resource:void 0}}function Cs(){return kt().memoizedState}function Ju(t,e,l,a){var n=de();a=a===void 0?null:a,ct.flags|=t,n.memoizedState=Da(1|e,$u(),l,a)}function Un(t,e,l,a){var n=kt();a=a===void 0?null:a;var i=n.memoizedState.inst;Tt!==null&&a!==null&&Fc(a,Tt.memoizedState.deps)?n.memoizedState=Da(e,i,l,a):(ct.flags|=t,n.memoizedState=Da(1|e,i,l,a))}function xs(t,e){Ju(8390656,8,t,e)}function Rs(t,e){Un(2048,8,t,e)}function Ms(t,e){return Un(4,2,t,e)}function Ds(t,e){return Un(4,4,t,e)}function Us(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Bs(t,e,l){l=l!=null?l.concat([t]):null,Un(4,4,Us.bind(null,e,t),l)}function rr(){}function Ns(t,e){var l=kt();e=e===void 0?null:e;var a=l.memoizedState;return e!==null&&Fc(e,a[1])?a[0]:(l.memoizedState=[t,e],t)}function Hs(t,e){var l=kt();e=e===void 0?null:e;var a=l.memoizedState;if(e!==null&&Fc(e,a[1]))return a[0];if(a=t(),la){pl(!0);try{t()}finally{pl(!1)}}return l.memoizedState=[a,e],a}function fr(t,e,l){return l===void 0||(_l&1073741824)!==0?t.memoizedState=e:(t.memoizedState=l,t=jd(),ct.lanes|=t,Ul|=t,l)}function qs(t,e,l,a){return be(l,e)?l:xa.current!==null?(t=fr(t,l,a),be(t,e)||(Pt=!0),t):(_l&42)===0?(Pt=!0,t.memoizedState=l):(t=jd(),ct.lanes|=t,Ul|=t,e)}function Ys(t,e,l,a,n){var i=G.p;G.p=i!==0&&8>i?i:8;var o=M.T,d={};M.T=d,dr(t,!1,e,l);try{var y=n(),O=M.S;if(O!==null&&O(d,y),y!==null&&typeof y=="object"&&typeof y.then=="function"){var U=uy(y,a);Bn(t,e,U,Oe(t))}else Bn(t,e,a,Oe(t))}catch(H){Bn(t,e,{then:function(){},status:"rejected",reason:H},Oe())}finally{G.p=i,M.T=o}}function oy(){}function or(t,e,l,a){if(t.tag!==5)throw Error(r(476));var n=js(t).queue;Ys(t,n,e,P,l===null?oy:function(){return Gs(t),l(a)})}function js(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:P},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Gs(t){var e=js(t).next.queue;Bn(t,e,{},Oe())}function sr(){return ie(Fn)}function ws(){return kt().memoizedState}function Xs(){return kt().memoizedState}function sy(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=Oe();t=El(l);var a=Ol(e,t,l);a!==null&&(_e(a,e,l),Cn(a,e,l)),e={cache:Xc()},t.payload=e;return}e=e.return}}function dy(t,e,l){var a=Oe();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Wu(t)?Ls(e,l):(l=Dc(t,e,l,a),l!==null&&(_e(l,t,a),Vs(l,e,a)))}function Qs(t,e,l){var a=Oe();Bn(t,e,l,a)}function Bn(t,e,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Wu(t))Ls(e,n);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var o=e.lastRenderedState,d=i(o,l);if(n.hasEagerState=!0,n.eagerState=d,be(d,o))return Bu(t,e,n,0),Mt===null&&Uu(),!1}catch{}finally{}if(l=Dc(t,e,n,a),l!==null)return _e(l,t,a),Vs(l,e,a),!0}return!1}function dr(t,e,l,a){if(a={lane:2,revertLane:Vr(),action:a,hasEagerState:!1,eagerState:null,next:null},Wu(t)){if(e)throw Error(r(479))}else e=Dc(t,l,a,2),e!==null&&_e(e,t,2)}function Wu(t){var e=t.alternate;return t===ct||e!==null&&e===ct}function Ls(t,e){Ra=Vu=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Vs(t,e,l){if((l&4194048)!==0){var a=e.lanes;a&=t.pendingLanes,l|=a,e.lanes=l,Ff(t,l)}}var Fu={readContext:ie,use:Ku,useCallback:Lt,useContext:Lt,useEffect:Lt,useImperativeHandle:Lt,useLayoutEffect:Lt,useInsertionEffect:Lt,useMemo:Lt,useReducer:Lt,useRef:Lt,useState:Lt,useDebugValue:Lt,useDeferredValue:Lt,useTransition:Lt,useSyncExternalStore:Lt,useId:Lt,useHostTransitionStatus:Lt,useFormState:Lt,useActionState:Lt,useOptimistic:Lt,useMemoCache:Lt,useCacheRefresh:Lt},Zs={readContext:ie,use:Ku,useCallback:function(t,e){return de().memoizedState=[t,e===void 0?null:e],t},useContext:ie,useEffect:xs,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,Ju(4194308,4,Us.bind(null,e,t),l)},useLayoutEffect:function(t,e){return Ju(4194308,4,t,e)},useInsertionEffect:function(t,e){Ju(4,2,t,e)},useMemo:function(t,e){var l=de();e=e===void 0?null:e;var a=t();if(la){pl(!0);try{t()}finally{pl(!1)}}return l.memoizedState=[a,e],a},useReducer:function(t,e,l){var a=de();if(l!==void 0){var n=l(e);if(la){pl(!0);try{l(e)}finally{pl(!1)}}}else n=e;return a.memoizedState=a.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},a.queue=t,t=t.dispatch=dy.bind(null,ct,t),[a.memoizedState,t]},useRef:function(t){var e=de();return t={current:t},e.memoizedState=t},useState:function(t){t=ir(t);var e=t.queue,l=Qs.bind(null,ct,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:rr,useDeferredValue:function(t,e){var l=de();return fr(l,t,e)},useTransition:function(){var t=ir(!1);return t=Ys.bind(null,ct,t.queue,!0,!1),de().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var a=ct,n=de();if(yt){if(l===void 0)throw Error(r(407));l=l()}else{if(l=e(),Mt===null)throw Error(r(349));(dt&124)!==0||ds(a,e,l)}n.memoizedState=l;var i={value:l,getSnapshot:e};return n.queue=i,xs(ms.bind(null,a,i,t),[t]),a.flags|=2048,Da(9,$u(),hs.bind(null,a,i,l,e),null),l},useId:function(){var t=de(),e=Mt.identifierPrefix;if(yt){var l=al,a=ll;l=(a&~(1<<32-pe(a)-1)).toString(32)+l,e="«"+e+"R"+l,l=Zu++,0<l&&(e+="H"+l.toString(32)),e+="»"}else l=iy++,e="«"+e+"r"+l.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:sr,useFormState:Es,useActionState:Es,useOptimistic:function(t){var e=de();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=dr.bind(null,ct,!0,l),l.dispatch=e,[t,e]},useMemoCache:ar,useCacheRefresh:function(){return de().memoizedState=sy.bind(null,ct)}},Ks={readContext:ie,use:Ku,useCallback:Ns,useContext:ie,useEffect:Rs,useImperativeHandle:Bs,useInsertionEffect:Ms,useLayoutEffect:Ds,useMemo:Hs,useReducer:ku,useRef:Cs,useState:function(){return ku(il)},useDebugValue:rr,useDeferredValue:function(t,e){var l=kt();return qs(l,Tt.memoizedState,t,e)},useTransition:function(){var t=ku(il)[0],e=kt().memoizedState;return[typeof t=="boolean"?t:Dn(t),e]},useSyncExternalStore:ss,useId:ws,useHostTransitionStatus:sr,useFormState:Os,useActionState:Os,useOptimistic:function(t,e){var l=kt();return vs(l,Tt,t,e)},useMemoCache:ar,useCacheRefresh:Xs},hy={readContext:ie,use:Ku,useCallback:Ns,useContext:ie,useEffect:Rs,useImperativeHandle:Bs,useInsertionEffect:Ms,useLayoutEffect:Ds,useMemo:Hs,useReducer:ur,useRef:Cs,useState:function(){return ur(il)},useDebugValue:rr,useDeferredValue:function(t,e){var l=kt();return Tt===null?fr(l,t,e):qs(l,Tt.memoizedState,t,e)},useTransition:function(){var t=ur(il)[0],e=kt().memoizedState;return[typeof t=="boolean"?t:Dn(t),e]},useSyncExternalStore:ss,useId:ws,useHostTransitionStatus:sr,useFormState:zs,useActionState:zs,useOptimistic:function(t,e){var l=kt();return Tt!==null?vs(l,Tt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:ar,useCacheRefresh:Xs},Ua=null,Nn=0;function Pu(t){var e=Nn;return Nn+=1,Ua===null&&(Ua=[]),as(Ua,t,e)}function Hn(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Iu(t,e){throw e.$$typeof===R?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function ks(t){var e=t._init;return e(t._payload)}function $s(t){function e(T,b){if(t){var E=T.deletions;E===null?(T.deletions=[b],T.flags|=16):E.push(b)}}function l(T,b){if(!t)return null;for(;b!==null;)e(T,b),b=b.sibling;return null}function a(T){for(var b=new Map;T!==null;)T.key!==null?b.set(T.key,T):b.set(T.index,T),T=T.sibling;return b}function n(T,b){return T=el(T,b),T.index=0,T.sibling=null,T}function i(T,b,E){return T.index=E,t?(E=T.alternate,E!==null?(E=E.index,E<b?(T.flags|=67108866,b):E):(T.flags|=67108866,b)):(T.flags|=1048576,b)}function o(T){return t&&T.alternate===null&&(T.flags|=67108866),T}function d(T,b,E,B){return b===null||b.tag!==6?(b=Bc(E,T.mode,B),b.return=T,b):(b=n(b,E),b.return=T,b)}function y(T,b,E,B){var Z=E.type;return Z===Y?U(T,b,E.props.children,B,E.key):b!==null&&(b.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===pt&&ks(Z)===b.type)?(b=n(b,E.props),Hn(b,E),b.return=T,b):(b=Hu(E.type,E.key,E.props,null,T.mode,B),Hn(b,E),b.return=T,b)}function O(T,b,E,B){return b===null||b.tag!==4||b.stateNode.containerInfo!==E.containerInfo||b.stateNode.implementation!==E.implementation?(b=Nc(E,T.mode,B),b.return=T,b):(b=n(b,E.children||[]),b.return=T,b)}function U(T,b,E,B,Z){return b===null||b.tag!==7?(b=$l(E,T.mode,B,Z),b.return=T,b):(b=n(b,E),b.return=T,b)}function H(T,b,E){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Bc(""+b,T.mode,E),b.return=T,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case N:return E=Hu(b.type,b.key,b.props,null,T.mode,E),Hn(E,b),E.return=T,E;case w:return b=Nc(b,T.mode,E),b.return=T,b;case pt:var B=b._init;return b=B(b._payload),H(T,b,E)}if(_t(b)||F(b))return b=$l(b,T.mode,E,null),b.return=T,b;if(typeof b.then=="function")return H(T,Pu(b),E);if(b.$$typeof===$)return H(T,Gu(T,b),E);Iu(T,b)}return null}function _(T,b,E,B){var Z=b!==null?b.key:null;if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return Z!==null?null:d(T,b,""+E,B);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case N:return E.key===Z?y(T,b,E,B):null;case w:return E.key===Z?O(T,b,E,B):null;case pt:return Z=E._init,E=Z(E._payload),_(T,b,E,B)}if(_t(E)||F(E))return Z!==null?null:U(T,b,E,B,null);if(typeof E.then=="function")return _(T,b,Pu(E),B);if(E.$$typeof===$)return _(T,b,Gu(T,E),B);Iu(T,E)}return null}function C(T,b,E,B,Z){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return T=T.get(E)||null,d(b,T,""+B,Z);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case N:return T=T.get(B.key===null?E:B.key)||null,y(b,T,B,Z);case w:return T=T.get(B.key===null?E:B.key)||null,O(b,T,B,Z);case pt:var rt=B._init;return B=rt(B._payload),C(T,b,E,B,Z)}if(_t(B)||F(B))return T=T.get(E)||null,U(b,T,B,Z,null);if(typeof B.then=="function")return C(T,b,E,Pu(B),Z);if(B.$$typeof===$)return C(T,b,E,Gu(b,B),Z);Iu(b,B)}return null}function at(T,b,E,B){for(var Z=null,rt=null,W=b,lt=b=0,te=null;W!==null&&lt<E.length;lt++){W.index>lt?(te=W,W=null):te=W.sibling;var mt=_(T,W,E[lt],B);if(mt===null){W===null&&(W=te);break}t&&W&&mt.alternate===null&&e(T,W),b=i(mt,b,lt),rt===null?Z=mt:rt.sibling=mt,rt=mt,W=te}if(lt===E.length)return l(T,W),yt&&Wl(T,lt),Z;if(W===null){for(;lt<E.length;lt++)W=H(T,E[lt],B),W!==null&&(b=i(W,b,lt),rt===null?Z=W:rt.sibling=W,rt=W);return yt&&Wl(T,lt),Z}for(W=a(W);lt<E.length;lt++)te=C(W,T,lt,E[lt],B),te!==null&&(t&&te.alternate!==null&&W.delete(te.key===null?lt:te.key),b=i(te,b,lt),rt===null?Z=te:rt.sibling=te,rt=te);return t&&W.forEach(function(Xl){return e(T,Xl)}),yt&&Wl(T,lt),Z}function et(T,b,E,B){if(E==null)throw Error(r(151));for(var Z=null,rt=null,W=b,lt=b=0,te=null,mt=E.next();W!==null&&!mt.done;lt++,mt=E.next()){W.index>lt?(te=W,W=null):te=W.sibling;var Xl=_(T,W,mt.value,B);if(Xl===null){W===null&&(W=te);break}t&&W&&Xl.alternate===null&&e(T,W),b=i(Xl,b,lt),rt===null?Z=Xl:rt.sibling=Xl,rt=Xl,W=te}if(mt.done)return l(T,W),yt&&Wl(T,lt),Z;if(W===null){for(;!mt.done;lt++,mt=E.next())mt=H(T,mt.value,B),mt!==null&&(b=i(mt,b,lt),rt===null?Z=mt:rt.sibling=mt,rt=mt);return yt&&Wl(T,lt),Z}for(W=a(W);!mt.done;lt++,mt=E.next())mt=C(W,T,lt,mt.value,B),mt!==null&&(t&&mt.alternate!==null&&W.delete(mt.key===null?lt:mt.key),b=i(mt,b,lt),rt===null?Z=mt:rt.sibling=mt,rt=mt);return t&&W.forEach(function(mg){return e(T,mg)}),yt&&Wl(T,lt),Z}function Et(T,b,E,B){if(typeof E=="object"&&E!==null&&E.type===Y&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case N:t:{for(var Z=E.key;b!==null;){if(b.key===Z){if(Z=E.type,Z===Y){if(b.tag===7){l(T,b.sibling),B=n(b,E.props.children),B.return=T,T=B;break t}}else if(b.elementType===Z||typeof Z=="object"&&Z!==null&&Z.$$typeof===pt&&ks(Z)===b.type){l(T,b.sibling),B=n(b,E.props),Hn(B,E),B.return=T,T=B;break t}l(T,b);break}else e(T,b);b=b.sibling}E.type===Y?(B=$l(E.props.children,T.mode,B,E.key),B.return=T,T=B):(B=Hu(E.type,E.key,E.props,null,T.mode,B),Hn(B,E),B.return=T,T=B)}return o(T);case w:t:{for(Z=E.key;b!==null;){if(b.key===Z)if(b.tag===4&&b.stateNode.containerInfo===E.containerInfo&&b.stateNode.implementation===E.implementation){l(T,b.sibling),B=n(b,E.children||[]),B.return=T,T=B;break t}else{l(T,b);break}else e(T,b);b=b.sibling}B=Nc(E,T.mode,B),B.return=T,T=B}return o(T);case pt:return Z=E._init,E=Z(E._payload),Et(T,b,E,B)}if(_t(E))return at(T,b,E,B);if(F(E)){if(Z=F(E),typeof Z!="function")throw Error(r(150));return E=Z.call(E),et(T,b,E,B)}if(typeof E.then=="function")return Et(T,b,Pu(E),B);if(E.$$typeof===$)return Et(T,b,Gu(T,E),B);Iu(T,E)}return typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint"?(E=""+E,b!==null&&b.tag===6?(l(T,b.sibling),B=n(b,E),B.return=T,T=B):(l(T,b),B=Bc(E,T.mode,B),B.return=T,T=B),o(T)):l(T,b)}return function(T,b,E,B){try{Nn=0;var Z=Et(T,b,E,B);return Ua=null,Z}catch(W){if(W===_n||W===Xu)throw W;var rt=Se(29,W,null,T.mode);return rt.lanes=B,rt.return=T,rt}finally{}}}var Ba=$s(!0),Js=$s(!1),He=q(null),Ze=null;function zl(t){var e=t.alternate;X(Wt,Wt.current&1),X(He,t),Ze===null&&(e===null||xa.current!==null||e.memoizedState!==null)&&(Ze=t)}function Ws(t){if(t.tag===22){if(X(Wt,Wt.current),X(He,t),Ze===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Ze=t)}}else Cl()}function Cl(){X(Wt,Wt.current),X(He,He.current)}function cl(t){V(He),Ze===t&&(Ze=null),V(Wt)}var Wt=q(0);function ti(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||lf(l)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function hr(t,e,l,a){e=t.memoizedState,l=l(a,e),l=l==null?e:x({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var mr={enqueueSetState:function(t,e,l){t=t._reactInternals;var a=Oe(),n=El(a);n.payload=e,l!=null&&(n.callback=l),e=Ol(t,n,a),e!==null&&(_e(e,t,a),Cn(e,t,a))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var a=Oe(),n=El(a);n.tag=1,n.payload=e,l!=null&&(n.callback=l),e=Ol(t,n,a),e!==null&&(_e(e,t,a),Cn(e,t,a))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=Oe(),a=El(l);a.tag=2,e!=null&&(a.callback=e),e=Ol(t,a,l),e!==null&&(_e(e,t,l),Cn(e,t,l))}};function Fs(t,e,l,a,n,i,o){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(a,i,o):e.prototype&&e.prototype.isPureReactComponent?!vn(l,a)||!vn(n,i):!0}function Ps(t,e,l,a){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,a),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,a),e.state!==t&&mr.enqueueReplaceState(e,e.state,null)}function aa(t,e){var l=e;if("ref"in e){l={};for(var a in e)a!=="ref"&&(l[a]=e[a])}if(t=t.defaultProps){l===e&&(l=x({},l));for(var n in t)l[n]===void 0&&(l[n]=t[n])}return l}var ei=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Is(t){ei(t)}function td(t){console.error(t)}function ed(t){ei(t)}function li(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(a){setTimeout(function(){throw a})}}function ld(t,e,l){try{var a=t.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function yr(t,e,l){return l=El(l),l.tag=3,l.payload={element:null},l.callback=function(){li(t,e)},l}function ad(t){return t=El(t),t.tag=3,t}function nd(t,e,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var i=a.value;t.payload=function(){return n(i)},t.callback=function(){ld(e,l,a)}}var o=l.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(t.callback=function(){ld(e,l,a),typeof n!="function"&&(Bl===null?Bl=new Set([this]):Bl.add(this));var d=a.stack;this.componentDidCatch(a.value,{componentStack:d!==null?d:""})})}function my(t,e,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(e=l.alternate,e!==null&&An(e,l,n,!0),l=He.current,l!==null){switch(l.tag){case 13:return Ze===null?Gr():l.alternate===null&&Gt===0&&(Gt=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Vc?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([a]):e.add(a),Xr(t,a,n)),!1;case 22:return l.flags|=65536,a===Vc?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([a]):l.add(a)),Xr(t,a,n)),!1}throw Error(r(435,l.tag))}return Xr(t,a,n),Gr(),!1}if(yt)return e=He.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,a!==Yc&&(t=Error(r(422),{cause:a}),Tn(De(t,l)))):(a!==Yc&&(e=Error(r(423),{cause:a}),Tn(De(e,l))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,a=De(a,l),n=yr(t.stateNode,a,n),kc(t,n),Gt!==4&&(Gt=2)),!1;var i=Error(r(520),{cause:a});if(i=De(i,l),Qn===null?Qn=[i]:Qn.push(i),Gt!==4&&(Gt=2),e===null)return!0;a=De(a,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=n&-n,l.lanes|=t,t=yr(l.stateNode,a,t),kc(l,t),!1;case 1:if(e=l.type,i=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(Bl===null||!Bl.has(i))))return l.flags|=65536,n&=-n,l.lanes|=n,n=ad(n),nd(n,t,l,a),kc(l,n),!1}l=l.return}while(l!==null);return!1}var ud=Error(r(461)),Pt=!1;function ee(t,e,l,a){e.child=t===null?Js(e,null,l,a):Ba(e,t.child,l,a)}function id(t,e,l,a,n){l=l.render;var i=e.ref;if("ref"in a){var o={};for(var d in a)d!=="ref"&&(o[d]=a[d])}else o=a;return ta(e),a=Pc(t,e,l,o,i,n),d=Ic(),t!==null&&!Pt?(tr(t,e,n),rl(t,e,n)):(yt&&d&&Hc(e),e.flags|=1,ee(t,e,a,n),e.child)}function cd(t,e,l,a,n){if(t===null){var i=l.type;return typeof i=="function"&&!Uc(i)&&i.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=i,rd(t,e,i,a,n)):(t=Hu(l.type,null,a,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!Er(t,n)){var o=i.memoizedProps;if(l=l.compare,l=l!==null?l:vn,l(o,a)&&t.ref===e.ref)return rl(t,e,n)}return e.flags|=1,t=el(i,a),t.ref=e.ref,t.return=e,e.child=t}function rd(t,e,l,a,n){if(t!==null){var i=t.memoizedProps;if(vn(i,a)&&t.ref===e.ref)if(Pt=!1,e.pendingProps=a=i,Er(t,n))(t.flags&131072)!==0&&(Pt=!0);else return e.lanes=t.lanes,rl(t,e,n)}return gr(t,e,l,a,n)}function fd(t,e,l){var a=e.pendingProps,n=a.children,i=t!==null?t.memoizedState:null;if(a.mode==="hidden"){if((e.flags&128)!==0){if(a=i!==null?i.baseLanes|l:l,t!==null){for(n=e.child=t.child,i=0;n!==null;)i=i|n.lanes|n.childLanes,n=n.sibling;e.childLanes=i&~a}else e.childLanes=0,e.child=null;return od(t,e,a,l)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&wu(e,i!==null?i.cachePool:null),i!==null?rs(e,i):Jc(),Ws(e);else return e.lanes=e.childLanes=536870912,od(t,e,i!==null?i.baseLanes|l:l,l)}else i!==null?(wu(e,i.cachePool),rs(e,i),Cl(),e.memoizedState=null):(t!==null&&wu(e,null),Jc(),Cl());return ee(t,e,n,l),e.child}function od(t,e,l,a){var n=Lc();return n=n===null?null:{parent:Jt._currentValue,pool:n},e.memoizedState={baseLanes:l,cachePool:n},t!==null&&wu(e,null),Jc(),Ws(e),t!==null&&An(t,e,a,!0),null}function ai(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(r(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function gr(t,e,l,a,n){return ta(e),l=Pc(t,e,l,a,void 0,n),a=Ic(),t!==null&&!Pt?(tr(t,e,n),rl(t,e,n)):(yt&&a&&Hc(e),e.flags|=1,ee(t,e,l,n),e.child)}function sd(t,e,l,a,n,i){return ta(e),e.updateQueue=null,l=os(e,a,l,n),fs(t),a=Ic(),t!==null&&!Pt?(tr(t,e,i),rl(t,e,i)):(yt&&a&&Hc(e),e.flags|=1,ee(t,e,l,i),e.child)}function dd(t,e,l,a,n){if(ta(e),e.stateNode===null){var i=Ea,o=l.contextType;typeof o=="object"&&o!==null&&(i=ie(o)),i=new l(a,i),e.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=mr,e.stateNode=i,i._reactInternals=e,i=e.stateNode,i.props=a,i.state=e.memoizedState,i.refs={},Zc(e),o=l.contextType,i.context=typeof o=="object"&&o!==null?ie(o):Ea,i.state=e.memoizedState,o=l.getDerivedStateFromProps,typeof o=="function"&&(hr(e,l,o,a),i.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(o=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),o!==i.state&&mr.enqueueReplaceState(i,i.state,null),Rn(e,a,i,n),xn(),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!0}else if(t===null){i=e.stateNode;var d=e.memoizedProps,y=aa(l,d);i.props=y;var O=i.context,U=l.contextType;o=Ea,typeof U=="object"&&U!==null&&(o=ie(U));var H=l.getDerivedStateFromProps;U=typeof H=="function"||typeof i.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,U||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(d||O!==o)&&Ps(e,i,a,o),Al=!1;var _=e.memoizedState;i.state=_,Rn(e,a,i,n),xn(),O=e.memoizedState,d||_!==O||Al?(typeof H=="function"&&(hr(e,l,H,a),O=e.memoizedState),(y=Al||Fs(e,l,y,a,_,O,o))?(U||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(e.flags|=4194308)):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=a,e.memoizedState=O),i.props=a,i.state=O,i.context=o,a=y):(typeof i.componentDidMount=="function"&&(e.flags|=4194308),a=!1)}else{i=e.stateNode,Kc(t,e),o=e.memoizedProps,U=aa(l,o),i.props=U,H=e.pendingProps,_=i.context,O=l.contextType,y=Ea,typeof O=="object"&&O!==null&&(y=ie(O)),d=l.getDerivedStateFromProps,(O=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==H||_!==y)&&Ps(e,i,a,y),Al=!1,_=e.memoizedState,i.state=_,Rn(e,a,i,n),xn();var C=e.memoizedState;o!==H||_!==C||Al||t!==null&&t.dependencies!==null&&ju(t.dependencies)?(typeof d=="function"&&(hr(e,l,d,a),C=e.memoizedState),(U=Al||Fs(e,l,U,a,_,C,y)||t!==null&&t.dependencies!==null&&ju(t.dependencies))?(O||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(a,C,y),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(a,C,y)),typeof i.componentDidUpdate=="function"&&(e.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===t.memoizedProps&&_===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&_===t.memoizedState||(e.flags|=1024),e.memoizedProps=a,e.memoizedState=C),i.props=a,i.state=C,i.context=y,a=U):(typeof i.componentDidUpdate!="function"||o===t.memoizedProps&&_===t.memoizedState||(e.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===t.memoizedProps&&_===t.memoizedState||(e.flags|=1024),a=!1)}return i=a,ai(t,e),a=(e.flags&128)!==0,i||a?(i=e.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:i.render(),e.flags|=1,t!==null&&a?(e.child=Ba(e,t.child,null,n),e.child=Ba(e,null,l,n)):ee(t,e,l,n),e.memoizedState=i.state,t=e.child):t=rl(t,e,n),t}function hd(t,e,l,a){return Sn(),e.flags|=256,ee(t,e,l,a),e.child}var vr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function pr(t){return{baseLanes:t,cachePool:ts()}}function br(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=qe),t}function md(t,e,l){var a=e.pendingProps,n=!1,i=(e.flags&128)!==0,o;if((o=i)||(o=t!==null&&t.memoizedState===null?!1:(Wt.current&2)!==0),o&&(n=!0,e.flags&=-129),o=(e.flags&32)!==0,e.flags&=-33,t===null){if(yt){if(n?zl(e):Cl(),yt){var d=jt,y;if(y=d){t:{for(y=d,d=Ve;y.nodeType!==8;){if(!d){d=null;break t}if(y=Qe(y.nextSibling),y===null){d=null;break t}}d=y}d!==null?(e.memoizedState={dehydrated:d,treeContext:Jl!==null?{id:ll,overflow:al}:null,retryLane:536870912,hydrationErrors:null},y=Se(18,null,null,0),y.stateNode=d,y.return=e,e.child=y,fe=e,jt=null,y=!0):y=!1}y||Pl(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return lf(d)?e.lanes=32:e.lanes=536870912,null;cl(e)}return d=a.children,a=a.fallback,n?(Cl(),n=e.mode,d=ni({mode:"hidden",children:d},n),a=$l(a,n,l,null),d.return=e,a.return=e,d.sibling=a,e.child=d,n=e.child,n.memoizedState=pr(l),n.childLanes=br(t,o,l),e.memoizedState=vr,a):(zl(e),Sr(e,d))}if(y=t.memoizedState,y!==null&&(d=y.dehydrated,d!==null)){if(i)e.flags&256?(zl(e),e.flags&=-257,e=Tr(t,e,l)):e.memoizedState!==null?(Cl(),e.child=t.child,e.flags|=128,e=null):(Cl(),n=a.fallback,d=e.mode,a=ni({mode:"visible",children:a.children},d),n=$l(n,d,l,null),n.flags|=2,a.return=e,n.return=e,a.sibling=n,e.child=a,Ba(e,t.child,null,l),a=e.child,a.memoizedState=pr(l),a.childLanes=br(t,o,l),e.memoizedState=vr,e=n);else if(zl(e),lf(d)){if(o=d.nextSibling&&d.nextSibling.dataset,o)var O=o.dgst;o=O,a=Error(r(419)),a.stack="",a.digest=o,Tn({value:a,source:null,stack:null}),e=Tr(t,e,l)}else if(Pt||An(t,e,l,!1),o=(l&t.childLanes)!==0,Pt||o){if(o=Mt,o!==null&&(a=l&-l,a=(a&42)!==0?1:ac(a),a=(a&(o.suspendedLanes|l))!==0?0:a,a!==0&&a!==y.retryLane))throw y.retryLane=a,Aa(t,a),_e(o,t,a),ud;d.data==="$?"||Gr(),e=Tr(t,e,l)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=y.treeContext,jt=Qe(d.nextSibling),fe=e,yt=!0,Fl=null,Ve=!1,t!==null&&(Be[Ne++]=ll,Be[Ne++]=al,Be[Ne++]=Jl,ll=t.id,al=t.overflow,Jl=e),e=Sr(e,a.children),e.flags|=4096);return e}return n?(Cl(),n=a.fallback,d=e.mode,y=t.child,O=y.sibling,a=el(y,{mode:"hidden",children:a.children}),a.subtreeFlags=y.subtreeFlags&65011712,O!==null?n=el(O,n):(n=$l(n,d,l,null),n.flags|=2),n.return=e,a.return=e,a.sibling=n,e.child=a,a=n,n=e.child,d=t.child.memoizedState,d===null?d=pr(l):(y=d.cachePool,y!==null?(O=Jt._currentValue,y=y.parent!==O?{parent:O,pool:O}:y):y=ts(),d={baseLanes:d.baseLanes|l,cachePool:y}),n.memoizedState=d,n.childLanes=br(t,o,l),e.memoizedState=vr,a):(zl(e),l=t.child,t=l.sibling,l=el(l,{mode:"visible",children:a.children}),l.return=e,l.sibling=null,t!==null&&(o=e.deletions,o===null?(e.deletions=[t],e.flags|=16):o.push(t)),e.child=l,e.memoizedState=null,l)}function Sr(t,e){return e=ni({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function ni(t,e){return t=Se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Tr(t,e,l){return Ba(e,t.child,null,l),t=Sr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function yd(t,e,l){t.lanes|=e;var a=t.alternate;a!==null&&(a.lanes|=e),Gc(t.return,e,l)}function Ar(t,e,l,a,n){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=a,i.tail=l,i.tailMode=n)}function gd(t,e,l){var a=e.pendingProps,n=a.revealOrder,i=a.tail;if(ee(t,e,a.children,l),a=Wt.current,(a&2)!==0)a=a&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&yd(t,l,e);else if(t.tag===19)yd(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}a&=1}switch(X(Wt,a),n){case"forwards":for(l=e.child,n=null;l!==null;)t=l.alternate,t!==null&&ti(t)===null&&(n=l),l=l.sibling;l=n,l===null?(n=e.child,e.child=null):(n=l.sibling,l.sibling=null),Ar(e,!1,n,l,i);break;case"backwards":for(l=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&ti(t)===null){e.child=n;break}t=n.sibling,n.sibling=l,l=n,n=t}Ar(e,!0,l,null,i);break;case"together":Ar(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function rl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),Ul|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(An(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,l=el(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=el(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Er(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&ju(t)))}function yy(t,e,l){switch(e.tag){case 3:Ut(e,e.stateNode.containerInfo),Tl(e,Jt,t.memoizedState.cache),Sn();break;case 27:case 5:Pi(e);break;case 4:Ut(e,e.stateNode.containerInfo);break;case 10:Tl(e,e.type,e.memoizedProps.value);break;case 13:var a=e.memoizedState;if(a!==null)return a.dehydrated!==null?(zl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?md(t,e,l):(zl(e),t=rl(t,e,l),t!==null?t.sibling:null);zl(e);break;case 19:var n=(t.flags&128)!==0;if(a=(l&e.childLanes)!==0,a||(An(t,e,l,!1),a=(l&e.childLanes)!==0),n){if(a)return gd(t,e,l);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),X(Wt,Wt.current),a)break;return null;case 22:case 23:return e.lanes=0,fd(t,e,l);case 24:Tl(e,Jt,t.memoizedState.cache)}return rl(t,e,l)}function vd(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Pt=!0;else{if(!Er(t,l)&&(e.flags&128)===0)return Pt=!1,yy(t,e,l);Pt=(t.flags&131072)!==0}else Pt=!1,yt&&(e.flags&1048576)!==0&&ko(e,Yu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var a=e.elementType,n=a._init;if(a=n(a._payload),e.type=a,typeof a=="function")Uc(a)?(t=aa(a,t),e.tag=1,e=dd(null,e,a,t,l)):(e.tag=0,e=gr(null,e,a,t,l));else{if(a!=null){if(n=a.$$typeof,n===k){e.tag=11,e=id(null,e,a,t,l);break t}else if(n===J){e.tag=14,e=cd(null,e,a,t,l);break t}}throw e=qt(a)||a,Error(r(306,e,""))}}return e;case 0:return gr(t,e,e.type,e.pendingProps,l);case 1:return a=e.type,n=aa(a,e.pendingProps),dd(t,e,a,n,l);case 3:t:{if(Ut(e,e.stateNode.containerInfo),t===null)throw Error(r(387));a=e.pendingProps;var i=e.memoizedState;n=i.element,Kc(t,e),Rn(e,a,null,l);var o=e.memoizedState;if(a=o.cache,Tl(e,Jt,a),a!==i.cache&&wc(e,[Jt],l,!0),xn(),a=o.element,i.isDehydrated)if(i={element:a,isDehydrated:!1,cache:o.cache},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){e=hd(t,e,a,l);break t}else if(a!==n){n=De(Error(r(424)),e),Tn(n),e=hd(t,e,a,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(jt=Qe(t.firstChild),fe=e,yt=!0,Fl=null,Ve=!0,l=Js(e,null,a,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(Sn(),a===n){e=rl(t,e,l);break t}ee(t,e,a,l)}e=e.child}return e;case 26:return ai(t,e),t===null?(l=T0(e.type,null,e.pendingProps,null))?e.memoizedState=l:yt||(l=e.type,t=e.pendingProps,a=pi(ut.current).createElement(l),a[ue]=e,a[oe]=t,ae(a,l,t),Ft(a),e.stateNode=a):e.memoizedState=T0(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Pi(e),t===null&&yt&&(a=e.stateNode=p0(e.type,e.pendingProps,ut.current),fe=e,Ve=!0,n=jt,ql(e.type)?(af=n,jt=Qe(a.firstChild)):jt=n),ee(t,e,e.pendingProps.children,l),ai(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&yt&&((n=a=jt)&&(a=Ly(a,e.type,e.pendingProps,Ve),a!==null?(e.stateNode=a,fe=e,jt=Qe(a.firstChild),Ve=!1,n=!0):n=!1),n||Pl(e)),Pi(e),n=e.type,i=e.pendingProps,o=t!==null?t.memoizedProps:null,a=i.children,Ir(n,i)?a=null:o!==null&&Ir(n,o)&&(e.flags|=32),e.memoizedState!==null&&(n=Pc(t,e,cy,null,null,l),Fn._currentValue=n),ai(t,e),ee(t,e,a,l),e.child;case 6:return t===null&&yt&&((t=l=jt)&&(l=Vy(l,e.pendingProps,Ve),l!==null?(e.stateNode=l,fe=e,jt=null,t=!0):t=!1),t||Pl(e)),null;case 13:return md(t,e,l);case 4:return Ut(e,e.stateNode.containerInfo),a=e.pendingProps,t===null?e.child=Ba(e,null,a,l):ee(t,e,a,l),e.child;case 11:return id(t,e,e.type,e.pendingProps,l);case 7:return ee(t,e,e.pendingProps,l),e.child;case 8:return ee(t,e,e.pendingProps.children,l),e.child;case 12:return ee(t,e,e.pendingProps.children,l),e.child;case 10:return a=e.pendingProps,Tl(e,e.type,a.value),ee(t,e,a.children,l),e.child;case 9:return n=e.type._context,a=e.pendingProps.children,ta(e),n=ie(n),a=a(n),e.flags|=1,ee(t,e,a,l),e.child;case 14:return cd(t,e,e.type,e.pendingProps,l);case 15:return rd(t,e,e.type,e.pendingProps,l);case 19:return gd(t,e,l);case 31:return a=e.pendingProps,l=e.mode,a={mode:a.mode,children:a.children},t===null?(l=ni(a,l),l.ref=e.ref,e.child=l,l.return=e,e=l):(l=el(t.child,a),l.ref=e.ref,e.child=l,l.return=e,e=l),e;case 22:return fd(t,e,l);case 24:return ta(e),a=ie(Jt),t===null?(n=Lc(),n===null&&(n=Mt,i=Xc(),n.pooledCache=i,i.refCount++,i!==null&&(n.pooledCacheLanes|=l),n=i),e.memoizedState={parent:a,cache:n},Zc(e),Tl(e,Jt,n)):((t.lanes&l)!==0&&(Kc(t,e),Rn(e,null,null,l),xn()),n=t.memoizedState,i=e.memoizedState,n.parent!==a?(n={parent:a,cache:a},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),Tl(e,Jt,a)):(a=i.cache,Tl(e,Jt,a),a!==n.cache&&wc(e,[Jt],l,!0))),ee(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function fl(t){t.flags|=4}function pd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!z0(e)){if(e=He.current,e!==null&&((dt&4194048)===dt?Ze!==null:(dt&62914560)!==dt&&(dt&536870912)===0||e!==Ze))throw zn=Vc,es;t.flags|=8192}}function ui(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Jf():536870912,t.lanes|=e,Ya|=e)}function qn(t,e){if(!yt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:a.sibling=null}}function Yt(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,a=0;if(e)for(var n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=a,t.childLanes=l,e}function gy(t,e,l){var a=e.pendingProps;switch(qc(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Yt(e),null;case 1:return Yt(e),null;case 3:return l=e.stateNode,a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ul(Jt),vl(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(bn(e)?fl(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Wo())),Yt(e),null;case 26:return l=e.memoizedState,t===null?(fl(e),l!==null?(Yt(e),pd(e,l)):(Yt(e),e.flags&=-16777217)):l?l!==t.memoizedState?(fl(e),Yt(e),pd(e,l)):(Yt(e),e.flags&=-16777217):(t.memoizedProps!==a&&fl(e),Yt(e),e.flags&=-16777217),null;case 27:gu(e),l=ut.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==a&&fl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return Yt(e),null}t=tt.current,bn(e)?$o(e):(t=p0(n,a,l),e.stateNode=t,fl(e))}return Yt(e),null;case 5:if(gu(e),l=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==a&&fl(e);else{if(!a){if(e.stateNode===null)throw Error(r(166));return Yt(e),null}if(t=tt.current,bn(e))$o(e);else{switch(n=pi(ut.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?t.multiple=!0:a.size&&(t.size=a.size);break;default:t=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}t[ue]=e,t[oe]=a;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(ae(t,l,a),l){case"button":case"input":case"select":case"textarea":t=!!a.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&fl(e)}}return Yt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==a&&fl(e);else{if(typeof a!="string"&&e.stateNode===null)throw Error(r(166));if(t=ut.current,bn(e)){if(t=e.stateNode,l=e.memoizedProps,a=null,n=fe,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}t[ue]=e,t=!!(t.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||s0(t.nodeValue,l)),t||Pl(e)}else t=pi(t).createTextNode(a),t[ue]=e,e.stateNode=t}return Yt(e),null;case 13:if(a=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=bn(e),a!==null&&a.dehydrated!==null){if(t===null){if(!n)throw Error(r(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(r(317));n[ue]=e}else Sn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Yt(e),n=!1}else n=Wo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?(cl(e),e):(cl(e),null)}if(cl(e),(e.flags&128)!==0)return e.lanes=l,e;if(l=a!==null,t=t!==null&&t.memoizedState!==null,l){a=e.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var i=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(i=a.memoizedState.cachePool.pool),i!==n&&(a.flags|=2048)}return l!==t&&l&&(e.child.flags|=8192),ui(e,e.updateQueue),Yt(e),null;case 4:return vl(),t===null&&$r(e.stateNode.containerInfo),Yt(e),null;case 10:return ul(e.type),Yt(e),null;case 19:if(V(Wt),n=e.memoizedState,n===null)return Yt(e),null;if(a=(e.flags&128)!==0,i=n.rendering,i===null)if(a)qn(n,!1);else{if(Gt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(i=ti(t),i!==null){for(e.flags|=128,qn(n,!1),t=i.updateQueue,e.updateQueue=t,ui(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Ko(l,t),l=l.sibling;return X(Wt,Wt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Le()>ri&&(e.flags|=128,a=!0,qn(n,!1),e.lanes=4194304)}else{if(!a)if(t=ti(i),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,ui(e,t),qn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!i.alternate&&!yt)return Yt(e),null}else 2*Le()-n.renderingStartTime>ri&&l!==536870912&&(e.flags|=128,a=!0,qn(n,!1),e.lanes=4194304);n.isBackwards?(i.sibling=e.child,e.child=i):(t=n.last,t!==null?t.sibling=i:e.child=i,n.last=i)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Le(),e.sibling=null,t=Wt.current,X(Wt,a?t&1|2:t&1),e):(Yt(e),null);case 22:case 23:return cl(e),Wc(),a=e.memoizedState!==null,t!==null?t.memoizedState!==null!==a&&(e.flags|=8192):a&&(e.flags|=8192),a?(l&536870912)!==0&&(e.flags&128)===0&&(Yt(e),e.subtreeFlags&6&&(e.flags|=8192)):Yt(e),l=e.updateQueue,l!==null&&ui(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),a=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),a!==l&&(e.flags|=2048),t!==null&&V(ea),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),ul(Jt),Yt(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function vy(t,e){switch(qc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ul(Jt),vl(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return gu(e),null;case 13:if(cl(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));Sn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return V(Wt),null;case 4:return vl(),null;case 10:return ul(e.type),null;case 22:case 23:return cl(e),Wc(),t!==null&&V(ea),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ul(Jt),null;case 25:return null;default:return null}}function bd(t,e){switch(qc(e),e.tag){case 3:ul(Jt),vl();break;case 26:case 27:case 5:gu(e);break;case 4:vl();break;case 13:cl(e);break;case 19:V(Wt);break;case 10:ul(e.type);break;case 22:case 23:cl(e),Wc(),t!==null&&V(ea);break;case 24:ul(Jt)}}function Yn(t,e){try{var l=e.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&t)===t){a=void 0;var i=l.create,o=l.inst;a=i(),o.destroy=a}l=l.next}while(l!==n)}}catch(d){Ct(e,e.return,d)}}function xl(t,e,l){try{var a=e.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var i=n.next;a=i;do{if((a.tag&t)===t){var o=a.inst,d=o.destroy;if(d!==void 0){o.destroy=void 0,n=e;var y=l,O=d;try{O()}catch(U){Ct(n,y,U)}}}a=a.next}while(a!==i)}}catch(U){Ct(e,e.return,U)}}function Sd(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{cs(e,l)}catch(a){Ct(t,t.return,a)}}}function Td(t,e,l){l.props=aa(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(a){Ct(t,e,a)}}function jn(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var a=t.stateNode;break;case 30:a=t.stateNode;break;default:a=t.stateNode}typeof l=="function"?t.refCleanup=l(a):l.current=a}}catch(n){Ct(t,e,n)}}function Ke(t,e){var l=t.ref,a=t.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){Ct(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){Ct(t,e,n)}else l.current=null}function Ad(t){var e=t.type,l=t.memoizedProps,a=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break t;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){Ct(t,t.return,n)}}function Or(t,e,l){try{var a=t.stateNode;jy(a,t.type,l,e),a[oe]=e}catch(n){Ct(t,t.return,n)}}function Ed(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&ql(t.type)||t.tag===4}function _r(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ed(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&ql(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zr(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=vi));else if(a!==4&&(a===27&&ql(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(zr(t,e,l),t=t.sibling;t!==null;)zr(t,e,l),t=t.sibling}function ii(t,e,l){var a=t.tag;if(a===5||a===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(a!==4&&(a===27&&ql(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(ii(t,e,l),t=t.sibling;t!==null;)ii(t,e,l),t=t.sibling}function Od(t){var e=t.stateNode,l=t.memoizedProps;try{for(var a=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);ae(e,a,l),e[ue]=t,e[oe]=l}catch(i){Ct(t,t.return,i)}}var ol=!1,Vt=!1,Cr=!1,_d=typeof WeakSet=="function"?WeakSet:Set,It=null;function py(t,e){if(t=t.containerInfo,Fr=Oi,t=qo(t),_c(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,i=a.focusNode;a=a.focusOffset;try{l.nodeType,i.nodeType}catch{l=null;break t}var o=0,d=-1,y=-1,O=0,U=0,H=t,_=null;e:for(;;){for(var C;H!==l||n!==0&&H.nodeType!==3||(d=o+n),H!==i||a!==0&&H.nodeType!==3||(y=o+a),H.nodeType===3&&(o+=H.nodeValue.length),(C=H.firstChild)!==null;)_=H,H=C;for(;;){if(H===t)break e;if(_===l&&++O===n&&(d=o),_===i&&++U===a&&(y=o),(C=H.nextSibling)!==null)break;H=_,_=H.parentNode}H=C}l=d===-1||y===-1?null:{start:d,end:y}}else l=null}l=l||{start:0,end:0}}else l=null;for(Pr={focusedElem:t,selectionRange:l},Oi=!1,It=e;It!==null;)if(e=It,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,It=t;else for(;It!==null;){switch(e=It,i=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&i!==null){t=void 0,l=e,n=i.memoizedProps,i=i.memoizedState,a=l.stateNode;try{var at=aa(l.type,n,l.elementType===l.type);t=a.getSnapshotBeforeUpdate(at,i),a.__reactInternalSnapshotBeforeUpdate=t}catch(et){Ct(l,l.return,et)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)ef(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ef(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,It=t;break}It=e.return}}function zd(t,e,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Rl(t,l),a&4&&Yn(5,l);break;case 1:if(Rl(t,l),a&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(o){Ct(l,l.return,o)}else{var n=aa(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(o){Ct(l,l.return,o)}}a&64&&Sd(l),a&512&&jn(l,l.return);break;case 3:if(Rl(t,l),a&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{cs(t,e)}catch(o){Ct(l,l.return,o)}}break;case 27:e===null&&a&4&&Od(l);case 26:case 5:Rl(t,l),e===null&&a&4&&Ad(l),a&512&&jn(l,l.return);break;case 12:Rl(t,l);break;case 13:Rl(t,l),a&4&&Rd(t,l),a&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Cy.bind(null,l),Zy(t,l))));break;case 22:if(a=l.memoizedState!==null||ol,!a){e=e!==null&&e.memoizedState!==null||Vt,n=ol;var i=Vt;ol=a,(Vt=e)&&!i?Ml(t,l,(l.subtreeFlags&8772)!==0):Rl(t,l),ol=n,Vt=i}break;case 30:break;default:Rl(t,l)}}function Cd(t){var e=t.alternate;e!==null&&(t.alternate=null,Cd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ic(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Bt=null,he=!1;function sl(t,e,l){for(l=l.child;l!==null;)xd(t,e,l),l=l.sibling}function xd(t,e,l){if(ve&&typeof ve.onCommitFiberUnmount=="function")try{ve.onCommitFiberUnmount(nn,l)}catch{}switch(l.tag){case 26:Vt||Ke(l,e),sl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Vt||Ke(l,e);var a=Bt,n=he;ql(l.type)&&(Bt=l.stateNode,he=!1),sl(t,e,l),kn(l.stateNode),Bt=a,he=n;break;case 5:Vt||Ke(l,e);case 6:if(a=Bt,n=he,Bt=null,sl(t,e,l),Bt=a,he=n,Bt!==null)if(he)try{(Bt.nodeType===9?Bt.body:Bt.nodeName==="HTML"?Bt.ownerDocument.body:Bt).removeChild(l.stateNode)}catch(i){Ct(l,e,i)}else try{Bt.removeChild(l.stateNode)}catch(i){Ct(l,e,i)}break;case 18:Bt!==null&&(he?(t=Bt,g0(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),eu(t)):g0(Bt,l.stateNode));break;case 4:a=Bt,n=he,Bt=l.stateNode.containerInfo,he=!0,sl(t,e,l),Bt=a,he=n;break;case 0:case 11:case 14:case 15:Vt||xl(2,l,e),Vt||xl(4,l,e),sl(t,e,l);break;case 1:Vt||(Ke(l,e),a=l.stateNode,typeof a.componentWillUnmount=="function"&&Td(l,e,a)),sl(t,e,l);break;case 21:sl(t,e,l);break;case 22:Vt=(a=Vt)||l.memoizedState!==null,sl(t,e,l),Vt=a;break;default:sl(t,e,l)}}function Rd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{eu(t)}catch(l){Ct(e,e.return,l)}}function by(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new _d),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new _d),e;default:throw Error(r(435,t.tag))}}function xr(t,e){var l=by(t);e.forEach(function(a){var n=xy.bind(null,t,a);l.has(a)||(l.add(a),a.then(n,n))})}function Te(t,e){var l=e.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],i=t,o=e,d=o;t:for(;d!==null;){switch(d.tag){case 27:if(ql(d.type)){Bt=d.stateNode,he=!1;break t}break;case 5:Bt=d.stateNode,he=!1;break t;case 3:case 4:Bt=d.stateNode.containerInfo,he=!0;break t}d=d.return}if(Bt===null)throw Error(r(160));xd(i,o,n),Bt=null,he=!1,i=n.alternate,i!==null&&(i.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Md(e,t),e=e.sibling}var Xe=null;function Md(t,e){var l=t.alternate,a=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Te(e,t),Ae(t),a&4&&(xl(3,t,t.return),Yn(3,t),xl(5,t,t.return));break;case 1:Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),a&64&&ol&&(t=t.updateQueue,t!==null&&(a=t.callbacks,a!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Xe;if(Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),a&4){var i=l!==null?l.memoizedState:null;if(a=t.memoizedState,l===null)if(a===null)if(t.stateNode===null){t:{a=t.type,l=t.memoizedProps,n=n.ownerDocument||n;e:switch(a){case"title":i=n.getElementsByTagName("title")[0],(!i||i[rn]||i[ue]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=n.createElement(a),n.head.insertBefore(i,n.querySelector("head > title"))),ae(i,a,l),i[ue]=t,Ft(i),a=i;break t;case"link":var o=O0("link","href",n).get(a+(l.href||""));if(o){for(var d=0;d<o.length;d++)if(i=o[d],i.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&i.getAttribute("rel")===(l.rel==null?null:l.rel)&&i.getAttribute("title")===(l.title==null?null:l.title)&&i.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){o.splice(d,1);break e}}i=n.createElement(a),ae(i,a,l),n.head.appendChild(i);break;case"meta":if(o=O0("meta","content",n).get(a+(l.content||""))){for(d=0;d<o.length;d++)if(i=o[d],i.getAttribute("content")===(l.content==null?null:""+l.content)&&i.getAttribute("name")===(l.name==null?null:l.name)&&i.getAttribute("property")===(l.property==null?null:l.property)&&i.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&i.getAttribute("charset")===(l.charSet==null?null:l.charSet)){o.splice(d,1);break e}}i=n.createElement(a),ae(i,a,l),n.head.appendChild(i);break;default:throw Error(r(468,a))}i[ue]=t,Ft(i),a=i}t.stateNode=a}else _0(n,t.type,t.stateNode);else t.stateNode=E0(n,a,t.memoizedProps);else i!==a?(i===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):i.count--,a===null?_0(n,t.type,t.stateNode):E0(n,a,t.memoizedProps)):a===null&&t.stateNode!==null&&Or(t,t.memoizedProps,l.memoizedProps)}break;case 27:Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),l!==null&&a&4&&Or(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Te(e,t),Ae(t),a&512&&(Vt||l===null||Ke(l,l.return)),t.flags&32){n=t.stateNode;try{ya(n,"")}catch(C){Ct(t,t.return,C)}}a&4&&t.stateNode!=null&&(n=t.memoizedProps,Or(t,n,l!==null?l.memoizedProps:n)),a&1024&&(Cr=!0);break;case 6:if(Te(e,t),Ae(t),a&4){if(t.stateNode===null)throw Error(r(162));a=t.memoizedProps,l=t.stateNode;try{l.nodeValue=a}catch(C){Ct(t,t.return,C)}}break;case 3:if(Ti=null,n=Xe,Xe=bi(e.containerInfo),Te(e,t),Xe=n,Ae(t),a&4&&l!==null&&l.memoizedState.isDehydrated)try{eu(e.containerInfo)}catch(C){Ct(t,t.return,C)}Cr&&(Cr=!1,Dd(t));break;case 4:a=Xe,Xe=bi(t.stateNode.containerInfo),Te(e,t),Ae(t),Xe=a;break;case 12:Te(e,t),Ae(t);break;case 13:Te(e,t),Ae(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Nr=Le()),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,xr(t,a)));break;case 22:n=t.memoizedState!==null;var y=l!==null&&l.memoizedState!==null,O=ol,U=Vt;if(ol=O||n,Vt=U||y,Te(e,t),Vt=U,ol=O,Ae(t),a&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(l===null||y||ol||Vt||na(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){y=l=e;try{if(i=y.stateNode,n)o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{d=y.stateNode;var H=y.memoizedProps.style,_=H!=null&&H.hasOwnProperty("display")?H.display:null;d.style.display=_==null||typeof _=="boolean"?"":(""+_).trim()}}catch(C){Ct(y,y.return,C)}}}else if(e.tag===6){if(l===null){y=e;try{y.stateNode.nodeValue=n?"":y.memoizedProps}catch(C){Ct(y,y.return,C)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}a&4&&(a=t.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,xr(t,l))));break;case 19:Te(e,t),Ae(t),a&4&&(a=t.updateQueue,a!==null&&(t.updateQueue=null,xr(t,a)));break;case 30:break;case 21:break;default:Te(e,t),Ae(t)}}function Ae(t){var e=t.flags;if(e&2){try{for(var l,a=t.return;a!==null;){if(Ed(a)){l=a;break}a=a.return}if(l==null)throw Error(r(160));switch(l.tag){case 27:var n=l.stateNode,i=_r(t);ii(t,i,n);break;case 5:var o=l.stateNode;l.flags&32&&(ya(o,""),l.flags&=-33);var d=_r(t);ii(t,d,o);break;case 3:case 4:var y=l.stateNode.containerInfo,O=_r(t);zr(t,O,y);break;default:throw Error(r(161))}}catch(U){Ct(t,t.return,U)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Dd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Dd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Rl(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)zd(t,e.alternate,e),e=e.sibling}function na(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:xl(4,e,e.return),na(e);break;case 1:Ke(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&Td(e,e.return,l),na(e);break;case 27:kn(e.stateNode);case 26:case 5:Ke(e,e.return),na(e);break;case 22:e.memoizedState===null&&na(e);break;case 30:na(e);break;default:na(e)}t=t.sibling}}function Ml(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var a=e.alternate,n=t,i=e,o=i.flags;switch(i.tag){case 0:case 11:case 15:Ml(n,i,l),Yn(4,i);break;case 1:if(Ml(n,i,l),a=i,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(O){Ct(a,a.return,O)}if(a=i,n=a.updateQueue,n!==null){var d=a.stateNode;try{var y=n.shared.hiddenCallbacks;if(y!==null)for(n.shared.hiddenCallbacks=null,n=0;n<y.length;n++)is(y[n],d)}catch(O){Ct(a,a.return,O)}}l&&o&64&&Sd(i),jn(i,i.return);break;case 27:Od(i);case 26:case 5:Ml(n,i,l),l&&a===null&&o&4&&Ad(i),jn(i,i.return);break;case 12:Ml(n,i,l);break;case 13:Ml(n,i,l),l&&o&4&&Rd(n,i);break;case 22:i.memoizedState===null&&Ml(n,i,l),jn(i,i.return);break;case 30:break;default:Ml(n,i,l)}e=e.sibling}}function Rr(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&En(l))}function Mr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&En(t))}function ke(t,e,l,a){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ud(t,e,l,a),e=e.sibling}function Ud(t,e,l,a){var n=e.flags;switch(e.tag){case 0:case 11:case 15:ke(t,e,l,a),n&2048&&Yn(9,e);break;case 1:ke(t,e,l,a);break;case 3:ke(t,e,l,a),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&En(t)));break;case 12:if(n&2048){ke(t,e,l,a),t=e.stateNode;try{var i=e.memoizedProps,o=i.id,d=i.onPostCommit;typeof d=="function"&&d(o,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(y){Ct(e,e.return,y)}}else ke(t,e,l,a);break;case 13:ke(t,e,l,a);break;case 23:break;case 22:i=e.stateNode,o=e.alternate,e.memoizedState!==null?i._visibility&2?ke(t,e,l,a):Gn(t,e):i._visibility&2?ke(t,e,l,a):(i._visibility|=2,Na(t,e,l,a,(e.subtreeFlags&10256)!==0)),n&2048&&Rr(o,e);break;case 24:ke(t,e,l,a),n&2048&&Mr(e.alternate,e);break;default:ke(t,e,l,a)}}function Na(t,e,l,a,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var i=t,o=e,d=l,y=a,O=o.flags;switch(o.tag){case 0:case 11:case 15:Na(i,o,d,y,n),Yn(8,o);break;case 23:break;case 22:var U=o.stateNode;o.memoizedState!==null?U._visibility&2?Na(i,o,d,y,n):Gn(i,o):(U._visibility|=2,Na(i,o,d,y,n)),n&&O&2048&&Rr(o.alternate,o);break;case 24:Na(i,o,d,y,n),n&&O&2048&&Mr(o.alternate,o);break;default:Na(i,o,d,y,n)}e=e.sibling}}function Gn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,a=e,n=a.flags;switch(a.tag){case 22:Gn(l,a),n&2048&&Rr(a.alternate,a);break;case 24:Gn(l,a),n&2048&&Mr(a.alternate,a);break;default:Gn(l,a)}e=e.sibling}}var wn=8192;function Ha(t){if(t.subtreeFlags&wn)for(t=t.child;t!==null;)Bd(t),t=t.sibling}function Bd(t){switch(t.tag){case 26:Ha(t),t.flags&wn&&t.memoizedState!==null&&ng(Xe,t.memoizedState,t.memoizedProps);break;case 5:Ha(t);break;case 3:case 4:var e=Xe;Xe=bi(t.stateNode.containerInfo),Ha(t),Xe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=wn,wn=16777216,Ha(t),wn=e):Ha(t));break;default:Ha(t)}}function Nd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Xn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];It=a,qd(a,t)}Nd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hd(t),t=t.sibling}function Hd(t){switch(t.tag){case 0:case 11:case 15:Xn(t),t.flags&2048&&xl(9,t,t.return);break;case 3:Xn(t);break;case 12:Xn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ci(t)):Xn(t);break;default:Xn(t)}}function ci(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var a=e[l];It=a,qd(a,t)}Nd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:xl(8,e,e.return),ci(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,ci(e));break;default:ci(e)}t=t.sibling}}function qd(t,e){for(;It!==null;){var l=It;switch(l.tag){case 0:case 11:case 15:xl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:En(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,It=a;else t:for(l=t;It!==null;){a=It;var n=a.sibling,i=a.return;if(Cd(a),a===l){It=null;break t}if(n!==null){n.return=i,It=n;break t}It=i}}}var Sy={getCacheForType:function(t){var e=ie(Jt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l}},Ty=typeof WeakMap=="function"?WeakMap:Map,bt=0,Mt=null,ft=null,dt=0,St=0,Ee=null,Dl=!1,qa=!1,Dr=!1,dl=0,Gt=0,Ul=0,ua=0,Ur=0,qe=0,Ya=0,Qn=null,me=null,Br=!1,Nr=0,ri=1/0,fi=null,Bl=null,le=0,Nl=null,ja=null,Ga=0,Hr=0,qr=null,Yd=null,Ln=0,Yr=null;function Oe(){if((bt&2)!==0&&dt!==0)return dt&-dt;if(M.T!==null){var t=za;return t!==0?t:Vr()}return Pf()}function jd(){qe===0&&(qe=(dt&536870912)===0||yt?$f():536870912);var t=He.current;return t!==null&&(t.flags|=32),qe}function _e(t,e,l){(t===Mt&&(St===2||St===9)||t.cancelPendingCommit!==null)&&(wa(t,0),Hl(t,dt,qe,!1)),cn(t,l),((bt&2)===0||t!==Mt)&&(t===Mt&&((bt&2)===0&&(ua|=l),Gt===4&&Hl(t,dt,qe,!1)),$e(t))}function Gd(t,e,l){if((bt&6)!==0)throw Error(r(327));var a=!l&&(e&124)===0&&(e&t.expiredLanes)===0||un(t,e),n=a?Oy(t,e):wr(t,e,!0),i=a;do{if(n===0){qa&&!a&&Hl(t,e,0,!1);break}else{if(l=t.current.alternate,i&&!Ay(l)){n=wr(t,e,!1),i=!1;continue}if(n===2){if(i=e,t.errorRecoveryDisabledLanes&i)var o=0;else o=t.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){e=o;t:{var d=t;n=Qn;var y=d.current.memoizedState.isDehydrated;if(y&&(wa(d,o).flags|=256),o=wr(d,o,!1),o!==2){if(Dr&&!y){d.errorRecoveryDisabledLanes|=i,ua|=i,n=4;break t}i=me,me=n,i!==null&&(me===null?me=i:me.push.apply(me,i))}n=o}if(i=!1,n!==2)continue}}if(n===1){wa(t,0),Hl(t,e,0,!0);break}t:{switch(a=t,i=n,i){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Hl(a,e,qe,!Dl);break t;case 2:me=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(n=Nr+300-Le(),10<n)){if(Hl(a,e,qe,!Dl),Su(a,0,!0)!==0)break t;a.timeoutHandle=m0(wd.bind(null,a,l,me,fi,Br,e,qe,ua,Ya,Dl,i,2,-0,0),n);break t}wd(a,l,me,fi,Br,e,qe,ua,Ya,Dl,i,0,-0,0)}}break}while(!0);$e(t)}function wd(t,e,l,a,n,i,o,d,y,O,U,H,_,C){if(t.timeoutHandle=-1,H=e.subtreeFlags,(H&8192||(H&16785408)===16785408)&&(Wn={stylesheets:null,count:0,unsuspend:ag},Bd(e),H=ug(),H!==null)){t.cancelPendingCommit=H(kd.bind(null,t,e,i,l,a,n,o,d,y,U,1,_,C)),Hl(t,i,o,!O);return}kd(t,e,i,l,a,n,o,d,y)}function Ay(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],i=n.getSnapshot;n=n.value;try{if(!be(i(),n))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Hl(t,e,l,a){e&=~Ur,e&=~ua,t.suspendedLanes|=e,t.pingedLanes&=~e,a&&(t.warmLanes|=e),a=t.expirationTimes;for(var n=e;0<n;){var i=31-pe(n),o=1<<i;a[i]=-1,n&=~o}l!==0&&Wf(t,l,e)}function oi(){return(bt&6)===0?(Vn(0),!1):!0}function jr(){if(ft!==null){if(St===0)var t=ft.return;else t=ft,nl=Il=null,er(t),Ua=null,Nn=0,t=ft;for(;t!==null;)bd(t.alternate,t),t=t.return;ft=null}}function wa(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,wy(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),jr(),Mt=t,ft=l=el(t.current,null),dt=e,St=0,Ee=null,Dl=!1,qa=un(t,e),Dr=!1,Ya=qe=Ur=ua=Ul=Gt=0,me=Qn=null,Br=!1,(e&8)!==0&&(e|=e&32);var a=t.entangledLanes;if(a!==0)for(t=t.entanglements,a&=e;0<a;){var n=31-pe(a),i=1<<n;e|=t[n],a&=~i}return dl=e,Uu(),l}function Xd(t,e){ct=null,M.H=Fu,e===_n||e===Xu?(e=ns(),St=3):e===es?(e=ns(),St=4):St=e===ud?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ee=e,ft===null&&(Gt=1,li(t,De(e,t.current)))}function Qd(){var t=M.H;return M.H=Fu,t===null?Fu:t}function Ld(){var t=M.A;return M.A=Sy,t}function Gr(){Gt=4,Dl||(dt&4194048)!==dt&&He.current!==null||(qa=!0),(Ul&134217727)===0&&(ua&134217727)===0||Mt===null||Hl(Mt,dt,qe,!1)}function wr(t,e,l){var a=bt;bt|=2;var n=Qd(),i=Ld();(Mt!==t||dt!==e)&&(fi=null,wa(t,e)),e=!1;var o=Gt;t:do try{if(St!==0&&ft!==null){var d=ft,y=Ee;switch(St){case 8:jr(),o=6;break t;case 3:case 2:case 9:case 6:He.current===null&&(e=!0);var O=St;if(St=0,Ee=null,Xa(t,d,y,O),l&&qa){o=0;break t}break;default:O=St,St=0,Ee=null,Xa(t,d,y,O)}}Ey(),o=Gt;break}catch(U){Xd(t,U)}while(!0);return e&&t.shellSuspendCounter++,nl=Il=null,bt=a,M.H=n,M.A=i,ft===null&&(Mt=null,dt=0,Uu()),o}function Ey(){for(;ft!==null;)Vd(ft)}function Oy(t,e){var l=bt;bt|=2;var a=Qd(),n=Ld();Mt!==t||dt!==e?(fi=null,ri=Le()+500,wa(t,e)):qa=un(t,e);t:do try{if(St!==0&&ft!==null){e=ft;var i=Ee;e:switch(St){case 1:St=0,Ee=null,Xa(t,e,i,1);break;case 2:case 9:if(ls(i)){St=0,Ee=null,Zd(e);break}e=function(){St!==2&&St!==9||Mt!==t||(St=7),$e(t)},i.then(e,e);break t;case 3:St=7;break t;case 4:St=5;break t;case 7:ls(i)?(St=0,Ee=null,Zd(e)):(St=0,Ee=null,Xa(t,e,i,7));break;case 5:var o=null;switch(ft.tag){case 26:o=ft.memoizedState;case 5:case 27:var d=ft;if(!o||z0(o)){St=0,Ee=null;var y=d.sibling;if(y!==null)ft=y;else{var O=d.return;O!==null?(ft=O,si(O)):ft=null}break e}}St=0,Ee=null,Xa(t,e,i,5);break;case 6:St=0,Ee=null,Xa(t,e,i,6);break;case 8:jr(),Gt=6;break t;default:throw Error(r(462))}}_y();break}catch(U){Xd(t,U)}while(!0);return nl=Il=null,M.H=a,M.A=n,bt=l,ft!==null?0:(Mt=null,dt=0,Uu(),Gt)}function _y(){for(;ft!==null&&!kh();)Vd(ft)}function Vd(t){var e=vd(t.alternate,t,dl);t.memoizedProps=t.pendingProps,e===null?si(t):ft=e}function Zd(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=sd(l,e,e.pendingProps,e.type,void 0,dt);break;case 11:e=sd(l,e,e.pendingProps,e.type.render,e.ref,dt);break;case 5:er(e);default:bd(l,e),e=ft=Ko(e,dl),e=vd(l,e,dl)}t.memoizedProps=t.pendingProps,e===null?si(t):ft=e}function Xa(t,e,l,a){nl=Il=null,er(e),Ua=null,Nn=0;var n=e.return;try{if(my(t,n,e,l,dt)){Gt=1,li(t,De(l,t.current)),ft=null;return}}catch(i){if(n!==null)throw ft=n,i;Gt=1,li(t,De(l,t.current)),ft=null;return}e.flags&32768?(yt||a===1?t=!0:qa||(dt&536870912)!==0?t=!1:(Dl=t=!0,(a===2||a===9||a===3||a===6)&&(a=He.current,a!==null&&a.tag===13&&(a.flags|=16384))),Kd(e,t)):si(e)}function si(t){var e=t;do{if((e.flags&32768)!==0){Kd(e,Dl);return}t=e.return;var l=gy(e.alternate,e,dl);if(l!==null){ft=l;return}if(e=e.sibling,e!==null){ft=e;return}ft=e=t}while(e!==null);Gt===0&&(Gt=5)}function Kd(t,e){do{var l=vy(t.alternate,t);if(l!==null){l.flags&=32767,ft=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ft=t;return}ft=t=l}while(t!==null);Gt=6,ft=null}function kd(t,e,l,a,n,i,o,d,y){t.cancelPendingCommit=null;do di();while(le!==0);if((bt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(i=e.lanes|e.childLanes,i|=Mc,am(t,l,i,o,d,y),t===Mt&&(ft=Mt=null,dt=0),ja=e,Nl=t,Ga=l,Hr=i,qr=n,Yd=a,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,Ry(vu,function(){return Pd(),null})):(t.callbackNode=null,t.callbackPriority=0),a=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||a){a=M.T,M.T=null,n=G.p,G.p=2,o=bt,bt|=4;try{py(t,e,l)}finally{bt=o,G.p=n,M.T=a}}le=1,$d(),Jd(),Wd()}}function $d(){if(le===1){le=0;var t=Nl,e=ja,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=M.T,M.T=null;var a=G.p;G.p=2;var n=bt;bt|=4;try{Md(e,t);var i=Pr,o=qo(t.containerInfo),d=i.focusedElem,y=i.selectionRange;if(o!==d&&d&&d.ownerDocument&&Ho(d.ownerDocument.documentElement,d)){if(y!==null&&_c(d)){var O=y.start,U=y.end;if(U===void 0&&(U=O),"selectionStart"in d)d.selectionStart=O,d.selectionEnd=Math.min(U,d.value.length);else{var H=d.ownerDocument||document,_=H&&H.defaultView||window;if(_.getSelection){var C=_.getSelection(),at=d.textContent.length,et=Math.min(y.start,at),Et=y.end===void 0?et:Math.min(y.end,at);!C.extend&&et>Et&&(o=Et,Et=et,et=o);var T=No(d,et),b=No(d,Et);if(T&&b&&(C.rangeCount!==1||C.anchorNode!==T.node||C.anchorOffset!==T.offset||C.focusNode!==b.node||C.focusOffset!==b.offset)){var E=H.createRange();E.setStart(T.node,T.offset),C.removeAllRanges(),et>Et?(C.addRange(E),C.extend(b.node,b.offset)):(E.setEnd(b.node,b.offset),C.addRange(E))}}}}for(H=[],C=d;C=C.parentNode;)C.nodeType===1&&H.push({element:C,left:C.scrollLeft,top:C.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<H.length;d++){var B=H[d];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}Oi=!!Fr,Pr=Fr=null}finally{bt=n,G.p=a,M.T=l}}t.current=e,le=2}}function Jd(){if(le===2){le=0;var t=Nl,e=ja,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=M.T,M.T=null;var a=G.p;G.p=2;var n=bt;bt|=4;try{zd(t,e.alternate,e)}finally{bt=n,G.p=a,M.T=l}}le=3}}function Wd(){if(le===4||le===3){le=0,$h();var t=Nl,e=ja,l=Ga,a=Yd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?le=5:(le=0,ja=Nl=null,Fd(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(Bl=null),nc(l),e=e.stateNode,ve&&typeof ve.onCommitFiberRoot=="function")try{ve.onCommitFiberRoot(nn,e,void 0,(e.current.flags&128)===128)}catch{}if(a!==null){e=M.T,n=G.p,G.p=2,M.T=null;try{for(var i=t.onRecoverableError,o=0;o<a.length;o++){var d=a[o];i(d.value,{componentStack:d.stack})}}finally{M.T=e,G.p=n}}(Ga&3)!==0&&di(),$e(t),n=t.pendingLanes,(l&4194090)!==0&&(n&42)!==0?t===Yr?Ln++:(Ln=0,Yr=t):Ln=0,Vn(0)}}function Fd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,En(e)))}function di(t){return $d(),Jd(),Wd(),Pd()}function Pd(){if(le!==5)return!1;var t=Nl,e=Hr;Hr=0;var l=nc(Ga),a=M.T,n=G.p;try{G.p=32>l?32:l,M.T=null,l=qr,qr=null;var i=Nl,o=Ga;if(le=0,ja=Nl=null,Ga=0,(bt&6)!==0)throw Error(r(331));var d=bt;if(bt|=4,Hd(i.current),Ud(i,i.current,o,l),bt=d,Vn(0,!1),ve&&typeof ve.onPostCommitFiberRoot=="function")try{ve.onPostCommitFiberRoot(nn,i)}catch{}return!0}finally{G.p=n,M.T=a,Fd(t,e)}}function Id(t,e,l){e=De(l,e),e=yr(t.stateNode,e,2),t=Ol(t,e,2),t!==null&&(cn(t,2),$e(t))}function Ct(t,e,l){if(t.tag===3)Id(t,t,l);else for(;e!==null;){if(e.tag===3){Id(e,t,l);break}else if(e.tag===1){var a=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Bl===null||!Bl.has(a))){t=De(l,t),l=ad(2),a=Ol(e,l,2),a!==null&&(nd(l,a,e,t),cn(a,2),$e(a));break}}e=e.return}}function Xr(t,e,l){var a=t.pingCache;if(a===null){a=t.pingCache=new Ty;var n=new Set;a.set(e,n)}else n=a.get(e),n===void 0&&(n=new Set,a.set(e,n));n.has(l)||(Dr=!0,n.add(l),t=zy.bind(null,t,e,l),e.then(t,t))}function zy(t,e,l){var a=t.pingCache;a!==null&&a.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Mt===t&&(dt&l)===l&&(Gt===4||Gt===3&&(dt&62914560)===dt&&300>Le()-Nr?(bt&2)===0&&wa(t,0):Ur|=l,Ya===dt&&(Ya=0)),$e(t)}function t0(t,e){e===0&&(e=Jf()),t=Aa(t,e),t!==null&&(cn(t,e),$e(t))}function Cy(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),t0(t,l)}function xy(t,e){var l=0;switch(t.tag){case 13:var a=t.stateNode,n=t.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=t.stateNode;break;case 22:a=t.stateNode._retryCache;break;default:throw Error(r(314))}a!==null&&a.delete(e),t0(t,l)}function Ry(t,e){return tc(t,e)}var hi=null,Qa=null,Qr=!1,mi=!1,Lr=!1,ia=0;function $e(t){t!==Qa&&t.next===null&&(Qa===null?hi=Qa=t:Qa=Qa.next=t),mi=!0,Qr||(Qr=!0,Dy())}function Vn(t,e){if(!Lr&&mi){Lr=!0;do for(var l=!1,a=hi;a!==null;){if(t!==0){var n=a.pendingLanes;if(n===0)var i=0;else{var o=a.suspendedLanes,d=a.pingedLanes;i=(1<<31-pe(42|t)+1)-1,i&=n&~(o&~d),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(l=!0,n0(a,i))}else i=dt,i=Su(a,a===Mt?i:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(i&3)===0||un(a,i)||(l=!0,n0(a,i));a=a.next}while(l);Lr=!1}}function My(){e0()}function e0(){mi=Qr=!1;var t=0;ia!==0&&(Gy()&&(t=ia),ia=0);for(var e=Le(),l=null,a=hi;a!==null;){var n=a.next,i=l0(a,e);i===0?(a.next=null,l===null?hi=n:l.next=n,n===null&&(Qa=l)):(l=a,(t!==0||(i&3)!==0)&&(mi=!0)),a=n}Vn(t)}function l0(t,e){for(var l=t.suspendedLanes,a=t.pingedLanes,n=t.expirationTimes,i=t.pendingLanes&-62914561;0<i;){var o=31-pe(i),d=1<<o,y=n[o];y===-1?((d&l)===0||(d&a)!==0)&&(n[o]=lm(d,e)):y<=e&&(t.expiredLanes|=d),i&=~d}if(e=Mt,l=dt,l=Su(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a=t.callbackNode,l===0||t===e&&(St===2||St===9)||t.cancelPendingCommit!==null)return a!==null&&a!==null&&ec(a),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||un(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(a!==null&&ec(a),nc(l)){case 2:case 8:l=Kf;break;case 32:l=vu;break;case 268435456:l=kf;break;default:l=vu}return a=a0.bind(null,t),l=tc(l,a),t.callbackPriority=e,t.callbackNode=l,e}return a!==null&&a!==null&&ec(a),t.callbackPriority=2,t.callbackNode=null,2}function a0(t,e){if(le!==0&&le!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(di()&&t.callbackNode!==l)return null;var a=dt;return a=Su(t,t===Mt?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),a===0?null:(Gd(t,a,e),l0(t,Le()),t.callbackNode!=null&&t.callbackNode===l?a0.bind(null,t):null)}function n0(t,e){if(di())return null;Gd(t,e,!0)}function Dy(){Xy(function(){(bt&6)!==0?tc(Zf,My):e0()})}function Vr(){return ia===0&&(ia=$f()),ia}function u0(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:_u(""+t)}function i0(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function Uy(t,e,l,a,n){if(e==="submit"&&l&&l.stateNode===n){var i=u0((n[oe]||null).action),o=a.submitter;o&&(e=(e=o[oe]||null)?u0(e.formAction):o.getAttribute("formAction"),e!==null&&(i=e,o=null));var d=new Ru("action","action",null,a,n);t.push({event:d,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ia!==0){var y=o?i0(n,o):new FormData(n);or(l,{pending:!0,data:y,method:n.method,action:i},null,y)}}else typeof i=="function"&&(d.preventDefault(),y=o?i0(n,o):new FormData(n),or(l,{pending:!0,data:y,method:n.method,action:i},i,y))},currentTarget:n}]})}}for(var Zr=0;Zr<Rc.length;Zr++){var Kr=Rc[Zr],By=Kr.toLowerCase(),Ny=Kr[0].toUpperCase()+Kr.slice(1);we(By,"on"+Ny)}we(Go,"onAnimationEnd"),we(wo,"onAnimationIteration"),we(Xo,"onAnimationStart"),we("dblclick","onDoubleClick"),we("focusin","onFocus"),we("focusout","onBlur"),we(Fm,"onTransitionRun"),we(Pm,"onTransitionStart"),we(Im,"onTransitionCancel"),we(Qo,"onTransitionEnd"),da("onMouseEnter",["mouseout","mouseover"]),da("onMouseLeave",["mouseout","mouseover"]),da("onPointerEnter",["pointerout","pointerover"]),da("onPointerLeave",["pointerout","pointerover"]),Vl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Vl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Vl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Vl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Vl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Vl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Zn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Hy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Zn));function c0(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var a=t[l],n=a.event;a=a.listeners;t:{var i=void 0;if(e)for(var o=a.length-1;0<=o;o--){var d=a[o],y=d.instance,O=d.currentTarget;if(d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(U){ei(U)}n.currentTarget=null,i=y}else for(o=0;o<a.length;o++){if(d=a[o],y=d.instance,O=d.currentTarget,d=d.listener,y!==i&&n.isPropagationStopped())break t;i=d,n.currentTarget=O;try{i(n)}catch(U){ei(U)}n.currentTarget=null,i=y}}}}function ot(t,e){var l=e[uc];l===void 0&&(l=e[uc]=new Set);var a=t+"__bubble";l.has(a)||(r0(e,t,2,!1),l.add(a))}function kr(t,e,l){var a=0;e&&(a|=4),r0(l,t,a,e)}var yi="_reactListening"+Math.random().toString(36).slice(2);function $r(t){if(!t[yi]){t[yi]=!0,to.forEach(function(l){l!=="selectionchange"&&(Hy.has(l)||kr(l,!1,t),kr(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[yi]||(e[yi]=!0,kr("selectionchange",!1,e))}}function r0(t,e,l,a){switch(U0(e)){case 2:var n=rg;break;case 8:n=fg;break;default:n=ff}l=n.bind(null,e,l,t),n=void 0,!gc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),a?n!==void 0?t.addEventListener(e,l,{capture:!0,passive:n}):t.addEventListener(e,l,!0):n!==void 0?t.addEventListener(e,l,{passive:n}):t.addEventListener(e,l,!1)}function Jr(t,e,l,a,n){var i=a;if((e&1)===0&&(e&2)===0&&a!==null)t:for(;;){if(a===null)return;var o=a.tag;if(o===3||o===4){var d=a.stateNode.containerInfo;if(d===n)break;if(o===4)for(o=a.return;o!==null;){var y=o.tag;if((y===3||y===4)&&o.stateNode.containerInfo===n)return;o=o.return}for(;d!==null;){if(o=fa(d),o===null)return;if(y=o.tag,y===5||y===6||y===26||y===27){a=i=o;continue t}d=d.parentNode}}a=a.return}yo(function(){var O=i,U=mc(l),H=[];t:{var _=Lo.get(t);if(_!==void 0){var C=Ru,at=t;switch(t){case"keypress":if(Cu(l)===0)break t;case"keydown":case"keyup":C=Rm;break;case"focusin":at="focus",C=Sc;break;case"focusout":at="blur",C=Sc;break;case"beforeblur":case"afterblur":C=Sc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":C=po;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":C=vm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":C=Um;break;case Go:case wo:case Xo:C=Sm;break;case Qo:C=Nm;break;case"scroll":case"scrollend":C=ym;break;case"wheel":C=qm;break;case"copy":case"cut":case"paste":C=Am;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":C=So;break;case"toggle":case"beforetoggle":C=jm}var et=(e&4)!==0,Et=!et&&(t==="scroll"||t==="scrollend"),T=et?_!==null?_+"Capture":null:_;et=[];for(var b=O,E;b!==null;){var B=b;if(E=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||E===null||T===null||(B=on(b,T),B!=null&&et.push(Kn(b,B,E))),Et)break;b=b.return}0<et.length&&(_=new C(_,at,null,l,U),H.push({event:_,listeners:et}))}}if((e&7)===0){t:{if(_=t==="mouseover"||t==="pointerover",C=t==="mouseout"||t==="pointerout",_&&l!==hc&&(at=l.relatedTarget||l.fromElement)&&(fa(at)||at[ra]))break t;if((C||_)&&(_=U.window===U?U:(_=U.ownerDocument)?_.defaultView||_.parentWindow:window,C?(at=l.relatedTarget||l.toElement,C=O,at=at?fa(at):null,at!==null&&(Et=h(at),et=at.tag,at!==Et||et!==5&&et!==27&&et!==6)&&(at=null)):(C=null,at=O),C!==at)){if(et=po,B="onMouseLeave",T="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(et=So,B="onPointerLeave",T="onPointerEnter",b="pointer"),Et=C==null?_:fn(C),E=at==null?_:fn(at),_=new et(B,b+"leave",C,l,U),_.target=Et,_.relatedTarget=E,B=null,fa(U)===O&&(et=new et(T,b+"enter",at,l,U),et.target=E,et.relatedTarget=Et,B=et),Et=B,C&&at)e:{for(et=C,T=at,b=0,E=et;E;E=La(E))b++;for(E=0,B=T;B;B=La(B))E++;for(;0<b-E;)et=La(et),b--;for(;0<E-b;)T=La(T),E--;for(;b--;){if(et===T||T!==null&&et===T.alternate)break e;et=La(et),T=La(T)}et=null}else et=null;C!==null&&f0(H,_,C,et,!1),at!==null&&Et!==null&&f0(H,Et,at,et,!0)}}t:{if(_=O?fn(O):window,C=_.nodeName&&_.nodeName.toLowerCase(),C==="select"||C==="input"&&_.type==="file")var Z=xo;else if(zo(_))if(Ro)Z=$m;else{Z=Km;var rt=Zm}else C=_.nodeName,!C||C.toLowerCase()!=="input"||_.type!=="checkbox"&&_.type!=="radio"?O&&dc(O.elementType)&&(Z=xo):Z=km;if(Z&&(Z=Z(t,O))){Co(H,Z,l,U);break t}rt&&rt(t,_,O),t==="focusout"&&O&&_.type==="number"&&O.memoizedProps.value!=null&&sc(_,"number",_.value)}switch(rt=O?fn(O):window,t){case"focusin":(zo(rt)||rt.contentEditable==="true")&&(ba=rt,zc=O,pn=null);break;case"focusout":pn=zc=ba=null;break;case"mousedown":Cc=!0;break;case"contextmenu":case"mouseup":case"dragend":Cc=!1,Yo(H,l,U);break;case"selectionchange":if(Wm)break;case"keydown":case"keyup":Yo(H,l,U)}var W;if(Ac)t:{switch(t){case"compositionstart":var lt="onCompositionStart";break t;case"compositionend":lt="onCompositionEnd";break t;case"compositionupdate":lt="onCompositionUpdate";break t}lt=void 0}else pa?Oo(t,l)&&(lt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(lt="onCompositionStart");lt&&(To&&l.locale!=="ko"&&(pa||lt!=="onCompositionStart"?lt==="onCompositionEnd"&&pa&&(W=go()):(Sl=U,vc="value"in Sl?Sl.value:Sl.textContent,pa=!0)),rt=gi(O,lt),0<rt.length&&(lt=new bo(lt,t,null,l,U),H.push({event:lt,listeners:rt}),W?lt.data=W:(W=_o(l),W!==null&&(lt.data=W)))),(W=wm?Xm(t,l):Qm(t,l))&&(lt=gi(O,"onBeforeInput"),0<lt.length&&(rt=new bo("onBeforeInput","beforeinput",null,l,U),H.push({event:rt,listeners:lt}),rt.data=W)),Uy(H,t,O,l,U)}c0(H,e)})}function Kn(t,e,l){return{instance:t,listener:e,currentTarget:l}}function gi(t,e){for(var l=e+"Capture",a=[];t!==null;){var n=t,i=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||i===null||(n=on(t,l),n!=null&&a.unshift(Kn(t,n,i)),n=on(t,e),n!=null&&a.push(Kn(t,n,i))),t.tag===3)return a;t=t.return}return[]}function La(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function f0(t,e,l,a,n){for(var i=e._reactName,o=[];l!==null&&l!==a;){var d=l,y=d.alternate,O=d.stateNode;if(d=d.tag,y!==null&&y===a)break;d!==5&&d!==26&&d!==27||O===null||(y=O,n?(O=on(l,i),O!=null&&o.unshift(Kn(l,O,y))):n||(O=on(l,i),O!=null&&o.push(Kn(l,O,y)))),l=l.return}o.length!==0&&t.push({event:e,listeners:o})}var qy=/\r\n?/g,Yy=/\u0000|\uFFFD/g;function o0(t){return(typeof t=="string"?t:""+t).replace(qy,`
`).replace(Yy,"")}function s0(t,e){return e=o0(e),o0(t)===e}function vi(){}function At(t,e,l,a,n,i){switch(l){case"children":typeof a=="string"?e==="body"||e==="textarea"&&a===""||ya(t,a):(typeof a=="number"||typeof a=="bigint")&&e!=="body"&&ya(t,""+a);break;case"className":Au(t,"class",a);break;case"tabIndex":Au(t,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Au(t,l,a);break;case"style":ho(t,a,i);break;case"data":if(e!=="object"){Au(t,"data",a);break}case"src":case"href":if(a===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(l==="formAction"?(e!=="input"&&At(t,e,"name",n.name,n,null),At(t,e,"formEncType",n.formEncType,n,null),At(t,e,"formMethod",n.formMethod,n,null),At(t,e,"formTarget",n.formTarget,n,null)):(At(t,e,"encType",n.encType,n,null),At(t,e,"method",n.method,n,null),At(t,e,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){t.removeAttribute(l);break}a=_u(""+a),t.setAttribute(l,a);break;case"onClick":a!=null&&(t.onclick=vi);break;case"onScroll":a!=null&&ot("scroll",t);break;case"onScrollEnd":a!=null&&ot("scrollend",t);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"multiple":t.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":t.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){t.removeAttribute("xlink:href");break}l=_u(""+a),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""+a):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":a===!0?t.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?t.setAttribute(l,a):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?t.setAttribute(l,a):t.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?t.removeAttribute(l):t.setAttribute(l,a);break;case"popover":ot("beforetoggle",t),ot("toggle",t),Tu(t,"popover",a);break;case"xlinkActuate":Ie(t,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Ie(t,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Ie(t,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Ie(t,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Ie(t,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Ie(t,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Ie(t,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":Tu(t,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=hm.get(l)||l,Tu(t,l,a))}}function Wr(t,e,l,a,n,i){switch(l){case"style":ho(t,a,i);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(r(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(r(60));t.innerHTML=l}}break;case"children":typeof a=="string"?ya(t,a):(typeof a=="number"||typeof a=="bigint")&&ya(t,""+a);break;case"onScroll":a!=null&&ot("scroll",t);break;case"onScrollEnd":a!=null&&ot("scrollend",t);break;case"onClick":a!=null&&(t.onclick=vi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!eo.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),e=l.slice(2,n?l.length-7:void 0),i=t[oe]||null,i=i!=null?i[l]:null,typeof i=="function"&&t.removeEventListener(e,i,n),typeof a=="function")){typeof i!="function"&&i!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,a,n);break t}l in t?t[l]=a:a===!0?t.setAttribute(l,""):Tu(t,l,a)}}}function ae(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ot("error",t),ot("load",t);var a=!1,n=!1,i;for(i in l)if(l.hasOwnProperty(i)){var o=l[i];if(o!=null)switch(i){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:At(t,e,i,o,l,null)}}n&&At(t,e,"srcSet",l.srcSet,l,null),a&&At(t,e,"src",l.src,l,null);return;case"input":ot("invalid",t);var d=i=o=n=null,y=null,O=null;for(a in l)if(l.hasOwnProperty(a)){var U=l[a];if(U!=null)switch(a){case"name":n=U;break;case"type":o=U;break;case"checked":y=U;break;case"defaultChecked":O=U;break;case"value":i=U;break;case"defaultValue":d=U;break;case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(r(137,e));break;default:At(t,e,a,U,l,null)}}ro(t,i,d,y,O,o,n,!1),Eu(t);return;case"select":ot("invalid",t),a=o=i=null;for(n in l)if(l.hasOwnProperty(n)&&(d=l[n],d!=null))switch(n){case"value":i=d;break;case"defaultValue":o=d;break;case"multiple":a=d;default:At(t,e,n,d,l,null)}e=i,l=o,t.multiple=!!a,e!=null?ma(t,!!a,e,!1):l!=null&&ma(t,!!a,l,!0);return;case"textarea":ot("invalid",t),i=n=a=null;for(o in l)if(l.hasOwnProperty(o)&&(d=l[o],d!=null))switch(o){case"value":a=d;break;case"defaultValue":n=d;break;case"children":i=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(r(91));break;default:At(t,e,o,d,l,null)}oo(t,a,n,i),Eu(t);return;case"option":for(y in l)if(l.hasOwnProperty(y)&&(a=l[y],a!=null))switch(y){case"selected":t.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:At(t,e,y,a,l,null)}return;case"dialog":ot("beforetoggle",t),ot("toggle",t),ot("cancel",t),ot("close",t);break;case"iframe":case"object":ot("load",t);break;case"video":case"audio":for(a=0;a<Zn.length;a++)ot(Zn[a],t);break;case"image":ot("error",t),ot("load",t);break;case"details":ot("toggle",t);break;case"embed":case"source":case"link":ot("error",t),ot("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in l)if(l.hasOwnProperty(O)&&(a=l[O],a!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:At(t,e,O,a,l,null)}return;default:if(dc(e)){for(U in l)l.hasOwnProperty(U)&&(a=l[U],a!==void 0&&Wr(t,e,U,a,l,void 0));return}}for(d in l)l.hasOwnProperty(d)&&(a=l[d],a!=null&&At(t,e,d,a,l,null))}function jy(t,e,l,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,i=null,o=null,d=null,y=null,O=null,U=null;for(C in l){var H=l[C];if(l.hasOwnProperty(C)&&H!=null)switch(C){case"checked":break;case"value":break;case"defaultValue":y=H;default:a.hasOwnProperty(C)||At(t,e,C,null,a,H)}}for(var _ in a){var C=a[_];if(H=l[_],a.hasOwnProperty(_)&&(C!=null||H!=null))switch(_){case"type":i=C;break;case"name":n=C;break;case"checked":O=C;break;case"defaultChecked":U=C;break;case"value":o=C;break;case"defaultValue":d=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(r(137,e));break;default:C!==H&&At(t,e,_,C,a,H)}}oc(t,o,d,y,O,U,i,n);return;case"select":C=o=d=_=null;for(i in l)if(y=l[i],l.hasOwnProperty(i)&&y!=null)switch(i){case"value":break;case"multiple":C=y;default:a.hasOwnProperty(i)||At(t,e,i,null,a,y)}for(n in a)if(i=a[n],y=l[n],a.hasOwnProperty(n)&&(i!=null||y!=null))switch(n){case"value":_=i;break;case"defaultValue":d=i;break;case"multiple":o=i;default:i!==y&&At(t,e,n,i,a,y)}e=d,l=o,a=C,_!=null?ma(t,!!l,_,!1):!!a!=!!l&&(e!=null?ma(t,!!l,e,!0):ma(t,!!l,l?[]:"",!1));return;case"textarea":C=_=null;for(d in l)if(n=l[d],l.hasOwnProperty(d)&&n!=null&&!a.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:At(t,e,d,null,a,n)}for(o in a)if(n=a[o],i=l[o],a.hasOwnProperty(o)&&(n!=null||i!=null))switch(o){case"value":_=n;break;case"defaultValue":C=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(r(91));break;default:n!==i&&At(t,e,o,n,a,i)}fo(t,_,C);return;case"option":for(var at in l)if(_=l[at],l.hasOwnProperty(at)&&_!=null&&!a.hasOwnProperty(at))switch(at){case"selected":t.selected=!1;break;default:At(t,e,at,null,a,_)}for(y in a)if(_=a[y],C=l[y],a.hasOwnProperty(y)&&_!==C&&(_!=null||C!=null))switch(y){case"selected":t.selected=_&&typeof _!="function"&&typeof _!="symbol";break;default:At(t,e,y,_,a,C)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var et in l)_=l[et],l.hasOwnProperty(et)&&_!=null&&!a.hasOwnProperty(et)&&At(t,e,et,null,a,_);for(O in a)if(_=a[O],C=l[O],a.hasOwnProperty(O)&&_!==C&&(_!=null||C!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,e));break;default:At(t,e,O,_,a,C)}return;default:if(dc(e)){for(var Et in l)_=l[Et],l.hasOwnProperty(Et)&&_!==void 0&&!a.hasOwnProperty(Et)&&Wr(t,e,Et,void 0,a,_);for(U in a)_=a[U],C=l[U],!a.hasOwnProperty(U)||_===C||_===void 0&&C===void 0||Wr(t,e,U,_,a,C);return}}for(var T in l)_=l[T],l.hasOwnProperty(T)&&_!=null&&!a.hasOwnProperty(T)&&At(t,e,T,null,a,_);for(H in a)_=a[H],C=l[H],!a.hasOwnProperty(H)||_===C||_==null&&C==null||At(t,e,H,_,a,C)}var Fr=null,Pr=null;function pi(t){return t.nodeType===9?t:t.ownerDocument}function d0(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function h0(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Ir(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var tf=null;function Gy(){var t=window.event;return t&&t.type==="popstate"?t===tf?!1:(tf=t,!0):(tf=null,!1)}var m0=typeof setTimeout=="function"?setTimeout:void 0,wy=typeof clearTimeout=="function"?clearTimeout:void 0,y0=typeof Promise=="function"?Promise:void 0,Xy=typeof queueMicrotask=="function"?queueMicrotask:typeof y0<"u"?function(t){return y0.resolve(null).then(t).catch(Qy)}:m0;function Qy(t){setTimeout(function(){throw t})}function ql(t){return t==="head"}function g0(t,e){var l=e,a=0,n=0;do{var i=l.nextSibling;if(t.removeChild(l),i&&i.nodeType===8)if(l=i.data,l==="/$"){if(0<a&&8>a){l=a;var o=t.ownerDocument;if(l&1&&kn(o.documentElement),l&2&&kn(o.body),l&4)for(l=o.head,kn(l),o=l.firstChild;o;){var d=o.nextSibling,y=o.nodeName;o[rn]||y==="SCRIPT"||y==="STYLE"||y==="LINK"&&o.rel.toLowerCase()==="stylesheet"||l.removeChild(o),o=d}}if(n===0){t.removeChild(i),eu(e);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=i}while(l);eu(e)}function ef(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":ef(l),ic(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function Ly(t,e,l,a){for(;t.nodeType===1;){var n=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!a&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(a){if(!t[rn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(i=t.getAttribute("rel"),i==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(i!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(i=t.getAttribute("src"),(i!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&i&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var i=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===i)return t}else return t;if(t=Qe(t.nextSibling),t===null)break}return null}function Vy(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Qe(t.nextSibling),t===null))return null;return t}function lf(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Zy(t,e){var l=t.ownerDocument;if(t.data!=="$?"||l.readyState==="complete")e();else{var a=function(){e(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),t._reactRetry=a}}function Qe(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var af=null;function v0(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"){if(e===0)return t;e--}else l==="/$"&&e++}t=t.previousSibling}return null}function p0(t,e,l){switch(e=pi(l),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function kn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ic(t)}var Ye=new Map,b0=new Set;function bi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var hl=G.d;G.d={f:Ky,r:ky,D:$y,C:Jy,L:Wy,m:Fy,X:Iy,S:Py,M:tg};function Ky(){var t=hl.f(),e=oi();return t||e}function ky(t){var e=oa(t);e!==null&&e.tag===5&&e.type==="form"?Gs(e):hl.r(t)}var Va=typeof document>"u"?null:document;function S0(t,e,l){var a=Va;if(a&&typeof e=="string"&&e){var n=Me(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),b0.has(n)||(b0.add(n),t={rel:t,crossOrigin:l,href:e},a.querySelector(n)===null&&(e=a.createElement("link"),ae(e,"link",t),Ft(e),a.head.appendChild(e)))}}function $y(t){hl.D(t),S0("dns-prefetch",t,null)}function Jy(t,e){hl.C(t,e),S0("preconnect",t,e)}function Wy(t,e,l){hl.L(t,e,l);var a=Va;if(a&&t&&e){var n='link[rel="preload"][as="'+Me(e)+'"]';e==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+Me(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+Me(l.imageSizes)+'"]')):n+='[href="'+Me(t)+'"]';var i=n;switch(e){case"style":i=Za(t);break;case"script":i=Ka(t)}Ye.has(i)||(t=x({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),Ye.set(i,t),a.querySelector(n)!==null||e==="style"&&a.querySelector($n(i))||e==="script"&&a.querySelector(Jn(i))||(e=a.createElement("link"),ae(e,"link",t),Ft(e),a.head.appendChild(e)))}}function Fy(t,e){hl.m(t,e);var l=Va;if(l&&t){var a=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+Me(a)+'"][href="'+Me(t)+'"]',i=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=Ka(t)}if(!Ye.has(i)&&(t=x({rel:"modulepreload",href:t},e),Ye.set(i,t),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Jn(i)))return}a=l.createElement("link"),ae(a,"link",t),Ft(a),l.head.appendChild(a)}}}function Py(t,e,l){hl.S(t,e,l);var a=Va;if(a&&t){var n=sa(a).hoistableStyles,i=Za(t);e=e||"default";var o=n.get(i);if(!o){var d={loading:0,preload:null};if(o=a.querySelector($n(i)))d.loading=5;else{t=x({rel:"stylesheet",href:t,"data-precedence":e},l),(l=Ye.get(i))&&nf(t,l);var y=o=a.createElement("link");Ft(y),ae(y,"link",t),y._p=new Promise(function(O,U){y.onload=O,y.onerror=U}),y.addEventListener("load",function(){d.loading|=1}),y.addEventListener("error",function(){d.loading|=2}),d.loading|=4,Si(o,e,a)}o={type:"stylesheet",instance:o,count:1,state:d},n.set(i,o)}}}function Iy(t,e){hl.X(t,e);var l=Va;if(l&&t){var a=sa(l).hoistableScripts,n=Ka(t),i=a.get(n);i||(i=l.querySelector(Jn(n)),i||(t=x({src:t,async:!0},e),(e=Ye.get(n))&&uf(t,e),i=l.createElement("script"),Ft(i),ae(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function tg(t,e){hl.M(t,e);var l=Va;if(l&&t){var a=sa(l).hoistableScripts,n=Ka(t),i=a.get(n);i||(i=l.querySelector(Jn(n)),i||(t=x({src:t,async:!0,type:"module"},e),(e=Ye.get(n))&&uf(t,e),i=l.createElement("script"),Ft(i),ae(i,"link",t),l.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},a.set(n,i))}}function T0(t,e,l,a){var n=(n=ut.current)?bi(n):null;if(!n)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=Za(l.href),l=sa(n).hoistableStyles,a=l.get(e),a||(a={type:"style",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=Za(l.href);var i=sa(n).hoistableStyles,o=i.get(t);if(o||(n=n.ownerDocument||n,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(t,o),(i=n.querySelector($n(t)))&&!i._p&&(o.instance=i,o.state.loading=5),Ye.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ye.set(t,l),i||eg(n,t,l,o.state))),e&&a===null)throw Error(r(528,""));return o}if(e&&a!==null)throw Error(r(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Ka(l),l=sa(n).hoistableScripts,a=l.get(e),a||(a={type:"script",instance:null,count:0,state:null},l.set(e,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Za(t){return'href="'+Me(t)+'"'}function $n(t){return'link[rel="stylesheet"]['+t+"]"}function A0(t){return x({},t,{"data-precedence":t.precedence,precedence:null})}function eg(t,e,l,a){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?a.loading=1:(e=t.createElement("link"),a.preload=e,e.addEventListener("load",function(){return a.loading|=1}),e.addEventListener("error",function(){return a.loading|=2}),ae(e,"link",l),Ft(e),t.head.appendChild(e))}function Ka(t){return'[src="'+Me(t)+'"]'}function Jn(t){return"script[async]"+t}function E0(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var a=t.querySelector('style[data-href~="'+Me(l.href)+'"]');if(a)return e.instance=a,Ft(a),a;var n=x({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(t.ownerDocument||t).createElement("style"),Ft(a),ae(a,"style",n),Si(a,l.precedence,t),e.instance=a;case"stylesheet":n=Za(l.href);var i=t.querySelector($n(n));if(i)return e.state.loading|=4,e.instance=i,Ft(i),i;a=A0(l),(n=Ye.get(n))&&nf(a,n),i=(t.ownerDocument||t).createElement("link"),Ft(i);var o=i;return o._p=new Promise(function(d,y){o.onload=d,o.onerror=y}),ae(i,"link",a),e.state.loading|=4,Si(i,l.precedence,t),e.instance=i;case"script":return i=Ka(l.src),(n=t.querySelector(Jn(i)))?(e.instance=n,Ft(n),n):(a=l,(n=Ye.get(i))&&(a=x({},l),uf(a,n)),t=t.ownerDocument||t,n=t.createElement("script"),Ft(n),ae(n,"link",a),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(a=e.instance,e.state.loading|=4,Si(a,l.precedence,t));return e.instance}function Si(t,e,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,i=n,o=0;o<a.length;o++){var d=a[o];if(d.dataset.precedence===e)i=d;else if(i!==n)break}i?i.parentNode.insertBefore(t,i.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function nf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function uf(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ti=null;function O0(t,e,l){if(Ti===null){var a=new Map,n=Ti=new Map;n.set(l,a)}else n=Ti,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(t))return a;for(a.set(t,null),l=l.getElementsByTagName(t),n=0;n<l.length;n++){var i=l[n];if(!(i[rn]||i[ue]||t==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var o=i.getAttribute(e)||"";o=t+o;var d=a.get(o);d?d.push(i):a.set(o,[i])}}return a}function _0(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function lg(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function z0(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Wn=null;function ag(){}function ng(t,e,l){if(Wn===null)throw Error(r(475));var a=Wn;if(e.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Za(l.href),i=t.querySelector($n(n));if(i){t=i._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(a.count++,a=Ai.bind(a),t.then(a,a)),e.state.loading|=4,e.instance=i,Ft(i);return}i=t.ownerDocument||t,l=A0(l),(n=Ye.get(n))&&nf(l,n),i=i.createElement("link"),Ft(i);var o=i;o._p=new Promise(function(d,y){o.onload=d,o.onerror=y}),ae(i,"link",l),e.instance=i}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(a.count++,e=Ai.bind(a),t.addEventListener("load",e),t.addEventListener("error",e))}}function ug(){if(Wn===null)throw Error(r(475));var t=Wn;return t.stylesheets&&t.count===0&&cf(t,t.stylesheets),0<t.count?function(e){var l=setTimeout(function(){if(t.stylesheets&&cf(t,t.stylesheets),t.unsuspend){var a=t.unsuspend;t.unsuspend=null,a()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(l)}}:null}function Ai(){if(this.count--,this.count===0){if(this.stylesheets)cf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ei=null;function cf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ei=new Map,e.forEach(ig,t),Ei=null,Ai.call(t))}function ig(t,e){if(!(e.state.loading&4)){var l=Ei.get(t);if(l)var a=l.get(null);else{l=new Map,Ei.set(t,l);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<n.length;i++){var o=n[i];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(l.set(o.dataset.precedence,o),a=o)}a&&l.set(null,a)}n=e.instance,o=n.getAttribute("data-precedence"),i=l.get(o)||a,i===a&&l.set(null,n),l.set(o,n),this.count++,a=Ai.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),i?i.parentNode.insertBefore(n,i.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Fn={$$typeof:$,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function cg(t,e,l,a,n,i,o,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=lc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lc(0),this.hiddenUpdates=lc(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=i,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function C0(t,e,l,a,n,i,o,d,y,O,U,H){return t=new cg(t,e,l,o,d,y,O,H),e=1,i===!0&&(e|=24),i=Se(3,null,null,e),t.current=i,i.stateNode=t,e=Xc(),e.refCount++,t.pooledCache=e,e.refCount++,i.memoizedState={element:a,isDehydrated:l,cache:e},Zc(i),t}function x0(t){return t?(t=Ea,t):Ea}function R0(t,e,l,a,n,i){n=x0(n),a.context===null?a.context=n:a.pendingContext=n,a=El(e),a.payload={element:l},i=i===void 0?null:i,i!==null&&(a.callback=i),l=Ol(t,a,e),l!==null&&(_e(l,t,e),Cn(l,t,e))}function M0(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function rf(t,e){M0(t,e),(t=t.alternate)&&M0(t,e)}function D0(t){if(t.tag===13){var e=Aa(t,67108864);e!==null&&_e(e,t,67108864),rf(t,67108864)}}var Oi=!0;function rg(t,e,l,a){var n=M.T;M.T=null;var i=G.p;try{G.p=2,ff(t,e,l,a)}finally{G.p=i,M.T=n}}function fg(t,e,l,a){var n=M.T;M.T=null;var i=G.p;try{G.p=8,ff(t,e,l,a)}finally{G.p=i,M.T=n}}function ff(t,e,l,a){if(Oi){var n=of(a);if(n===null)Jr(t,e,a,_i,l),B0(t,a);else if(sg(n,t,e,l,a))a.stopPropagation();else if(B0(t,a),e&4&&-1<og.indexOf(t)){for(;n!==null;){var i=oa(n);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var o=Ll(i.pendingLanes);if(o!==0){var d=i;for(d.pendingLanes|=2,d.entangledLanes|=2;o;){var y=1<<31-pe(o);d.entanglements[1]|=y,o&=~y}$e(i),(bt&6)===0&&(ri=Le()+500,Vn(0))}}break;case 13:d=Aa(i,2),d!==null&&_e(d,i,2),oi(),rf(i,2)}if(i=of(a),i===null&&Jr(t,e,a,_i,l),i===n)break;n=i}n!==null&&a.stopPropagation()}else Jr(t,e,a,null,l)}}function of(t){return t=mc(t),sf(t)}var _i=null;function sf(t){if(_i=null,t=fa(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=v(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return _i=t,null}function U0(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Jh()){case Zf:return 2;case Kf:return 8;case vu:case Wh:return 32;case kf:return 268435456;default:return 32}default:return 32}}var df=!1,Yl=null,jl=null,Gl=null,Pn=new Map,In=new Map,wl=[],og="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function B0(t,e){switch(t){case"focusin":case"focusout":Yl=null;break;case"dragenter":case"dragleave":jl=null;break;case"mouseover":case"mouseout":Gl=null;break;case"pointerover":case"pointerout":Pn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":In.delete(e.pointerId)}}function tu(t,e,l,a,n,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:l,eventSystemFlags:a,nativeEvent:i,targetContainers:[n]},e!==null&&(e=oa(e),e!==null&&D0(e)),t):(t.eventSystemFlags|=a,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function sg(t,e,l,a,n){switch(e){case"focusin":return Yl=tu(Yl,t,e,l,a,n),!0;case"dragenter":return jl=tu(jl,t,e,l,a,n),!0;case"mouseover":return Gl=tu(Gl,t,e,l,a,n),!0;case"pointerover":var i=n.pointerId;return Pn.set(i,tu(Pn.get(i)||null,t,e,l,a,n)),!0;case"gotpointercapture":return i=n.pointerId,In.set(i,tu(In.get(i)||null,t,e,l,a,n)),!0}return!1}function N0(t){var e=fa(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=v(l),e!==null){t.blockedOn=e,nm(t.priority,function(){if(l.tag===13){var a=Oe();a=ac(a);var n=Aa(l,a);n!==null&&_e(n,l,a),rf(l,a)}});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zi(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=of(t.nativeEvent);if(l===null){l=t.nativeEvent;var a=new l.constructor(l.type,l);hc=a,l.target.dispatchEvent(a),hc=null}else return e=oa(l),e!==null&&D0(e),t.blockedOn=l,!1;e.shift()}return!0}function H0(t,e,l){zi(t)&&l.delete(e)}function dg(){df=!1,Yl!==null&&zi(Yl)&&(Yl=null),jl!==null&&zi(jl)&&(jl=null),Gl!==null&&zi(Gl)&&(Gl=null),Pn.forEach(H0),In.forEach(H0)}function Ci(t,e){t.blockedOn===e&&(t.blockedOn=null,df||(df=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,dg)))}var xi=null;function q0(t){xi!==t&&(xi=t,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){xi===t&&(xi=null);for(var e=0;e<t.length;e+=3){var l=t[e],a=t[e+1],n=t[e+2];if(typeof a!="function"){if(sf(a||l)===null)continue;break}var i=oa(l);i!==null&&(t.splice(e,3),e-=3,or(i,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function eu(t){function e(y){return Ci(y,t)}Yl!==null&&Ci(Yl,t),jl!==null&&Ci(jl,t),Gl!==null&&Ci(Gl,t),Pn.forEach(e),In.forEach(e);for(var l=0;l<wl.length;l++){var a=wl[l];a.blockedOn===t&&(a.blockedOn=null)}for(;0<wl.length&&(l=wl[0],l.blockedOn===null);)N0(l),l.blockedOn===null&&wl.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],i=l[a+1],o=n[oe]||null;if(typeof i=="function")o||q0(l);else if(o){var d=null;if(i&&i.hasAttribute("formAction")){if(n=i,o=i[oe]||null)d=o.formAction;else if(sf(n)!==null)continue}else d=o.action;typeof d=="function"?l[a+1]=d:(l.splice(a,3),a-=3),q0(l)}}}function hf(t){this._internalRoot=t}Ri.prototype.render=hf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var l=e.current,a=Oe();R0(l,a,t,e,null,null)},Ri.prototype.unmount=hf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;R0(t.current,2,null,t,null,null),oi(),e[ra]=null}};function Ri(t){this._internalRoot=t}Ri.prototype.unstable_scheduleHydration=function(t){if(t){var e=Pf();t={blockedOn:null,target:t,priority:e};for(var l=0;l<wl.length&&e!==0&&e<wl[l].priority;l++);wl.splice(l,0,t),l===0&&N0(t)}};var Y0=c.version;if(Y0!=="19.1.0")throw Error(r(527,Y0,"19.1.0"));G.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=A(e),t=t!==null?g(t):null,t=t===null?null:t.stateNode,t};var hg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Mi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mi.isDisabled&&Mi.supportsFiber)try{nn=Mi.inject(hg),ve=Mi}catch{}}return au.createRoot=function(t,e){if(!s(t))throw Error(r(299));var l=!1,a="",n=Is,i=td,o=ed,d=null;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(a=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(i=e.onCaughtError),e.onRecoverableError!==void 0&&(o=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=C0(t,1,!1,null,null,l,a,n,i,o,d,null),t[ra]=e.current,$r(t),new hf(e)},au.hydrateRoot=function(t,e,l){if(!s(t))throw Error(r(299));var a=!1,n="",i=Is,o=td,d=ed,y=null,O=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(i=l.onUncaughtError),l.onCaughtError!==void 0&&(o=l.onCaughtError),l.onRecoverableError!==void 0&&(d=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(y=l.unstable_transitionCallbacks),l.formState!==void 0&&(O=l.formState)),e=C0(t,1,!0,e,l??null,a,n,i,o,d,y,O),e.context=x0(null),l=e.current,a=Oe(),a=ac(a),n=El(a),n.callback=null,Ol(l,n,a),l=a,e.current.lanes=l,cn(e,l),$e(e),t[ra]=e.current,$r(t),new Ri(e)},au.version="19.1.0",au}var $0;function Og(){if($0)return gf.exports;$0=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(c){console.error(c)}}return u(),gf.exports=Eg(),gf.exports}var _g=Og();const zg=ph(_g),Cg=({text:u})=>Qt.jsx("div",{className:"joke-card",children:Qt.jsx("p",{children:u})}),xg=({text:u})=>Qt.jsx("div",{className:"fact-card",children:Qt.jsx("p",{children:u})}),J0=u=>u,Rg=()=>{let u=J0;return{configure(c){u=c},generate(c){return u(c)},reset(){u=J0}}},Mg=Rg();function ca(u,...c){const f=new URL(`https://mui.com/production-error/?code=${u}`);return c.forEach(r=>f.searchParams.append("args[]",r)),`Minified MUI error #${u}; visit ${f} for the full message.`}function tn(u){if(typeof u!="string")throw new Error(ca(7));return u.charAt(0).toUpperCase()+u.slice(1)}function Sh(u){var c,f,r="";if(typeof u=="string"||typeof u=="number")r+=u;else if(typeof u=="object")if(Array.isArray(u)){var s=u.length;for(c=0;c<s;c++)u[c]&&(f=Sh(u[c]))&&(r&&(r+=" "),r+=f)}else for(f in u)u[f]&&(r&&(r+=" "),r+=f);return r}function Dg(){for(var u,c,f=0,r="",s=arguments.length;f<s;f++)(u=arguments[f])&&(c=Sh(u))&&(r&&(r+=" "),r+=c);return r}function Ug(u,c,f=void 0){const r={};for(const s in u){const h=u[s];let v="",S=!0;for(let A=0;A<h.length;A+=1){const g=h[A];g&&(v+=(S===!0?"":" ")+c(g),S=!1,f&&f[g]&&(v+=" "+f[g]))}r[s]=v}return r}var Sf={exports:{}},Ot={};/**
 * @license React
 * react-is.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var W0;function Bg(){if(W0)return Ot;W0=1;var u=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),f=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),g=Symbol.for("react.suspense_list"),x=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),N=Symbol.for("react.view_transition"),w=Symbol.for("react.client.reference");function Y(z){if(typeof z=="object"&&z!==null){var L=z.$$typeof;switch(L){case u:switch(z=z.type,z){case f:case s:case r:case A:case g:case N:return z;default:switch(z=z&&z.$$typeof,z){case v:case S:case R:case x:return z;case h:return z;default:return L}}case c:return L}}}return Ot.ContextConsumer=h,Ot.ContextProvider=v,Ot.Element=u,Ot.ForwardRef=S,Ot.Fragment=f,Ot.Lazy=R,Ot.Memo=x,Ot.Portal=c,Ot.Profiler=s,Ot.StrictMode=r,Ot.Suspense=A,Ot.SuspenseList=g,Ot.isContextConsumer=function(z){return Y(z)===h},Ot.isContextProvider=function(z){return Y(z)===v},Ot.isElement=function(z){return typeof z=="object"&&z!==null&&z.$$typeof===u},Ot.isForwardRef=function(z){return Y(z)===S},Ot.isFragment=function(z){return Y(z)===f},Ot.isLazy=function(z){return Y(z)===R},Ot.isMemo=function(z){return Y(z)===x},Ot.isPortal=function(z){return Y(z)===c},Ot.isProfiler=function(z){return Y(z)===s},Ot.isStrictMode=function(z){return Y(z)===r},Ot.isSuspense=function(z){return Y(z)===A},Ot.isSuspenseList=function(z){return Y(z)===g},Ot.isValidElementType=function(z){return typeof z=="string"||typeof z=="function"||z===f||z===s||z===r||z===A||z===g||typeof z=="object"&&z!==null&&(z.$$typeof===R||z.$$typeof===x||z.$$typeof===v||z.$$typeof===h||z.$$typeof===S||z.$$typeof===w||z.getModuleId!==void 0)},Ot.typeOf=Y,Ot}var F0;function Ng(){return F0||(F0=1,Sf.exports=Bg()),Sf.exports}var Th=Ng();function yl(u){if(typeof u!="object"||u===null)return!1;const c=Object.getPrototypeOf(u);return(c===null||c===Object.prototype||Object.getPrototypeOf(c)===null)&&!(Symbol.toStringTag in u)&&!(Symbol.iterator in u)}function Ah(u){if(Ht.isValidElement(u)||Th.isValidElementType(u)||!yl(u))return u;const c={};return Object.keys(u).forEach(f=>{c[f]=Ah(u[f])}),c}function Ce(u,c,f={clone:!0}){const r=f.clone?{...u}:u;return yl(u)&&yl(c)&&Object.keys(c).forEach(s=>{Ht.isValidElement(c[s])||Th.isValidElementType(c[s])?r[s]=c[s]:yl(c[s])&&Object.prototype.hasOwnProperty.call(u,s)&&yl(u[s])?r[s]=Ce(u[s],c[s],f):f.clone?r[s]=yl(c[s])?Ah(c[s]):c[s]:r[s]=c[s]}),r}function ru(u,c){return c?Ce(u,c,{clone:!1}):u}function Hg(u,c){if(!u.containerQueries)return c;const f=Object.keys(c).filter(r=>r.startsWith("@container")).sort((r,s)=>{var v,S;const h=/min-width:\s*([0-9.]+)/;return+(((v=r.match(h))==null?void 0:v[1])||0)-+(((S=s.match(h))==null?void 0:S[1])||0)});return f.length?f.reduce((r,s)=>{const h=c[s];return delete r[s],r[s]=h,r},{...c}):c}function qg(u,c){return c==="@"||c.startsWith("@")&&(u.some(f=>c.startsWith(`@${f}`))||!!c.match(/^@\d/))}function Yg(u,c){const f=c.match(/^@([^/]+)?\/?(.+)?$/);if(!f)return null;const[,r,s]=f,h=Number.isNaN(+r)?r||0:+r;return u.containerQueries(s).up(h)}function jg(u){const c=(h,v)=>h.replace("@media",v?`@container ${v}`:"@container");function f(h,v){h.up=(...S)=>c(u.breakpoints.up(...S),v),h.down=(...S)=>c(u.breakpoints.down(...S),v),h.between=(...S)=>c(u.breakpoints.between(...S),v),h.only=(...S)=>c(u.breakpoints.only(...S),v),h.not=(...S)=>{const A=c(u.breakpoints.not(...S),v);return A.includes("not all and")?A.replace("not all and ","").replace("min-width:","width<").replace("max-width:","width>").replace("and","or"):A}}const r={},s=h=>(f(r,h),r);return f(s),{...u,containerQueries:s}}const wi={xs:0,sm:600,md:900,lg:1200,xl:1536},P0={keys:["xs","sm","md","lg","xl"],up:u=>`@media (min-width:${wi[u]}px)`},Gg={containerQueries:u=>({up:c=>{let f=typeof c=="number"?c:wi[c]||c;return typeof f=="number"&&(f=`${f}px`),u?`@container ${u} (min-width:${f})`:`@container (min-width:${f})`}})};function gl(u,c,f){const r=u.theme||{};if(Array.isArray(c)){const h=r.breakpoints||P0;return c.reduce((v,S,A)=>(v[h.up(h.keys[A])]=f(c[A]),v),{})}if(typeof c=="object"){const h=r.breakpoints||P0;return Object.keys(c).reduce((v,S)=>{if(qg(h.keys,S)){const A=Yg(r.containerQueries?r:Gg,S);A&&(v[A]=f(c[S],S))}else if(Object.keys(h.values||wi).includes(S)){const A=h.up(S);v[A]=f(c[S],S)}else{const A=S;v[A]=c[A]}return v},{})}return f(c)}function wg(u={}){var f;return((f=u.keys)==null?void 0:f.reduce((r,s)=>{const h=u.up(s);return r[h]={},r},{}))||{}}function Xg(u,c){return u.reduce((f,r)=>{const s=f[r];return(!s||Object.keys(s).length===0)&&delete f[r],f},c)}function Xi(u,c,f=!0){if(!c||typeof c!="string")return null;if(u&&u.vars&&f){const r=`vars.${c}`.split(".").reduce((s,h)=>s&&s[h]?s[h]:null,u);if(r!=null)return r}return c.split(".").reduce((r,s)=>r&&r[s]!=null?r[s]:null,u)}function ji(u,c,f,r=f){let s;return typeof u=="function"?s=u(f):Array.isArray(u)?s=u[f]||r:s=Xi(u,f)||r,c&&(s=c(s,r,u)),s}function Zt(u){const{prop:c,cssProperty:f=u.prop,themeKey:r,transform:s}=u,h=v=>{if(v[c]==null)return null;const S=v[c],A=v.theme,g=Xi(A,r)||{};return gl(v,S,R=>{let N=ji(g,s,R);return R===N&&typeof R=="string"&&(N=ji(g,s,`${c}${R==="default"?"":tn(R)}`,R)),f===!1?N:{[f]:N}})};return h.propTypes={},h.filterProps=[c],h}function Qg(u){const c={};return f=>(c[f]===void 0&&(c[f]=u(f)),c[f])}const Lg={m:"margin",p:"padding"},Vg={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},I0={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Zg=Qg(u=>{if(u.length>2)if(I0[u])u=I0[u];else return[u];const[c,f]=u.split(""),r=Lg[c],s=Vg[f]||"";return Array.isArray(s)?s.map(h=>r+h):[r+s]}),Nf=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Hf=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"];[...Nf,...Hf];function hu(u,c,f,r){const s=Xi(u,c,!0)??f;return typeof s=="number"||typeof s=="string"?h=>typeof h=="string"?h:typeof s=="string"?s.startsWith("var(")&&h===0?0:s.startsWith("var(")&&h===1?s:`calc(${h} * ${s})`:s*h:Array.isArray(s)?h=>{if(typeof h=="string")return h;const v=Math.abs(h),S=s[v];return h>=0?S:typeof S=="number"?-S:typeof S=="string"&&S.startsWith("var(")?`calc(-1 * ${S})`:`-${S}`}:typeof s=="function"?s:()=>{}}function qf(u){return hu(u,"spacing",8)}function mu(u,c){return typeof c=="string"||c==null?c:u(c)}function Kg(u,c){return f=>u.reduce((r,s)=>(r[s]=mu(c,f),r),{})}function kg(u,c,f,r){if(!c.includes(f))return null;const s=Zg(f),h=Kg(s,r),v=u[f];return gl(u,v,h)}function Eh(u,c){const f=qf(u.theme);return Object.keys(u).map(r=>kg(u,c,r,f)).reduce(ru,{})}function wt(u){return Eh(u,Nf)}wt.propTypes={};wt.filterProps=Nf;function Xt(u){return Eh(u,Hf)}Xt.propTypes={};Xt.filterProps=Hf;function Qi(...u){const c=u.reduce((r,s)=>(s.filterProps.forEach(h=>{r[h]=s}),r),{}),f=r=>Object.keys(r).reduce((s,h)=>c[h]?ru(s,c[h](r)):s,{});return f.propTypes={},f.filterProps=u.reduce((r,s)=>r.concat(s.filterProps),[]),f}function je(u){return typeof u!="number"?u:`${u}px solid`}function Ge(u,c){return Zt({prop:u,themeKey:"borders",transform:c})}const $g=Ge("border",je),Jg=Ge("borderTop",je),Wg=Ge("borderRight",je),Fg=Ge("borderBottom",je),Pg=Ge("borderLeft",je),Ig=Ge("borderColor"),t1=Ge("borderTopColor"),e1=Ge("borderRightColor"),l1=Ge("borderBottomColor"),a1=Ge("borderLeftColor"),n1=Ge("outline",je),u1=Ge("outlineColor"),Li=u=>{if(u.borderRadius!==void 0&&u.borderRadius!==null){const c=hu(u.theme,"shape.borderRadius",4),f=r=>({borderRadius:mu(c,r)});return gl(u,u.borderRadius,f)}return null};Li.propTypes={};Li.filterProps=["borderRadius"];Qi($g,Jg,Wg,Fg,Pg,Ig,t1,e1,l1,a1,Li,n1,u1);const Vi=u=>{if(u.gap!==void 0&&u.gap!==null){const c=hu(u.theme,"spacing",8),f=r=>({gap:mu(c,r)});return gl(u,u.gap,f)}return null};Vi.propTypes={};Vi.filterProps=["gap"];const Zi=u=>{if(u.columnGap!==void 0&&u.columnGap!==null){const c=hu(u.theme,"spacing",8),f=r=>({columnGap:mu(c,r)});return gl(u,u.columnGap,f)}return null};Zi.propTypes={};Zi.filterProps=["columnGap"];const Ki=u=>{if(u.rowGap!==void 0&&u.rowGap!==null){const c=hu(u.theme,"spacing",8),f=r=>({rowGap:mu(c,r)});return gl(u,u.rowGap,f)}return null};Ki.propTypes={};Ki.filterProps=["rowGap"];const i1=Zt({prop:"gridColumn"}),c1=Zt({prop:"gridRow"}),r1=Zt({prop:"gridAutoFlow"}),f1=Zt({prop:"gridAutoColumns"}),o1=Zt({prop:"gridAutoRows"}),s1=Zt({prop:"gridTemplateColumns"}),d1=Zt({prop:"gridTemplateRows"}),h1=Zt({prop:"gridTemplateAreas"}),m1=Zt({prop:"gridArea"});Qi(Vi,Zi,Ki,i1,c1,r1,f1,o1,s1,d1,h1,m1);function Pa(u,c){return c==="grey"?c:u}const y1=Zt({prop:"color",themeKey:"palette",transform:Pa}),g1=Zt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Pa}),v1=Zt({prop:"backgroundColor",themeKey:"palette",transform:Pa});Qi(y1,g1,v1);function ze(u){return u<=1&&u!==0?`${u*100}%`:u}const p1=Zt({prop:"width",transform:ze}),Yf=u=>{if(u.maxWidth!==void 0&&u.maxWidth!==null){const c=f=>{var s,h,v,S,A;const r=((v=(h=(s=u.theme)==null?void 0:s.breakpoints)==null?void 0:h.values)==null?void 0:v[f])||wi[f];return r?((A=(S=u.theme)==null?void 0:S.breakpoints)==null?void 0:A.unit)!=="px"?{maxWidth:`${r}${u.theme.breakpoints.unit}`}:{maxWidth:r}:{maxWidth:ze(f)}};return gl(u,u.maxWidth,c)}return null};Yf.filterProps=["maxWidth"];const b1=Zt({prop:"minWidth",transform:ze}),S1=Zt({prop:"height",transform:ze}),T1=Zt({prop:"maxHeight",transform:ze}),A1=Zt({prop:"minHeight",transform:ze});Zt({prop:"size",cssProperty:"width",transform:ze});Zt({prop:"size",cssProperty:"height",transform:ze});const E1=Zt({prop:"boxSizing"});Qi(p1,Yf,b1,S1,T1,A1,E1);const ki={border:{themeKey:"borders",transform:je},borderTop:{themeKey:"borders",transform:je},borderRight:{themeKey:"borders",transform:je},borderBottom:{themeKey:"borders",transform:je},borderLeft:{themeKey:"borders",transform:je},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:je},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Li},color:{themeKey:"palette",transform:Pa},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Pa},backgroundColor:{themeKey:"palette",transform:Pa},p:{style:Xt},pt:{style:Xt},pr:{style:Xt},pb:{style:Xt},pl:{style:Xt},px:{style:Xt},py:{style:Xt},padding:{style:Xt},paddingTop:{style:Xt},paddingRight:{style:Xt},paddingBottom:{style:Xt},paddingLeft:{style:Xt},paddingX:{style:Xt},paddingY:{style:Xt},paddingInline:{style:Xt},paddingInlineStart:{style:Xt},paddingInlineEnd:{style:Xt},paddingBlock:{style:Xt},paddingBlockStart:{style:Xt},paddingBlockEnd:{style:Xt},m:{style:wt},mt:{style:wt},mr:{style:wt},mb:{style:wt},ml:{style:wt},mx:{style:wt},my:{style:wt},margin:{style:wt},marginTop:{style:wt},marginRight:{style:wt},marginBottom:{style:wt},marginLeft:{style:wt},marginX:{style:wt},marginY:{style:wt},marginInline:{style:wt},marginInlineStart:{style:wt},marginInlineEnd:{style:wt},marginBlock:{style:wt},marginBlockStart:{style:wt},marginBlockEnd:{style:wt},displayPrint:{cssProperty:!1,transform:u=>({"@media print":{display:u}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Vi},rowGap:{style:Ki},columnGap:{style:Zi},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:ze},maxWidth:{style:Yf},minWidth:{transform:ze},height:{transform:ze},maxHeight:{transform:ze},minHeight:{transform:ze},boxSizing:{},font:{themeKey:"font"},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}};function O1(...u){const c=u.reduce((r,s)=>r.concat(Object.keys(s)),[]),f=new Set(c);return u.every(r=>f.size===Object.keys(r).length)}function _1(u,c){return typeof u=="function"?u(c):u}function z1(){function u(f,r,s,h){const v={[f]:r,theme:s},S=h[f];if(!S)return{[f]:r};const{cssProperty:A=f,themeKey:g,transform:x,style:R}=S;if(r==null)return null;if(g==="typography"&&r==="inherit")return{[f]:r};const N=Xi(s,g)||{};return R?R(v):gl(v,r,Y=>{let z=ji(N,x,Y);return Y===z&&typeof Y=="string"&&(z=ji(N,x,`${f}${Y==="default"?"":tn(Y)}`,Y)),A===!1?z:{[A]:z}})}function c(f){const{sx:r,theme:s={}}=f||{};if(!r)return null;const h=s.unstable_sxConfig??ki;function v(S){let A=S;if(typeof S=="function")A=S(s);else if(typeof S!="object")return S;if(!A)return null;const g=wg(s.breakpoints),x=Object.keys(g);let R=g;return Object.keys(A).forEach(N=>{const w=_1(A[N],s);if(w!=null)if(typeof w=="object")if(h[N])R=ru(R,u(N,w,s,h));else{const Y=gl({theme:s},w,z=>({[N]:z}));O1(Y,w)?R[N]=c({sx:w,theme:s}):R=ru(R,Y)}else R=ru(R,u(N,w,s,h))}),Hg(s,Xg(x,R))}return Array.isArray(r)?r.map(v):v(r)}return c}const en=z1();en.filterProps=["sx"];function _f(){return _f=Object.assign?Object.assign.bind():function(u){for(var c=1;c<arguments.length;c++){var f=arguments[c];for(var r in f)({}).hasOwnProperty.call(f,r)&&(u[r]=f[r])}return u},_f.apply(null,arguments)}function C1(u){if(u.sheet)return u.sheet;for(var c=0;c<document.styleSheets.length;c++)if(document.styleSheets[c].ownerNode===u)return document.styleSheets[c]}function x1(u){var c=document.createElement("style");return c.setAttribute("data-emotion",u.key),u.nonce!==void 0&&c.setAttribute("nonce",u.nonce),c.appendChild(document.createTextNode("")),c.setAttribute("data-s",""),c}var R1=function(){function u(f){var r=this;this._insertTag=function(s){var h;r.tags.length===0?r.insertionPoint?h=r.insertionPoint.nextSibling:r.prepend?h=r.container.firstChild:h=r.before:h=r.tags[r.tags.length-1].nextSibling,r.container.insertBefore(s,h),r.tags.push(s)},this.isSpeedy=f.speedy===void 0?!0:f.speedy,this.tags=[],this.ctr=0,this.nonce=f.nonce,this.key=f.key,this.container=f.container,this.prepend=f.prepend,this.insertionPoint=f.insertionPoint,this.before=null}var c=u.prototype;return c.hydrate=function(r){r.forEach(this._insertTag)},c.insert=function(r){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(x1(this));var s=this.tags[this.tags.length-1];if(this.isSpeedy){var h=C1(s);try{h.insertRule(r,h.cssRules.length)}catch{}}else s.appendChild(document.createTextNode(r));this.ctr++},c.flush=function(){this.tags.forEach(function(r){var s;return(s=r.parentNode)==null?void 0:s.removeChild(r)}),this.tags=[],this.ctr=0},u}(),re="-ms-",Gi="-moz-",gt="-webkit-",Oh="comm",jf="rule",Gf="decl",M1="@import",_h="@keyframes",D1="@layer",U1=Math.abs,$i=String.fromCharCode,B1=Object.assign;function N1(u,c){return ne(u,0)^45?(((c<<2^ne(u,0))<<2^ne(u,1))<<2^ne(u,2))<<2^ne(u,3):0}function zh(u){return u.trim()}function H1(u,c){return(u=c.exec(u))?u[0]:u}function vt(u,c,f){return u.replace(c,f)}function zf(u,c){return u.indexOf(c)}function ne(u,c){return u.charCodeAt(c)|0}function fu(u,c,f){return u.slice(c,f)}function We(u){return u.length}function wf(u){return u.length}function Di(u,c){return c.push(u),u}function q1(u,c){return u.map(c).join("")}var Ji=1,ln=1,Ch=0,ye=0,$t=0,an="";function Wi(u,c,f,r,s,h,v){return{value:u,root:c,parent:f,type:r,props:s,children:h,line:Ji,column:ln,length:v,return:""}}function nu(u,c){return B1(Wi("",null,null,"",null,null,0),u,{length:-u.length},c)}function Y1(){return $t}function j1(){return $t=ye>0?ne(an,--ye):0,ln--,$t===10&&(ln=1,Ji--),$t}function xe(){return $t=ye<Ch?ne(an,ye++):0,ln++,$t===10&&(ln=1,Ji++),$t}function Pe(){return ne(an,ye)}function Ni(){return ye}function yu(u,c){return fu(an,u,c)}function ou(u){switch(u){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function xh(u){return Ji=ln=1,Ch=We(an=u),ye=0,[]}function Rh(u){return an="",u}function Hi(u){return zh(yu(ye-1,Cf(u===91?u+2:u===40?u+1:u)))}function G1(u){for(;($t=Pe())&&$t<33;)xe();return ou(u)>2||ou($t)>3?"":" "}function w1(u,c){for(;--c&&xe()&&!($t<48||$t>102||$t>57&&$t<65||$t>70&&$t<97););return yu(u,Ni()+(c<6&&Pe()==32&&xe()==32))}function Cf(u){for(;xe();)switch($t){case u:return ye;case 34:case 39:u!==34&&u!==39&&Cf($t);break;case 40:u===41&&Cf(u);break;case 92:xe();break}return ye}function X1(u,c){for(;xe()&&u+$t!==57;)if(u+$t===84&&Pe()===47)break;return"/*"+yu(c,ye-1)+"*"+$i(u===47?u:xe())}function Q1(u){for(;!ou(Pe());)xe();return yu(u,ye)}function L1(u){return Rh(qi("",null,null,null,[""],u=xh(u),0,[0],u))}function qi(u,c,f,r,s,h,v,S,A){for(var g=0,x=0,R=v,N=0,w=0,Y=0,z=1,L=1,K=1,nt=0,$="",k=s,j=h,I=r,J=$;L;)switch(Y=nt,nt=xe()){case 40:if(Y!=108&&ne(J,R-1)==58){zf(J+=vt(Hi(nt),"&","&\f"),"&\f")!=-1&&(K=-1);break}case 34:case 39:case 91:J+=Hi(nt);break;case 9:case 10:case 13:case 32:J+=G1(Y);break;case 92:J+=w1(Ni()-1,7);continue;case 47:switch(Pe()){case 42:case 47:Di(V1(X1(xe(),Ni()),c,f),A);break;default:J+="/"}break;case 123*z:S[g++]=We(J)*K;case 125*z:case 59:case 0:switch(nt){case 0:case 125:L=0;case 59+x:K==-1&&(J=vt(J,/\f/g,"")),w>0&&We(J)-R&&Di(w>32?eh(J+";",r,f,R-1):eh(vt(J," ","")+";",r,f,R-2),A);break;case 59:J+=";";default:if(Di(I=th(J,c,f,g,x,s,S,$,k=[],j=[],R),h),nt===123)if(x===0)qi(J,c,I,I,k,h,R,S,j);else switch(N===99&&ne(J,3)===110?100:N){case 100:case 108:case 109:case 115:qi(u,I,I,r&&Di(th(u,I,I,0,0,s,S,$,s,k=[],R),j),s,j,R,S,r?k:j);break;default:qi(J,I,I,I,[""],j,0,S,j)}}g=x=w=0,z=K=1,$=J="",R=v;break;case 58:R=1+We(J),w=Y;default:if(z<1){if(nt==123)--z;else if(nt==125&&z++==0&&j1()==125)continue}switch(J+=$i(nt),nt*z){case 38:K=x>0?1:(J+="\f",-1);break;case 44:S[g++]=(We(J)-1)*K,K=1;break;case 64:Pe()===45&&(J+=Hi(xe())),N=Pe(),x=R=We($=J+=Q1(Ni())),nt++;break;case 45:Y===45&&We(J)==2&&(z=0)}}return h}function th(u,c,f,r,s,h,v,S,A,g,x){for(var R=s-1,N=s===0?h:[""],w=wf(N),Y=0,z=0,L=0;Y<r;++Y)for(var K=0,nt=fu(u,R+1,R=U1(z=v[Y])),$=u;K<w;++K)($=zh(z>0?N[K]+" "+nt:vt(nt,/&\f/g,N[K])))&&(A[L++]=$);return Wi(u,c,f,s===0?jf:S,A,g,x)}function V1(u,c,f){return Wi(u,c,f,Oh,$i(Y1()),fu(u,2,-2),0)}function eh(u,c,f,r){return Wi(u,c,f,Gf,fu(u,0,r),fu(u,r+1,-1),r)}function Ia(u,c){for(var f="",r=wf(u),s=0;s<r;s++)f+=c(u[s],s,u,c)||"";return f}function Z1(u,c,f,r){switch(u.type){case D1:if(u.children.length)break;case M1:case Gf:return u.return=u.return||u.value;case Oh:return"";case _h:return u.return=u.value+"{"+Ia(u.children,r)+"}";case jf:u.value=u.props.join(",")}return We(f=Ia(u.children,r))?u.return=u.value+"{"+f+"}":""}function K1(u){var c=wf(u);return function(f,r,s,h){for(var v="",S=0;S<c;S++)v+=u[S](f,r,s,h)||"";return v}}function k1(u){return function(c){c.root||(c=c.return)&&u(c)}}function Mh(u){var c=Object.create(null);return function(f){return c[f]===void 0&&(c[f]=u(f)),c[f]}}var $1=function(c,f,r){for(var s=0,h=0;s=h,h=Pe(),s===38&&h===12&&(f[r]=1),!ou(h);)xe();return yu(c,ye)},J1=function(c,f){var r=-1,s=44;do switch(ou(s)){case 0:s===38&&Pe()===12&&(f[r]=1),c[r]+=$1(ye-1,f,r);break;case 2:c[r]+=Hi(s);break;case 4:if(s===44){c[++r]=Pe()===58?"&\f":"",f[r]=c[r].length;break}default:c[r]+=$i(s)}while(s=xe());return c},W1=function(c,f){return Rh(J1(xh(c),f))},lh=new WeakMap,F1=function(c){if(!(c.type!=="rule"||!c.parent||c.length<1)){for(var f=c.value,r=c.parent,s=c.column===r.column&&c.line===r.line;r.type!=="rule";)if(r=r.parent,!r)return;if(!(c.props.length===1&&f.charCodeAt(0)!==58&&!lh.get(r))&&!s){lh.set(c,!0);for(var h=[],v=W1(f,h),S=r.props,A=0,g=0;A<v.length;A++)for(var x=0;x<S.length;x++,g++)c.props[g]=h[A]?v[A].replace(/&\f/g,S[x]):S[x]+" "+v[A]}}},P1=function(c){if(c.type==="decl"){var f=c.value;f.charCodeAt(0)===108&&f.charCodeAt(2)===98&&(c.return="",c.value="")}};function Dh(u,c){switch(N1(u,c)){case 5103:return gt+"print-"+u+u;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return gt+u+u;case 5349:case 4246:case 4810:case 6968:case 2756:return gt+u+Gi+u+re+u+u;case 6828:case 4268:return gt+u+re+u+u;case 6165:return gt+u+re+"flex-"+u+u;case 5187:return gt+u+vt(u,/(\w+).+(:[^]+)/,gt+"box-$1$2"+re+"flex-$1$2")+u;case 5443:return gt+u+re+"flex-item-"+vt(u,/flex-|-self/,"")+u;case 4675:return gt+u+re+"flex-line-pack"+vt(u,/align-content|flex-|-self/,"")+u;case 5548:return gt+u+re+vt(u,"shrink","negative")+u;case 5292:return gt+u+re+vt(u,"basis","preferred-size")+u;case 6060:return gt+"box-"+vt(u,"-grow","")+gt+u+re+vt(u,"grow","positive")+u;case 4554:return gt+vt(u,/([^-])(transform)/g,"$1"+gt+"$2")+u;case 6187:return vt(vt(vt(u,/(zoom-|grab)/,gt+"$1"),/(image-set)/,gt+"$1"),u,"")+u;case 5495:case 3959:return vt(u,/(image-set\([^]*)/,gt+"$1$`$1");case 4968:return vt(vt(u,/(.+:)(flex-)?(.*)/,gt+"box-pack:$3"+re+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+gt+u+u;case 4095:case 3583:case 4068:case 2532:return vt(u,/(.+)-inline(.+)/,gt+"$1$2")+u;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(We(u)-1-c>6)switch(ne(u,c+1)){case 109:if(ne(u,c+4)!==45)break;case 102:return vt(u,/(.+:)(.+)-([^]+)/,"$1"+gt+"$2-$3$1"+Gi+(ne(u,c+3)==108?"$3":"$2-$3"))+u;case 115:return~zf(u,"stretch")?Dh(vt(u,"stretch","fill-available"),c)+u:u}break;case 4949:if(ne(u,c+1)!==115)break;case 6444:switch(ne(u,We(u)-3-(~zf(u,"!important")&&10))){case 107:return vt(u,":",":"+gt)+u;case 101:return vt(u,/(.+:)([^;!]+)(;|!.+)?/,"$1"+gt+(ne(u,14)===45?"inline-":"")+"box$3$1"+gt+"$2$3$1"+re+"$2box$3")+u}break;case 5936:switch(ne(u,c+11)){case 114:return gt+u+re+vt(u,/[svh]\w+-[tblr]{2}/,"tb")+u;case 108:return gt+u+re+vt(u,/[svh]\w+-[tblr]{2}/,"tb-rl")+u;case 45:return gt+u+re+vt(u,/[svh]\w+-[tblr]{2}/,"lr")+u}return gt+u+re+u+u}return u}var I1=function(c,f,r,s){if(c.length>-1&&!c.return)switch(c.type){case Gf:c.return=Dh(c.value,c.length);break;case _h:return Ia([nu(c,{value:vt(c.value,"@","@"+gt)})],s);case jf:if(c.length)return q1(c.props,function(h){switch(H1(h,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Ia([nu(c,{props:[vt(h,/:(read-\w+)/,":"+Gi+"$1")]})],s);case"::placeholder":return Ia([nu(c,{props:[vt(h,/:(plac\w+)/,":"+gt+"input-$1")]}),nu(c,{props:[vt(h,/:(plac\w+)/,":"+Gi+"$1")]}),nu(c,{props:[vt(h,/:(plac\w+)/,re+"input-$1")]})],s)}return""})}},tv=[I1],ev=function(c){var f=c.key;if(f==="css"){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,function(z){var L=z.getAttribute("data-emotion");L.indexOf(" ")!==-1&&(document.head.appendChild(z),z.setAttribute("data-s",""))})}var s=c.stylisPlugins||tv,h={},v,S=[];v=c.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+f+' "]'),function(z){for(var L=z.getAttribute("data-emotion").split(" "),K=1;K<L.length;K++)h[L[K]]=!0;S.push(z)});var A,g=[F1,P1];{var x,R=[Z1,k1(function(z){x.insert(z)})],N=K1(g.concat(s,R)),w=function(L){return Ia(L1(L),N)};A=function(L,K,nt,$){x=nt,w(L?L+"{"+K.styles+"}":K.styles),$&&(Y.inserted[K.name]=!0)}}var Y={key:f,sheet:new R1({key:f,container:v,nonce:c.nonce,speedy:c.speedy,prepend:c.prepend,insertionPoint:c.insertionPoint}),nonce:c.nonce,inserted:h,registered:{},insert:A};return Y.sheet.hydrate(S),Y},lv=!0;function av(u,c,f){var r="";return f.split(" ").forEach(function(s){u[s]!==void 0?c.push(u[s]+";"):s&&(r+=s+" ")}),r}var Uh=function(c,f,r){var s=c.key+"-"+f.name;(r===!1||lv===!1)&&c.registered[s]===void 0&&(c.registered[s]=f.styles)},nv=function(c,f,r){Uh(c,f,r);var s=c.key+"-"+f.name;if(c.inserted[f.name]===void 0){var h=f;do c.insert(f===h?"."+s:"",h,c.sheet,!0),h=h.next;while(h!==void 0)}};function uv(u){for(var c=0,f,r=0,s=u.length;s>=4;++r,s-=4)f=u.charCodeAt(r)&255|(u.charCodeAt(++r)&255)<<8|(u.charCodeAt(++r)&255)<<16|(u.charCodeAt(++r)&255)<<24,f=(f&65535)*1540483477+((f>>>16)*59797<<16),f^=f>>>24,c=(f&65535)*1540483477+((f>>>16)*59797<<16)^(c&65535)*1540483477+((c>>>16)*59797<<16);switch(s){case 3:c^=(u.charCodeAt(r+2)&255)<<16;case 2:c^=(u.charCodeAt(r+1)&255)<<8;case 1:c^=u.charCodeAt(r)&255,c=(c&65535)*1540483477+((c>>>16)*59797<<16)}return c^=c>>>13,c=(c&65535)*1540483477+((c>>>16)*59797<<16),((c^c>>>15)>>>0).toString(36)}var iv={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},cv=/[A-Z]|^ms/g,rv=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Bh=function(c){return c.charCodeAt(1)===45},ah=function(c){return c!=null&&typeof c!="boolean"},Tf=Mh(function(u){return Bh(u)?u:u.replace(cv,"-$&").toLowerCase()}),nh=function(c,f){switch(c){case"animation":case"animationName":if(typeof f=="string")return f.replace(rv,function(r,s,h){return Fe={name:s,styles:h,next:Fe},s})}return iv[c]!==1&&!Bh(c)&&typeof f=="number"&&f!==0?f+"px":f};function su(u,c,f){if(f==null)return"";var r=f;if(r.__emotion_styles!==void 0)return r;switch(typeof f){case"boolean":return"";case"object":{var s=f;if(s.anim===1)return Fe={name:s.name,styles:s.styles,next:Fe},s.name;var h=f;if(h.styles!==void 0){var v=h.next;if(v!==void 0)for(;v!==void 0;)Fe={name:v.name,styles:v.styles,next:Fe},v=v.next;var S=h.styles+";";return S}return fv(u,c,f)}case"function":{if(u!==void 0){var A=Fe,g=f(u);return Fe=A,su(u,c,g)}break}}var x=f;if(c==null)return x;var R=c[x];return R!==void 0?R:x}function fv(u,c,f){var r="";if(Array.isArray(f))for(var s=0;s<f.length;s++)r+=su(u,c,f[s])+";";else for(var h in f){var v=f[h];if(typeof v!="object"){var S=v;c!=null&&c[S]!==void 0?r+=h+"{"+c[S]+"}":ah(S)&&(r+=Tf(h)+":"+nh(h,S)+";")}else if(Array.isArray(v)&&typeof v[0]=="string"&&(c==null||c[v[0]]===void 0))for(var A=0;A<v.length;A++)ah(v[A])&&(r+=Tf(h)+":"+nh(h,v[A])+";");else{var g=su(u,c,v);switch(h){case"animation":case"animationName":{r+=Tf(h)+":"+g+";";break}default:r+=h+"{"+g+"}"}}}return r}var uh=/label:\s*([^\s;{]+)\s*(;|$)/g,Fe;function Nh(u,c,f){if(u.length===1&&typeof u[0]=="object"&&u[0]!==null&&u[0].styles!==void 0)return u[0];var r=!0,s="";Fe=void 0;var h=u[0];if(h==null||h.raw===void 0)r=!1,s+=su(f,c,h);else{var v=h;s+=v[0]}for(var S=1;S<u.length;S++)if(s+=su(f,c,u[S]),r){var A=h;s+=A[S]}uh.lastIndex=0;for(var g="",x;(x=uh.exec(s))!==null;)g+="-"+x[1];var R=uv(s)+g;return{name:R,styles:s,next:Fe}}var ov=function(c){return c()},sv=Q0.useInsertionEffect?Q0.useInsertionEffect:!1,dv=sv||ov,Hh=Ht.createContext(typeof HTMLElement<"u"?ev({key:"css"}):null);Hh.Provider;var hv=function(c){return Ht.forwardRef(function(f,r){var s=Ht.useContext(Hh);return c(f,s,r)})},mv=Ht.createContext({}),yv=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,gv=Mh(function(u){return yv.test(u)||u.charCodeAt(0)===111&&u.charCodeAt(1)===110&&u.charCodeAt(2)<91}),vv=gv,pv=function(c){return c!=="theme"},ih=function(c){return typeof c=="string"&&c.charCodeAt(0)>96?vv:pv},ch=function(c,f,r){var s;if(f){var h=f.shouldForwardProp;s=c.__emotion_forwardProp&&h?function(v){return c.__emotion_forwardProp(v)&&h(v)}:h}return typeof s!="function"&&r&&(s=c.__emotion_forwardProp),s},bv=function(c){var f=c.cache,r=c.serialized,s=c.isStringTag;return Uh(f,r,s),dv(function(){return nv(f,r,s)}),null},Sv=function u(c,f){var r=c.__emotion_real===c,s=r&&c.__emotion_base||c,h,v;f!==void 0&&(h=f.label,v=f.target);var S=ch(c,f,r),A=S||ih(s),g=!A("as");return function(){var x=arguments,R=r&&c.__emotion_styles!==void 0?c.__emotion_styles.slice(0):[];if(h!==void 0&&R.push("label:"+h+";"),x[0]==null||x[0].raw===void 0)R.push.apply(R,x);else{var N=x[0];R.push(N[0]);for(var w=x.length,Y=1;Y<w;Y++)R.push(x[Y],N[Y])}var z=hv(function(L,K,nt){var $=g&&L.as||s,k="",j=[],I=L;if(L.theme==null){I={};for(var J in L)I[J]=L[J];I.theme=Ht.useContext(mv)}typeof L.className=="string"?k=av(K.registered,j,L.className):L.className!=null&&(k=L.className+" ");var pt=Nh(R.concat(j),K.registered,I);k+=K.key+"-"+pt.name,v!==void 0&&(k+=" "+v);var Dt=g&&S===void 0?ih($):A,m={};for(var Q in L)g&&Q==="as"||Dt(Q)&&(m[Q]=L[Q]);return m.className=k,nt&&(m.ref=nt),Ht.createElement(Ht.Fragment,null,Ht.createElement(bv,{cache:K,serialized:pt,isStringTag:typeof $=="string"}),Ht.createElement($,m))});return z.displayName=h!==void 0?h:"Styled("+(typeof s=="string"?s:s.displayName||s.name||"Component")+")",z.defaultProps=c.defaultProps,z.__emotion_real=z,z.__emotion_base=s,z.__emotion_styles=R,z.__emotion_forwardProp=S,Object.defineProperty(z,"toString",{value:function(){return"."+v}}),z.withComponent=function(L,K){var nt=u(L,_f({},f,K,{shouldForwardProp:ch(z,K,!0)}));return nt.apply(void 0,R)},z}},Tv=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],xf=Sv.bind(null);Tv.forEach(function(u){xf[u]=xf(u)});function Av(u,c){return xf(u,c)}function Ev(u,c){Array.isArray(u.__emotion_styles)&&(u.__emotion_styles=c(u.__emotion_styles))}const rh=[];function fh(u){return rh[0]=u,Nh(rh)}const Ov=u=>{const c=Object.keys(u).map(f=>({key:f,val:u[f]}))||[];return c.sort((f,r)=>f.val-r.val),c.reduce((f,r)=>({...f,[r.key]:r.val}),{})};function _v(u){const{values:c={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:f="px",step:r=5,...s}=u,h=Ov(c),v=Object.keys(h);function S(N){return`@media (min-width:${typeof c[N]=="number"?c[N]:N}${f})`}function A(N){return`@media (max-width:${(typeof c[N]=="number"?c[N]:N)-r/100}${f})`}function g(N,w){const Y=v.indexOf(w);return`@media (min-width:${typeof c[N]=="number"?c[N]:N}${f}) and (max-width:${(Y!==-1&&typeof c[v[Y]]=="number"?c[v[Y]]:w)-r/100}${f})`}function x(N){return v.indexOf(N)+1<v.length?g(N,v[v.indexOf(N)+1]):S(N)}function R(N){const w=v.indexOf(N);return w===0?S(v[1]):w===v.length-1?A(v[w]):g(N,v[v.indexOf(N)+1]).replace("@media","@media not all and")}return{keys:v,values:h,up:S,down:A,between:g,only:x,not:R,unit:f,...s}}const zv={borderRadius:4};function qh(u=8,c=qf({spacing:u})){if(u.mui)return u;const f=(...r)=>(r.length===0?[1]:r).map(h=>{const v=c(h);return typeof v=="number"?`${v}px`:v}).join(" ");return f.mui=!0,f}function Cv(u,c){var r;const f=this;if(f.vars){if(!((r=f.colorSchemes)!=null&&r[u])||typeof f.getColorSchemeSelector!="function")return{};let s=f.getColorSchemeSelector(u);return s==="&"?c:((s.includes("data-")||s.includes("."))&&(s=`*:where(${s.replace(/\s*&$/,"")}) &`),{[s]:c})}return f.palette.mode===u?c:{}}function Yh(u={},...c){const{breakpoints:f={},palette:r={},spacing:s,shape:h={},...v}=u,S=_v(f),A=qh(s);let g=Ce({breakpoints:S,direction:"ltr",components:{},palette:{mode:"light",...r},spacing:A,shape:{...zv,...h}},v);return g=jg(g),g.applyStyles=Cv,g=c.reduce((x,R)=>Ce(x,R),g),g.unstable_sxConfig={...ki,...v==null?void 0:v.unstable_sxConfig},g.unstable_sx=function(R){return en({sx:R,theme:this})},g}const xv={active:"active",checked:"checked",completed:"completed",disabled:"disabled",error:"error",expanded:"expanded",focused:"focused",focusVisible:"focusVisible",open:"open",readOnly:"readOnly",required:"required",selected:"selected"};function jh(u,c,f="Mui"){const r=xv[c];return r?`${f}-${r}`:`${Mg.generate(u)}-${c}`}function Rv(u,c,f="Mui"){const r={};return c.forEach(s=>{r[s]=jh(u,s,f)}),r}function Gh(u){const{variants:c,...f}=u,r={variants:c,style:fh(f),isProcessed:!0};return r.style===f||c&&c.forEach(s=>{typeof s.style!="function"&&(s.style=fh(s.style))}),r}const Mv=Yh();function Af(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}function Dv(u){return u?(c,f)=>f[u]:null}function Uv(u,c,f){u.theme=Hv(u.theme)?f:u.theme[c]||u.theme}function Yi(u,c){const f=typeof c=="function"?c(u):c;if(Array.isArray(f))return f.flatMap(r=>Yi(u,r));if(Array.isArray(f==null?void 0:f.variants)){let r;if(f.isProcessed)r=f.style;else{const{variants:s,...h}=f;r=h}return wh(u,f.variants,[r])}return f!=null&&f.isProcessed?f.style:f}function wh(u,c,f=[]){var s;let r;t:for(let h=0;h<c.length;h+=1){const v=c[h];if(typeof v.props=="function"){if(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),!v.props(r))continue}else for(const S in v.props)if(u[S]!==v.props[S]&&((s=u.ownerState)==null?void 0:s[S])!==v.props[S])continue t;typeof v.style=="function"?(r??(r={...u,...u.ownerState,ownerState:u.ownerState}),f.push(v.style(r))):f.push(v.style)}return f}function Bv(u={}){const{themeId:c,defaultTheme:f=Mv,rootShouldForwardProp:r=Af,slotShouldForwardProp:s=Af}=u;function h(S){Uv(S,c,f)}return(S,A={})=>{Ev(S,j=>j.filter(I=>I!==en));const{name:g,slot:x,skipVariantsResolver:R,skipSx:N,overridesResolver:w=Dv(Yv(x)),...Y}=A,z=R!==void 0?R:x&&x!=="Root"&&x!=="root"||!1,L=N||!1;let K=Af;x==="Root"||x==="root"?K=r:x?K=s:qv(S)&&(K=void 0);const nt=Av(S,{shouldForwardProp:K,label:Nv(),...Y}),$=j=>{if(j.__emotion_real===j)return j;if(typeof j=="function")return function(J){return Yi(J,j)};if(yl(j)){const I=Gh(j);return I.variants?function(pt){return Yi(pt,I)}:I.style}return j},k=(...j)=>{const I=[],J=j.map($),pt=[];if(I.push(h),g&&w&&pt.push(function(F){var M,G;const qt=(G=(M=F.theme.components)==null?void 0:M[g])==null?void 0:G.styleOverrides;if(!qt)return null;const _t={};for(const P in qt)_t[P]=Yi(F,qt[P]);return w(F,_t)}),g&&!z&&pt.push(function(F){var _t,M;const st=F.theme,qt=(M=(_t=st==null?void 0:st.components)==null?void 0:_t[g])==null?void 0:M.variants;return qt?wh(F,qt):null}),L||pt.push(en),Array.isArray(J[0])){const Q=J.shift(),F=new Array(I.length).fill(""),st=new Array(pt.length).fill("");let qt;qt=[...F,...Q,...st],qt.raw=[...F,...Q.raw,...st],I.unshift(qt)}const Dt=[...I,...J,...pt],m=nt(...Dt);return S.muiName&&(m.muiName=S.muiName),m};return nt.withConfig&&(k.withConfig=nt.withConfig),k}}function Nv(u,c){return void 0}function Hv(u){for(const c in u)return!1;return!0}function qv(u){return typeof u=="string"&&u.charCodeAt(0)>96}function Yv(u){return u&&u.charAt(0).toLowerCase()+u.slice(1)}function Rf(u,c){const f={...c};for(const r in u)if(Object.prototype.hasOwnProperty.call(u,r)){const s=r;if(s==="components"||s==="slots")f[s]={...u[s],...f[s]};else if(s==="componentsProps"||s==="slotProps"){const h=u[s],v=c[s];if(!v)f[s]=h||{};else if(!h)f[s]=v;else{f[s]={...v};for(const S in h)if(Object.prototype.hasOwnProperty.call(h,S)){const A=S;f[s][A]=Rf(h[A],v[A])}}}else f[s]===void 0&&(f[s]=u[s])}return f}function jv(u,c=Number.MIN_SAFE_INTEGER,f=Number.MAX_SAFE_INTEGER){return Math.max(c,Math.min(u,f))}function Xf(u,c=0,f=1){return jv(u,c,f)}function Gv(u){u=u.slice(1);const c=new RegExp(`.{1,${u.length>=6?2:1}}`,"g");let f=u.match(c);return f&&f[0].length===1&&(f=f.map(r=>r+r)),f?`rgb${f.length===4?"a":""}(${f.map((r,s)=>s<3?parseInt(r,16):Math.round(parseInt(r,16)/255*1e3)/1e3).join(", ")})`:""}function Ql(u){if(u.type)return u;if(u.charAt(0)==="#")return Ql(Gv(u));const c=u.indexOf("("),f=u.substring(0,c);if(!["rgb","rgba","hsl","hsla","color"].includes(f))throw new Error(ca(9,u));let r=u.substring(c+1,u.length-1),s;if(f==="color"){if(r=r.split(" "),s=r.shift(),r.length===4&&r[3].charAt(0)==="/"&&(r[3]=r[3].slice(1)),!["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].includes(s))throw new Error(ca(10,s))}else r=r.split(",");return r=r.map(h=>parseFloat(h)),{type:f,values:r,colorSpace:s}}const wv=u=>{const c=Ql(u);return c.values.slice(0,3).map((f,r)=>c.type.includes("hsl")&&r!==0?`${f}%`:f).join(" ")},iu=(u,c)=>{try{return wv(u)}catch{return u}};function Fi(u){const{type:c,colorSpace:f}=u;let{values:r}=u;return c.includes("rgb")?r=r.map((s,h)=>h<3?parseInt(s,10):s):c.includes("hsl")&&(r[1]=`${r[1]}%`,r[2]=`${r[2]}%`),c.includes("color")?r=`${f} ${r.join(" ")}`:r=`${r.join(", ")}`,`${c}(${r})`}function Xh(u){u=Ql(u);const{values:c}=u,f=c[0],r=c[1]/100,s=c[2]/100,h=r*Math.min(s,1-s),v=(g,x=(g+f/30)%12)=>s-h*Math.max(Math.min(x-3,9-x,1),-1);let S="rgb";const A=[Math.round(v(0)*255),Math.round(v(8)*255),Math.round(v(4)*255)];return u.type==="hsla"&&(S+="a",A.push(c[3])),Fi({type:S,values:A})}function Mf(u){u=Ql(u);let c=u.type==="hsl"||u.type==="hsla"?Ql(Xh(u)).values:u.values;return c=c.map(f=>(u.type!=="color"&&(f/=255),f<=.03928?f/12.92:((f+.055)/1.055)**2.4)),Number((.2126*c[0]+.7152*c[1]+.0722*c[2]).toFixed(3))}function Xv(u,c){const f=Mf(u),r=Mf(c);return(Math.max(f,r)+.05)/(Math.min(f,r)+.05)}function Qv(u,c){return u=Ql(u),c=Xf(c),(u.type==="rgb"||u.type==="hsl")&&(u.type+="a"),u.type==="color"?u.values[3]=`/${c}`:u.values[3]=c,Fi(u)}function Ui(u,c,f){try{return Qv(u,c)}catch{return u}}function Qf(u,c){if(u=Ql(u),c=Xf(c),u.type.includes("hsl"))u.values[2]*=1-c;else if(u.type.includes("rgb")||u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]*=1-c;return Fi(u)}function xt(u,c,f){try{return Qf(u,c)}catch{return u}}function Lf(u,c){if(u=Ql(u),c=Xf(c),u.type.includes("hsl"))u.values[2]+=(100-u.values[2])*c;else if(u.type.includes("rgb"))for(let f=0;f<3;f+=1)u.values[f]+=(255-u.values[f])*c;else if(u.type.includes("color"))for(let f=0;f<3;f+=1)u.values[f]+=(1-u.values[f])*c;return Fi(u)}function Rt(u,c,f){try{return Lf(u,c)}catch{return u}}function Lv(u,c=.15){return Mf(u)>.5?Qf(u,c):Lf(u,c)}function Bi(u,c,f){try{return Lv(u,c)}catch{return u}}const Vv=Ht.createContext(void 0);function Zv(u){const{theme:c,name:f,props:r}=u;if(!c||!c.components||!c.components[f])return r;const s=c.components[f];return s.defaultProps?Rf(s.defaultProps,r):!s.styleOverrides&&!s.variants?Rf(s,r):r}function Kv({props:u,name:c}){const f=Ht.useContext(Vv);return Zv({props:u,name:c,theme:{components:f}})}const oh={theme:void 0};function kv(u){let c,f;return function(s){let h=c;return(h===void 0||s.theme!==f)&&(oh.theme=s.theme,h=Gh(u(oh)),c=h,f=s.theme),h}}function $v(u=""){function c(...r){if(!r.length)return"";const s=r[0];return typeof s=="string"&&!s.match(/(#|\(|\)|(-?(\d*\.)?\d+)(px|em|%|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc))|^(-?(\d*\.)?\d+)$|(\d+ \d+ \d+)/)?`, var(--${u?`${u}-`:""}${s}${c(...r.slice(1))})`:`, ${s}`}return(r,...s)=>`var(--${u?`${u}-`:""}${r}${c(...s)})`}const sh=(u,c,f,r=[])=>{let s=u;c.forEach((h,v)=>{v===c.length-1?Array.isArray(s)?s[Number(h)]=f:s&&typeof s=="object"&&(s[h]=f):s&&typeof s=="object"&&(s[h]||(s[h]=r.includes(h)?[]:{}),s=s[h])})},Jv=(u,c,f)=>{function r(s,h=[],v=[]){Object.entries(s).forEach(([S,A])=>{(!f||f&&!f([...h,S]))&&A!=null&&(typeof A=="object"&&Object.keys(A).length>0?r(A,[...h,S],Array.isArray(A)?[...v,S]:v):c([...h,S],A,v))})}r(u)},Wv=(u,c)=>typeof c=="number"?["lineHeight","fontWeight","opacity","zIndex"].some(r=>u.includes(r))||u[u.length-1].toLowerCase().includes("opacity")?c:`${c}px`:c;function Ef(u,c){const{prefix:f,shouldSkipGeneratingVar:r}=c||{},s={},h={},v={};return Jv(u,(S,A,g)=>{if((typeof A=="string"||typeof A=="number")&&(!r||!r(S,A))){const x=`--${f?`${f}-`:""}${S.join("-")}`,R=Wv(S,A);Object.assign(s,{[x]:R}),sh(h,S,`var(${x})`,g),sh(v,S,`var(${x}, ${R})`,g)}},S=>S[0]==="vars"),{css:s,vars:h,varsWithDefaults:v}}function Fv(u,c={}){const{getSelector:f=L,disableCssColorScheme:r,colorSchemeSelector:s}=c,{colorSchemes:h={},components:v,defaultColorScheme:S="light",...A}=u,{vars:g,css:x,varsWithDefaults:R}=Ef(A,c);let N=R;const w={},{[S]:Y,...z}=h;if(Object.entries(z||{}).forEach(([$,k])=>{const{vars:j,css:I,varsWithDefaults:J}=Ef(k,c);N=Ce(N,J),w[$]={css:I,vars:j}}),Y){const{css:$,vars:k,varsWithDefaults:j}=Ef(Y,c);N=Ce(N,j),w[S]={css:$,vars:k}}function L($,k){var I,J;let j=s;if(s==="class"&&(j=".%s"),s==="data"&&(j="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(j=`[${s}="%s"]`),$){if(j==="media")return u.defaultColorScheme===$?":root":{[`@media (prefers-color-scheme: ${((J=(I=h[$])==null?void 0:I.palette)==null?void 0:J.mode)||$})`]:{":root":k}};if(j)return u.defaultColorScheme===$?`:root, ${j.replace("%s",String($))}`:j.replace("%s",String($))}return":root"}return{vars:N,generateThemeVars:()=>{let $={...g};return Object.entries(w).forEach(([,{vars:k}])=>{$=Ce($,k)}),$},generateStyleSheets:()=>{var pt,Dt;const $=[],k=u.defaultColorScheme||"light";function j(m,Q){Object.keys(Q).length&&$.push(typeof m=="string"?{[m]:{...Q}}:m)}j(f(void 0,{...x}),x);const{[k]:I,...J}=w;if(I){const{css:m}=I,Q=(Dt=(pt=h[k])==null?void 0:pt.palette)==null?void 0:Dt.mode,F=!r&&Q?{colorScheme:Q,...m}:{...m};j(f(k,{...F}),F)}return Object.entries(J).forEach(([m,{css:Q}])=>{var qt,_t;const F=(_t=(qt=h[m])==null?void 0:qt.palette)==null?void 0:_t.mode,st=!r&&F?{colorScheme:F,...Q}:{...Q};j(f(m,{...st}),st)}),$}}}function Pv(u){return function(f){return u==="media"?`@media (prefers-color-scheme: ${f})`:u?u.startsWith("data-")&&!u.includes("%s")?`[${u}="${f}"] &`:u==="class"?`.${f} &`:u==="data"?`[data-${f}] &`:`${u.replace("%s",f)} &`:"&"}}const du={black:"#000",white:"#fff"},Iv={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},ka={50:"#f3e5f5",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",700:"#7b1fa2"},$a={300:"#e57373",400:"#ef5350",500:"#f44336",700:"#d32f2f",800:"#c62828"},uu={300:"#ffb74d",400:"#ffa726",500:"#ff9800",700:"#f57c00",900:"#e65100"},Ja={50:"#e3f2fd",200:"#90caf9",400:"#42a5f5",700:"#1976d2",800:"#1565c0"},Wa={300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",700:"#0288d1",900:"#01579b"},Fa={300:"#81c784",400:"#66bb6a",500:"#4caf50",700:"#388e3c",800:"#2e7d32",900:"#1b5e20"};function Qh(){return{text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:du.white,default:du.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}}}const tp=Qh();function Lh(){return{text:{primary:du.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:du.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}}}const dh=Lh();function hh(u,c,f,r){const s=r.light||r,h=r.dark||r*1.5;u[c]||(u.hasOwnProperty(f)?u[c]=u[f]:c==="light"?u.light=Lf(u.main,s):c==="dark"&&(u.dark=Qf(u.main,h)))}function ep(u="light"){return u==="dark"?{main:Ja[200],light:Ja[50],dark:Ja[400]}:{main:Ja[700],light:Ja[400],dark:Ja[800]}}function lp(u="light"){return u==="dark"?{main:ka[200],light:ka[50],dark:ka[400]}:{main:ka[500],light:ka[300],dark:ka[700]}}function ap(u="light"){return u==="dark"?{main:$a[500],light:$a[300],dark:$a[700]}:{main:$a[700],light:$a[400],dark:$a[800]}}function np(u="light"){return u==="dark"?{main:Wa[400],light:Wa[300],dark:Wa[700]}:{main:Wa[700],light:Wa[500],dark:Wa[900]}}function up(u="light"){return u==="dark"?{main:Fa[400],light:Fa[300],dark:Fa[700]}:{main:Fa[800],light:Fa[500],dark:Fa[900]}}function ip(u="light"){return u==="dark"?{main:uu[400],light:uu[300],dark:uu[700]}:{main:"#ed6c02",light:uu[500],dark:uu[900]}}function Vf(u){const{mode:c="light",contrastThreshold:f=3,tonalOffset:r=.2,...s}=u,h=u.primary||ep(c),v=u.secondary||lp(c),S=u.error||ap(c),A=u.info||np(c),g=u.success||up(c),x=u.warning||ip(c);function R(z){return Xv(z,dh.text.primary)>=f?dh.text.primary:tp.text.primary}const N=({color:z,name:L,mainShade:K=500,lightShade:nt=300,darkShade:$=700})=>{if(z={...z},!z.main&&z[K]&&(z.main=z[K]),!z.hasOwnProperty("main"))throw new Error(ca(11,L?` (${L})`:"",K));if(typeof z.main!="string")throw new Error(ca(12,L?` (${L})`:"",JSON.stringify(z.main)));return hh(z,"light",nt,r),hh(z,"dark",$,r),z.contrastText||(z.contrastText=R(z.main)),z};let w;return c==="light"?w=Qh():c==="dark"&&(w=Lh()),Ce({common:{...du},mode:c,primary:N({color:h,name:"primary"}),secondary:N({color:v,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:N({color:S,name:"error"}),warning:N({color:x,name:"warning"}),info:N({color:A,name:"info"}),success:N({color:g,name:"success"}),grey:Iv,contrastThreshold:f,getContrastText:R,augmentColor:N,tonalOffset:r,...w},s)}function cp(u){const c={};return Object.entries(u).forEach(r=>{const[s,h]=r;typeof h=="object"&&(c[s]=`${h.fontStyle?`${h.fontStyle} `:""}${h.fontVariant?`${h.fontVariant} `:""}${h.fontWeight?`${h.fontWeight} `:""}${h.fontStretch?`${h.fontStretch} `:""}${h.fontSize||""}${h.lineHeight?`/${h.lineHeight} `:""}${h.fontFamily||""}`)}),c}function rp(u,c){return{toolbar:{minHeight:56,[u.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[u.up("sm")]:{minHeight:64}},...c}}function fp(u){return Math.round(u*1e5)/1e5}const mh={textTransform:"uppercase"},yh='"Roboto", "Helvetica", "Arial", sans-serif';function op(u,c){const{fontFamily:f=yh,fontSize:r=14,fontWeightLight:s=300,fontWeightRegular:h=400,fontWeightMedium:v=500,fontWeightBold:S=700,htmlFontSize:A=16,allVariants:g,pxToRem:x,...R}=typeof c=="function"?c(u):c,N=r/14,w=x||(L=>`${L/A*N}rem`),Y=(L,K,nt,$,k)=>({fontFamily:f,fontWeight:L,fontSize:w(K),lineHeight:nt,...f===yh?{letterSpacing:`${fp($/K)}em`}:{},...k,...g}),z={h1:Y(s,96,1.167,-1.5),h2:Y(s,60,1.2,-.5),h3:Y(h,48,1.167,0),h4:Y(h,34,1.235,.25),h5:Y(h,24,1.334,0),h6:Y(v,20,1.6,.15),subtitle1:Y(h,16,1.75,.15),subtitle2:Y(v,14,1.57,.1),body1:Y(h,16,1.5,.15),body2:Y(h,14,1.43,.15),button:Y(v,14,1.75,.4,mh),caption:Y(h,12,1.66,.4),overline:Y(h,12,2.66,1,mh),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return Ce({htmlFontSize:A,pxToRem:w,fontFamily:f,fontSize:r,fontWeightLight:s,fontWeightRegular:h,fontWeightMedium:v,fontWeightBold:S,...z},R,{clone:!1})}const sp=.2,dp=.14,hp=.12;function Nt(...u){return[`${u[0]}px ${u[1]}px ${u[2]}px ${u[3]}px rgba(0,0,0,${sp})`,`${u[4]}px ${u[5]}px ${u[6]}px ${u[7]}px rgba(0,0,0,${dp})`,`${u[8]}px ${u[9]}px ${u[10]}px ${u[11]}px rgba(0,0,0,${hp})`].join(",")}const mp=["none",Nt(0,2,1,-1,0,1,1,0,0,1,3,0),Nt(0,3,1,-2,0,2,2,0,0,1,5,0),Nt(0,3,3,-2,0,3,4,0,0,1,8,0),Nt(0,2,4,-1,0,4,5,0,0,1,10,0),Nt(0,3,5,-1,0,5,8,0,0,1,14,0),Nt(0,3,5,-1,0,6,10,0,0,1,18,0),Nt(0,4,5,-2,0,7,10,1,0,2,16,1),Nt(0,5,5,-3,0,8,10,1,0,3,14,2),Nt(0,5,6,-3,0,9,12,1,0,3,16,2),Nt(0,6,6,-3,0,10,14,1,0,4,18,3),Nt(0,6,7,-4,0,11,15,1,0,4,20,3),Nt(0,7,8,-4,0,12,17,2,0,5,22,4),Nt(0,7,8,-4,0,13,19,2,0,5,24,4),Nt(0,7,9,-4,0,14,21,2,0,5,26,4),Nt(0,8,9,-5,0,15,22,2,0,6,28,5),Nt(0,8,10,-5,0,16,24,2,0,6,30,5),Nt(0,8,11,-5,0,17,26,2,0,6,32,5),Nt(0,9,11,-5,0,18,28,2,0,7,34,6),Nt(0,9,12,-6,0,19,29,2,0,7,36,6),Nt(0,10,13,-6,0,20,31,3,0,8,38,7),Nt(0,10,13,-6,0,21,33,3,0,8,40,7),Nt(0,10,14,-6,0,22,35,3,0,8,42,7),Nt(0,11,14,-7,0,23,36,3,0,9,44,8),Nt(0,11,15,-7,0,24,38,3,0,9,46,8)],yp={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},gp={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function gh(u){return`${Math.round(u)}ms`}function vp(u){if(!u)return 0;const c=u/36;return Math.min(Math.round((4+15*c**.25+c/5)*10),3e3)}function pp(u){const c={...yp,...u.easing},f={...gp,...u.duration};return{getAutoHeightDuration:vp,create:(s=["all"],h={})=>{const{duration:v=f.standard,easing:S=c.easeInOut,delay:A=0,...g}=h;return(Array.isArray(s)?s:[s]).map(x=>`${x} ${typeof v=="string"?v:gh(v)} ${S} ${typeof A=="string"?A:gh(A)}`).join(",")},...u,easing:c,duration:f}}const bp={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500};function Sp(u){return yl(u)||typeof u>"u"||typeof u=="string"||typeof u=="boolean"||typeof u=="number"||Array.isArray(u)}function Vh(u={}){const c={...u};function f(r){const s=Object.entries(r);for(let h=0;h<s.length;h++){const[v,S]=s[h];!Sp(S)||v.startsWith("unstable_")?delete r[v]:yl(S)&&(r[v]={...S},f(r[v]))}}return f(c),`import { unstable_createBreakpoints as createBreakpoints, createTransitions } from '@mui/material/styles';

const theme = ${JSON.stringify(c,null,2)};

theme.breakpoints = createBreakpoints(theme.breakpoints || {});
theme.transitions = createTransitions(theme.transitions || {});

export default theme;`}function Df(u={},...c){const{breakpoints:f,mixins:r={},spacing:s,palette:h={},transitions:v={},typography:S={},shape:A,...g}=u;if(u.vars&&u.generateThemeVars===void 0)throw new Error(ca(20));const x=Vf(h),R=Yh(u);let N=Ce(R,{mixins:rp(R.breakpoints,r),palette:x,shadows:mp.slice(),typography:op(x,S),transitions:pp(v),zIndex:{...bp}});return N=Ce(N,g),N=c.reduce((w,Y)=>Ce(w,Y),N),N.unstable_sxConfig={...ki,...g==null?void 0:g.unstable_sxConfig},N.unstable_sx=function(Y){return en({sx:Y,theme:this})},N.toRuntimeSource=Vh,N}function Tp(u){let c;return u<1?c=5.11916*u**2:c=4.5*Math.log(u+1)+2,Math.round(c*10)/1e3}const Ap=[...Array(25)].map((u,c)=>{if(c===0)return"none";const f=Tp(c);return`linear-gradient(rgba(255 255 255 / ${f}), rgba(255 255 255 / ${f}))`});function Zh(u){return{inputPlaceholder:u==="dark"?.5:.42,inputUnderline:u==="dark"?.7:.42,switchTrackDisabled:u==="dark"?.2:.12,switchTrack:u==="dark"?.3:.38}}function Kh(u){return u==="dark"?Ap:[]}function Ep(u){const{palette:c={mode:"light"},opacity:f,overlays:r,...s}=u,h=Vf(c);return{palette:h,opacity:{...Zh(h.mode),...f},overlays:r||Kh(h.mode),...s}}function Op(u){var c;return!!u[0].match(/(cssVarPrefix|colorSchemeSelector|rootSelector|typography|mixins|breakpoints|direction|transitions)/)||!!u[0].match(/sxConfig$/)||u[0]==="palette"&&!!((c=u[1])!=null&&c.match(/(mode|contrastThreshold|tonalOffset)/))}const _p=u=>[...[...Array(25)].map((c,f)=>`--${u?`${u}-`:""}overlays-${f}`),`--${u?`${u}-`:""}palette-AppBar-darkBg`,`--${u?`${u}-`:""}palette-AppBar-darkColor`],zp=u=>(c,f)=>{const r=u.rootSelector||":root",s=u.colorSchemeSelector;let h=s;if(s==="class"&&(h=".%s"),s==="data"&&(h="[data-%s]"),s!=null&&s.startsWith("data-")&&!s.includes("%s")&&(h=`[${s}="%s"]`),u.defaultColorScheme===c){if(c==="dark"){const v={};return _p(u.cssVarPrefix).forEach(S=>{v[S]=f[S],delete f[S]}),h==="media"?{[r]:f,"@media (prefers-color-scheme: dark)":{[r]:v}}:h?{[h.replace("%s",c)]:v,[`${r}, ${h.replace("%s",c)}`]:f}:{[r]:{...f,...v}}}if(h&&h!=="media")return`${r}, ${h.replace("%s",String(c))}`}else if(c){if(h==="media")return{[`@media (prefers-color-scheme: ${String(c)})`]:{[r]:f}};if(h)return h.replace("%s",String(c))}return r};function Cp(u,c){c.forEach(f=>{u[f]||(u[f]={})})}function D(u,c,f){!u[c]&&f&&(u[c]=f)}function cu(u){return typeof u!="string"||!u.startsWith("hsl")?u:Xh(u)}function ml(u,c){`${c}Channel`in u||(u[`${c}Channel`]=iu(cu(u[c])))}function xp(u){return typeof u=="number"?`${u}px`:typeof u=="string"||typeof u=="function"||Array.isArray(u)?u:"8px"}const Je=u=>{try{return u()}catch{}},Rp=(u="mui")=>$v(u);function Of(u,c,f,r){if(!c)return;c=c===!0?{}:c;const s=r==="dark"?"dark":"light";if(!f){u[r]=Ep({...c,palette:{mode:s,...c==null?void 0:c.palette}});return}const{palette:h,...v}=Df({...f,palette:{mode:s,...c==null?void 0:c.palette}});return u[r]={...c,palette:h,opacity:{...Zh(s),...c==null?void 0:c.opacity},overlays:(c==null?void 0:c.overlays)||Kh(s)},v}function Mp(u={},...c){const{colorSchemes:f={light:!0},defaultColorScheme:r,disableCssColorScheme:s=!1,cssVarPrefix:h="mui",shouldSkipGeneratingVar:v=Op,colorSchemeSelector:S=f.light&&f.dark?"media":void 0,rootSelector:A=":root",...g}=u,x=Object.keys(f)[0],R=r||(f.light&&x!=="light"?"light":x),N=Rp(h),{[R]:w,light:Y,dark:z,...L}=f,K={...L};let nt=w;if((R==="dark"&&!("dark"in f)||R==="light"&&!("light"in f))&&(nt=!0),!nt)throw new Error(ca(21,R));const $=Of(K,nt,g,R);Y&&!K.light&&Of(K,Y,void 0,"light"),z&&!K.dark&&Of(K,z,void 0,"dark");let k={defaultColorScheme:R,...$,cssVarPrefix:h,colorSchemeSelector:S,rootSelector:A,getCssVar:N,colorSchemes:K,font:{...cp($.typography),...$.font},spacing:xp(g.spacing)};Object.keys(k.colorSchemes).forEach(Dt=>{const m=k.colorSchemes[Dt].palette,Q=F=>{const st=F.split("-"),qt=st[1],_t=st[2];return N(F,m[qt][_t])};if(m.mode==="light"&&(D(m.common,"background","#fff"),D(m.common,"onBackground","#000")),m.mode==="dark"&&(D(m.common,"background","#000"),D(m.common,"onBackground","#fff")),Cp(m,["Alert","AppBar","Avatar","Button","Chip","FilledInput","LinearProgress","Skeleton","Slider","SnackbarContent","SpeedDialAction","StepConnector","StepContent","Switch","TableCell","Tooltip"]),m.mode==="light"){D(m.Alert,"errorColor",xt(m.error.light,.6)),D(m.Alert,"infoColor",xt(m.info.light,.6)),D(m.Alert,"successColor",xt(m.success.light,.6)),D(m.Alert,"warningColor",xt(m.warning.light,.6)),D(m.Alert,"errorFilledBg",Q("palette-error-main")),D(m.Alert,"infoFilledBg",Q("palette-info-main")),D(m.Alert,"successFilledBg",Q("palette-success-main")),D(m.Alert,"warningFilledBg",Q("palette-warning-main")),D(m.Alert,"errorFilledColor",Je(()=>m.getContrastText(m.error.main))),D(m.Alert,"infoFilledColor",Je(()=>m.getContrastText(m.info.main))),D(m.Alert,"successFilledColor",Je(()=>m.getContrastText(m.success.main))),D(m.Alert,"warningFilledColor",Je(()=>m.getContrastText(m.warning.main))),D(m.Alert,"errorStandardBg",Rt(m.error.light,.9)),D(m.Alert,"infoStandardBg",Rt(m.info.light,.9)),D(m.Alert,"successStandardBg",Rt(m.success.light,.9)),D(m.Alert,"warningStandardBg",Rt(m.warning.light,.9)),D(m.Alert,"errorIconColor",Q("palette-error-main")),D(m.Alert,"infoIconColor",Q("palette-info-main")),D(m.Alert,"successIconColor",Q("palette-success-main")),D(m.Alert,"warningIconColor",Q("palette-warning-main")),D(m.AppBar,"defaultBg",Q("palette-grey-100")),D(m.Avatar,"defaultBg",Q("palette-grey-400")),D(m.Button,"inheritContainedBg",Q("palette-grey-300")),D(m.Button,"inheritContainedHoverBg",Q("palette-grey-A100")),D(m.Chip,"defaultBorder",Q("palette-grey-400")),D(m.Chip,"defaultAvatarColor",Q("palette-grey-700")),D(m.Chip,"defaultIconColor",Q("palette-grey-700")),D(m.FilledInput,"bg","rgba(0, 0, 0, 0.06)"),D(m.FilledInput,"hoverBg","rgba(0, 0, 0, 0.09)"),D(m.FilledInput,"disabledBg","rgba(0, 0, 0, 0.12)"),D(m.LinearProgress,"primaryBg",Rt(m.primary.main,.62)),D(m.LinearProgress,"secondaryBg",Rt(m.secondary.main,.62)),D(m.LinearProgress,"errorBg",Rt(m.error.main,.62)),D(m.LinearProgress,"infoBg",Rt(m.info.main,.62)),D(m.LinearProgress,"successBg",Rt(m.success.main,.62)),D(m.LinearProgress,"warningBg",Rt(m.warning.main,.62)),D(m.Skeleton,"bg",`rgba(${Q("palette-text-primaryChannel")} / 0.11)`),D(m.Slider,"primaryTrack",Rt(m.primary.main,.62)),D(m.Slider,"secondaryTrack",Rt(m.secondary.main,.62)),D(m.Slider,"errorTrack",Rt(m.error.main,.62)),D(m.Slider,"infoTrack",Rt(m.info.main,.62)),D(m.Slider,"successTrack",Rt(m.success.main,.62)),D(m.Slider,"warningTrack",Rt(m.warning.main,.62));const F=Bi(m.background.default,.8);D(m.SnackbarContent,"bg",F),D(m.SnackbarContent,"color",Je(()=>m.getContrastText(F))),D(m.SpeedDialAction,"fabHoverBg",Bi(m.background.paper,.15)),D(m.StepConnector,"border",Q("palette-grey-400")),D(m.StepContent,"border",Q("palette-grey-400")),D(m.Switch,"defaultColor",Q("palette-common-white")),D(m.Switch,"defaultDisabledColor",Q("palette-grey-100")),D(m.Switch,"primaryDisabledColor",Rt(m.primary.main,.62)),D(m.Switch,"secondaryDisabledColor",Rt(m.secondary.main,.62)),D(m.Switch,"errorDisabledColor",Rt(m.error.main,.62)),D(m.Switch,"infoDisabledColor",Rt(m.info.main,.62)),D(m.Switch,"successDisabledColor",Rt(m.success.main,.62)),D(m.Switch,"warningDisabledColor",Rt(m.warning.main,.62)),D(m.TableCell,"border",Rt(Ui(m.divider,1),.88)),D(m.Tooltip,"bg",Ui(m.grey[700],.92))}if(m.mode==="dark"){D(m.Alert,"errorColor",Rt(m.error.light,.6)),D(m.Alert,"infoColor",Rt(m.info.light,.6)),D(m.Alert,"successColor",Rt(m.success.light,.6)),D(m.Alert,"warningColor",Rt(m.warning.light,.6)),D(m.Alert,"errorFilledBg",Q("palette-error-dark")),D(m.Alert,"infoFilledBg",Q("palette-info-dark")),D(m.Alert,"successFilledBg",Q("palette-success-dark")),D(m.Alert,"warningFilledBg",Q("palette-warning-dark")),D(m.Alert,"errorFilledColor",Je(()=>m.getContrastText(m.error.dark))),D(m.Alert,"infoFilledColor",Je(()=>m.getContrastText(m.info.dark))),D(m.Alert,"successFilledColor",Je(()=>m.getContrastText(m.success.dark))),D(m.Alert,"warningFilledColor",Je(()=>m.getContrastText(m.warning.dark))),D(m.Alert,"errorStandardBg",xt(m.error.light,.9)),D(m.Alert,"infoStandardBg",xt(m.info.light,.9)),D(m.Alert,"successStandardBg",xt(m.success.light,.9)),D(m.Alert,"warningStandardBg",xt(m.warning.light,.9)),D(m.Alert,"errorIconColor",Q("palette-error-main")),D(m.Alert,"infoIconColor",Q("palette-info-main")),D(m.Alert,"successIconColor",Q("palette-success-main")),D(m.Alert,"warningIconColor",Q("palette-warning-main")),D(m.AppBar,"defaultBg",Q("palette-grey-900")),D(m.AppBar,"darkBg",Q("palette-background-paper")),D(m.AppBar,"darkColor",Q("palette-text-primary")),D(m.Avatar,"defaultBg",Q("palette-grey-600")),D(m.Button,"inheritContainedBg",Q("palette-grey-800")),D(m.Button,"inheritContainedHoverBg",Q("palette-grey-700")),D(m.Chip,"defaultBorder",Q("palette-grey-700")),D(m.Chip,"defaultAvatarColor",Q("palette-grey-300")),D(m.Chip,"defaultIconColor",Q("palette-grey-300")),D(m.FilledInput,"bg","rgba(255, 255, 255, 0.09)"),D(m.FilledInput,"hoverBg","rgba(255, 255, 255, 0.13)"),D(m.FilledInput,"disabledBg","rgba(255, 255, 255, 0.12)"),D(m.LinearProgress,"primaryBg",xt(m.primary.main,.5)),D(m.LinearProgress,"secondaryBg",xt(m.secondary.main,.5)),D(m.LinearProgress,"errorBg",xt(m.error.main,.5)),D(m.LinearProgress,"infoBg",xt(m.info.main,.5)),D(m.LinearProgress,"successBg",xt(m.success.main,.5)),D(m.LinearProgress,"warningBg",xt(m.warning.main,.5)),D(m.Skeleton,"bg",`rgba(${Q("palette-text-primaryChannel")} / 0.13)`),D(m.Slider,"primaryTrack",xt(m.primary.main,.5)),D(m.Slider,"secondaryTrack",xt(m.secondary.main,.5)),D(m.Slider,"errorTrack",xt(m.error.main,.5)),D(m.Slider,"infoTrack",xt(m.info.main,.5)),D(m.Slider,"successTrack",xt(m.success.main,.5)),D(m.Slider,"warningTrack",xt(m.warning.main,.5));const F=Bi(m.background.default,.98);D(m.SnackbarContent,"bg",F),D(m.SnackbarContent,"color",Je(()=>m.getContrastText(F))),D(m.SpeedDialAction,"fabHoverBg",Bi(m.background.paper,.15)),D(m.StepConnector,"border",Q("palette-grey-600")),D(m.StepContent,"border",Q("palette-grey-600")),D(m.Switch,"defaultColor",Q("palette-grey-300")),D(m.Switch,"defaultDisabledColor",Q("palette-grey-600")),D(m.Switch,"primaryDisabledColor",xt(m.primary.main,.55)),D(m.Switch,"secondaryDisabledColor",xt(m.secondary.main,.55)),D(m.Switch,"errorDisabledColor",xt(m.error.main,.55)),D(m.Switch,"infoDisabledColor",xt(m.info.main,.55)),D(m.Switch,"successDisabledColor",xt(m.success.main,.55)),D(m.Switch,"warningDisabledColor",xt(m.warning.main,.55)),D(m.TableCell,"border",xt(Ui(m.divider,1),.68)),D(m.Tooltip,"bg",Ui(m.grey[700],.92))}ml(m.background,"default"),ml(m.background,"paper"),ml(m.common,"background"),ml(m.common,"onBackground"),ml(m,"divider"),Object.keys(m).forEach(F=>{const st=m[F];F!=="tonalOffset"&&st&&typeof st=="object"&&(st.main&&D(m[F],"mainChannel",iu(cu(st.main))),st.light&&D(m[F],"lightChannel",iu(cu(st.light))),st.dark&&D(m[F],"darkChannel",iu(cu(st.dark))),st.contrastText&&D(m[F],"contrastTextChannel",iu(cu(st.contrastText))),F==="text"&&(ml(m[F],"primary"),ml(m[F],"secondary")),F==="action"&&(st.active&&ml(m[F],"active"),st.selected&&ml(m[F],"selected")))})}),k=c.reduce((Dt,m)=>Ce(Dt,m),k);const j={prefix:h,disableCssColorScheme:s,shouldSkipGeneratingVar:v,getSelector:zp(k)},{vars:I,generateThemeVars:J,generateStyleSheets:pt}=Fv(k,j);return k.vars=I,Object.entries(k.colorSchemes[k.defaultColorScheme]).forEach(([Dt,m])=>{k[Dt]=m}),k.generateThemeVars=J,k.generateStyleSheets=pt,k.generateSpacing=function(){return qh(g.spacing,qf(this))},k.getColorSchemeSelector=Pv(S),k.spacing=k.generateSpacing(),k.shouldSkipGeneratingVar=v,k.unstable_sxConfig={...ki,...g==null?void 0:g.unstable_sxConfig},k.unstable_sx=function(m){return en({sx:m,theme:this})},k.toRuntimeSource=Vh,k}function vh(u,c,f){u.colorSchemes&&f&&(u.colorSchemes[c]={...f!==!0&&f,palette:Vf({...f===!0?{}:f.palette,mode:c})})}function Dp(u={},...c){const{palette:f,cssVariables:r=!1,colorSchemes:s=f?void 0:{light:!0},defaultColorScheme:h=f==null?void 0:f.mode,...v}=u,S=h||"light",A=s==null?void 0:s[S],g={...s,...f?{[S]:{...typeof A!="boolean"&&A,palette:f}}:void 0};if(r===!1){if(!("colorSchemes"in u))return Df(u,...c);let x=f;"palette"in u||g[S]&&(g[S]!==!0?x=g[S].palette:S==="dark"&&(x={mode:"dark"}));const R=Df({...u,palette:x},...c);return R.defaultColorScheme=S,R.colorSchemes=g,R.palette.mode==="light"&&(R.colorSchemes.light={...g.light!==!0&&g.light,palette:R.palette},vh(R,"dark",g.dark)),R.palette.mode==="dark"&&(R.colorSchemes.dark={...g.dark!==!0&&g.dark,palette:R.palette},vh(R,"light",g.light)),R}return!f&&!("light"in g)&&S==="light"&&(g.light=!0),Mp({...v,colorSchemes:g,defaultColorScheme:S,...typeof r!="boolean"&&r},...c)}const Up=Dp(),Bp="$$material";function Np(u){return u!=="ownerState"&&u!=="theme"&&u!=="sx"&&u!=="as"}const Hp=u=>Np(u)&&u!=="classes",qp=Bv({themeId:Bp,defaultTheme:Up,rootShouldForwardProp:Hp}),Yp=kv;function jp(u){return Kv(u)}function Gp(u){return jh("MuiSvgIcon",u)}Rv("MuiSvgIcon",["root","colorPrimary","colorSecondary","colorAction","colorError","colorDisabled","fontSizeInherit","fontSizeSmall","fontSizeMedium","fontSizeLarge"]);const wp=u=>{const{color:c,fontSize:f,classes:r}=u,s={root:["root",c!=="inherit"&&`color${tn(c)}`,`fontSize${tn(f)}`]};return Ug(s,Gp,r)},Xp=qp("svg",{name:"MuiSvgIcon",slot:"Root",overridesResolver:(u,c)=>{const{ownerState:f}=u;return[c.root,f.color!=="inherit"&&c[`color${tn(f.color)}`],c[`fontSize${tn(f.fontSize)}`]]}})(Yp(({theme:u})=>{var c,f,r,s,h,v,S,A,g,x,R,N,w,Y;return{userSelect:"none",width:"1em",height:"1em",display:"inline-block",flexShrink:0,transition:(s=(c=u.transitions)==null?void 0:c.create)==null?void 0:s.call(c,"fill",{duration:(r=(f=(u.vars??u).transitions)==null?void 0:f.duration)==null?void 0:r.shorter}),variants:[{props:z=>!z.hasSvgAsChild,style:{fill:"currentColor"}},{props:{fontSize:"inherit"},style:{fontSize:"inherit"}},{props:{fontSize:"small"},style:{fontSize:((v=(h=u.typography)==null?void 0:h.pxToRem)==null?void 0:v.call(h,20))||"1.25rem"}},{props:{fontSize:"medium"},style:{fontSize:((A=(S=u.typography)==null?void 0:S.pxToRem)==null?void 0:A.call(S,24))||"1.5rem"}},{props:{fontSize:"large"},style:{fontSize:((x=(g=u.typography)==null?void 0:g.pxToRem)==null?void 0:x.call(g,35))||"2.1875rem"}},...Object.entries((u.vars??u).palette).filter(([,z])=>z&&z.main).map(([z])=>{var L,K;return{props:{color:z},style:{color:(K=(L=(u.vars??u).palette)==null?void 0:L[z])==null?void 0:K.main}}}),{props:{color:"action"},style:{color:(N=(R=(u.vars??u).palette)==null?void 0:R.action)==null?void 0:N.active}},{props:{color:"disabled"},style:{color:(Y=(w=(u.vars??u).palette)==null?void 0:w.action)==null?void 0:Y.disabled}},{props:{color:"inherit"},style:{color:void 0}}]}})),Uf=Ht.forwardRef(function(c,f){const r=jp({props:c,name:"MuiSvgIcon"}),{children:s,className:h,color:v="inherit",component:S="svg",fontSize:A="medium",htmlColor:g,inheritViewBox:x=!1,titleAccess:R,viewBox:N="0 0 24 24",...w}=r,Y=Ht.isValidElement(s)&&s.type==="svg",z={...r,color:v,component:S,fontSize:A,instanceFontSize:c.fontSize,inheritViewBox:x,viewBox:N,hasSvgAsChild:Y},L={};x||(L.viewBox=N);const K=wp(z);return Qt.jsxs(Xp,{as:S,className:Dg(K.root,h),focusable:"false",color:g,"aria-hidden":R?void 0:!0,role:R?"img":void 0,ref:f,...L,...w,...Y&&s.props,ownerState:z,children:[Y?s.props.children:s,R?Qt.jsx("title",{children:R}):null]})});Uf.muiName="SvgIcon";function Qp(u,c){function f(r,s){return Qt.jsx(Uf,{"data-testid":void 0,ref:s,...r,children:u})}return f.muiName=Uf.muiName,Ht.memo(Ht.forwardRef(f))}const Lp=Qp(Qt.jsx("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4z"}));function Vp(){const[u,c]=Ht.useState("joke"),[f,r]=Ht.useState(""),[s,h]=Ht.useState(!1),[v,S]=Ht.useState(null),A=async()=>{h(!0),S(null);try{let g="",x={};u==="joke"?(g="https://icanhazdadjoke.com/",x={Accept:"application/json"}):u==="fact"&&(g="https://uselessfacts.jsph.pl/random.json?language=en");const N=await(await fetch(g,{headers:x})).json();u==="joke"?r(N.joke):u==="fact"&&r(N.text)}catch{S("Failed to fetch 😢")}finally{h(!1)}};return Ht.useEffect(()=>{r(""),A()},[u]),Qt.jsxs("div",{className:"app",children:[Qt.jsx("h1",{children:"The Grin Bin"}),Qt.jsxs("div",{className:"category-buttons",children:[Qt.jsx("button",{className:u==="joke"?"active":"",onClick:()=>c("joke"),children:"Dad Joke"}),Qt.jsx("button",{className:u==="fact"?"active":"",onClick:()=>c("fact"),children:"Random Fact"})]}),v&&Qt.jsx("p",{children:v}),Qt.jsx("div",{className:"card-wrapper",children:f&&Qt.jsxs("div",{className:"card-transition",children:[u==="joke"&&Qt.jsx(Cg,{text:f}),u==="fact"&&Qt.jsx(xg,{text:f})]},u)}),Qt.jsxs("button",{className:"refresh-button",onClick:A,children:[Qt.jsx(Lp,{style:{marginRight:"0.5rem"}})," Get Another"]})]})}zg.createRoot(document.getElementById("root")).render(Qt.jsx(bh.StrictMode,{children:Qt.jsx(Vp,{})}));
