import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/cookie-consent.css';

const CookieConsent = () => {
  const [showBanner, setShowBanner] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user has already given consent
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) {
      // Show banner after a short delay to not interfere with page load
      const timer = setTimeout(() => {
        setShowBanner(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookieConsent', 'accepted');
    localStorage.setItem('cookieConsentDate', new Date().toISOString());
    setShowBanner(false);
    
    // Enable Google Analytics tracking
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'granted'
      });
    }
  };

  const handleDecline = () => {
    localStorage.setItem('cookieConsent', 'declined');
    localStorage.setItem('cookieConsentDate', new Date().toISOString());
    setShowBanner(false);
    
    // Disable Google Analytics tracking
    if (window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': 'denied'
      });
    }
  };

  const handleCustomize = () => {
    // For now, just show the privacy policy
    // In a more complex implementation, you could show a detailed consent form
    navigate('/privacy');
  };

  if (!showBanner) {
    return null;
  }

  return (
    <div className="cookie-consent-banner">
      <div className="cookie-consent-content">
        <div className="cookie-consent-text">
          <h3>🍪 We use cookies</h3>
          <p>
            We use cookies and similar technologies to improve your experience, analyze site usage, 
            and assist with our marketing efforts. This includes Google Analytics to understand how 
            you interact with our site.
          </p>
          <p>
            By clicking "Accept All", you consent to our use of cookies. You can manage your 
            preferences or learn more in our{' '}
            <button 
              className="privacy-link" 
              onClick={() => navigate('/privacy')}
            >
              Privacy Policy
            </button>.
          </p>
        </div>
        
        <div className="cookie-consent-actions">
          <button 
            className="cookie-btn cookie-btn-decline" 
            onClick={handleDecline}
          >
            Decline
          </button>
          <button 
            className="cookie-btn cookie-btn-customize" 
            onClick={handleCustomize}
          >
            Learn More
          </button>
          <button 
            className="cookie-btn cookie-btn-accept" 
            onClick={handleAccept}
          >
            Accept All
          </button>
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
