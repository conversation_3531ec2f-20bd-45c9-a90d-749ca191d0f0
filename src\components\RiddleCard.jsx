import React, { useState, useEffect } from 'react';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import ShareIcon from '@mui/icons-material/Share';

const RiddleCard = ({ question, answer, onSave }) => {
  const [showAnswer, setShowAnswer] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Reset showAnswer when question changes and handle fade-in
  useEffect(() => {
    setShowAnswer(false);

    // Add a small delay before showing the card to ensure smooth transition
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    return () => {
      clearTimeout(timer);
      setIsVisible(false);
    };
  }, [question]);

  const toggleAnswer = () => {
    setShowAnswer(!showAnswer);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Check out this riddle!',
          text: `Q: ${question}\nA: ${answer}`
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback for browsers that don't support Web Share API
      navigator.clipboard.writeText(`Q: ${question}\nA: ${answer}`);
      alert('Riddle copied to clipboard!');
    }
  };

  return (
    <div className={`riddle-card card ${isVisible ? 'visible' : ''}`}
      style={{ opacity: isVisible ? 1 : 0 }}>
      <div className="riddle-question">
        <h3>{question}</h3>
      </div>

      <div className="riddle-answer">
        <button
          className="answer-reveal-button"
          onClick={toggleAnswer}
        >
          {showAnswer ? 'Hide Answer' : 'Reveal Answer'}
        </button>

        {showAnswer && (
          <div className="answer-text">
            <p>{answer}</p>
          </div>
        )}
      </div>

      <div className="card-actions">
        <button className="save-button" onClick={onSave}>
          <SaveAltIcon />
        </button>
        <button className="share-button" onClick={handleShare}>
          <ShareIcon />
        </button>
      </div>
    </div>
  );
};

export default RiddleCard;


