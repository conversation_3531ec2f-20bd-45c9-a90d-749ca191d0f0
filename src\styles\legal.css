.legal-page {
  min-height: 100vh;
  padding: 2rem 1rem;
  background: var(--bg-light);
  color: var(--text-light);
  transition: background-color 0.3s ease, color 0.3s ease;
  display: flex;
  flex-direction: column;
}

.dark-mode .legal-page {
  background: var(--bg-dark);
  color: var(--text-dark);
}

.legal-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  line-height: 1.6;
  flex: 1;
}

.dark-mode .legal-container {
  background: var(--notepad-content-dark);
  color: var(--text-dark);
}

.legal-container h1 {
  color: var(--text-light);
  margin-bottom: 0.5rem;
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
}

.dark-mode .legal-container h1 {
  color: var(--text-dark);
}

.last-updated {
  text-align: center;
  font-style: italic;
  color: #666;
  margin-bottom: 2rem;
  font-size: 0.9rem;
}

.dark-mode .last-updated {
  color: #aaa;
}

.legal-container h2 {
  color: var(--text-light);
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
  border-bottom: 2px solid #e3f2fd;
  padding-bottom: 0.5rem;
}

.dark-mode .legal-container h2 {
  color: var(--text-dark);
  border-bottom-color: #0d47a1;
}

.legal-container h3 {
  color: var(--text-light);
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.dark-mode .legal-container h3 {
  color: var(--text-dark);
}

.legal-container section {
  margin-bottom: 2rem;
}

.legal-container ul,
.legal-container ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.legal-container li {
  margin-bottom: 0.5rem;
}

.legal-container p {
  margin-bottom: 1rem;
  text-align: justify;
}

.legal-container strong {
  color: var(--text-light);
  font-weight: 600;
}

.dark-mode .legal-container strong {
  color: var(--text-dark);
}

/* About page specific styles */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.feature {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid var(--text-light);
}

.dark-mode .feature {
  background: #2d2d2d;
  border-left-color: var(--text-dark);
}

.feature h3 {
  margin-top: 0;
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
}

.feature p {
  margin-bottom: 0;
  font-size: 0.95rem;
}

.contact-info {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin: 1rem 0;
}

.dark-mode .contact-info {
  background: #2d2d2d;
}

.contact-info p {
  margin-bottom: 0.5rem;
}

.contact-info p:last-child {
  margin-bottom: 0;
}

/* Contact page specific styles */
.single-contact-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  border-left: 4px solid var(--text-light);
  margin: 1.5rem 0;
  text-align: center;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.dark-mode .single-contact-card {
  background: #2d2d2d;
  border-left-color: var(--text-dark);
}

.single-contact-card h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  color: var(--text-light);
}

.dark-mode .single-contact-card h3 {
  color: var(--text-dark);
}

.main-email {
  margin-bottom: 1.5rem;
}

.main-email .contact-link {
  font-size: 1.2rem;
  font-weight: 600;
  text-decoration: none;
  color: var(--text-light);
  padding: 0.75rem 1.5rem;
  background: rgba(13, 71, 161, 0.1);
  border-radius: 8px;
  display: inline-block;
  transition: all 0.2s ease;
}

.main-email .contact-link:hover {
  background: rgba(13, 71, 161, 0.2);
  transform: translateY(-1px);
}

.dark-mode .main-email .contact-link {
  color: var(--text-dark);
  background: rgba(227, 242, 253, 0.1);
}

.dark-mode .main-email .contact-link:hover {
  background: rgba(227, 242, 253, 0.2);
}

.contact-description {
  margin-bottom: 1rem;
  color: #666;
  font-size: 1rem;
}

.dark-mode .contact-description {
  color: #aaa;
}

.contact-types {
  text-align: left;
  max-width: 500px;
  margin: 0 auto;
  padding-left: 1.5rem;
}

.contact-types li {
  margin-bottom: 0.5rem;
  color: #555;
  line-height: 1.4;
}

.dark-mode .contact-types li {
  color: #bbb;
}

.contact-link {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 600;
}

.contact-link:hover {
  text-decoration: underline;
}

.dark-mode .contact-link {
  color: var(--text-dark);
}

.contact-form {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin: 1.5rem 0;
}

.dark-mode .contact-form {
  background: #2d2d2d;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-light);
}

.dark-mode .form-group label {
  color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--text-light);
}

.dark-mode .form-group input,
.dark-mode .form-group select,
.dark-mode .form-group textarea {
  background: #3a3a3a;
  border-color: #555;
  color: var(--text-dark);
}

.dark-mode .form-group input:focus,
.dark-mode .form-group select:focus,
.dark-mode .form-group textarea:focus {
  border-color: var(--text-dark);
}

.submit-btn {
  background: var(--text-light);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.submit-btn:hover {
  background: #0d47a1;
}

.dark-mode .submit-btn {
  background: var(--text-dark);
  color: var(--bg-dark);
}

.dark-mode .submit-btn:hover {
  background: #ffffff;
}

.faq-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e9ecef;
}

.dark-mode .faq-item {
  border-bottom-color: #444;
}

.faq-item:last-child {
  border-bottom: none;
}

.faq-item h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .legal-page {
    padding: 1rem 0.5rem;
  }

  .legal-container {
    padding: 1.5rem;
  }

  .legal-container h1 {
    font-size: 2rem;
  }

  .legal-container h2 {
    font-size: 1.3rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .feature {
    padding: 1rem;
  }

  .single-contact-card {
    padding: 1.5rem;
    text-align: left;
  }

  .main-email {
    text-align: center;
  }
}