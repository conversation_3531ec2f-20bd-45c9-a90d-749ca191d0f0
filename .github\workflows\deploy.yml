name: deploy
on:
  push:
    branches:
      - main
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Install SSH Key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSHKEY }}
          known_hosts: unnecessary

      - name: Adding Known Hosts
        run: ssh-keyscan -p ${{ secrets.PORT}} -H ${{ secrets.HOST }}  >> ~/.ssh/known_hosts

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build project
        run: npm run build

      - name: Deploy with rsync
        run: |
          # Deploy built files from dist directory
          rsync -avz -e "ssh -p ${{ secrets.PORT }}" --delete dist/ ${{ secrets.USERNAME }}@${{ secrets.HOST }}:/var/www/thegrinbin/public_html/
          # Deploy .htaccess file
          rsync -avz -e "ssh -p ${{ secrets.PORT }}" .htaccess ${{ secrets.USERNAME }}@${{ secrets.HOST }}:/var/www/thegrinbin/public_html/