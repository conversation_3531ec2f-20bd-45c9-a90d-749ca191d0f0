import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  addDoc,
  getDocs,
  query,
  where,
  limit as firestoreLimit,  // Rename to avoid confusion
  orderBy,
  doc,
  updateDoc,
  getDoc,
  arrayUnion,
  arrayRemove,
  increment
} from 'firebase/firestore';
import { getAnalytics } from 'firebase/analytics';

// Directly use the Firebase configuration values
const firebaseConfig = {
  apiKey: "AIzaSyADXts2Cb5BesfSAIn4SRcxt4sXDYH3W6M",
  authDomain: "the-grin-bin.firebaseapp.com",
  projectId: "the-grin-bin",
  storageBucket: "the-grin-bin.appspot.com", // Note: Fixed this from firebasestorage.app to appspot.com
  messagingSenderId: "975214279512",
  appId: "1:975214279512:web:734e61bec5c4a80658432d",
  measurementId: "G-XYDZDVYH5L"
};

// Log the config to verify
console.log('Using Firebase config:', firebaseConfig);

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const analytics = getAnalytics(app);

// Test connection to Firestore
const testConnection = async () => {
  try {
    console.log('Testing Firebase connection...');
    // Just try to get the collection reference first
    const collRef = collection(db, 'userSubmissions');
    console.log('Collection reference created successfully');

    // Then try a simple query
    const testQuery = query(collRef, firestoreLimit(1));
    console.log('Query created successfully, attempting to execute...');

    // Execute the query with a timeout
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Firebase connection test timed out')), 5000)
    );

    const queryPromise = getDocs(testQuery);

    // Race the query against the timeout
    const snapshot = await Promise.race([queryPromise, timeoutPromise]);
    console.log('Firebase connection successful. Documents found:', snapshot.size);
    return true;
  } catch (error) {
    console.error('Firebase connection test failed:', error);
    return false;
  }
};

// Run the test immediately
testConnection().then(success => {
  if (!success) {
    console.warn('Firebase connection test failed. Check your configuration and network.');
  }
});

// Submit user content with retry logic
export const submitUserContent = async (type, content, authorName) => {
  try {
    // Validate content
    if (!content || (typeof content === 'string' && !content.trim())) {
      throw new Error('Content cannot be empty');
    }

    // Create submission document
    const submissionData = {
      type,
      content,
      authorName: authorName || 'Anonymous',
      status: 'pending',
      createdAt: new Date(),
      upvotes: 0,
      downvotes: 0,
      netVotes: 0,
      voters: [] // Array to track who has voted to prevent duplicate votes
    };

    // Add to Firestore
    const docRef = await addDoc(collection(db, 'userSubmissions'), submissionData);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Error submitting content:', error);
    return { success: false, error: error.message };
  }
};

// Fetch approved content ordered by votes
export const fetchApprovedContent = async (type, limitCount = 10) => {
  try {
    // First, get all approved content of the specified type
    const q = query(
      collection(db, 'userSubmissions'),
      where('type', '==', type),
      where('status', '==', 'approved'),
      firestoreLimit(limitCount * 2)  // Get more to allow for sorting
    );

    const snapshot = await getDocs(q);
    const content = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        // Ensure voting fields exist with defaults
        upvotes: data.upvotes || 0,
        downvotes: data.downvotes || 0,
        netVotes: data.netVotes || 0,
        voters: data.voters || []
      };
    });

    // Sort by netVotes in JavaScript (client-side sorting)
    content.sort((a, b) => {
      const aVotes = a.netVotes || 0;
      const bVotes = b.netVotes || 0;
      if (bVotes !== aVotes) {
        return bVotes - aVotes; // Sort by votes descending
      }
      // If votes are equal, sort by creation date (newest first)
      const aDate = a.createdAt?.toDate?.() || a.createdAt || new Date(0);
      const bDate = b.createdAt?.toDate?.() || b.createdAt || new Date(0);
      return bDate - aDate;
    });

    // Return only the requested number of items
    return content.slice(0, limitCount);
  } catch (error) {
    console.error('Error fetching approved content:', error);
    return [];
  }
};

// Add this simplified function for testing
export const testSubmission = async () => {
  try {
    console.log('Testing submission to Firebase...');

    // Create a simple test document
    const testData = {
      type: 'test',
      content: 'Test submission ' + new Date().toISOString(),
      status: 'test',
      createdAt: new Date()
    };

    console.log('Attempting to add test document...');
    const docRef = await addDoc(collection(db, 'userSubmissions'), testData);
    console.log('Test submission successful, document ID:', docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Test submission failed:', error);
    return { success: false, error: error.message };
  }
};

// Add this function to check network connectivity
export const checkNetworkConnectivity = async () => {
  try {
    console.log('Checking network connectivity...');

    // Check basic online status
    const isOnline = navigator.onLine;
    console.log('Navigator.onLine status:', isOnline);

    // Try to fetch a simple resource
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Network fetch timed out')), 5000)
    );

    const fetchPromise = fetch('https://www.google.com/favicon.ico', {
      mode: 'no-cors',
      cache: 'no-cache'
    });

    await Promise.race([fetchPromise, timeoutPromise]);
    console.log('Network connectivity test passed');
    return true;
  } catch (error) {
    console.error('Network connectivity test failed:', error);
    return false;
  }
};

// Run the network test
checkNetworkConnectivity().then(isConnected => {
  console.log('Network connectivity:', isConnected ? 'OK' : 'FAILED');
});

// Simplified submission function
export const simpleSubmit = async (content) => {
  try {
    console.log('Simple submit starting...');

    // Create a basic document
    const docData = {
      content: content || 'Test content',
      timestamp: new Date()
    };

    // Try to add it to Firestore
    console.log('Adding document to Firestore...');
    const docRef = await addDoc(collection(db, 'test_collection'), docData);

    console.log('Document added successfully:', docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Simple submit failed:', error);
    return { success: false, error: error.message };
  }
};

// Add this simple test function at the end of your file
export const testFirebaseConnection = async () => {
  try {
    console.log('Testing direct Firebase connection...');

    // Create a very simple document
    const testDoc = {
      test: true,
      timestamp: new Date().toISOString()
    };

    // Try to write to a test collection
    const docRef = await addDoc(collection(db, 'test_collection'), testDoc);
    console.log('Test successful! Document written with ID:', docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Firebase test failed with error:', error);
    return { success: false, error: error.message };
  }
};

// Run the test immediately
testFirebaseConnection().then(result => {
  console.log('Firebase connection test result:', result);
});

// Voting functions
export const voteOnContent = async (contentId, voteType, userId) => {
  try {
    const contentRef = doc(db, 'userSubmissions', contentId);
    const contentDoc = await getDoc(contentRef);

    if (!contentDoc.exists()) {
      throw new Error('Content not found');
    }

    const contentData = contentDoc.data();
    const voters = contentData.voters || [];
    const existingVote = voters.find(vote => vote.userId === userId);

    let updateData = {};

    if (existingVote) {
      // User has already voted
      if (existingVote.voteType === voteType) {
        // Same vote type - remove the vote
        updateData = {
          voters: arrayRemove(existingVote),
          [voteType === 'upvote' ? 'upvotes' : 'downvotes']: increment(-1),
          netVotes: increment(voteType === 'upvote' ? -1 : 1)
        };
      } else {
        // Different vote type - change the vote
        updateData = {
          voters: arrayRemove(existingVote),
          [existingVote.voteType === 'upvote' ? 'upvotes' : 'downvotes']: increment(-1),
          [voteType === 'upvote' ? 'upvotes' : 'downvotes']: increment(1),
          netVotes: increment(voteType === 'upvote' ? 2 : -2)
        };

        // Add the new vote
        await updateDoc(contentRef, updateData);
        await updateDoc(contentRef, {
          voters: arrayUnion({ userId, voteType })
        });
        return { success: true };
      }
    } else {
      // New vote
      updateData = {
        voters: arrayUnion({ userId, voteType }),
        [voteType === 'upvote' ? 'upvotes' : 'downvotes']: increment(1),
        netVotes: increment(voteType === 'upvote' ? 1 : -1)
      };
    }

    await updateDoc(contentRef, updateData);
    return { success: true };
  } catch (error) {
    console.error('Error voting on content:', error);
    return { success: false, error: error.message };
  }
};

// Get user's vote status for a piece of content
export const getUserVote = async (contentId, userId) => {
  try {
    const contentRef = doc(db, 'userSubmissions', contentId);
    const contentDoc = await getDoc(contentRef);

    if (!contentDoc.exists()) {
      return null;
    }

    const voters = contentDoc.data().voters || [];
    const userVote = voters.find(vote => vote.userId === userId);
    return userVote ? userVote.voteType : null;
  } catch (error) {
    console.error('Error getting user vote:', error);
    return null;
  }
};

// Initialize voting fields for existing content that doesn't have them
export const initializeVotingFields = async (contentId) => {
  try {
    const contentRef = doc(db, 'userSubmissions', contentId);
    const contentDoc = await getDoc(contentRef);

    if (!contentDoc.exists()) {
      return { success: false, error: 'Content not found' };
    }

    const data = contentDoc.data();

    // Check if voting fields already exist
    if (data.upvotes !== undefined && data.downvotes !== undefined && data.netVotes !== undefined) {
      return { success: true, message: 'Voting fields already exist' };
    }

    // Add voting fields
    const updateData = {};
    if (data.upvotes === undefined) updateData.upvotes = 0;
    if (data.downvotes === undefined) updateData.downvotes = 0;
    if (data.netVotes === undefined) updateData.netVotes = 0;
    if (data.voters === undefined) updateData.voters = [];

    await updateDoc(contentRef, updateData);
    return { success: true, message: 'Voting fields initialized' };
  } catch (error) {
    console.error('Error initializing voting fields:', error);
    return { success: false, error: error.message };
  }
};






