import React from 'react';
import '../styles/footer.css';

const Footer = ({ onNavigate }) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="app-footer">
      <div className="footer-content">
        <div className="footer-section">
          <h3>The Grin Bin</h3>
          <p>Your destination for word play and classic games!</p>
        </div>

        <div className="footer-sections-mobile">

          <div className="footer-section">
            <h4>Legal</h4>
            <ul>
              <li>
                <button
                  className="footer-link"
                  onClick={() => onNavigate('privacy')}
                >
                  Privacy Policy
                </button>
              </li>
              <li>
                <button
                  className="footer-link"
                  onClick={() => onNavigate('terms')}
                >
                  Terms of Service
                </button>
              </li>
              <li>
                <button
                  className="footer-link"
                  onClick={() => onNavigate('about')}
                >
                  About Us
                </button>
              </li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Contact</h4>
            <ul>
              <li>
                <button
                  className="footer-link"
                  onClick={() => onNavigate('contact')}
                >
                  Contact Us
                </button>
              </li>
              <li>
                <a href="mailto:<EMAIL>" className="footer-link">
                  Email
                </a>
              </li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Community</h4>
            <ul>
              <li>
                <button
                  className="footer-link"
                  onClick={() => onNavigate('guidelines')}
                >
                  Guidelines
                </button>
              </li>
              <li>
                <button
                  className="footer-link"
                  onClick={() => onNavigate('dmca')}
                >
                  DMCA Policy
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <div className="footer-bottom">
        <p>&copy; {currentYear} The Grin Bin. All rights reserved.</p>
        <p className="footer-disclaimer">
          Content from external APIs is provided by third parties.
          Community content is user-generated and moderated.
        </p>
      </div>
    </footer>
  );
};

export default Footer;
