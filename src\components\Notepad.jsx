import CopyAllIcon from '@mui/icons-material/CopyAll';
import ClearAllIcon from '@mui/icons-material/ClearAll';
import UndoIcon from '@mui/icons-material/Undo';
import { useState } from 'react';

const Notepad = ({
    savedJokes,
    savedFacts,
    savedRiddles, // Add this prop
    savedTrivia,
    notepadTab,
    setNotepadTab,
    copyNotepad,
    clearJokes,
    clearFacts,
    clearRiddles, // Add this prop
    clearTrivia,
    removeItem,  // New prop for removing individual items
    undoRemove,  // New prop for undoing removal
}) => {
    // Change from single lastRemoved to a history array
    const [removeHistory, setRemoveHistory] = useState([]);

    const getActiveItems = () => {
        switch (notepadTab) {
            case 'joke': return savedJokes;
            case 'fact': return savedFacts;
            case 'riddle': return savedRiddles;
            case 'trivia': return savedTrivia;
            default: return [];
        }
    };

    const handleRemoveItem = (index) => {
        const item = getActiveItems()[index];
        // Add to history instead of replacing
        setRemoveHistory([...removeHistory, { tab: notepadTab, item, index }]);
        removeItem(notepadTab, index);
    };

    const handleUndo = () => {
        if (removeHistory.length > 0) {
            // Get the last item from history
            const lastRemoved = removeHistory[removeHistory.length - 1];
            undoRemove(lastRemoved.tab, lastRemoved.item, lastRemoved.index);

            // Remove the last item from history
            setRemoveHistory(removeHistory.slice(0, -1));
        }
    };

    const currentItems = getActiveItems();

    return (
        <div className="notepad">
            <div className="notepad-tabs">
                <button
                    className={notepadTab === 'joke' ? 'active' : ''}
                    onClick={() => setNotepadTab('joke')}
                >
                    Dad Jokes
                </button>
                <button
                    className={notepadTab === 'fact' ? 'active' : ''}
                    onClick={() => setNotepadTab('fact')}
                >
                    Random Facts
                </button>
                <button
                    className={notepadTab === 'riddle' ? 'active' : ''}
                    onClick={() => setNotepadTab('riddle')}
                >
                    Riddles
                </button>
                <button
                    className={notepadTab === 'trivia' ? 'active' : ''}
                    onClick={() => setNotepadTab('trivia')}
                >
                    Trivia
                </button>
            </div>

            <div className="notepad-content">
                {currentItems.length === 0 ? (
                    <p className="notepad-empty">Nothing saved yet!</p>
                ) : (
                    <ul>
                        {currentItems.map((item, i) => (
                            <li key={i} className="notepad-item" onClick={() => handleRemoveItem(i)}>
                                {item}
                            </li>
                        ))}
                    </ul>
                )}

                <div className="notepad-actions" style={{ display: 'flex', gap: '0.5rem', justifyContent: 'flex-end', marginTop: '1rem' }}>
                    {removeHistory.length > 0 && (
                        <button onClick={handleUndo} title={`Undo Remove (${removeHistory.length})`} className="undo-button">
                            <UndoIcon /> {removeHistory.length > 1 && <span>{removeHistory.length}</span>}
                        </button>
                    )}
                    <button onClick={copyNotepad} title="Copy All">
                        <CopyAllIcon />
                    </button>
                    {/* Clear All button changes based on active tab */}
                    {notepadTab === 'joke' ? (
                        <button onClick={clearJokes} title="Clear All" className="clear-button">
                            <ClearAllIcon />
                        </button>
                    ) : notepadTab === 'fact' ? (
                        <button onClick={clearFacts} title="Clear All" className="clear-button">
                            <ClearAllIcon />
                        </button>
                    ) : notepadTab === 'riddle' ? (
                        <button onClick={clearRiddles} title="Clear All" className="clear-button">
                            <ClearAllIcon />
                        </button>
                    ) : (
                        <button onClick={clearTrivia} title="Clear All" className="clear-button">
                            <ClearAllIcon />
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Notepad;
