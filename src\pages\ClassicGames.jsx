import React, { useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeContext } from '../context/ThemeContext';
import Brightness4Icon from '@mui/icons-material/Brightness4';
import Brightness7Icon from '@mui/icons-material/Brightness7';
import Footer from '../components/Footer';
import CookieConsent from '../components/CookieConsent';
import logoLight from '../assets/text-only-modern.svg';
import logoDark from '../assets/text-only-modern-dark.svg';
import '../styles/app.css';
import '../styles/classic-games.css';

const ClassicGames = () => {
  const { darkMode, toggleTheme } = useContext(ThemeContext);
  const navigate = useNavigate();

  const handleNavigate = (page) => {
    navigate(`/${page}`);
  };

  return (
    <div className={`app ${darkMode ? 'dark-mode' : 'light-mode'}`}>
      <div className="theme-toggle">
        <button onClick={toggleTheme} className="theme-button">
          {darkMode ? <Brightness7Icon /> : <Brightness4Icon />}
        </button>
      </div>

      <div className="logo-container">
        <img
          src={darkMode ? logoDark : logoLight}
          alt="The Grin Bin"
          className="main-logo"
          onClick={() => navigate('/')}
          style={{ cursor: 'pointer' }}
        />
      </div>

      <div className="classic-games-container">
        <h1 className="page-title">Classic Games</h1>
        <p className="page-subtitle">Timeless entertainment coming soon!</p>

        <div className="games-grid">
          <div className="game-card" onClick={() => navigate('/brick-breaker')}>
            <div className="game-icon">🧱</div>
            <h3>Brick Breaker</h3>
            <p>Break all the bricks with your paddle and ball in this classic arcade game</p>
            <button className="play-button">Play Now!</button>
          </div>

          <div className="game-card" onClick={() => navigate('/snake')}>
            <div className="game-icon">🐍</div>
            <h3>Snake</h3>
            <p>Guide the snake to eat food and grow longer without hitting the walls</p>
            <button className="play-button">Play Now!</button>
          </div>

          <div className="game-card coming-soon">
            <div className="game-icon">🚀</div>
            <h3>Asteroids</h3>
            <p>Navigate your spaceship through an asteroid field and survive as long as possible</p>
            <div className="coming-soon-badge">Coming Soon</div>
          </div>

          <div className="game-card coming-soon">
            <div className="game-icon">🟦</div>
            <h3>Tetris</h3>
            <p>Arrange falling blocks to create complete lines in this timeless puzzle game</p>
            <div className="coming-soon-badge">Coming Soon</div>
          </div>
        </div>

        <div className="development-notice">
          <h2>🚧 Under Development</h2>
          <p>
            We're working hard to bring you an amazing collection of classic games!
            Each game will be carefully crafted to provide the best possible experience
            while maintaining the nostalgic feel of the originals.
          </p>
          <p>
            In the meantime, check out our <button
              className="inline-link"
              onClick={() => navigate('/wordplay')}
            >
              Word Play section
            </button> for jokes, facts, riddles, and trivia!
          </p>
        </div>
      </div>

      <Footer onNavigate={handleNavigate} />
      <CookieConsent />
    </div>
  );
};

export default ClassicGames;
