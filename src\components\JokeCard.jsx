import React from 'react';
import SaveAltIcon from '@mui/icons-material/SaveAlt';
import ShareIcon from '@mui/icons-material/Share';

const JokeCard = ({ text, onSave }) => {
    const handleShare = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: 'Check out this joke!',
                    text: text
                });
            } catch (error) {
                console.log('Error sharing:', error);
            }
        } else {
            // Fallback for browsers that don't support Web Share API
            navigator.clipboard.writeText(text);
            alert('Joke copied to clipboard!');
        }
    };

    return (
        <div className="joke-card card">
            <p>{text}</p>
            <div className="card-actions">
                <button className="save-button" onClick={onSave}>
                    <SaveAltIcon />
                </button>
                <button className="share-button" onClick={handleShare}>
                    <ShareIcon />
                </button>
            </div>
        </div>
    );
};

export default JokeCard;
